package com.yeepay.g3.core.aggregation.config.convert.decorator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.enumerate.BankAccountTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AliIndirectEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SiteInfo;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryRequestConvert;
import com.yeepay.g3.core.aggregation.config.external.constant.ChannelConst;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.SiteInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/18 18:52
 */
@Service
@Slf4j
public class AliIndirectChannelEntryRequestConvertImpl implements ChannelEntryRequestConvert<AliIndirectEntryApply> {
    private final ChannelEntryRequestConvert<AliIndirectEntryApply> channelEntryRequestConvert;

    public AliIndirectChannelEntryRequestConvertImpl(@Qualifier("baseChannelEntryRequestConvert")
                                                     ChannelEntryRequestConvert<AliIndirectEntryApply> baseChannelEntryRequestConvert) {
        this.channelEntryRequestConvert = baseChannelEntryRequestConvert;
    }

    @Override
    public OpenPayAsyncReportRequestDTO buildChannelRequest(final EntryApplyContext<AliIndirectEntryApply> context) {
        final OpenPayAsyncReportRequestDTO request = channelEntryRequestConvert.buildChannelRequest(context);
        request.setMerchantLevel(context.getEntryApply().getMerchantLevel());
        final List<SiteInfo> siteInfos = context.getEntryApply().getSiteInfos();
        final List<SiteInfoDTO> siteInfoList = siteInfos.stream()
                .filter(Objects::nonNull)
                .map(s -> {
                    SiteInfoDTO siteInfo = new SiteInfoDTO();
                    siteInfo.setSiteType(s.getSiteType());
                    siteInfo.setSiteUrl(s.getSiteUrl());
                    siteInfo.setSiteName(s.getSiteName());
                    return siteInfo;
                }).collect(Collectors.toList());

        request.setSiteInfoDTOList(siteInfoList);

        // 结算卡账户

        final SettleAccountInfo settleAccountInfo = context.getEntryApply().getSettleAccountInfo();
        final BankAccountTypeEnum bankAccountType = settleAccountInfo.getBankAccountType();
        request.setMerAccBank(settleAccountInfo.getBankName());
        request.setMerAccBankName(settleAccountInfo.getBankBranchName());
        request.setMerAccBankCode(settleAccountInfo.getBankCode());
        request.setMerAccBankNo(settleAccountInfo.getBranchCode());
        request.setMerAccBankType(bankAccountType == null ? null : bankAccountType.getChannelBankAccountType());
        request.setMerAccNo(settleAccountInfo.getCardNo());
        request.setMerAccName(settleAccountInfo.getCardName());
        request.setMerAccBankDistinctCode(settleAccountInfo.getDistinctCode());
        final Map<String, String> externalInfo = convertToExternalInfo(context.getEntryApply().getExtendInfo(),
                context.getEntryApply().getBackupSwitch());
        request.setExternalInfo(externalInfo);
        return request;
    }

    private static Map<String, String> convertToExternalInfo(final String extendInfo, final boolean backupSwitch) {
        Map<String, String> externalInfo = JsonUtils.fromJson(extendInfo, new TypeReference<Map<String, String>>() {
        });
        if (backupSwitch) {
            if (externalInfo != null) {
                externalInfo.put(ChannelConst.BACKUP_SWITCH, ChannelConst.ON);
            } else {
                externalInfo = new HashMap<>();
                externalInfo.put(ChannelConst.BACKUP_SWITCH, ChannelConst.ON);
            }
        }
        return externalInfo;
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        boolean flag = PaySceneEnum.DIRECT.equals(payScene) || PaySceneEnum.DIRECT_STANDARD.equals(payScene);
        return PayChannelEnum.ALIPAY == payChannel && !flag;
    }
}
