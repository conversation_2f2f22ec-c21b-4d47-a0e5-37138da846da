package com.yeepay.g3.core.aggregation.config.factory.decorator;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.auth.AuthApplyBO;
import com.yeepay.g3.core.aggregation.config.factory.handle.MerchantInfoStrategy;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;

/**
 * description: 事业单位商户信息装饰器
 * <AUTHOR>
 * @since 2025/5/22:11:19
 * Company: 易宝支付(YeePay)
 */
public class InstitutionsMerchantInfoDecorator extends MerchantInfoDecorator {

    public InstitutionsMerchantInfoDecorator(MerchantInfoStrategy MerchantInfoStrategy) {
        super(MerchantInfoStrategy);
    }

    @Override
    public AuthApplyBO buildAndValidateAuth(AuthApplyReqDTO authApplyReqDTO) {

        AuthApplyBO authApplyBO = super.buildAndValidateAuth(authApplyReqDTO);

        // 主体：政府机关、事业单位：可选填经办人
//        String legalType = authApplyReqDTO.getSubjectInfo().getCompanyRepresentativeInfo().getLegalType();
//        LegalTypeEnum legalTypeEnum = DocumentedEnum.fromValue(LegalTypeEnum.class, legalType);
//        authApplyBO.getSubjectInfo().getCompanyRepresentativeInfo().setLegalType(legalTypeEnum);
        return authApplyBO;
    }

}
