package com.yeepay.g3.core.aggregation.config.dao;

import com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppidControlDao {

    /**
     * 根据ID查询记录
     *
     * @param id 主键ID
     * @return AddidControlEntity 对象
     */
    AppidControlEntity queryById(Long id);

    /**
     * 根据APPID查询记录
     *
     * @param appid 应用ID
     * @return AddidControlEntity 对象列表
     */
    AppidControlEntity queryByAppid(@Param("appid") String appid);

    /**
     * 插入单条记录
     *
     * @param appidControlEntity 要插入的实体对象
     * @return 影响的行数
     */
    int insertSelective(AppidControlEntity appidControlEntity);


    /**
     * 更新单条记录
     *
     * @param appidControlEntity 要更新的实体对象
     * @param version
     * @return 影响的行数
     */
    int updateByIdVersion(@Param("entity") AppidControlEntity appidControlEntity, @Param("oldVersion") long version);


    /**
     * 查询管控列表
     * @param appid
     * @param notifyStatus
     * @return
     */
    List<AppidControlEntity> queryByControlList(@Param("appid") String appid, @Param("notifyStatus") String notifyStatus);
}
