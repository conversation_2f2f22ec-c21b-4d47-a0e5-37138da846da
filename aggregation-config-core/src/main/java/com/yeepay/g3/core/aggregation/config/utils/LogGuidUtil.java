package com.yeepay.g3.core.aggregation.config.utils;

import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.threadcontext.ThreadContextType;
import com.yeepay.g3.utils.rmi.soa.SoaSupportUtils;

/**
 * MQ线程池场景重刷 GUID
 */
public class LogGuidUtil {


    /**
     * 日志guid处理
     */
    public static void clearLogContext() {
        clearLogContext(ThreadContextType.TASK);
    }

    public static void clearLogContext(ThreadContextType threadContextType) {
        try {
            if (ThreadContextUtils.contextInitialized()) {
                ThreadContextUtils.clearContext();
            }
            ThreadContextUtils.initContext(SoaSupportUtils.getAppName(), null, threadContextType);
        } catch (Throwable t) {

        }
    }
}
