package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ChannelNoApplyStatusEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoCreateBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.QueryChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.convert.ThirdPartyRecordConvert;
import com.yeepay.g3.core.aggregation.config.dao.ChannelNoApplyRecordDao;
import com.yeepay.g3.core.aggregation.config.dao.ChannelNoDao;
import com.yeepay.g3.core.aggregation.config.dao.ThirdPartyRecordDao;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoApplyRecordEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.MerchantAuthApplyEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.service.ChannelNoService;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.List;

/**
 * description: 商户渠道号申请服务实现类
 * <AUTHOR>
 * @since 2025/6/2:23:36
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service("merchantChannelService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelNoServiceImpl implements ChannelNoService {

    private final ThirdPartyRecordDao thirdPartyRecordDao;

    private final ChannelNoApplyRecordDao applyRecordDao;

    private final ChannelNoDao channelNoDao;

    @Override
    public ChannelNoApplyRecordEntity queryChannelNoApply(String applyNo) {
        if (CheckUtils.isEmpty(applyNo)) {
            throw new BusinessException(ResultCode.PARAM_VALID_ERROR);
        }
        Weekend<ChannelNoApplyRecordEntity> weekend = Weekend.of(ChannelNoApplyRecordEntity.class);
        weekend.weekendCriteria().andEqualTo(ChannelNoApplyRecordEntity::getApplyNo, applyNo);
        return applyRecordDao.selectOneByExample(weekend);
    }

    @Override
    public ChannelNoApplyRecordEntity queryChannelNoApplyByBizApplyNo(BizSceneEnum bizSceneEnum,
                                                                      String bizApplyNo) {
        if (CheckUtils.isEmpty(bizApplyNo)) {
            throw new BusinessException(ResultCode.PARAM_VALID_ERROR);
        }
        Weekend<ChannelNoApplyRecordEntity> weekend = Weekend.of(ChannelNoApplyRecordEntity.class);
        WeekendCriteria<ChannelNoApplyRecordEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(ChannelNoApplyRecordEntity::getBizScene, bizSceneEnum.getDocument())
                .andEqualTo(ChannelNoApplyRecordEntity::getBizApplyNo, bizApplyNo);
        return applyRecordDao.selectOneByExample(weekend);
    }

    @Override
    public String saveChannelApplyRecord(ChannelNoApplyBO channelNoApplyBO) {
        ChannelNoApplyRecordEntity channelApplyEntity = ChannelNoApplyRecordEntity.build(channelNoApplyBO);
        // 保存商户认证申请记录
        applyRecordDao.insertSelective(channelApplyEntity);
        return channelApplyEntity.getApplyNo();
    }

    @Override
    public void updateChannelApplyResult(String requestNo, ChannelNoApplyResultBO result) {
        ChannelNoApplyStatusEnum channelStatus = ChannelNoApplyStatusEnum.getEnumByChannelStatus(result.getApplyResult());
        Weekend<ThirdPartyRecordEntity> thirdPartyRecordEntityWeekend = Weekend.of(ThirdPartyRecordEntity.class);
        WeekendCriteria<ThirdPartyRecordEntity, Object> thirdPartySelectExample = thirdPartyRecordEntityWeekend.weekendCriteria();
        thirdPartySelectExample.andEqualTo(ThirdPartyRecordEntity::getRequestNo, requestNo);
        // 更新第三方记录基础：渠道返回码、渠道返回信息
        ThirdPartyRecordEntity thirdPartyRecordEntity = ThirdPartyRecordConvert.convertUpdate(requestNo,
                result.getApplyResult(), result.getApplyResultDesc(),
                channelStatus.getThirdRecordStatus());
        thirdPartyRecordDao.updateByExampleSelective(thirdPartyRecordEntity, thirdPartyRecordEntityWeekend);

        // 更新商户渠道申请记录
        ThirdPartyRecordEntity oldRecordEntity = thirdPartyRecordDao.selectOneByExample(thirdPartyRecordEntityWeekend);
        ChannelNoApplyRecordEntity channelApplyEntity = ChannelNoApplyRecordEntity.buildUpdateStatus(oldRecordEntity.getApplyNo(), channelStatus, result);
        Weekend<ChannelNoApplyRecordEntity> applyWeekend = Weekend.of(ChannelNoApplyRecordEntity.class);
        WeekendCriteria<ChannelNoApplyRecordEntity, Object> channelCriteria = applyWeekend.weekendCriteria();
        channelCriteria.andEqualTo(ChannelNoApplyRecordEntity::getApplyNo, oldRecordEntity.getApplyNo());
        applyRecordDao.updateByExampleSelective(channelApplyEntity, applyWeekend);
    }

    @Override
    public ChannelNoApplyRecordEntity getChannelApplyRecord(QueryChannelNoApplyResultBO applyQueryBO) {
        if (CheckUtils.isEmpty(applyQueryBO.getMerchantNo())
                || CheckUtils.isEmpty(applyQueryBO.getPayChannel())
                || CheckUtils.isEmpty(applyQueryBO.getPayScene())
                || CheckUtils.isEmpty(applyQueryBO.getActivityType())
                ) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "查询渠道申请记录参数不完整");
        }

        Weekend<ChannelNoApplyRecordEntity> weekend = Weekend.of(ChannelNoApplyRecordEntity.class);
        WeekendCriteria<ChannelNoApplyRecordEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(ChannelNoApplyRecordEntity::getMerchantNo, applyQueryBO.getMerchantNo())
                .andEqualTo(ChannelNoApplyRecordEntity::getPayChannel, applyQueryBO.getPayChannel().getDocument())
                .andEqualTo(ChannelNoApplyRecordEntity::getPayScene, applyQueryBO.getPayScene().getDocument())
                .andEqualTo(ChannelNoApplyRecordEntity::getActivityType, applyQueryBO.getActivityType().getDocument())
                .andEqualTo(ChannelNoApplyRecordEntity::getDigitalCurrencyBankCode, applyQueryBO.getDigitalCurrencyBankCode());
        weekend.orderBy(Const.UPDATE_DT).desc();
        List<ChannelNoApplyRecordEntity> channelNoApplyRecordEntities = applyRecordDao.selectByExample(weekend);
        if (CheckUtils.isEmpty(channelNoApplyRecordEntities)) {
            return null;
        }
        return channelNoApplyRecordEntities.get(0);
    }

    @Override
    public List<ChannelNoApplyRecordEntity> queryUnfinishedApplyRecord(Date startTime, Date endTime) {
        if (CheckUtils.isEmpty(startTime) || CheckUtils.isEmpty(endTime)) {
            throw new BusinessException(ResultCode.PARAM_VALID_ERROR);
        }
        Weekend<MerchantAuthApplyEntity> weekend = Weekend.of(MerchantAuthApplyEntity.class);
        WeekendCriteria<MerchantAuthApplyEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(MerchantAuthApplyEntity::getStatus, ChannelNoApplyStatusEnum.APPLYING.getDocument())
                .andGreaterThanOrEqualTo(MerchantAuthApplyEntity::getUpdateDt, startTime)
                .andLessThan(MerchantAuthApplyEntity::getUpdateDt, endTime);
        weekend.orderBy(Const.UPDATE_DT).asc();
        return applyRecordDao.selectByExample(weekend);
    }

    @Override
    public void saveMerchantChannelRelation(ChannelNoCreateBO channelNoCreateBO) {
        ChannelNoEntity channelInfoEntity = ChannelNoEntity.build(channelNoCreateBO);
        // todo-LZX一期线上未建表
//        channelNoDao.insertSelective(channelInfoEntity);
    }

    @Override
    public ChannelNoEntity getChannelRelationResult(QueryChannelNoApplyResultBO applyQueryBO) {
        if (CheckUtils.isEmpty(applyQueryBO.getMerchantNo())
                || CheckUtils.isEmpty(applyQueryBO.getPayChannel())
                || CheckUtils.isEmpty(applyQueryBO.getPayScene())
                || CheckUtils.isEmpty(applyQueryBO.getActivityType())
        ) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "查询渠道申请记录参数不完整");
        }

        Weekend<ChannelNoEntity> weekend = Weekend.of(ChannelNoEntity.class);
        WeekendCriteria<ChannelNoEntity, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(ChannelNoEntity::getMerchantNo, applyQueryBO.getMerchantNo())
                .andEqualTo(ChannelNoEntity::getPayChannel, applyQueryBO.getPayChannel().getDocument())
                .andEqualTo(ChannelNoEntity::getPayScene, applyQueryBO.getPayScene().getDocument())
                .andEqualTo(ChannelNoEntity::getActivityType, applyQueryBO.getActivityType().getDocument());
        // todo-LZX一期线上未建表
        return ChannelNoEntity.builder().build();
//        return channelNoDao.selectOneByExample(weekend);
    }
}
