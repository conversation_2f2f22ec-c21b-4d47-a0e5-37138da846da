package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored;

import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request.AnchoredApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.AnchoredInfoDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.MerchantInfoDTO;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/2 17:27
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
public class AnchoredCmd {

    /**
     * 商户编号
     */
    private final String merchantNo;

    /**
     * 父商编
     */
    private final String parentMerchantNo;

    /**
     * 顶级商户编号
     */
    private final String topLevelMerchantNo;

    /**
     * 业务方编码
     */
    private final BizSceneEnum bizScene;

    /**
     * 业务方请求号
     */
    private final String bizApplyNo;

    /**
     * 上游想批量执行挂靠
     */
    private List<AnchoredDetailCmd> anchoredDetailCmdList;

    public static AnchoredCmd build(final AnchoredInfoDTO anchoredInfoDTO, final MerchantInfoDTO merchantInfo,
                                    final PaySceneEnum payScene, final PayChannelEnum payChannel,
                                    final ActivityTypeEnum activityType) {
        final AnchoredType anchoredType = DocumentedEnum.fromValue(AnchoredType.class, anchoredInfoDTO.getAnchoredType());
        Assert.isFalse(AnchoredType.DESIGNATE_MERCHANT == anchoredType
                && StringUtils.isBlank(anchoredInfoDTO.getAnchoredMerchantNo()), ResultCode.PARAM_VALID_ERROR, "挂靠类型不合法");

        List<AnchoredDetailCmd> anchoredDetailCmdList = Lists.newArrayList(AnchoredDetailCmd
                .build(anchoredInfoDTO, payScene, payChannel, activityType));

        return AnchoredCmd.builder()
                .bizScene(BizSceneEnum.AGG_ENTRY)
                .merchantNo(merchantInfo.getMerchantNo())
                .parentMerchantNo(merchantInfo.getParentMerchantNo())
                .topLevelMerchantNo(merchantInfo.getTopLevelMerchantNo())
                .anchoredDetailCmdList(anchoredDetailCmdList)
                .build();
    }

    public static AnchoredCmd build(AnchoredApplyReqDTO reqDTO) {

        List<AnchoredDetailCmd> anchoredDetailCmdList = reqDTO.getAnchoredApplyDetailList().stream()
                .map(AnchoredDetailCmd::build)
                .collect(Collectors.toList());

        return builder()
                .merchantNo(reqDTO.getMerchantNo())
                .parentMerchantNo(reqDTO.getParentMerchantNo())
                .topLevelMerchantNo(reqDTO.getTopLevelMerchantNo())
                .bizScene(DocumentedEnum.fromValue(BizSceneEnum.class, reqDTO.getBizScene()))
                .bizApplyNo(reqDTO.getBizApplyNo())
                .anchoredDetailCmdList(anchoredDetailCmdList)
                .build();
    }

}
