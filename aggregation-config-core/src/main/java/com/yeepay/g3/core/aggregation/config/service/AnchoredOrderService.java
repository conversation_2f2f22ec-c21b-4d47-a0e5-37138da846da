package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredOrder;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity;

import java.util.Date;
import java.util.List;

/**
 * @author: Mr.yin
 * @date: 2025/6/8  16:32
 */
public interface AnchoredOrderService {
    /**
     * 根据业务方+请求号查询订单是否存在
     * @param bizScene
     * @param bizApplyNo
     * @return
     */
    AnchoredOrderEntity queryAnchoredOrderByRequestNo(String bizScene, String bizApplyNo);

    /**
     * 根据orderNo 查询订单是否存在
     * @param orderNo
     * @return
     */
    AnchoredOrderEntity queryAnchoredOrderByOrderNo(String orderNo);

    /**
     * 创建订单
     * @param anchoredOrder
     */
    void createAnchoredOrder(AnchoredOrder anchoredOrder);

    /**
     * 更新订单完成
     * @param orderNo
     * @return
     */
    Boolean updateAnchoredOrderFinish(String orderNo);

    /**
     * 补偿查询未闭环的订单
     */
    List<AnchoredOrderEntity> selectUnFinishAnchoredOrderList(List<String> orderNoList, Date startTime, Date endTime);
}
