package com.yeepay.g3.core.aggregation.config.mq;

import com.yeepay.g3.core.aggregation.config.biz.AttachResultBiz;
import com.yeepay.g3.core.aggregation.config.biz.ChannelAppIdBindBiz;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAttachEvent;
import com.yeepay.g3.core.aggregation.config.utils.SpringContextUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2025-01-02 13:24
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
public class ChannelAttachListener extends MqBaseListener<ChannelAttachEvent> {

    public ChannelAttachListener(Class<ChannelAttachEvent> channelAttachEventClass) {
        super(channelAttachEventClass);
    }

    @Override
    public String topicName() {
        return "同步商户挂靠关系";
    }

    @Override
    public void onMessage(ChannelAttachEvent message) throws Exception {
        //查询客户中心
        MerchantGroupEntity merchantGroupEntity = null;
        try {
            MerchantCenterExternal merchantCenterExternal = SpringContextUtils.getBean(MerchantCenterExternal.class);
            merchantGroupEntity = merchantCenterExternal.queryMerchantGroupByMerchantNo(message.getYeepayMerchantNo());
        }catch (Throwable e) {
            throw new AggConfigException(ErrorCodeEnum.MQ_CONSUMER_ERROR,e.getMessage());
        }

        //处理落库
        Pair<Boolean, AttachResultEntity> handle;
        try {
            handle =SpringContextUtils.getBean(AttachResultBiz.class).handle(message, merchantGroupEntity);
        }catch (Throwable e){
            log.error("接收挂靠数据异常,e=",e);
            throw new AggConfigException(ErrorCodeEnum.MQ_CONSUMER_ERROR,e.getMessage());
        }
        if (handle.getKey()) {
            SpringContextUtils.getBean("asyncExecutor", AtlasThreadPoolExecutor.class)
                    .execute(() ->
                    SpringContextUtils.getBean(ChannelAppIdBindBiz.class).attachBind(handle.getValue())
                    );
        }
    }
}
