package com.yeepay.g3.core.aggregation.config.biz.atmanage.impl;

import com.yeepay.g3.core.aggregation.config.biz.atmanage.AuthBiz;
import com.yeepay.g3.core.aggregation.config.external.ChannelAuthExternal;
import com.yeepay.g3.core.aggregation.config.service.MerchantAuthService;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: 认证业务实现类
 * <AUTHOR>
 * @since 2025/5/21:15:01
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service("authBiz")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthBizImpl implements AuthBiz {

    private final MerchantAuthService authService;

    private final ChannelAuthExternal channelAuthExternal;

    private final ThirdPartyRecordService thirdPartyRecordService;

//    @Override
//    public String authApply(AuthApplyBO authApplyBO) {
//
//        AuthQueryBO queryAuthRecord = AuthQueryBO.builder()
//                .channel(authApplyBO.getChannelType())
//                .payScene(authApplyBO.getPayScene())
//                .channelIdentifier(authApplyBO.getChannelIdentifier())
//                .channelNo(authApplyBO.getChannelNo())
//                .bizApplyNo(authApplyBO.getBizApplyNo())
//                .build();
//        MerchantAuthApplyEntity merchantAuthApplyEntity = authService.queryAuthRecord(queryAuthRecord);
//        Assert.notNull(merchantAuthApplyEntity, ResultCode.PARAM_VALID_ERROR, "商户认证申请已存在，请勿重复提交。如果需修改，先撤销认证申请");
//
//        // 1.保存数据
//        String applyNo = authService.saveAuthRecord(authApplyBO);
//
//        // 保存第三方记录
//        ThirdPartyRecordEntity recordEntity = ThirdPartyRecordConvert.convert(applyNo, ThirdPartyBusinessTypeEnum.MERCHANT_AUTH, Const.SYSTEM);
//        thirdPartyRecordService.saveThirdPartyRecord(recordEntity);
//        // 2.调用认证接口
//        AuthApplyResultBO applyResultBO = channelAuthExternal.submitIdentityAuth(recordEntity.getRequestNo(), authApplyBO);
//
//        // 3.更新结果
//        authService.updateAuthApplyResult(recordEntity.getRequestNo(), applyResultBO);
//
//        if (!"成功".equals(applyResultBO.getReturnCode())) {
//            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, applyResultBO.getReturnMessage());
//        }
//
//        // 4.返回结果
//        return applyResultBO.getChannelApplyNo();
//    }
//
//    @Override
//    public void authCancel(AuthCancelBO authCancelBO) {
//        // 1.校验是否存在申请记录
//        AuthQueryBO authQueryBO = AuthQueryBO.builder().payScene(authCancelBO.getPayScene())
//                .channelNo(authCancelBO.getChannelNo())
//                .channelIdentifier(authCancelBO.getChannelIdentifier())
//                .bizApplyNo(authCancelBO.getBizApplyNo())
//                .channelApplyNo(authCancelBO.getChannelApplyNo()).build();
//        MerchantAuthApplyEntity merchantAuthApplyEntity = authService.queryAuthRecord(authQueryBO);
//        if (merchantAuthApplyEntity == null) {
//            // 无记录需要透传
//            log.info("商户认证取消申请,未找到对应的认证申请记录,进行透传,请求参数:{}", authCancelBO);
//            channelAuthExternal.cancelIdentityAuth(authCancelBO.getBizApplyNo(), authCancelBO);
//            return;
//        }
//
//        if (AuthStatusEnum.AUDIT_CANCELED.getDocument().equals(merchantAuthApplyEntity.getStatus())) {
//            log.info("商户认证取消申请,认证状态已是取消状态,请求参数:{}", authCancelBO);
//            return;
//        }
//
//        if (AuthStatusEnum.AUDIT_PASS.getDocument().equals(merchantAuthApplyEntity.getStatus())) {
//            log.error("商户认证取消申请,认证状态不允许取消,请求参数:{}", authCancelBO);
//            throw new AggConfigException(ErrorCodeEnum.DATA_STATUS_ERROR, "未找到可取消的认证申请记录,请检查认证状态");
//        }
//
//        // 2.调用渠道取消认证接口
//        channelAuthExternal.cancelIdentityAuth(merchantAuthApplyEntity.getRequestNo(), authCancelBO);
//
//        // 3.更新取消结果
//        authService.cancelAuthApply(merchantAuthApplyEntity.getRequestNo());
//    }
//
//    @Override
//    public AuthResultBO queryAuthResult(AuthQueryBO authQueryBO) {
//        // 1.校验是否存在申请记录
//        MerchantAuthApplyEntity merchantAuthApplyEntity = authService.queryAuthRecord(authQueryBO);
//        if (merchantAuthApplyEntity == null) {
//            log.warn("商户认证查询,未找到对应的认证申请记录,进行透传");
//            // 2.调用渠道查询认证结果接口
//            return channelAuthExternal.queryIdentityAuth(merchantAuthApplyEntity.getRequestNo(), authQueryBO);
//        }
//
//        if (DocumentedEnum.stringInRightEnums(merchantAuthApplyEntity.getStatus(),
//                AuthStatusEnum.AUDIT_CANCELED, AuthStatusEnum.AUDIT_PASS,
//                AuthStatusEnum.AUDIT_REJECT, AuthStatusEnum.AUDIT_FREEZE)) {
//            log.info("商户认证查询,认证状本地返回");
//            return AuthResultBO.builder()
//                    .authStatusEnum(DocumentedEnum.fromValue(AuthStatusEnum.class, merchantAuthApplyEntity.getStatus()))
//                    .qrcodeData(merchantAuthApplyEntity.getQrcodeData())
//                    .rejectReasonList(JsonUtils.fromJson(merchantAuthApplyEntity.getRejectReason(), new TypeReference<List<AuthRejectReasonBO>>() {}))
//                    .build();
//        }
//
//        // 2.如果处理中申请记录，调用渠道查询认证结果接口
//        return queryAndUpdateAuthResult(merchantAuthApplyEntity.getRequestNo(), authQueryBO);
//    }
//
//    @Override
//    public void handleAuthResult(String requestNo, Date startTime, Date endTime) {
//        ThirdPartyRecord thirdPartyRecord = null;
//        if (!CheckUtils.isEmpty(requestNo)) {
//            thirdPartyRecord = thirdPartyRecordService.queryThirdRecordByRequestNo(requestNo, ThirdPartyBusinessTypeEnum.MERCHANT_AUTH.name());
//
//        }
//        List<MerchantAuthApplyEntity> needHandleList = ScheduleUtils.getScheduleList(thirdPartyRecord,
//                ()-> authService.queryUnfinishedAuthApply(startTime, endTime),
//                authService::queryAuthApplyByApplyNo);
//
//        if (CheckUtils.isEmpty(needHandleList)) {
//            log.info("handleAuthResultSchedule: 未查询到需要处理的认证申请记录");
//            return;
//        }
//
//        // 2.遍历需要处理的认证申请记录，查询并更新认证结果
//        for (MerchantAuthApplyEntity authApplyEntity : needHandleList) {
//            queryAndUpdateAuthResult(authApplyEntity.getRequestNo(),
//                    AuthQueryBO.builder()
//                            .channel(DocumentedEnum.fromValue(PayChannelEnum.class, authApplyEntity.getInstitutionCode()))
//                            .payScene(DocumentedEnum.fromValue(PaySceneEnum.class, authApplyEntity.getPayScene()))
//                            .channelIdentifier(authApplyEntity.getChannelIdentifier())
//                            .channelNo(authApplyEntity.getChannelNo())
//                            .bizApplyNo(authApplyEntity.getBizApplyNo())
//                            .build());
//        }
//    }
//
//    /**
//     * 查询并更新认证结果
//     * @param requestNo 请求号
//     * @param authQueryBO 认证查询业务对象
//     * @return 认证结果业务对象
//     */
//    private AuthResultBO queryAndUpdateAuthResult(String requestNo, AuthQueryBO authQueryBO) {
//
//        // 1.调用渠道查询认证结果接口
//        AuthResultBO authResultBO = channelAuthExternal.queryIdentityAuth(requestNo, authQueryBO);
//
//        // 2.更新认证结果
//        authService.updateAuthResult(requestNo, authResultBO);
//
//        // 3.返回认证结果
//        return authResultBO;
//    }

}
