package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.facade.aggregation.config.enums.ControlStatusEnum;

/**
 * @description: 管控配置下层服务层
 * @author: xuchen.liu
 * @date: 2024-12-13 15:12
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface ControlConfigService {

    /**
     * 保存配置
     * @param controlConfigEntity controlConfigEntity
     */
    void saveConfig(ControlConfigEntity controlConfigEntity);

    /**
     * 修改配置
     * @param controlConfigEntity controlConfigEntity
     */
    void  updateConfig(ControlConfigEntity controlConfigEntity);

    /**
     * 根据id查询配置信息
     * @param id 主键id
     */
    ControlConfigEntity queryById(Long id);

    /**
     * 根据商编查询有效的配置数据
     *
     * @param merchantNo    商户编号
     * @param controlStatus 配置状态
     * @return controlConfigEntity
     */
    ControlConfigEntity queryValidByMerchantNo(String merchantNo, ControlStatusEnum controlStatus);
}
