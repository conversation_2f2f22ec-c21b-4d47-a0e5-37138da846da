package com.yeepay.g3.core.aggregation.config.external.impl;

import com.google.common.collect.Sets;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.enums.DivideReceiverStatus;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.ChannelDivideExternal;
import com.yeepay.g3.core.aggregation.config.external.ChannelNoExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.DivideCreateSubmitResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelDivideInfo;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelNoSignSubjectBO;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.trade.bankcooper.OpenPayAsyncReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenDivideReceiverQueryRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenDivideReceiverQueryResponseDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenSubmitDivideReceiverRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenSubmitDivideReceiverResponseDTO;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 渠道微信直连 分账方相关
 *
 * @author: Mr.yin
 * @date: 2025/6/13  18:14
 */
@Service
public class ChannelDivideExternalImpl implements ChannelDivideExternal {
    @Resource
    private ChannelNoExternal channelNoExternal;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private OpenPayAsyncReportFacade openPayAsyncReportFacade = RemoteServiceFactory.getService(OpenPayAsyncReportFacade.class);

    private static final String BANK_CHANNEL_SUCCESS = "0000";

    /**
     * 850003 请求号重复
     * 840005 分账接收方记录已存在 理论上没了
     * 851056 聚合分账接收方正在处理中
     * 851057 聚合分账接收方已成功
     * 899999 系统未知异常
     */
    private final static Set<String> SUBMIT_ORDER_PROCESS = Sets.newHashSet("840005", "850003", "851056", "851057", "899999");


    /**
     * 查询订单不存在的码
     */
    private final static Set<String> DIVIDE_ORDER_NOT_EXIST = Sets.newHashSet("840005", "851055", "830005");


    @Override
    public RemoteResult<DivideCreateSubmitResultBO> createDivideReceiver(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo) {
        OpenSubmitDivideReceiverRequestDTO requestDTO = new OpenSubmitDivideReceiverRequestDTO();
        requestDTO.setYeepayMerchantNo(entryApply.getMerchantInfo().getMerchantNo());
        requestDTO.setYeepayMerchantName(entryApply.getMerchantInfo().getMerchantName());
        requestDTO.setIndustryName(entryApply.getMerchantInfo().getIndustryLine());
        requestDTO.setBankCode("WECHAT");
        requestDTO.setRelationType("SERVICE_PROVIDER");
        requestDTO.setReportFee("WECHAT_DIRECT");
        requestDTO.setSource(Const.DEFAULT_SYSTEM_NAME);
        requestDTO.setChannelNo(entryApply.getChannelNo());
        // 查询渠道号信息
        RemoteResult<ChannelNoSignSubjectBO> channelInfo = channelNoExternal.queryChannelSubjectByChannelNo(entryApply.getChannelNo());
        if (!channelInfo.isSuccess()) {
            throw new AggConfigException(channelInfo.getChannelCode(), channelInfo.getChannelMessage());
        } else if (null == channelInfo.getData()) {
            return RemoteResult.fail(ResultCode.CHANNEL_NO_QUERY_ERROR.getCode(), "未查询到渠道号信息：" + entryApply.getChannelNo());
        }
        requestDTO.setReceiveMerchantNo(channelInfo.getData().getChannelMerchantNo());
        requestDTO.setRequestOrderNo(requestNo);

        OpenSubmitDivideReceiverResponseDTO responseDTO = null;
        try {
            logger.info("[remote][请求通道开通直连分账方]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = openPayAsyncReportFacade.submitDivideReceiver(requestDTO);
            logger.info("[remote][请求通道开通直连分账方]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[remote][请求通道开通直连分账方] 调用异常 requestDTO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (null == responseDTO) {
            logger.error("[remote][请求通道开通直连分账方] 调用异常返回为null requestDTO={%s})", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        /*840005-已存在申请的记录*/
        if (!BANK_CHANNEL_SUCCESS.equals(responseDTO.getBizCode())
                && !SUBMIT_ORDER_PROCESS.contains(responseDTO.getBizCode())) {
            logger.warn("[remote][请求通道开通直连分账方] 通道处理失败 requestDTO={}", JsonUtils.toJSONString(requestDTO));
            return RemoteResult.fail(responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        DivideCreateSubmitResultBO resultBO = new DivideCreateSubmitResultBO();
        resultBO.setChannelOrderNo(requestDTO.getTraceId());
        resultBO.setStatus(DivideReceiverStatus.SUBMIT_PROCESS);
        if ("851057".equals(responseDTO.getBizCode())) {
            resultBO.setStatus(DivideReceiverStatus.SUCCESS);
        }
        logger.info("[remote][请求通道开通直连分账方]  结果返回 resultBO={}", JsonUtils.toJSONString(resultBO));
        return RemoteResult.success(resultBO, responseDTO.getBizCode(), responseDTO.getBizMsg());
    }


    @Override
    public RemoteResult<ChannelDivideInfo> queryDivideReceiver(String merchantNo, String channelNo, String requestNo) {
        OpenDivideReceiverQueryRequestDTO requestDTO = new OpenDivideReceiverQueryRequestDTO();
        requestDTO.setYeepayMerchantNo(merchantNo);
        // 查询渠道号信息
        RemoteResult<ChannelNoSignSubjectBO> channelInfo = channelNoExternal.queryChannelSubjectByChannelNo(channelNo);
        if (!channelInfo.isSuccess()) {
            throw new AggConfigException(channelInfo.getChannelCode(), channelInfo.getChannelMessage());
        } else if (null == channelInfo.getData()) {
            return RemoteResult.fail(ResultCode.CHANNEL_NO_QUERY_ERROR.getCode(), "未查询到渠道号信息：" + channelNo);
        }
        requestDTO.setReceiveMerchantNo(channelInfo.getData().getChannelMerchantNo());
        requestDTO.setChannelNo(channelNo);
        requestDTO.setBankCode("WECHAT");
        requestDTO.setReportFee("WECHAT_DIRECT");
        requestDTO.setRequestOrderNo(requestNo);
        requestDTO.setSource(Const.DEFAULT_SYSTEM_NAME);
        OpenDivideReceiverQueryResponseDTO responseDTO = null;
        try {
            logger.info("[remote][查询分账方结果]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = openPayAsyncReportFacade.queryDivideReceiver(requestDTO);
            logger.info("[remote][查询分账方结果]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[remote][查询分账方结果] 调用异常 requestDTO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (null == responseDTO) {
            logger.error("[remote][查询分账方结果] 调用异常返回为null requestDTO={%s})", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        /*0000：表示查询成功，其它为失败
            -申请的记录不存在*/
        if (DIVIDE_ORDER_NOT_EXIST.contains(responseDTO.getBizCode())) {
            return RemoteResult.success(responseDTO.getBizCode(), responseDTO.getBizMsg());
        } else if (BANK_CHANNEL_SUCCESS.equals(responseDTO.getBizCode())) {
            ChannelDivideInfo channelDivideInfo = new ChannelDivideInfo();
            channelDivideInfo.setBankOrderNo(null);
            channelDivideInfo.setFailCode(responseDTO.getApplyResultCode());
            channelDivideInfo.setFailReason(responseDTO.getApplyResultDes());
            channelDivideInfo.setTopMerchantNo(responseDTO.getYeepayMerchantNo());
            channelDivideInfo.setMerchantNo(responseDTO.getReceiveMerchantNo());
            /*精准状态*/
            if ("SUCCESS".equals(responseDTO.getStatus())) {
                channelDivideInfo.setStatus(DivideReceiverStatus.SUCCESS);
            } else if ("FAIL".equals(responseDTO.getStatus())) {
                channelDivideInfo.setStatus(DivideReceiverStatus.FAIL);
            } else {
                channelDivideInfo.setStatus(DivideReceiverStatus.SUBMIT_PROCESS);
            }
            logger.info("[remote][查询分账方结果] 返回结果 reportResultBO={}", JsonUtils.toJSONString(channelDivideInfo));
            return RemoteResult.success(channelDivideInfo, responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        return RemoteResult.fail(responseDTO.getBizCode(), responseDTO.getBizMsg());
    }
}
