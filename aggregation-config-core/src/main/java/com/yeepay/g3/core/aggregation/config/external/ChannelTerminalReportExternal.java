package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.TerminalReportSubmitResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelTerminalReportResultBO;

/**
 * 通道终端报备相关接口
 */

public interface ChannelTerminalReportExternal {

    /**
     * 通道终端报备 这是个异步接口,,,,
     * 只有微信支付宝才需要
     * 851006:真正报备成功(回调报备成功后)再次相同的参数请求，返回次返回码 这种情况接不到回调，所以要特殊处理哦
     */
    RemoteResult<TerminalReportSubmitResultBO> applyChannelTerminalReport(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo);


    /**
     * 查询终端报备结果
     *
     * @param merchantNo
     * @param payChannel
     * @param requestNo  请求通道时的请求号
     * @return
     */
    RemoteResult<ChannelTerminalReportResultBO> queryChannelTerminalReport(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo);
}
