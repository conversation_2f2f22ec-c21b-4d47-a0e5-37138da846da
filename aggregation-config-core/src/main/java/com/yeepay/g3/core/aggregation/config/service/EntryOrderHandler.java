package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;

/**
 * <AUTHOR>
 * @date 2025/6/4 18:52
 */
public interface EntryOrderHandler {

     void handle(final EntryApplyContext<? extends EntryApply<? extends ChannelSpecialParams>> context);
}
