package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.TerminalReportBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WxIndirectEntryApply;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/8 22:47
 */
@Service
public class WechatIndirectEntryApplyHandler implements EntryApplyHandler<WxIndirectEntryApply> {

    private final EntryApplyHandler<WxIndirectEntryApply> baseEntryApplyHandler;
    private final TerminalReportBiz terminalReportBiz;

    @Autowired
    public WechatIndirectEntryApplyHandler(@Qualifier("baseEntryApplyHandler") final EntryApplyHandler<WxIndirectEntryApply> baseEntryApplyHandler,
                                           final TerminalReportBiz terminalReportBiz) {
        this.baseEntryApplyHandler = baseEntryApplyHandler;
        this.terminalReportBiz = terminalReportBiz;
    }

    @Override
    public void buildChannelNo(final WxIndirectEntryApply entryApply) {
        baseEntryApplyHandler.buildChannelNo(entryApply);
    }

    @Override
    public void buildBackupInfo(final WxIndirectEntryApply entryApply) {
        baseEntryApplyHandler.buildBackupInfo(entryApply);
    }

    @Override
    public ChannelEntryResult entryApply(final EntryApplyContext<WxIndirectEntryApply> entryApplyContext) {
        return baseEntryApplyHandler.entryApply(entryApplyContext);
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        boolean flag = PaySceneEnum.DIRECT.equals(payScene) || PaySceneEnum.DIRECT_STANDARD.equals(payScene)
                || PaySceneEnum.STORE_ASST.equals(payScene);
        return PayChannelEnum.WECHAT.equals(payChannel) && !flag;
    }

    @Override
    public WxIndirectEntryApply buildEntryApply(ChannelEntryOrderEntity orderEntity, ChannelEntryApplyDetailEntity detailEntity) {
        return new WxIndirectEntryApply(orderEntity, detailEntity);
    }

    @Override
    public void resultHandle(final EntryApplyContext<WxIndirectEntryApply> context,
                             final ChannelEntryResult channelEntryResult) {
        baseEntryApplyHandler.resultHandle(context, channelEntryResult);
        //如果进件成功且是需要终端报备 进行终端报备申请
        if (context.getEntryApply().canTerminalReport()) {
            terminalReportBiz.applyChannelTerminalReport(context.getEntryApply());
        }

    }
}
