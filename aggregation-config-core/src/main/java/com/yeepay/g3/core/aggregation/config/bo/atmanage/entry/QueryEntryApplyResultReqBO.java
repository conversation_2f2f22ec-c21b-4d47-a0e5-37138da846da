package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.QueryEntryAndAnchoredRequestDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * 查询聚合进件申请结果请求参数
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:50
 */
@Builder
@Getter
public class QueryEntryApplyResultReqBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 支付渠道
     */
    private PayChannelEnum payChannel;

    /**
     * 场景
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;

    /**
     * 数币银行编码
     */
    private String digitalCurrencyBankCode;


    public static QueryEntryApplyResultReqBO build(QueryEntryAndAnchoredRequestDTO requestDTO) {
        if (CheckUtils.isEmpty(requestDTO.getPayChannel()) || CheckUtils.isEmpty(requestDTO.getPayScene())) {
            return QueryEntryApplyResultReqBO.builder().merchantNo(requestDTO.getMerchantNo()).build();
        } else {
            PayChannelEnum payChannelEnum = DocumentedEnum.fromValue(PayChannelEnum.class, requestDTO.getPayChannel());
            PaySceneEnum paySceneEnum = DocumentedEnum.fromValue(PaySceneEnum.class, requestDTO.getPayScene());
            ActivityTypeEnum activityTypeEnum = CheckUtils.isEmpty(requestDTO.getActivityType()) ? null
                    : DocumentedEnum.fromValue(ActivityTypeEnum.class, requestDTO.getActivityType());
            return QueryEntryApplyResultReqBO.builder()
                    .merchantNo(requestDTO.getMerchantNo())
                    .payChannel(payChannelEnum)
                    .payScene(paySceneEnum)
                    .activityType(activityTypeEnum)
                    .digitalCurrencyBankCode(requestDTO.getDigitalCurrencyBankCode())
                    .build();
        }

    }


}

