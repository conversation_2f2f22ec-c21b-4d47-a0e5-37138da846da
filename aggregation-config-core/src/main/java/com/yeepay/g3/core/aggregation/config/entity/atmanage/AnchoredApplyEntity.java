package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredOrder;
import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/4 20:19
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_anchored_apply")
public class AnchoredApplyEntity extends BaseEntity {
    /**
     * 业务场景
     */
    @Column(name = "biz_scene", length = 16)
    private String bizScene;

    /**
     * 业务申请单号
     */
    @Column(name = "biz_apply_no", length = 32)
    private String bizApplyNo;


    @Column(name = "order_no", length = 32)
    private String orderNo;

    /**
     * 申请单号
     */
    @Column(name = "apply_no")
    private String applyNo;

    @Column(name = "merchant_no")
    private String merchantNo;

    @Column(name = "parent_merchant_no")
    private String parentMerchantNo;

    @Column(name = "top_merchant_no")
    private String topMerchantNo;

    @Column(name = "anchored_type")
    private String anchoredType;

    @Column(name = "anchored_dimension")
    private String anchoredDimension;

    @Column(name = "relation_merchant_no")
    private String relationMerchantNo;

    @Column(name = "pay_scene")
    private String payScene;

    @Column(name = "pay_channel")
    private String payChannel;

    @Column(name = "activity_type")
    private String activityType;

    @Column(name = "support_pay_type")
    private Long supportPayType;

    /**
     * 授权函文件名称
     */
    @Column(name = "auth_file_name")
    private String authFileName;

    /**
     * 授权函文件地址
     */
    @Column(name = "auth_file_url")
    private String authFileUrl;

    /**
     * 集团名称
     */
    @Column(name = "group_name")
    private String groupName;

    @Column(name = "anchored_status")
    private String anchoredStatus;

    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 聚合挂靠商编信息
     */
    @Column(name = "agg_anchored_merchant_text")
    private String aggAnchoredMerchantText;

    /**
     * 构建方法
     *
     * @param anchoredApply 挂靠申请BO
     * @return 挂靠申请实体
     */
    public static AnchoredApplyEntity build(AnchoredApply anchoredApply, AnchoredOrder anchoredOrder) {
        AnchoredApplyEntity anchoredApplyEntity = AnchoredApplyEntity.builder().build();
        anchoredApplyEntity.setBizScene(anchoredApply.getBizScene().getDocument());
        anchoredApplyEntity.setBizApplyNo(anchoredApply.getBizApplyNo());
        anchoredApplyEntity.setOrderNo(anchoredApply.getOrderNo());
        anchoredApplyEntity.setApplyNo(anchoredApply.getApplyNo());
        anchoredApplyEntity.setMerchantNo(anchoredApply.getMerchantNo());
        anchoredApplyEntity.setParentMerchantNo(anchoredOrder.getParentMerchantNo());
        anchoredApplyEntity.setTopMerchantNo(anchoredOrder.getTopLevelMerchantNo());
        anchoredApplyEntity.setAnchoredType(anchoredApply.getAnchoredType().getDocument());
        anchoredApplyEntity.setAnchoredDimension(null == anchoredApply.getAnchoredDimension() ? "false" : anchoredApply.getAnchoredDimension().toString());
        anchoredApplyEntity.setRelationMerchantNo(anchoredApply.getRelationMerchantNo());
        anchoredApplyEntity.setPayScene(anchoredApply.getPayScene().getDocument());
        anchoredApplyEntity.setPayChannel(anchoredApply.getChannelType().getDocument());
        anchoredApplyEntity.setActivityType(null == anchoredApply.getActivityType() ? null : anchoredApply.getActivityType().getDocument());
        anchoredApplyEntity.setSupportPayType(anchoredApply.getSupportPayType());
        anchoredApplyEntity.setAuthFileName(anchoredApply.getAuthFileName());
        anchoredApplyEntity.setAuthFileUrl(anchoredApply.getAuthFileUrl());
        anchoredApplyEntity.setGroupName(anchoredApply.getGroupName());
        anchoredApplyEntity.setAnchoredStatus(anchoredApply.getAnchoredStatus().getDocument());
//        anchoredApplyEntity.setFinishTime(null);
//        anchoredApplyEntity.setFailReason(null);
//        anchoredApplyEntity.setId(anchoredApply.getId());
//        anchoredApplyEntity.setValid(anchoredApply.getValid());
//        anchoredApplyEntity.setUpdatedBy(anchoredApply.getUpdatedBy());
//        anchoredApplyEntity.setUpdateDt(LocalDateTime.now());
//        anchoredApplyEntity.setCreatedBy(anchoredApply.getCreatedBy());
        anchoredApplyEntity.setCreateDt(LocalDateTime.now());
        return anchoredApplyEntity;
    }
}
