package com.yeepay.g3.core.aggregation.config.builder;

import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.facade.aggregation.config.dto.ControlConfigRequestDTO;
import com.yeepay.g3.facade.aggregation.config.enums.ControlNotifyWayEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * @description: 参数转换
 * @author: xuchen.liu
 * @date: 2024-12-13 18:12
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Mapper(componentModel = "spring")
public interface ControlConfigBuilder {

    ControlConfigBuilder INSTANCE = Mappers.getMapper(ControlConfigBuilder.class);

    @Mapping(source = "emailAddress", target = "emailAddress", qualifiedByName = "stringToList")
    @Mapping(source = "ccAddress", target = "ccAddress", qualifiedByName = "stringToList")
    ControlConfigEntity toEntity(ControlConfigRequestDTO controlConfigRequestDTO);

    @Named("stringToList")
    default List<String> stringToList(String someParam) {
        if (someParam == null || someParam.isEmpty()) {
            return Collections.emptyList(); // 返回空列表
        }
        return Arrays.asList(someParam.split(","));
    }

    @AfterMapping
    default void validate(ControlConfigRequestDTO controlConfigRequestDTO, @MappingTarget ControlConfigEntity controlConfigEntity) {
        if (ControlNotifyWayEnum.CALL_BACK.equals(controlConfigEntity.getControlNotifyWay())) {
            validateCallBack(controlConfigEntity);
        } else if (ControlNotifyWayEnum.EMAIL.equals(controlConfigEntity.getControlNotifyWay())) {
            validateEmail(controlConfigEntity);
        } else {
            validateEmail(controlConfigEntity);
            validateCallBack(controlConfigEntity);
        }
    }

    default void validateCallBack(ControlConfigEntity entity) {
        if (isNullOrEmpty(entity.getCallBackUrl())) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"回调URL不能为空");
        }
        if (isNullOrEmpty(entity.getAppKey())) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"应用appKey不能为空");
        }
        if (!isValidUrl(entity.getCallBackUrl())) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"回调URL格式不正确，应以 http 或 https 开头");
        }
    }

    default void validateEmail(ControlConfigEntity entity) {
        if (entity.getEmailAddress() == null || entity.getEmailAddress().isEmpty()) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"邮件地址不能为空");
        }
        Set<String> recipientSet = new HashSet<>();
        Set<String> ccSet = new HashSet<>();
        for (String recipient : entity.getEmailAddress()) {
            if (!recipientSet.add(recipient)) {
                throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"邮箱已存在: " + recipient);
            }
            if (!isValidEmail(recipient)) {
                throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"邮件地址格式不正确: " + recipient);
            }
        }
        for (String recipient : entity.getCcAddress()) {
            if (!ccSet.add(recipient)) {
                throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"抄送邮箱已存在: " + recipient);
            }
            if (!isValidEmail(recipient)) {
                throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"邮件地址格式不正确: " + recipient);
            }
        }
    }

    default boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }

    // 校验 URL 是否符合 http 或 https 格式
    default boolean isValidUrl(String url) {
        String urlRegex = "^https?://[a-zA-Z0-9.-]+(?:/[^\\s]*)?$";
        return url.matches(urlRegex);
    }

    // 校验邮件地址格式
    default boolean isValidEmail(String email) {
        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        return email.matches(emailRegex);
    }
}
