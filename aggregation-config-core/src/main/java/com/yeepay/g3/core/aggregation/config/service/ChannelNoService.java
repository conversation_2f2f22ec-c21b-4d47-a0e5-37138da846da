package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoCreateBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.QueryChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoApplyRecordEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoEntity;

import java.util.Date;
import java.util.List;

/**
 * description: 商户渠道号申请服务Service
 * <AUTHOR>
 * @since 2025/6/2:23:35
 * Company: 易宝支付(YeePay)
 */
public interface ChannelNoService {

    /**
     * 查询认证申请记录
     */
    ChannelNoApplyRecordEntity queryChannelNoApply(String applyNo);


    /**
     * 查询认证申请记录
     */
    ChannelNoApplyRecordEntity queryChannelNoApplyByBizApplyNo(BizSceneEnum bizSceneEnum,
                                                               String bizApplyNo);

    /**
     * 保存渠道申请记录
     */
    String saveChannelApplyRecord(ChannelNoApplyBO channelNoApplyBO);

    /**
     * 更新渠道申请记录-结果
     */
    void updateChannelApplyResult(String requestNo, ChannelNoApplyResultBO result);

    /**
     * 查询渠道申请记录
     */
    ChannelNoApplyRecordEntity getChannelApplyRecord(QueryChannelNoApplyResultBO applyQueryBO);

    /**
     * 查询未完成的申请记录
     */
    List<ChannelNoApplyRecordEntity> queryUnfinishedApplyRecord(Date startTime, Date endTime);


    void saveMerchantChannelRelation(ChannelNoCreateBO channelNoCreateBO);

    /**
     * 查询渠道申请记录
     */
    ChannelNoEntity getChannelRelationResult(QueryChannelNoApplyResultBO applyQueryBO);

}
