package com.yeepay.g3.core.aggregation.config.external.impl;

import com.yeepay.g3.core.aggregation.config.common.ExternalSystemEnum;
import com.yeepay.g3.core.aggregation.config.common.RemoteProxyFactory;
import com.yeepay.g3.core.aggregation.config.entity.YopNotifyEntity;
import com.yeepay.g3.core.aggregation.config.external.YopNotifyExternal;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.g3.facade.yop.ca.enums.CertTypeEnum;
import com.yeepay.g3.facade.yop.ca.enums.DigestAlgEnum;
import com.yeepay.g3.facade.yop.ca.enums.SymmetricEncryptAlgEnum;
import com.yeepay.g3.facade.yop.notifier.dto.DigitalNotifyRequestDTO;
import com.yeepay.g3.facade.yop.notifier.dto.DigitalNotifyResponseDTO;
import com.yeepay.g3.facade.yop.notifier.facade.DigitalNotifyFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: 商户通知外部实现
 * @author: xuchen.liu
 * @date: 2024-12-13 23:25
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class YopNotifyExternalImpl implements YopNotifyExternal {

    private static DigitalNotifyFacade digitalNotifyFacade = RemoteProxyFactory.getService(DigitalNotifyFacade.class, ExternalSystemEnum.YOP_NOTIFY);

    @Override
    public boolean call(YopNotifyEntity yopNotifyEntity) {
        if(null == yopNotifyEntity){
            return  false;
        }
        log.info("调用yop通知入参 :{}", JsonUtils.convert(yopNotifyEntity));
        Boolean res = Boolean.TRUE;
        try {
            DigitalNotifyRequestDTO digitalNotifyRequestDTO = getDigitalNotifyRequestDTO(yopNotifyEntity);
            DigitalNotifyResponseDTO digitalNotifyResponseDTO = digitalNotifyFacade.sendNotification(digitalNotifyRequestDTO);
            log.info("调用yop通知返回 :{}", JsonUtils.convert(digitalNotifyResponseDTO));
            if (digitalNotifyResponseDTO == null || !digitalNotifyResponseDTO.getSuccess()) {
                res = Boolean.FALSE;
            }
        }catch (Throwable e) {
            log.error("调用yop通知商户异常,"+e.getMessage(),e);
            res = Boolean.FALSE;
        }
        return res;
    }

    private static DigitalNotifyRequestDTO getDigitalNotifyRequestDTO(YopNotifyEntity yopNotifyEntity) {
        DigitalNotifyRequestDTO digitalNotifyRequestDTO = new DigitalNotifyRequestDTO();
        digitalNotifyRequestDTO.setSpiName("aggpay.yop.notify.appid.control");
        digitalNotifyRequestDTO.setUrl(yopNotifyEntity.getUrl());
        digitalNotifyRequestDTO.setAppKey(yopNotifyEntity.getAppKey());
        digitalNotifyRequestDTO.setNotifyMerchantNo(yopNotifyEntity.getMerchantNo());
        digitalNotifyRequestDTO.setNotificationId(yopNotifyEntity.getNotifyNo());
        digitalNotifyRequestDTO.setCertType(CertTypeEnum.RSA2048);
        digitalNotifyRequestDTO.setEncryptAlg(SymmetricEncryptAlgEnum.AES);
        digitalNotifyRequestDTO.setDigestAlg(DigestAlgEnum.SHA256);
        digitalNotifyRequestDTO.setBody(yopNotifyEntity.getNotifyInfo());
        return digitalNotifyRequestDTO;
    }
}
