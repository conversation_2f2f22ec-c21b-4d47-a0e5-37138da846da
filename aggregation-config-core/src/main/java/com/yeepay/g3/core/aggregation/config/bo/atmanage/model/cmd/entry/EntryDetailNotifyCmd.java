package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 进件信息
 * @author: Mr.yin
 * @date: 2025/6/3  20:13
 */
@Data
public class EntryDetailNotifyCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 进件状态
     */
    private EntryStatusEnum entryStatus;

    /**
     * 失败原因
     */
    private String failMessage;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道标识
     */
    private String channelIdentifier;

    /**
     * 机构商户号
     */
    private String institutionMerchantNo;

    /**
     * 签约地址
     * 目前只有微信直连场景下进件，部分状态下该字段才有值
     */
    private String signUrl;


}