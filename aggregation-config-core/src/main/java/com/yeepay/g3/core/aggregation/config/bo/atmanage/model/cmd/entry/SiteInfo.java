package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.SiteInfoDTO;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/2 20:24
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class SiteInfo {

    /**
     * 站点类型
     * 网站:01
     * APP :02
     * 服务窗:03
     * 公众号:04
     * 其他:05
     * 支付宝小程序:06
     */
    private String siteType;

    /**
     * 站点Url地址 站点类型：网站时必填
     */
    private String siteUrl;

    /**
     * 站点名称 站点类型：APP时必填
     */
    private String siteName;

    public static List<SiteInfo> build(final List<SiteInfoDTO> siteInfos) {
        //Assert.notEmpty(siteInfos, ResultCode.PARAM_VALID_ERROR, "siteInfoDTO can not be null!");
        if (CollectionUtils.isEmpty(siteInfos)) {
            return Collections.emptyList();
        }
        return siteInfos.stream()
                .map(SiteInfo::build)
                .collect(Collectors.toList());
    }

    public static SiteInfo build(final SiteInfoDTO siteInfo) {
        Assert.notNull(siteInfo, ResultCode.PARAM_VALID_ERROR, "siteInfoDTO can not be null!");

        return SiteInfo.builder()
                .siteType(siteInfo.getSiteType())
                .siteUrl(siteInfo.getSiteUrl())
                .siteName(siteInfo.getSiteName())
                .build();
    }


}
