package com.yeepay.g3.core.aggregation.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 来源枚举
 * @ClassName: SourceEnum
 * @Author: cong.huo
 * @Date: 2024/12/17 17:36   // 时间
 * @Version: 1.0
 */
@Getter
@AllArgsConstructor
public enum NotifyStatusEnum {
    WAIT_NOTIFY("待通知"),
    NOTIFIED("已通知");

    /**
     * 描述
     */
    private final String desc;
}
