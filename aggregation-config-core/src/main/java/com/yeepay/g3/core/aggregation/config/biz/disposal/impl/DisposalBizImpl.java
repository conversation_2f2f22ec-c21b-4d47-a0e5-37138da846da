package com.yeepay.g3.core.aggregation.config.biz.disposal.impl;

import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalBiz;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryResultEntity;
import com.yeepay.g3.core.aggregation.config.external.ChannelDisposalExternal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 17:25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DisposalBizImpl implements DisposalBiz {

    private final ChannelDisposalExternal channelDisposalExternal;

    @Override
    public DisposalQueryResultEntity pageQueryDisposalList(DisposalQueryEntity requestEntity) {
        return channelDisposalExternal.pageQueryDisposalList(requestEntity);
    }
}
