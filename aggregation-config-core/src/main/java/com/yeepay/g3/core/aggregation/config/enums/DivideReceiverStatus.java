package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 创建分账方  的状态
 */
@Getter
@AllArgsConstructor
public enum DivideReceiverStatus implements DocumentedEnum<String> {

    SUBMIT_FAIL("SUBMIT_FAIL", "提交失败"),
    SUBMIT_PROCESS("SUBMIT_PROCESS", "提交成功/处理中"),
    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    ;
    private final String document;
    private final String desc;
}
