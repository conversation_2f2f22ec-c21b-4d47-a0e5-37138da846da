package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.g3.core.aggregation.config.dao.AppIdMerchantBindDao;
import com.yeepay.g3.core.aggregation.config.dao.AppidAllMerchantNoBindDao;
import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import com.yeepay.g3.core.aggregation.config.enums.ChannelAppIdBindStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.service.AppIdMerchantBindService;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-16 18:29
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class AppIdMerchantBindServiceImpl implements AppIdMerchantBindService {

    @Autowired
    private AppIdMerchantBindDao appIdMerchantBindDao;

    @Autowired
    private AppidAllMerchantNoBindDao appIdAllMerchantNoBindDao;

    @Override
    public Boolean receiveMqData(AppIdMerchantBindEntity appIdMerchantBindEntity) {
        Boolean res = Boolean.TRUE;
        try {

            Date now = new Date();
            appIdMerchantBindEntity.setCreateTime(now);
            appIdMerchantBindEntity.setUpdateTime(now);
            AppIdMerchantBindEntity queryResult = appIdMerchantBindDao.queryByAppIdAndMerchantNo(appIdMerchantBindEntity.getAppId(), appIdMerchantBindEntity.getMerchantNo());
            if (queryResult == null) {
                appIdMerchantBindEntity.setCleanStatus(DataCleanStatusEnum.INIT);
                int result = appIdMerchantBindDao.insertSelective(appIdMerchantBindEntity);
                if (result == 0) {
                    throw new AggConfigException(ErrorCodeEnum.DATABASE_OPERATE_ERROR,"新增AppId和商户绑定关系异常");
                }
            }else {
                ChannelAppIdBindStatusEnum bindStatus = queryResult.getBindStatus();
                if (appIdMerchantBindEntity.getBindStatus().equals(bindStatus)) {
                    return Boolean.FALSE;
                }
                Date oldOperateTime = queryResult.getOperateTime();
                Date operateTime = appIdMerchantBindEntity.getOperateTime();
                if (oldOperateTime.getTime() > operateTime.getTime()) {
                    res = Boolean.FALSE;
                } else {
                    //创建时间不变
                    queryResult.setUpdateTime(now);
                    queryResult.setOperateTime(operateTime);
                    queryResult.setCleanStatus(DataCleanStatusEnum.INIT);
                    this.update(queryResult);
                    //更换时间
                    appIdMerchantBindEntity.setOperateTime(queryResult.getOperateTime());
                    appIdMerchantBindEntity.setCleanStatus(queryResult.getCleanStatus());
                    appIdMerchantBindEntity.setCreateTime(queryResult.getCreateTime());
                    appIdMerchantBindEntity.setUpdateTime(queryResult.getUpdateTime());
                }
            }
        } catch (DuplicateKeyException e) {
            log.error("新增AppId和商户绑定关系唯一索引冲突 merchantNo : " +appIdMerchantBindEntity.getMerchantNo() + "appId: " +appIdMerchantBindEntity.getAppId(),e);
            res = Boolean.FALSE;
        }catch (Throwable e){
            log.error("未知异常,e="+e.getMessage(),e);
            throw new AggConfigException(ErrorCodeEnum.MQ_CONSUMER_ERROR,e.getMessage());
        }
        return res;
    }

    @Override
    public void batchMerchantAll(AppIdMerchantBindEntity appIdMerchantBindEntity, Pair<List<AppidAllMerchantNoBindEntity>, List<AppidAllMerchantNoBindEntity>> listPair) {
        if (appIdMerchantBindEntity != null) {
            this.update(appIdMerchantBindEntity);
        }
        List<AppidAllMerchantNoBindEntity> insert = listPair.getKey();
        List<AppidAllMerchantNoBindEntity> update = listPair.getValue();
        if (CollectionUtils.isNotEmpty(insert)) {
            for (AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity : insert) {
                appIdAllMerchantNoBindDao.insert(appidAllMerchantNoBindEntity);
            }
        }
        if (CollectionUtils.isNotEmpty(update)) {
            for (AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity : update) {
                appIdAllMerchantNoBindDao.updateSelective(appidAllMerchantNoBindEntity);
            }
        }
    }

    private void update(AppIdMerchantBindEntity appIdMerchantBindEntity) {
        int result = appIdMerchantBindDao.updateSelective(appIdMerchantBindEntity);
        if (result == 0) {
            throw new AggConfigException(ErrorCodeEnum.DATABASE_OPERATE_ERROR,"修改AppId和商户绑定关系异常");
        }
    }

    @Override
    public AppIdMerchantBindEntity queryByAppIdAndMerchantNo(String appId, String merchantNo) {
        return appIdMerchantBindDao.queryByAppIdAndMerchantNo(appId,merchantNo);
    }

    @Override
    public List<AppidAllMerchantNoBindEntity> queryAllMerchantNoBind(String appId, String firstMerchantNo) {
        return appIdAllMerchantNoBindDao.queryByAppidAndMerchantNo(appId,firstMerchantNo);
    }

    @Override
    public List<AppidAllMerchantNoBindEntity> queryByAppidAndMerchantNoAndAttachMerchantNo(String appId, String attachMerchantNo) {
        return appIdAllMerchantNoBindDao.queryByAppidAndMerchantNoAndAttachMerchantNo(appId,attachMerchantNo);
    }

    @Override
    public List<AppidAllMerchantNoBindEntity> queryAllByAppIdList(List<String> appidList, String attachMerchantNo, String merchantNo) {
        return appIdAllMerchantNoBindDao.queryAllByAppIdList(appidList,merchantNo,attachMerchantNo);
    }

    @Override
    public List<AppIdMerchantBindEntity> queryAppidByMerchantNoAndChannelType(String merchantNo,ChannelTypeEnum channelType) {
        return appIdMerchantBindDao.queryAppidByMerchantNo(merchantNo, DataCleanStatusEnum.SUCCESS,
                channelType, ChannelAppIdBindStatusEnum.SUCCESS);
    }

    @Override
    public List<String> queryByAppidGroupByFirstMerchantNo(String appid,String warningStatus) {
        return  appIdAllMerchantNoBindDao.queryByAppidGroupByFirstMerchantNo(appid,warningStatus);

    }

    @Override
    public int updateWaringStatus(String appid, List<String> topMerchants) {
        return appIdAllMerchantNoBindDao.updateWaringStatus(appid,topMerchants);
    }

    @Override
    public List<String> getTopMerchantsByAppidAndConfigAndEmail(String appid,String notifyStatus,int merchantGrade) {
        return  appIdAllMerchantNoBindDao.getTopMerchantsByAppidAndConfigAndEmail(appid,notifyStatus,merchantGrade);
    }
    @Override
    public List<String> getTopMerchantsByAppidAndConfigAndUrl(String appid,String notifyStatus,int merchantGrade) {
        return  appIdAllMerchantNoBindDao.getTopMerchantsByAppidAndConfigAndUrl(appid,notifyStatus,merchantGrade);
    }

    @Override
    public void updateNotifyStatusEmail(String topMerchant, String appid, List<String> subMerchantNos,String emailNotifyStatus, int  emailLevel,int merchantGrade) {
          appIdAllMerchantNoBindDao.updateNotifyStatusEmail(topMerchant,appid,subMerchantNos,emailNotifyStatus,emailLevel,merchantGrade);
    }

    @Override
    public void updateNotifyStatusUrl(String topMerchant, String appid, List<String> subMerchantNos, String urlNotifyStatus, int urlLevel, int merchantGrade) {
        appIdAllMerchantNoBindDao.updateNotifyStatusUrl(topMerchant,appid,subMerchantNos,urlNotifyStatus,urlLevel,merchantGrade);
    }

    @Override
    public List<AppIdMerchantBindEntity> queryBindByAppid(String appId, String merchantNo, DataCleanStatusEnum cleanStatus) {
         return appIdMerchantBindDao.queryBindByAppid(appId,merchantNo,cleanStatus);
    }

    @Override
    public List<AppidAllMerchantNoBindEntity> queryAllMerchantNoBindByMerchantGrade(String appid, String merchantGradeNo, int merchantGrade) {
        return appIdAllMerchantNoBindDao.queryAllMerchantNoBindByMerchantGrade(appid,merchantGradeNo,merchantGrade);

    }

    @Override
    public void updateLevelByAppid(String appid) {
        appIdAllMerchantNoBindDao.updateLevelByAppid(appid);
    }
}
