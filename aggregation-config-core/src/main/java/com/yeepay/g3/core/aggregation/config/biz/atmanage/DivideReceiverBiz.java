package com.yeepay.g3.core.aggregation.config.biz.atmanage;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.enums.DivideReceiverStatus;
import com.yeepay.g3.core.aggregation.config.mq.event.DivideReceiverMessage;

/**
 * 分帐方创建业务处理
 *
 * @author: Mr.yin
 * @date: 2025/6/5  14:33
 */
public interface DivideReceiverBiz {

    /**
     * 创建分帐方接口
     * 840005等不到回调，直接成功
     */
    DivideReceiverStatus createDivideReceiver(BaseEntryApply<? extends ChannelSpecialParams> entryApply);

    void callBackDivideReceiver(DivideReceiverMessage callBackBO);
}
