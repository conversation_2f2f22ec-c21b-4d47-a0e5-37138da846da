package com.yeepay.g3.core.aggregation.config.factory;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.SubjectTypeEnum;
import com.yeepay.g3.core.aggregation.config.factory.decorator.*;
import com.yeepay.g3.core.aggregation.config.factory.handle.AliPayMerchantInfoStrategy;
import com.yeepay.g3.core.aggregation.config.factory.handle.MerchantInfoStrategy;
import com.yeepay.g3.core.aggregation.config.factory.handle.WechatMerchantInfoStrategy;

import java.util.HashMap;
import java.util.Map;

/**
 * description: 商户信息工厂接口
 * <AUTHOR>
 * @since 2025/5/22:11:03
 * Company: 易宝支付(YeePay)
 */
public class MerchantInfoFactory {

    /**
     * 渠道和主体类型对应的处理策略
     */
    static final Map<PayChannelEnum, Map<SubjectTypeEnum, MerchantInfoDecorator>> DOCORATOR_MAP = new HashMap<>();
    static {
        Map<PayChannelEnum, MerchantInfoStrategy> CHANNEL_MAP = new HashMap<>();
        CHANNEL_MAP.put(PayChannelEnum.WECHAT, new WechatMerchantInfoStrategy());
        CHANNEL_MAP.put(PayChannelEnum.ALIPAY, new AliPayMerchantInfoStrategy());
        // 其他渠道的处理策略可以在这里添加
        for (PayChannelEnum value : PayChannelEnum.values()) {
            Map<SubjectTypeEnum, MerchantInfoDecorator> SUBJECT_DECORATOR_MAP = new HashMap<>();
            SUBJECT_DECORATOR_MAP.put(SubjectTypeEnum.ENTERPRISE, new EnterpriseMerchantInfoDecorator(CHANNEL_MAP.get(value)));
            SUBJECT_DECORATOR_MAP.put(SubjectTypeEnum.INSTITUTION, new InstitutionsMerchantInfoDecorator(CHANNEL_MAP.get(value)));
            SUBJECT_DECORATOR_MAP.put(SubjectTypeEnum.GOVERNMENT, new GovernmentMerchantInfoDecorator(CHANNEL_MAP.get(value)));
            SUBJECT_DECORATOR_MAP.put(SubjectTypeEnum.INDIVIDUAL, new IndividualMerchantInfoDecorator(CHANNEL_MAP.get(value)));
            SUBJECT_DECORATOR_MAP.put(SubjectTypeEnum.MICRO, new MicorMerchantInfoDecorator(CHANNEL_MAP.get(value)));
            SUBJECT_DECORATOR_MAP.put(SubjectTypeEnum.OTHERS, new OthersMerchantInfoDecorator(CHANNEL_MAP.get(value)));
            DOCORATOR_MAP.put(value, SUBJECT_DECORATOR_MAP);
        }
    }

    /**
     * 获取商户信息处理策略
     * @param payChannelEnum 渠道
     * @param subjectTypeEnum 主体类型
     * @return 商户信息处理策略
     */
    public static MerchantInfoDecorator getMerchantInfoDecorator(PayChannelEnum payChannelEnum, SubjectTypeEnum subjectTypeEnum) {
        Map<SubjectTypeEnum, MerchantInfoDecorator> subjectTypeMap = DOCORATOR_MAP.get(payChannelEnum);
        if (subjectTypeMap == null) {
            return null;
        }
        return subjectTypeMap.get(subjectTypeEnum);
    }

}
