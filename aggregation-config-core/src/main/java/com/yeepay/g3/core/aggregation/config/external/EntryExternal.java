package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportResponseDTO;

import java.util.Collections;

/**
 * description: 进件外部接口
 *
 * <AUTHOR>
 * @since 2025/6/4:20:51
 * Company: 易宝支付(YeePay)
 */
public interface EntryExternal<T extends EntryApply<? extends ChannelSpecialParams>> {
    String SUCCESS = "0000";

    ChannelEntryResult asyncReport(final EntryApplyContext<T> reqDTO);

    default boolean isSuccess(final OpenPayAsyncReportResponseDTO response) {
        if (response == null) {
            return false;
        }
        return SUCCESS.equals(response.getBizCode());
    }

    ChannelEntryResult convert(final OpenPayAsyncReportResponseDTO response, final String requestNo);

    default ChannelEntryResult convertToFail(final OpenPayAsyncReportResponseDTO response, final String requestNo) {
        ChannelEntryResult.ChannelEntryDetail entryDetail;
        if (response == null || ChannelResultCode.getEntryFailedWaitRetryCode().contains(response.getBizCode())) {
            entryDetail = ChannelEntryResult.ChannelEntryDetail.processing();
        } else {
            entryDetail = ChannelEntryResult.ChannelEntryDetail.fail(response.getBizCode(), response.getBizMsg());
        }

        return ChannelEntryResult.builder()
                .requestNo(requestNo)
                .channelEntryDetails(Collections.singletonList(entryDetail))
                .build();
    }
}
