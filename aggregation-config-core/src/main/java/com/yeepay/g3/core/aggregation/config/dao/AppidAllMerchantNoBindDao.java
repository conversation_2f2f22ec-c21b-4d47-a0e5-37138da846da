package com.yeepay.g3.core.aggregation.config.dao;

import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @ClassName: AppidAllMerchantNoBindDao
 * @Author: cong.huo
 * @Date: 2024/12/17 14:59   // 时间
 * @Version: 1.0
 */
@Mapper
public interface AppidAllMerchantNoBindDao {

    /**
     * 根据ID查询记录
     *
     * @param id 主键ID
     * @return AppidAllMerchantNoBindEntity 对象
     */
    AppidAllMerchantNoBindEntity queryById(Long id);

    /**
     * 根据APPID和商户号查询记录
     *
     * @param appid      APPID
     * @param firstMerchantNo 商户号
     * @return AppidAllMerchantNoBindEntity 对象
     */
    List<AppidAllMerchantNoBindEntity> queryByAppidAndMerchantNo(@Param("appid") String appid, @Param("firstMerchantNo") String firstMerchantNo);

    /**
     * 根据APPID和商户号查询记录
     *
     * @param appid      APPID
     * @param attachMerchantNo 商户号
     * @return AppidAllMerchantNoBindEntity 对象
     */
    List<AppidAllMerchantNoBindEntity> queryByAppidAndMerchantNoAndAttachMerchantNo(@Param("appid") String appid, @Param("attachMerchantNo") String attachMerchantNo);


    /**
     * 选择性插入记录
     *
     * @param  appidAllMerchantNoBindEntity
     * @return 插入的记录数
     */
    int insert(AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity);

    /**
     * 根据ID选择性更新记录
     *
     * @return 更新的记录数
     */
    int updateSelective(AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity);


    /**
     * 根据appid查询
     * @param appid
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/18 18:32
     */
    List<String> queryByAppidGroupByFirstMerchantNo(@Param("appid") String appid, @Param("warningStatus") String warningStatus);

    /**
     * 根据appid列表查询
     * @param appidList appid列表
     * @param merchantNo 商户编号
     * @param attachMerchantNo 挂靠商户编号
     * @return list
     */
    List<AppidAllMerchantNoBindEntity> queryAllByAppIdList(@Param("appidList") List<String> appidList, @Param("merchantNo") String merchantNo, @Param("attachMerchantNo") String attachMerchantNo);

    /**
     * 更新告警的通知
     * @param appid
     * @param firstMerchants
     * @return int
     * <AUTHOR>
     * @date 2024/12/19 17:04
     */
    int updateWaringStatus(@Param("appid") String appid, @Param("firstMerchants") List<String> firstMerchants);



    /**
     *根据appid和配置通知的 查询顶级商编
     * @param appid
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/19 17:16
     */
    List<String> getTopMerchantsByAppidAndConfigAndEmail(@Param("appid") String appid,@Param("notifyStatus") String notifyStatus,@Param("merchantGrade") int merchantGrade);

    /**
     *根据appid和配置通知的 查询顶级商编
     * @param appid
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/19 17:16
     */
    List<String> getTopMerchantsByAppidAndConfigAndUrl(@Param("appid") String appid,@Param("notifyStatus") String notifyStatus,@Param("merchantGrade") int merchantGrade);

    /**
     * 更新邮件通知状态
     * @param merchantGradeNo
    * @param appid
    * @param subMerchantNos
    * @param emailNotifyStatus
    * @param
     * @return int
     * <AUTHOR>
     * @date 2024/12/19 19:26
     */
    int updateNotifyStatusEmail(@Param("merchantGradeNo")String merchantGradeNo, @Param("appid")String appid, @Param("subMerchantNos")List<String> subMerchantNos,@Param("emailNotifyStatus") String emailNotifyStatus,@Param("emailLevel")int emailLevel,@Param("merchantGrade") int merchantGrade);
    /**
     * 更新接口通知状态
     * @param merchantGradeNo
    * @param appid
    * @param subMerchantNos
    * @param
     * @return int
     * <AUTHOR>
     * @date 2024/12/19 19:26
     */
    int updateNotifyStatusUrl(@Param("merchantGradeNo")String merchantGradeNo, @Param("appid")String appid, @Param("subMerchantNos")List<String> subMerchantNos,@Param("urlNotifyStatus") String urlNotifyStatus,@Param("urlLevel")int urlLevel,@Param("merchantGrade") int merchantGrade);

    /**
     *
     * @param appid
    * @param merchantGradeNo
    * @param merchantGrade
     * @return java.util.List<com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity>
     * <AUTHOR>
     * @date 2024/12/27 12:59
     */
    List<AppidAllMerchantNoBindEntity> queryAllMerchantNoBindByMerchantGrade( @Param("appid") String appid,  @Param("merchantGradeNo") String merchantGradeNo,  @Param("merchantGrade") int merchantGrade);

    /**
     * 更新appid 对应的 等级
     * @param appid
     * @return void
     * <AUTHOR>
     * @date 2024/12/31 11:20
     */
    int updateLevelByAppid( @Param("appid") String appid);
}
