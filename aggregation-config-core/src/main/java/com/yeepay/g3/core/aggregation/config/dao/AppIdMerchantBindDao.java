package com.yeepay.g3.core.aggregation.config.dao;


import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.enums.ChannelAppIdBindStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AppidMerchantBind Dao层接口
 * 提供了对 TBL_APPID_MERCHANT_BIND 表的增删改查操作
 */
@Mapper
public interface AppIdMerchantBindDao {

    /**
     * 插入一条记录（选择性插入）
     * @param entity 要插入的实体
     * @return 插入成功的记录数
     */
    int insertSelective(AppIdMerchantBindEntity entity);

    /**
     *  插入
     * @param entity appIdMerchantBindEntity
     */
    void batchInsert(AppIdMerchantBindEntity entity);

    /**
     * 更新记录（选择性更新）
     * @param entity 要更新的实体
     * @return 更新成功的记录数
     */
    int updateSelective(AppIdMerchantBindEntity entity);

    /**
     * 根据ID查询记录
     * @param id 要查询的主键ID
     * @return 查询到的实体对象
     */
    AppIdMerchantBindEntity queryById(Long id);

    /**
     * 根据appId查询商户绑定关系
     * @param appId appId
     * @param merchantNo 商户号
     * @return 查询到的实体对象
     */
    AppIdMerchantBindEntity queryByAppIdAndMerchantNo(@Param("appId") String appId, @Param("merchantNo") String merchantNo);

    /**
     *  根据商编号查询
     * @param merchantNo 商户编号
     * @param dataCleanStatus 数据清洗状态
     * @param channelType 渠道类型
     * @return list
     */
    List<AppIdMerchantBindEntity> queryAppidByMerchantNo(@Param("merchantNo") String merchantNo, @Param("dataCleanStatus") DataCleanStatusEnum dataCleanStatus,
                                                         @Param("channelType") ChannelTypeEnum channelType, @Param("bindStatus") ChannelAppIdBindStatusEnum bindStatus);

    /**
     * 查询
     * @param appid
     * @return
     */
    List<AppIdMerchantBindEntity> queryBindByAppid(@Param("appid") String appid,@Param("merchantNo")String merchantNo, @Param("dataCleanStatus") DataCleanStatusEnum dataCleanStatus);
}
