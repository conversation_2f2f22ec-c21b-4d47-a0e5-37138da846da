package com.yeepay.g3.core.aggregation.config.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @description: yop通知实体
 * @author: xuchen.liu
 * @date: 2024-12-19 11:49
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Data
public class YopNotifyEntity implements Serializable {

    private static final long serialVersionUID = 1610433337567635304L;

    /**
     * 商编
     */
    private String merchantNo;

    /**
     * appKey
     */
    private String appKey;

    /**
     * 通知url
     */
    private String url;

    /**
     *  通知信息
     */
    private Map<String,Object> notifyInfo;

    /**
     * 通知号
     */
    private String notifyNo;

}
