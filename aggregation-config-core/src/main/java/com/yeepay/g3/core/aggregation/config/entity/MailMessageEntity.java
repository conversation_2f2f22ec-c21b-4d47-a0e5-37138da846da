package com.yeepay.g3.core.aggregation.config.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.File;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@ToString
public class MailMessageEntity implements Serializable {

    private static final long serialVersionUID = 4066109444616255992L;
    /**
     * 用户名
     */
    String userName;
    /**
     * 通知规则
     */
    String notifyRuleName;
    /**
     * 秘钥
     */
    String secretKey;
    /**
     * 收件人
     */
    List<String> recipientsList;
    /**
     * 抄送人
     */
    List<String> cc;
    /**
     * 邮件主体
     */
    Map<String,Object> messageParams;

    /**
     * 收单商编
     */
    String merchantNo;
    /**
     * 父商编
     */
    String parentMerchantNo;
    /**
     * 邮件文件
     */
    File mailFile;
}