package com.yeepay.g3.core.aggregation.config.common.mybatis;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

public class ListToStringTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        // 将 List<String> 转换为逗号分隔的字符串并设置到 PreparedStatement
        String result = String.join(",", parameter);
        ps.setString(i, result);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        // 获取数据库中的字符串并将其转换为 List<String>
        String result = rs.getString(columnName);
        if (result != null) {
            return Arrays.asList(result.split(","));
        }
        return null;
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        // 获取数据库中的字符串并将其转换为 List<String>
        String result = rs.getString(columnIndex);
        if (result != null) {
            return Arrays.asList(result.split(","));
        }
        return null;
    }

    @Override
    public List<String> getNullableResult(java.sql.CallableStatement cs, int columnIndex) throws SQLException {
        // 获取数据库中的字符串并将其转换为 List<String>
        String result = cs.getString(columnIndex);
        if (result != null) {
            return Arrays.asList(result.split(","));
        }
        return null;
    }
}
