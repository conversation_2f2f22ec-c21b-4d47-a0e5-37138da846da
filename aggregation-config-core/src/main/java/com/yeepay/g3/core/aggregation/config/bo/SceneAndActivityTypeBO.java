package com.yeepay.g3.core.aggregation.config.bo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 场景和活动类型业务对象
 * <AUTHOR>
 * @since 2025/5/28:15:08
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class SceneAndActivityTypeBO implements Serializable {

    private static final long serialVersionUID = -5412677193947582736L;

    /**
     * 渠道
     */
    private PayChannelEnum channel;

    /**
     * 场景类型
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;

    /**
     * 通道报备费率
     */
    private String channelFeeType;

    /**
     * 通道活动类型
     */
    private String channelActivityType;


}
