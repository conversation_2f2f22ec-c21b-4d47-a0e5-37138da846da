package com.yeepay.g3.core.aggregation.config.biz.impl;

import com.google.common.collect.Lists;
import com.yeepay.g3.core.aggregation.config.biz.ChannelAppidControlBiz;
import com.yeepay.g3.core.aggregation.config.common.MessageConst;
import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity;
import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.core.aggregation.config.entity.YopNotifyEntity;
import com.yeepay.g3.core.aggregation.config.enums.BindAllStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.MerchantGradeEnum;
import com.yeepay.g3.core.aggregation.config.enums.NotifyStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.WaringStatusEnum;
import com.yeepay.g3.core.aggregation.config.external.MailNotifyExternal;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.core.aggregation.config.external.YopNotifyExternal;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppidControlEvent;
import com.yeepay.g3.core.aggregation.config.service.AppIdMerchantBindService;
import com.yeepay.g3.core.aggregation.config.service.AppidControlService;
import com.yeepay.g3.core.aggregation.config.service.ControlConfigService;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ControlNotifyWayEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ControlStatusEnum;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: appid管控洗洗同步
 * @ClassName: ChannelAppIdControlBizImpl
 * @Author: cong.huo
 * @Date: 2024/12/18 11:36   // 时间
 * @Version: 1.0
 */
@Slf4j
@Service
public class ChannelAppidControlBizImpl implements ChannelAppidControlBiz {
    @Resource
    private AppidControlService appidControlService;
    @Resource
    private AppIdMerchantBindService appIdMerchantBindService;
    @Resource
    private MerchantCenterExternal merchantCenterExternal;
    @Resource
    private ControlConfigService controlConfigService;
    @Resource
    private YopNotifyExternal yopNotifyExternal;
    @Resource
    private MailNotifyExternal mailNotifyService;
    @Override
    public AppidControlEntity handle(ChannelAppidControlEvent message) throws ParseException {
        /**
         * 1  参数转换
         * 2  查询 如果没有 保存入库 如果有更新
         * 3  处理异步通知 接口通知 邮件通知
         */
        AppidControlEntity appidControlEntity = createAppidControl(message);
        AppidControlEntity appidControlEntityQuery = appidControlService.queryByAppid(appidControlEntity.getAppid());
        if(appidControlEntityQuery != null) {
           final Long version = appidControlEntityQuery.getVersion();
            appidControlEntityQuery.setNotifyType(appidControlEntity.getNotifyType());
            //比较 开始时间 和结束时间 如果不对应 则版本号+1 全量通知
            if (areDatesEqual(appidControlEntityQuery.getControlStartTime(), appidControlEntity.getControlStartTime())
                    || areDatesEqual(appidControlEntityQuery.getControlEndTime(), appidControlEntity.getControlEndTime())){
                appidControlEntityQuery.setVersion(appidControlEntityQuery.getVersion()+1);
                appidControlEntityQuery.setNotifyStatus(NotifyStatusEnum.WAIT_NOTIFY.name());
                appidControlEntityQuery.setControlStartTime(appidControlEntity.getControlStartTime());
                appidControlEntityQuery.setControlEndTime(appidControlEntity.getControlEndTime());
                appidControlEntityQuery.setNotifyType(appidControlEntity.getNotifyType());
                appidControlEntityQuery.setUpdateTime(new Date());
                int i = appidControlService.updateByIdVersion(appidControlEntityQuery, version);
            }
            // 不更新 继续通知新增的 绑定关系
        }else {
            //新增 全量通知配置
           appidControlService.insert(appidControlEntity);
            appidControlEntityQuery=appidControlEntity;
        }
        return appidControlEntityQuery;
    }

    @Override
    public void notify(AppidControlEntity appidControlEntity) {
        /**
         *
         *
         */

        String  notifyStatus = appidControlEntity.getNotifyStatus();

        if (NotifyStatusEnum.WAIT_NOTIFY.name().equals(notifyStatus)) {
            //全量报警
            fullWaring(appidControlEntity);
            // 全量通知
            fullNotification(appidControlEntity);
        } else if ( NotifyStatusEnum.NOTIFIED.name().equals(notifyStatus )) {
            //增量报警
            incrementalWaring(appidControlEntity);
            // 增量通知
            incrementalNotification(appidControlEntity);
        }

        //更通知状态为已经通知
        appidControlEntity.setNotifyStatus(NotifyStatusEnum.NOTIFIED.name());
        appidControlEntity.setUpdateTime(new Date());
        appidControlEntity.setVersion(appidControlEntity.getVersion());
        appidControlService.updateByIdVersion(appidControlEntity, appidControlEntity.getVersion());
    }

    @Override
    public void compensateNotify(String appId) {
        List<AppidControlEntity> appidControlEntities = appidControlService.queryByControlList(appId, null);
        if (CollectionUtils.isNotEmpty(appidControlEntities)) {
            for (AppidControlEntity appidControlEntity : appidControlEntities) {
                try {
                    notify(appidControlEntity);
                }catch (Throwable e) {
                    String format = String.format("补偿通知异常,appidControlEntity=%s", JsonUtils.convert(appidControlEntities));
                    log.error(format,e);
                }
            }
        }
    }

    /**
     * 增量报警
     * @param appidControlEntity
     * @return void
     * <AUTHOR>
     * @date 2024/12/19 19:36
     */
    private void incrementalWaring(AppidControlEntity appidControlEntity) {
        String appid = appidControlEntity.getAppid();
        String controlStartTime = DateUtils.toString(appidControlEntity.getControlStartTime(), DateUtils.DATE_FORMAT_DATETIME);
        String controlEndTime = DateUtils.toString(appidControlEntity.getControlEndTime(), DateUtils.DATE_FORMAT_DATETIME);
        if(null==appidControlEntity.getControlEndTime()){
            controlEndTime= MessageConst.MESSAGE;
        }
        List<String> firstMerchantNoList = appIdMerchantBindService.queryByAppidGroupByFirstMerchantNo(appid, WaringStatusEnum.WARING.name());

        if(CollectionUtils.isNotEmpty(firstMerchantNoList)){
            for (String firstMerchantNo : firstMerchantNoList) {
                List<AppidAllMerchantNoBindEntity> appidAllMerchantNoBindEntities = appIdMerchantBindService.queryAllMerchantNoBind(appidControlEntity.getAppid(), firstMerchantNo);
                //筛选出所有的子商编
                List<String> subMerchantNos = appidAllMerchantNoBindEntities.stream()
                        .filter(entity -> BindAllStatusEnum.BIND.equals(entity.getBindStatus())) // 过滤掉状态不是 bind 的数据
                        .filter(entity -> !WaringStatusEnum.WARING.equals(entity.getWaringStatus()))
                        .map(AppidAllMerchantNoBindEntity::getMerchantNo)
                        .collect(Collectors.toList());
                //查询出
                String industryLine = merchantCenterExternal.getIndustryLineByTopMerchant(firstMerchantNo);

                // 打印报警日志
                log.info("管控appid报警  行业线业绩属性:{},  管控appid:{} ,管控开始时间:{},管控结束时间:{},顶级商户商编:{} ,子商户列表 {}  ",industryLine,appidControlEntity.getAppid(),controlStartTime,controlEndTime,firstMerchantNo,subMerchantNos);
            }


            //更新报警状态未成功
            appIdMerchantBindService.updateWaringStatus(appid,firstMerchantNoList);
        }

    }

    /**
     * 增量通知
     * @param appidControlEntity
     * @return void
     * <AUTHOR>
     * @date 2024/12/19 19:36
     */
    private void incrementalNotification(AppidControlEntity appidControlEntity) {
        /**
         *  按三级商编增量通知
         *  按二级商编增量通知
         *  按一级商编增量通知
         *
         */


        incrementalNotificationByGradeEmail(appidControlEntity, MerchantGradeEnum.THREE.getCode());
        incrementalNotificationByGradeUrl(appidControlEntity, MerchantGradeEnum.THREE.getCode());

        incrementalNotificationByGradeEmail(appidControlEntity, MerchantGradeEnum.TWO.getCode());
        incrementalNotificationByGradeUrl(appidControlEntity, MerchantGradeEnum.TWO.getCode());

        incrementalNotificationByGradeEmail(appidControlEntity, MerchantGradeEnum.ONE.getCode());
        incrementalNotificationByGradeUrl(appidControlEntity, MerchantGradeEnum.ONE.getCode());


    }


    private void incrementalNotificationByGradeEmail(AppidControlEntity appidControlEntity, int merchantGrade) {

        String appid = appidControlEntity.getAppid();
        List<String> topMerchants = appIdMerchantBindService.getTopMerchantsByAppidAndConfigAndEmail(appid, NotifyStatusEnum.NOTIFIED.name(),merchantGrade);
        for (String topMerchant : topMerchants) {
            List<AppidAllMerchantNoBindEntity> appidAllMerchantNoBindEntities = appIdMerchantBindService.queryAllMerchantNoBindByMerchantGrade(appidControlEntity.getAppid(), topMerchant,merchantGrade);
            //筛选出所有的子商编
            List<AppidAllMerchantNoBindEntity> mailSubMerchantNos = new ArrayList<>(appidAllMerchantNoBindEntities.stream()
                    .filter(appidAllMerchantNoBindEntity -> BindAllStatusEnum.BIND.equals(appidAllMerchantNoBindEntity.getBindStatus()))
                    .filter(appidAllMerchantNoBindEntity -> !NotifyStatusEnum.NOTIFIED.name().equals(appidAllMerchantNoBindEntity.getEmailNotifyStatus()))
                    .filter(entity -> merchantGrade>=entity.getEmailLevel())
                    .collect(Collectors.toMap(
                            AppidAllMerchantNoBindEntity::getMerchantNo,
                            entity -> entity
                    ))
                    .values());



            ControlConfigEntity controlConfigEntity = controlConfigService.queryValidByMerchantNo(topMerchant, ControlStatusEnum.ACTIVE);
            if(null == controlConfigEntity) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(mailSubMerchantNos)) {
                notifyByConfigEmail(topMerchant, mailSubMerchantNos,controlConfigEntity,appidControlEntity,merchantGrade);
            }
        }
    }

    private void incrementalNotificationByGradeUrl(AppidControlEntity appidControlEntity, int merchantGrade) {

        String appid = appidControlEntity.getAppid();
        List<String> topMerchants = appIdMerchantBindService.getTopMerchantsByAppidAndConfigAndUrl(appid, NotifyStatusEnum.NOTIFIED.name(),merchantGrade);
        for (String topMerchant : topMerchants) {
            List<AppidAllMerchantNoBindEntity> appidAllMerchantNoBindEntities = appIdMerchantBindService.queryAllMerchantNoBindByMerchantGrade(appidControlEntity.getAppid(), topMerchant,merchantGrade);


            List<AppidAllMerchantNoBindEntity> urlSubMerchantNos = new ArrayList<>(appidAllMerchantNoBindEntities.stream()
                    .filter(appidAllMerchantNoBindEntity -> BindAllStatusEnum.BIND.equals(appidAllMerchantNoBindEntity.getBindStatus()))
                    .filter(appidAllMerchantNoBindEntity -> !NotifyStatusEnum.NOTIFIED.name().equals(appidAllMerchantNoBindEntity.getUrlNotifyStatus()))
                    .filter(entity -> merchantGrade>=entity.getEmailLevel())
                    .collect(Collectors.toMap(
                            AppidAllMerchantNoBindEntity::getMerchantNo,
                            entity -> entity
                    ))
                    .values());

            ControlConfigEntity controlConfigEntity = controlConfigService.queryValidByMerchantNo(topMerchant, ControlStatusEnum.ACTIVE);
            if(null == controlConfigEntity) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(urlSubMerchantNos)) {
                //如果 urlSubMerchantNos 大于1000条 按1000条发送
                pageNotifyByConfig(appidControlEntity, merchantGrade, topMerchant, urlSubMerchantNos, controlConfigEntity);
            }
        }
    }


        /**
         * 全量报警
         * @param appidControlEntity
         * @return void
         * <AUTHOR>
         * @date 2024/12/19 16:44
         */
    private void fullWaring(AppidControlEntity appidControlEntity) {
        String appid = appidControlEntity.getAppid();
        String controlStartTime = DateUtils.toString(appidControlEntity.getControlStartTime(), DateUtils.DATE_FORMAT_DATETIME);
        String controlEndTime = DateUtils.toString(appidControlEntity.getControlEndTime(), DateUtils.DATE_FORMAT_DATETIME);
        if(null==appidControlEntity.getControlEndTime()){
            controlEndTime= MessageConst.MESSAGE;
        }

        List<String> firstMerchantNoList = appIdMerchantBindService.queryByAppidGroupByFirstMerchantNo(appid,null);

        if (CollectionUtils.isNotEmpty(firstMerchantNoList)) {

            for (String firstMerchantNo : firstMerchantNoList) {
                List<AppidAllMerchantNoBindEntity> appidAllMerchantNoBindEntities = appIdMerchantBindService.queryAllMerchantNoBind(appidControlEntity.getAppid(), firstMerchantNo);
                //筛选出所有的子商编
                List<String> subMerchantNos = appidAllMerchantNoBindEntities.stream()
                        .filter(entity -> BindAllStatusEnum.BIND.equals(entity.getBindStatus())) // 过滤掉状态不是 bind 的数据
                        .map(AppidAllMerchantNoBindEntity::getMerchantNo)
                        .collect(Collectors.toList());
                //查询出
                String industryLine = merchantCenterExternal.getIndustryLineByTopMerchant(firstMerchantNo);

                // 打印报警日志
                log.info("管控appid报警  行业线业绩属性:{},  管控appid:{} ,管控开始时间:{},管控结束时间:{},顶级商户商编:{} ,子商户列表 {}  ",industryLine,appidControlEntity.getAppid(),controlStartTime,controlEndTime,firstMerchantNo,subMerchantNos);
            }

            //更新报警状态未成功
            appIdMerchantBindService.updateWaringStatus(appid,firstMerchantNoList);

        }
    }



    //TODO 跟产品确认一下  降级的时候按商户配置通知

    public void fullNotification(AppidControlEntity appidControlEntity) {
        /***
         * 把级别降为0 如果配置从三级降级到一级的时候可以被已经通知
         *
         * 按三级商编group
         *
         * 按二级商编group
         *
         * 按三级商编group
         *
         *
         */



        appIdMerchantBindService.updateLevelByAppid(appidControlEntity.getAppid());

        fullNotificationEmail(appidControlEntity, MerchantGradeEnum.THREE.getCode());
        fullNotificationUrl(appidControlEntity, MerchantGradeEnum.THREE.getCode());

        fullNotificationEmail(appidControlEntity, MerchantGradeEnum.TWO.getCode());
        fullNotificationUrl(appidControlEntity, MerchantGradeEnum.TWO.getCode());


        fullNotificationEmail(appidControlEntity, MerchantGradeEnum.ONE.getCode());
        fullNotificationUrl(appidControlEntity, MerchantGradeEnum.ONE.getCode());



    }

    public void fullNotificationEmail(AppidControlEntity appidControlEntity ,int merchantGrade) {

        String appid = appidControlEntity.getAppid();
        //查询所有需要全量邮件通知的商户
        List<String> merchantGradeList = appIdMerchantBindService.getTopMerchantsByAppidAndConfigAndEmail(appid,null,merchantGrade);
        log.info("需要全量通知的 merchantGradeList: {},merchantGrade:{} ", JsonUtils.convert(merchantGradeList),merchantGrade);


        for (String merchantGradeNo : merchantGradeList) {
            List<AppidAllMerchantNoBindEntity> appidAllMerchantNoBindEntities = appIdMerchantBindService.queryAllMerchantNoBindByMerchantGrade(appidControlEntity.getAppid(), merchantGradeNo,merchantGrade);
            //筛选出所有的子商编
            List<AppidAllMerchantNoBindEntity> mailSubMerchantNos = appidAllMerchantNoBindEntities.stream()
                    .filter(entity -> BindAllStatusEnum.BIND.equals(entity.getBindStatus())) // 过滤掉状态不是 bind 的数据
                    .filter(entity -> merchantGrade>=entity.getEmailLevel()) // 过滤掉等级大于当前等级的

                    .collect(Collectors.toMap(
                            AppidAllMerchantNoBindEntity::getMerchantNo, // 根据商编去重
                            entity -> entity,
                            (existing, replacement) -> existing // 如果有重复的商编，保留第一个
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());


            ControlConfigEntity controlConfigEntity = controlConfigService.queryValidByMerchantNo(merchantGradeNo, ControlStatusEnum.ACTIVE);
            if(null== controlConfigEntity) {
                continue;
            }
            //全量通知
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mailSubMerchantNos)) {
                notifyByConfigEmail(merchantGradeNo, mailSubMerchantNos,controlConfigEntity,appidControlEntity,merchantGrade);
            }
        }


    }


    public void fullNotificationUrl(AppidControlEntity appidControlEntity ,int merchantGrade) {

        String appid = appidControlEntity.getAppid();
        //查询所有需要全量接口通知的商户
        List<String> merchantGradeList = appIdMerchantBindService.getTopMerchantsByAppidAndConfigAndUrl(appid,null,merchantGrade);
        log.info("需要全量通知的 merchantGradeList: {},merchantGrade:{} ", JsonUtils.convert(merchantGradeList),merchantGrade);


        for (String merchantGradeNo : merchantGradeList) {
            List<AppidAllMerchantNoBindEntity> appidAllMerchantNoBindEntities = appIdMerchantBindService.queryAllMerchantNoBindByMerchantGrade(appidControlEntity.getAppid(), merchantGradeNo,merchantGrade);
            //筛选出所有的子商编
            List<AppidAllMerchantNoBindEntity> urlSubMerchantNos = appidAllMerchantNoBindEntities.stream()
                    .filter(entity -> BindAllStatusEnum.BIND.equals(entity.getBindStatus())) // 过滤掉状态不是 bind 的数据
                    .filter(entity -> merchantGrade>=entity.getUrlLevel()) // 过滤掉等级大于当前等级的

                    .collect(Collectors.toMap(
                            AppidAllMerchantNoBindEntity::getMerchantNo, // 根据商编去重
                            entity -> entity,
                            (existing, replacement) -> existing // 如果有重复的商编，保留第一个
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());


            ControlConfigEntity controlConfigEntity = controlConfigService.queryValidByMerchantNo(merchantGradeNo, ControlStatusEnum.ACTIVE);
            if(null== controlConfigEntity) {
                continue;
            }
            //全量通知
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(urlSubMerchantNos)) {
                //如果 urlSubMerchantNos 大于1000条 按1000条发送
                pageNotifyByConfig(appidControlEntity, merchantGrade, merchantGradeNo, urlSubMerchantNos, controlConfigEntity);
            }
        }


    }

    private void pageNotifyByConfig(AppidControlEntity appidControlEntity, int merchantGrade, String merchantGradeNo, List<AppidAllMerchantNoBindEntity> urlSubMerchantNos, ControlConfigEntity controlConfigEntity) {
        if(urlSubMerchantNos.size()>1000){
            List<List<AppidAllMerchantNoBindEntity>> partition = Lists.partition(urlSubMerchantNos, 1000);
            for (List<AppidAllMerchantNoBindEntity> subList : partition) {
                notifyByConfigUrl(merchantGradeNo, subList,controlConfigEntity,appidControlEntity,merchantGrade);
            }
        }
        notifyByConfigUrl(merchantGradeNo, urlSubMerchantNos,controlConfigEntity,appidControlEntity,merchantGrade);
    }


    public void notifyByConfigEmail(String topMerchant, List<AppidAllMerchantNoBindEntity> mailSubMerchantNos, ControlConfigEntity controlConfigEntity, AppidControlEntity appidControlEntity,int merchantGrade) {

        /**
         * 邮件通知状态
         */
         String emailNotifyStatus = NotifyStatusEnum.WAIT_NOTIFY.name();


         int emailLevel =0;

        //邮件通知

            if(mailNotifyService.createMailMessageByAppidAndSend(topMerchant,mailSubMerchantNos,controlConfigEntity,appidControlEntity)){
                emailNotifyStatus=NotifyStatusEnum.NOTIFIED.name();
            }
            emailLevel=merchantGrade;




        List<String> merchantNos = mailSubMerchantNos.stream()
                .map(AppidAllMerchantNoBindEntity::getMerchantNo)
                .collect(Collectors.toList());
        //更新通知状态
        appIdMerchantBindService.updateNotifyStatusEmail(topMerchant,appidControlEntity.getAppid(),merchantNos,emailNotifyStatus, emailLevel,merchantGrade);
    }


    public void notifyByConfigUrl(String topMerchant, List<AppidAllMerchantNoBindEntity> urlSubMerchantNos, ControlConfigEntity controlConfigEntity, AppidControlEntity appidControlEntity,int merchantGrade) {

        /**
         * 接口通知状态
         */
         String urlNotifyStatus = NotifyStatusEnum.WAIT_NOTIFY.name();


         int urlLevel =0;

        /**
         * 1 判断通知状态
         */
        //调用接口通知
        YopNotifyEntity yopNotifyEntity = buildYopNotifyEntity(topMerchant,urlSubMerchantNos,controlConfigEntity,appidControlEntity);
        if(yopNotifyExternal.call(yopNotifyEntity)){
            urlNotifyStatus=NotifyStatusEnum.NOTIFIED.name();
        }
        urlLevel=merchantGrade;

        List<String> merchantNos = urlSubMerchantNos.stream()
                .map(AppidAllMerchantNoBindEntity::getMerchantNo)
                .collect(Collectors.toList());
        //更新通知状态
        appIdMerchantBindService.updateNotifyStatusUrl(topMerchant,appidControlEntity.getAppid(),merchantNos,urlNotifyStatus, urlLevel,merchantGrade);
    }

    public YopNotifyEntity buildYopNotifyEntity(String topMerchant, List<AppidAllMerchantNoBindEntity> subMerchantNos, ControlConfigEntity controlConfigEntity, AppidControlEntity appidControlEntity) {
        if(CollectionUtils.isEmpty(subMerchantNos)){
            return null;
        }
        YopNotifyEntity yopNotifyEntity = new YopNotifyEntity();
        yopNotifyEntity.setMerchantNo(topMerchant);
        yopNotifyEntity.setAppKey(controlConfigEntity.getAppKey());
        yopNotifyEntity.setUrl(controlConfigEntity.getCallBackUrl());
        Map<String,Object>  map = new HashMap<String,Object>(16);
        map.put("receiver",topMerchant);
        map.put("controlAppid",appidControlEntity.getAppid());
        map.put("congtrolTime", DateUtils.getTimeStampStr(appidControlEntity.getControlStartTime()));
        if(null!= appidControlEntity.getControlEndTime()){
            map.put("controlExpireTime",DateUtils.getTimeStampStr(appidControlEntity.getControlEndTime()));
        }

        List<String> merchantNos = subMerchantNos.stream()
                .map(AppidAllMerchantNoBindEntity::getMerchantNo)
                .collect(Collectors.toList());
        map.put("merchantList",merchantNos);
        yopNotifyEntity.setNotifyInfo(map);
        yopNotifyEntity.setNotifyNo(UUIDUtils.randomV4UUID().replace("-", ""));
        return yopNotifyEntity;
    }


    private AppidControlEntity createAppidControl(ChannelAppidControlEvent message) throws ParseException {
        AppidControlEntity appidControlEntity = new AppidControlEntity();
        appidControlEntity.setAppid(message.getAppId());
        appidControlEntity.setControlStartTime(DateUtils.parseDate(message.getControlledTime(), DateUtils.DATE_FORMAT_DATETIME));
        if(StringUtils.isNotEmpty(message.getUncontrolledTime())){
            appidControlEntity.setControlEndTime(DateUtils.parseDate(message.getUncontrolledTime(), DateUtils.DATE_FORMAT_DATETIME));

        }
        appidControlEntity.setVersion(1L);
        appidControlEntity.setNotifyStatus(NotifyStatusEnum.WAIT_NOTIFY.name());


        appidControlEntity.setNotifyType(message.getNotifyType());
        return appidControlEntity;

    }


    private boolean areDatesEqual(Date date1, Date date2) {
        if (date1 == null && date2 == null) {
            return false;
        }
        if (date1 == null || date2 == null) {
            return true;
        }
        return date1.getTime() != date2.getTime();
    }
}
