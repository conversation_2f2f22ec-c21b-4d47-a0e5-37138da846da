package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.IdentificationTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.LegalTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.SuperAdminInfoDTO;
import lombok.*;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/6/7 11:31
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class SuperAdminInfo {

    /**
     * 联系人姓名
     */
    private final String contactName;

    /**
     * 联系人手机号
     */
    private final String contactMobileNo;

    /**
     * 联系人身份证号
     * 当超级管理员类型是经办人时，请上传超级管理员证件号码；
     */
    private final String contactCertNo;

    /**
     * 联系人类型
     * 1、主体为“小微/个人卖家 ”，可选择：65-经营者/法人。
     * 2、主体为“个体工商户/企业/政府机关/事业单位/社会组织”，可选择：65-经营者/法人、66- 经办人。
     */
    private final LegalTypeEnum contactType;

    /**
     * 当超级管理员类型是经办人时，请上传超级管理员证件类型。
     */
    private final IdentificationTypeEnum certType;

    /**
     * 联系人证件正面照片
     * 当超级管理员类型是经办人时，请上传超级管理员证件的正面照片；
     */
    private final String cardFrontImg;

    /**
     * 联系人证件反面照片
     * 当超级管理员类型是经办人时，请上传超级管理员证件的反面照片；
     */
    private final String cardBackImg;

    /**
     * 联系人证件有效期开始时间
     * 当超级管理员类型是经办人时，请上传证件有效期开始时间；
     */
    private final String effectTime;

    /**
     * 联系人证件有效期结束时间
     * 当超级管理员类型是经办人时，请上传证件有效期结束时间；
     */
    private final String expireTime;

    /**
     * 业务办理授权函
     * 1、当超级管理员类型是经办人时，请上传业务办理授权函；
     */
    private final String businessAuthorizationLetter;

    /**
     * 联系人邮箱
     */
    private final String contactEmail;

    public static SuperAdminInfo build(final SuperAdminInfoDTO superAdminInfo) {

        // 联系人类型
        final LegalTypeEnum legalType = DocumentedEnum.fromValueOfNullable(LegalTypeEnum.class, superAdminInfo.getContactType());
        final IdentificationTypeEnum certType = DocumentedEnum.fromValueOfNullable(IdentificationTypeEnum.class, superAdminInfo.getCertType());

        return SuperAdminInfo.builder()
                .contactType(legalType)
                .contactName(superAdminInfo.getContactName())
                .contactMobileNo(superAdminInfo.getContactMobileNo())
                .contactCertNo(superAdminInfo.getContactCertNo())
                .contactEmail(superAdminInfo.getContactEmail())
                .certType(certType)
                .cardFrontImg(superAdminInfo.getCardFrontImg())
                .cardBackImg(superAdminInfo.getCardBackImg())
                .effectTime(buildEffectTime(superAdminInfo.getEffectTime()))
                .expireTime(buildExpireTime(superAdminInfo.getExpireTime()))
                .businessAuthorizationLetter(superAdminInfo.getBusinessAuthorizationLetter())
                .build();

    }

    public static SuperAdminInfo buildByChannelNo(final SuperAdminInfoDTO superAdminInfo) {

        // 联系人类型
        final LegalTypeEnum legalType = DocumentedEnum.fromValueOfNullable(LegalTypeEnum.class, superAdminInfo.getContactType());
        final IdentificationTypeEnum certType = DocumentedEnum.fromValueOfNullable(IdentificationTypeEnum.class, superAdminInfo.getCertType());
        return SuperAdminInfo.builder()
                .contactType(legalType)
                .contactName(superAdminInfo.getContactName())
                .contactMobileNo(superAdminInfo.getContactMobileNo())
                .contactCertNo(superAdminInfo.getContactCertNo())
                .contactEmail(superAdminInfo.getContactEmail())
                .certType(certType)
                .cardFrontImg(superAdminInfo.getCardFrontImg())
                .cardBackImg(superAdminInfo.getCardBackImg())
                .effectTime(superAdminInfo.getEffectTime())
                .expireTime(superAdminInfo.getExpireTime())
                .businessAuthorizationLetter(superAdminInfo.getBusinessAuthorizationLetter())
                .build();

    }

    public static String buildEffectTime(String effectTime) {
        if (StringUtils.isNotBlank(effectTime)) {
            boolean effectTimeIsValid = Const.DATE_PATTERN.matcher(effectTime).matches();
            Assert.isTrue(effectTimeIsValid, ResultCode.PARAM_VALID_ERROR, "证书有效期限开始日期格式错误");
            return effectTime;
        }
        return null;
    }

    public static String buildExpireTime(String expireTime) {
        if (StringUtils.isNotBlank(expireTime)) {
            boolean expireTimeIsValid = Const.DATE_PATTERN.matcher(expireTime).matches()
                    || Const.PERMANENT.equals(expireTime);
            Assert.isTrue(expireTimeIsValid, ResultCode.PARAM_VALID_ERROR, "证书结束日期格式错误");
            return expireTime;
        }
        return null;
    }
}

