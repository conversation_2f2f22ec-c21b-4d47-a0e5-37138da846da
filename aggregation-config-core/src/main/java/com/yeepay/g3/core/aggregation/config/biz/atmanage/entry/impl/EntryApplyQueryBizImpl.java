package com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.QueryBaseStatusEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.EntryApplyQueryBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryApplyResultReqBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryResultRespBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryAnchoredInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryChannelTerminalInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryApplyDetailInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import com.yeepay.g3.core.aggregation.config.enums.DivideReceiverStatus;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.external.AggAttachRecordExternal;
import com.yeepay.g3.core.aggregation.config.external.ChannelAttachRecordExternal;
import com.yeepay.g3.core.aggregation.config.external.ChannelDivideExternal;
import com.yeepay.g3.core.aggregation.config.external.ChannelEntryExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.QueryEntryProcessBaseReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelDivideInfo;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/2 22:20
 */
@Slf4j
@Service
public class EntryApplyQueryBizImpl implements EntryApplyQueryBiz {

    @Qualifier("asyncExecutor")
    @Resource
    private AtlasThreadPoolExecutor executor;

    @Resource
    private ChannelEntryOrderService channelEntryOrderService;

    @Resource
    private ThirdPartyRecordService thirdPartyRecordService;

    @Resource
    private ChannelEntryExternal channelEntryExternal;

    @Resource
    private ChannelDivideExternal receiverExternal;

    @Resource
    private ChannelAttachRecordExternal attachRecordExternal;

    @Resource
    private AggAttachRecordExternal aggAttachRecordExternal;


    @Override
    public QueryEntryResultRespBO queryMerchantEntryResult(QueryEntryApplyResultReqBO reqBO) {
        QueryEntryProcessBaseReqBO baseReq =  QueryEntryProcessBaseReqBO.builder()
                .merchantNo(reqBO.getMerchantNo())
                .payChannel(reqBO.getPayChannel())
                .payScene(reqBO.getPayScene())
                .activityType(reqBO.getActivityType())
                .digitalCurrencyBankCode(reqBO.getDigitalCurrencyBankCode()).build();

        CompletableFuture<List<QueryEntryApplyDetailInfoBO> > entryApplyTask = CompletableFuture.supplyAsync(() ->
                this.queryEntryApplyResult(reqBO), executor);

        CompletableFuture<List<QueryEntryInfoBO>> entryTask = CompletableFuture.supplyAsync(() ->
            channelEntryExternal.queryMerchantEntryResult(baseReq), executor);

        CompletableFuture<List<QueryAnchoredInfoBO>> aggAttachTask = CompletableFuture.supplyAsync(() ->
            aggAttachRecordExternal.queryAttachRecord(baseReq), executor);

        CompletableFuture<List<QueryAnchoredInfoBO>> channelAttachTask = CompletableFuture.supplyAsync(() ->
            attachRecordExternal.queryAttachRecord(baseReq), executor);

        // 合并聚合挂靠和通道挂靠信息
        CompletableFuture<List<QueryAnchoredInfoBO>> combinedAttachTask = aggAttachTask.thenCombine(channelAttachTask, (aggList, channelList) -> {
            List<QueryAnchoredInfoBO> result = new ArrayList<>(aggList);
            result.addAll(channelList);
            return result;
        });

        try {
            List<QueryEntryInfoBO> queryEntryInfoBOList = entryTask.get();
            if (CheckUtils.isEmpty(queryEntryInfoBOList)) {
                return QueryEntryResultRespBO.build(
                        reqBO.getMerchantNo(),
                        entryApplyTask.get(),
                        null,
                        combinedAttachTask.get(),
                        null,
                        null,
                        new HashMap<>()
                );
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("[进件查询] 获取进件结果异常 reqBO={}", JsonUtils.toJSONString(reqBO), e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询异常!");
        }


        // 异步任务4：查询微信终端报备
        CompletableFuture<QueryChannelTerminalInfoBO> wxTerminalTask = entryTask.thenApply(entryList -> {
            if (entryList.stream().noneMatch(e -> PayChannelEnum.WECHAT.equals(e.getPayChannel()))) {
                return null;
            }
            return channelEntryExternal.queryTerminalEntryResult(reqBO.getMerchantNo(), PayChannelEnum.WECHAT);
        }).exceptionally(ex -> null);

        // 异步任务5：查询支付宝终端报备
        CompletableFuture<QueryChannelTerminalInfoBO> aliTerminalTask = entryTask.thenApply(entryList -> {
            if (entryList.stream().noneMatch(e -> PayChannelEnum.ALIPAY.equals(e.getPayChannel()))) {
                return null;
            }
            return channelEntryExternal.queryTerminalEntryResult(reqBO.getMerchantNo(), PayChannelEnum.ALIPAY);
        }).exceptionally(ex -> null);

        // 异步任务6：查询分账方
        CompletableFuture<HashMap<String, String>> receiverTask = entryTask.thenApply(entryList -> {
            HashMap<String, String> resultMap = new HashMap<>();
            for (QueryEntryInfoBO queryEntryInfoBO : entryList) {
                if (PayChannelEnum.WECHAT.equals(queryEntryInfoBO.getPayChannel())
                        && PaySceneEnum.DIRECT.equals(queryEntryInfoBO.getPayScene())) {
                    RemoteResult<ChannelDivideInfo> divideReceiver = receiverExternal.queryDivideReceiver(reqBO.getMerchantNo(), queryEntryInfoBO.getChannelNo(), null);
                    resultMap.put(queryEntryInfoBO.getChannelNo(), convertStatus(divideReceiver));
                }
            }
            return resultMap;
        }).exceptionally(ex -> new HashMap<>());

        return CompletableFuture.allOf(
                entryTask, combinedAttachTask, wxTerminalTask, aliTerminalTask, receiverTask)
            .thenApply(v -> QueryEntryResultRespBO.build(
                reqBO.getMerchantNo(),
                entryApplyTask.join(),
                entryTask.join(),
                combinedAttachTask.join(),
                wxTerminalTask.join(),
                aliTerminalTask.join(),
                receiverTask.join()
            ))
            .exceptionally(ex -> {
                Thread.currentThread().interrupt();
                log.error("异步任务执行失败", ex);
                if (ex instanceof BusinessException) {
                    throw (BusinessException) ex;
                } else {
                    throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询异常!");
                }
            })
            .join();

    }

    private List<QueryEntryApplyDetailInfoBO> queryEntryApplyResult(QueryEntryApplyResultReqBO reqBO) {
        List<BaseEntryApply<? extends ChannelSpecialParams>> baseEntryApplies = channelEntryOrderService.queryChannelEntryOrder(reqBO);
        if (CheckUtils.isEmpty(baseEntryApplies)) {
            return new ArrayList<>();
        }
        List<String> applyNoList = baseEntryApplies.stream().map(BaseEntryApply::getApplyNo).collect(Collectors.toList());
        List<ThirdPartyRecordEntity> thirdPartyRecords = thirdPartyRecordService.queryThirdPartyRecordListByApplyNo(applyNoList,
                ThirdPartyBusinessTypeEnum.CHANNEL_ENTRY.getDocument());
        return QueryEntryApplyDetailInfoBO.buildList(baseEntryApplies, thirdPartyRecords);
    }


    /**
     * 转换分账方状态
     */
    private String convertStatus(RemoteResult<ChannelDivideInfo> divideReceiver) {
        if (divideReceiver.isSuccess() && divideReceiver.getData() != null) {
            if (DivideReceiverStatus.SUBMIT_PROCESS == divideReceiver.getData().getStatus()) {
                return QueryBaseStatusEnum.PROCESSING.getDocument();
            } else if (DivideReceiverStatus.SUCCESS == divideReceiver.getData().getStatus()) {
                return QueryBaseStatusEnum.SUCCESS.getDocument();
            } else {
                return QueryBaseStatusEnum.FAIL.getDocument();
            }
        } else {
            return null;
        }
    }
}


