package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ContactInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.ActivityInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.enums.ChannelGatewayCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/4 22:32
 */
public interface EntryApply<T extends ChannelSpecialParams> {

    String getOrderNo();

    String getApplyNo();

    MerchantInfo getMerchantInfo();

    String getChannelNo();

    String getChannelIdentifier();

    SubjectInfo getSubjectInfo();

    ContactInfo getContactInfo();

    EntryTypeEnum getEntryType();

    PaySceneEnum getPayScene();

    PayChannelEnum getPayChannel();

    ActivityTypeEnum getActivityType();

    ActivityInfo getActivityInfo();

    EntryStatusEnum getEntryStatus();

    EntryStatusEnum getOldEntryStatus();

    EntryAuditStatus getEntryAuditStatus();

    EntryAuditStatus getOldEntryAuditStatus();

    Long getFlag();

    Long getFlagStatus();

    Integer getBackupCounts();

    boolean getBackupSwitch();

    T getChannelSpecialParams();

    String getChannelMchNos();

    ChannelGatewayCode getChannelGatewayCode();

    LocalDateTime getFinishTime();

    String getFailReason();

    String getExtendInfo();

    String getAnchoredApplyNo();

    SettleAccountInfo getSettleAccountInfo();
    default AnchoredOrder getAnchoredOrder() {
        return null;
    }
}
