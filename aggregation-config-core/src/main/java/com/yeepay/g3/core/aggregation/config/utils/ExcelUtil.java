package com.yeepay.g3.core.aggregation.config.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoMailEntity;
import com.yeepay.g3.utils.common.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @ClassName: ExcelUtil
 * @Author: cong.huo
 * @Date: 2024/12/19 18:45   // 时间
 * @Version: 1.0
 */
@Slf4j
public class ExcelUtil {


    public static <T> File writeDataToExcel(List<T> data, String fileName, String sheetName, Class<T> clazz) {
        File file = new File(fileName);
        try (FileOutputStream outputStream = new FileOutputStream(file)) {
            // 使用 LongestMatchColumnWidthStyleStrategy 实现列宽自适应
            LongestMatchColumnWidthStyleStrategy columnWidthStyleStrategy = new LongestMatchColumnWidthStyleStrategy();

            // 创建 ExcelWriterBuilder 并设置列宽策略
            ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream, clazz)
                    .registerWriteHandler(columnWidthStyleStrategy);


            // 写入数据
            writerBuilder.sheet(sheetName)
                    .doWrite(data);

            log.info("数据导出成功，文件路径：{}", file.getAbsolutePath());
        } catch (IOException e) {
            log.error("导出数据到 Excel 失败", e);
            return null;
        }
        return file;
    }


}
