package com.yeepay.g3.core.aggregation.config.impl.atmanage;

import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.AnchoredBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyRespBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request.AnchoredApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.response.AnchoredApplyResponseDTO;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.AnchoredApplyFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 挂靠相关服务
 *
 * @author: Mr.yin
 * @date: 2025/6/5  16:41
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AnchoredApplyFacadeImpl implements AnchoredApplyFacade {

    private final AnchoredBiz anchoredBiz;

    @Override
    public AnchoredApplyResponseDTO anchoredApply(AnchoredApplyReqDTO reqDTO) {
        log.info("[请求挂靠] reqDTO={}", JsonUtils.toJSONString(reqDTO));
        AnchoredCmd anchoredCmd = AnchoredCmd.build(reqDTO);
        AnchoredApplyRespBO respBO = anchoredBiz.anchoredApply(anchoredCmd);
        AnchoredApplyResponseDTO respDTO = new AnchoredApplyResponseDTO();
        respDTO.setBizScene(respBO.getBizScene());
        respDTO.setBizApplyNo(respBO.getBizApplyNo());
        respDTO.setOrderNo(respBO.getOrderNo());
        log.info("[请求挂靠] respBO={}", JsonUtils.toJSONString(respDTO));
        return respDTO;
    }
}
