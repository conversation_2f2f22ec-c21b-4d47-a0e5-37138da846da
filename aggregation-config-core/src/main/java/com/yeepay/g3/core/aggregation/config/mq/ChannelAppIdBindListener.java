package com.yeepay.g3.core.aggregation.config.mq;

import com.yeepay.g3.core.aggregation.config.biz.ChannelAppIdBindBiz;
import com.yeepay.g3.core.aggregation.config.builder.AppIdMerchantBindBuilder;
import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppIdBindEvent;
import com.yeepay.g3.core.aggregation.config.utils.SpringContextUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2025-01-02 13:22
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
public class ChannelAppIdBindListener extends MqBaseListener<ChannelAppIdBindEvent> {

    public ChannelAppIdBindListener(Class<ChannelAppIdBindEvent> channelAppIdBindEventClass) {
        super(channelAppIdBindEventClass);
    }

    @Override
    public String topicName() {
        return "同步AppId与商编的绑定关系";
    }

    public static String DATE_FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss";

    @Override
    public void onMessage(ChannelAppIdBindEvent message) throws Exception {
        AppIdMerchantBindEntity entity = SpringContextUtils.getBean(AppIdMerchantBindBuilder.class).eventToEntity(message);
        entity.setChannelType(ChannelTypeEnum.WECHAT);
        Date operateTime = DateUtils.parseDate(message.getConfigTime(),DATE_FORMAT_DATETIME);
        entity.setOperateTime(operateTime);

        //查询客户中心
        try {
            MerchantCenterExternal merchantCenterExternal = SpringContextUtils.getBean(MerchantCenterExternal.class);
            MerchantGroupEntity merchantGroupEntity = merchantCenterExternal.queryMerchantGroupByMerchantNo(entity.getMerchantNo());
            entity.setFirstMerchantNo(merchantGroupEntity.getGroup().get(0));
            entity.setSecondMerchantNo(merchantGroupEntity.getGroup().size() > 1 ? merchantGroupEntity.getGroup().get(1) : "");
            entity.setThreeMerchantNo(merchantGroupEntity.getGroup().size() > 2 ? merchantGroupEntity.getGroup().get(2) : "");
            entity.setMerchantName(merchantGroupEntity.getMerchantName());
        }catch (Throwable e) {
            throw new AggConfigException(ErrorCodeEnum.MQ_CONSUMER_ERROR,e.getMessage());
        }

        //处理方法
        entity.setCleanStatus(DataCleanStatusEnum.INIT);
        SpringContextUtils.getBean(ChannelAppIdBindBiz.class).handle(entity);

    }
}
