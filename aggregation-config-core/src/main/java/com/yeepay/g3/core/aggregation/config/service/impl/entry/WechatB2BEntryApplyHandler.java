package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryNotifyBizTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WechatB2BEntryApply;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:11
 */
@Slf4j
@Service
public class WechatB2BEntryApplyHandler implements EntryApplyHandler<WechatB2BEntryApply> {

    private final EntryApplyHandler<WechatB2BEntryApply> baseEntryApplyHandler;
    private final ChannelNoBiz channelNoBiz;
    private final EntryResultProducer entryResultProducer;


    @Autowired
    public WechatB2BEntryApplyHandler(@Qualifier("baseEntryApplyHandler") final EntryApplyHandler<WechatB2BEntryApply> baseEntryApplyHandler,
                                      final ChannelNoBiz channelNoBiz,
                                      final EntryResultProducer entryResultProducer) {
        this.baseEntryApplyHandler = baseEntryApplyHandler;
        this.channelNoBiz = channelNoBiz;
        this.entryResultProducer = entryResultProducer;
    }

    @Override
    public void buildChannelNo(final WechatB2BEntryApply entryApply) {
        // 1、查询微信B2B渠道号
        entryApply.buildChannelNo(channelNoBiz.queryChannelNoByWechatB2B());
    }

    @Override
    public void buildBackupInfo(final WechatB2BEntryApply entryApply) {
        if (log.isDebugEnabled()) {
            log.debug("微信B2B无备份");
        }
    }

    @Override
    public ChannelEntryResult entryApply(final EntryApplyContext<WechatB2BEntryApply> entryApplyContext) {
        return baseEntryApplyHandler.entryApply(entryApplyContext);
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {

        return PayChannelEnum.WECHAT == payChannel
                && PaySceneEnum.STORE_ASST == payScene;
    }

    @Override
    public WechatB2BEntryApply buildEntryApply(ChannelEntryOrderEntity orderEntity, ChannelEntryApplyDetailEntity detailEntity) {
        return new WechatB2BEntryApply(orderEntity, detailEntity);
    }

    @Override
    public void resultHandle(final EntryApplyContext<WechatB2BEntryApply> context,
                             final ChannelEntryResult channelEntryResult) {
        baseEntryApplyHandler.resultHandle(context, channelEntryResult);
        if (!context.getEntryApply().getEntryStatus().finalStatus()) {
            entryResultProducer.sendEntryNotifyMessage(context.getEntryApply(), EntryNotifyBizTypeEnum.ENTRY);
        }
    }
}
