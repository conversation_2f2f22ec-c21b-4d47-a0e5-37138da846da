package com.yeepay.g3.core.aggregation.config.biz.atmanage;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.enums.TerminalReportStatus;
import com.yeepay.g3.core.aggregation.config.mq.event.TerminalReportMessage;

/**
 * 终端报备业务处理
 *
 * @author: Mr.yin
 * @date: 2025/6/5  14:33
 */
public interface TerminalReportBiz {

    /**
     * 通道终端报备 这是个异步接口,,,,
     * 只有微信支付宝才需要
     * 851006:真正报备成功(回调报备成功后)再次相同的参数请求，返回次返回码 这种情况接不到回调，所以要特殊处理哦
     */
    TerminalReportStatus applyChannelTerminalReport(BaseEntryApply<? extends ChannelSpecialParams> entryApply);

    /**
     * 处理通道终端报备结果
     *
     * @param callBackBO
     */
    void callBackTerminalReport(TerminalReportMessage callBackBO);
}
