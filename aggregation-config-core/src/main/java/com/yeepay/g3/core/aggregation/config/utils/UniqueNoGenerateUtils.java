package com.yeepay.g3.core.aggregation.config.utils;

import com.yeepay.g3.utils.common.CommonUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * description: 请求号生成工具类
 * <AUTHOR>
 * @since 2025/5/26:11:29
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@UtilityClass
public class UniqueNoGenerateUtils {

    private static final Random RANDOM = new Random();
    private static final String LONG_DATE_FORMAT = "yyMMddHHmmssSSS";

    /**
     * 原子整数初始值
     */
    private static final AtomicInteger INIT_ATO_INTEGER = new AtomicInteger(RANDOM.nextInt(1000) + 10000);

    /**
     * 本机IP对应的十进制数字（只取四段中的后三段）
     */
    private static final String MACHINE_ID = String.format("%08d", (IPUtils.getRealLocalIpNum() & 0xFFFFFF));


    /**
     * 获取时间戳，毫秒级
     */
    private static String getCurrentTimeMillis() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(LONG_DATE_FORMAT);
        //200908172100044
        return simpleDateFormat.format(new Date());
    }

    /**
     * 获取时间戳，毫秒级
     */
    private static String getCurrentTimeMillis(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(LONG_DATE_FORMAT);
        //200908172100044
        return simpleDateFormat.format(date);
    }


    /**
     * 获取自增原子值（10000-99999循环）
     */
    private static String getIncrementValue() {
        // 获取下一个自增值，取二进制后16位（最大65535），无需关注Integer上下界即可实现循环
        return String.format("%05d", INIT_ATO_INTEGER.incrementAndGet() & 0xFFFF);

    }

    /**
     * 生成支付订单号
     * 15位时间（毫秒级） + 8位机器号 + 5位自增循环数
     */
    public static String getUniqueNo() {
        return getCurrentTimeMillis() + MACHINE_ID + getIncrementValue();
    }


    /**
     * 生成UUID
     */
    public static String getUUIDUniqueNo() {
        return getUUIDUniqueNo(new Date());
    }

    /**
     * 生成UUID
     */
    public static String getUUIDUniqueNo(Date date) {
        return getCurrentTimeMillis(date) + CommonUtils.getUUID().substring(24);
    }

}
