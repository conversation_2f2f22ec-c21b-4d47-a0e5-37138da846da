package com.yeepay.g3.core.aggregation.config.mq.producer.impl;

import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.constant.RocketMqConst;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.AnchoredBiz;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.DigitalCurrencyEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyDetailResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredResultBO;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.message.atmanage.EntryDetailResultNotifyMessage;
import com.yeepay.g3.facade.aggregation.config.message.atmanage.model.AnchoredInfoMessage;
import com.yeepay.g3.facade.aggregation.config.message.atmanage.model.EntryInfoMessage;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 挂靠结果发送者
 *
 * @author: Mr.yin
 * @date: 2025/6/10  17:30
 */
@Component
public class EntryResultProducerImpl implements EntryResultProducer {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final DefaultMQProducer aggConfigAnchoredProducer;

    private final AnchoredBiz anchoredBiz;

    private static final String SUCCESS = "SUCCESS";

    private static final String CLOSED = "CLOSED";

    private static final String PROCESSING = "PROCESSING";

    private static final String NOT_NEED = "NOT_NEED";


    @Autowired
    public EntryResultProducerImpl(@Qualifier("aggConfigAnchoredProducer") final DefaultMQProducer aggConfigAnchoredProducer,
                                   final AnchoredBiz anchoredBiz) {
        this.aggConfigAnchoredProducer = aggConfigAnchoredProducer;
        this.anchoredBiz = anchoredBiz;
    }

    /**
     * 发送进件通知消息
     */
    @Override
    public void sendEntryNotifyMessage(BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply, EntryNotifyBizTypeEnum notifyBizType) {
        logger.info("[发送进件相关通知消息] notifyBizType = {},baseEntryApply={}", notifyBizType, JsonUtils.toJSONString(baseEntryApply));
        EntryDetailResultNotifyMessage message = verifyAndAssembleEntryResultMessage(baseEntryApply, notifyBizType);
        String msgKey = baseEntryApply.getApplyNo() + "," + notifyBizType.getDocument() + System.currentTimeMillis();
        logger.info("[发送进件相关通知消息]msgKey={} message={}", msgKey, JsonUtils.toJSONString(message));
        Message msg = new Message(RocketMqConst.ENTRY_RESULT_TOPIC, JsonUtils.toJSONString(message).getBytes());
        msg.setKeys(msgKey);
        msg.setTags(baseEntryApply.getBizScene().getDocument());
        int i = 0;
        while (i < 3) {
            try {
                SendResult sendResult = aggConfigAnchoredProducer.send(msg);
                switch (sendResult.getSendStatus()) {
                    case SEND_OK:
                        logger.info("[发送进件相关通知消息]发送成功 msgId={} message={}", sendResult.getMsgId(), JsonUtils.toJSONString(message));
                        return;
                    default:
                        logger.warn("[发送进件相关通知消息]  [rocketMQ状态异常] ,message={}", JsonUtils.toJSONString(message));
                        throw new BusinessException(ResultCode.SERVER_ERROR, "系统异常");
                }
            } catch (Exception e) {
                logger.warn("[发送进件相关通知消息] MQ发送重试 ,message=" + JsonUtils.toJSONString(message) + " , cause by ", e);
            }
            i++;
            if (i == 3) {
                logger.error("[发送进件相关通知消息]MQ发送失败 message={}", JsonUtils.toJSONString(message));
            }
            try {
                Thread.sleep(100L);
            } catch (InterruptedException e) {
                //do nothing
            }
        }
    }

    private EntryDetailResultNotifyMessage verifyAndAssembleEntryResultMessage(BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply, EntryNotifyBizTypeEnum notifyBizType) {

        EntryDetailResultNotifyMessage message = assembleBaseEntryApplyInfo(baseEntryApply, notifyBizType);
        /*查询挂靠相关信息*/
        List<AnchoredInfoMessage> anchoredInfoList = assembleAnchoredResult(baseEntryApply, notifyBizType);
        message.setAnchoredInfoList(anchoredInfoList);

        EntryInfoMessage entryInfoMessage = new EntryInfoMessage();
        entryInfoMessage.setEntryAuditStatus(DocumentedEnum.parseName(baseEntryApply.getEntryAuditStatus()));
        entryInfoMessage.setEntryStatus(DocumentedEnum.parseName(baseEntryApply.getEntryStatus()));
        entryInfoMessage.setFailCode(baseEntryApply.getFailCode());
        entryInfoMessage.setFailMessage(baseEntryApply.getFailReason());
        entryInfoMessage.setChannelNo(baseEntryApply.getChannelNo());
        entryInfoMessage.setChannelIdentifier(baseEntryApply.getChannelIdentifier());
        entryInfoMessage.setInstitutionMerchantNo(baseEntryApply.getChannelMchNos());
        entryInfoMessage.setSignUrl(baseEntryApply.getSignUrl());
        entryInfoMessage.setEntryApplyNo(baseEntryApply.getApplyNo());
        List<EntryInfoMessage> entryInfoList = Lists.newArrayList(entryInfoMessage);
        message.setEntryInfoList(entryInfoList);


        /*终端报备结果*/
        if (baseEntryApply.getTerminalReportFlag()) {
            message.setTerminalReportStatus(baseEntryApply.getTerminalReportFlagStatus() ? SUCCESS : PROCESSING);

            if (baseEntryApply.needTerminalOrDivideClosed()) {
                message.setTerminalReportStatus(CLOSED);
            }
            /*因为进件上没有失败标记，补偿一直补，所以只有卡单*/
        } else {
            message.setTerminalReportStatus(NOT_NEED);
        }
        /*分账方结果*/
        if (baseEntryApply.getWxDirectCreateSplitterFlag()) {
            message.setSplitCreateStatus(baseEntryApply.getWxDirectCreateSplitterFlagStatus() ? SUCCESS : PROCESSING);
            if (baseEntryApply.needTerminalOrDivideClosed()) {
                message.setSplitCreateStatus(CLOSED);
            }
            /*因为进件上没有失败标记，补偿一直补，所以只有卡单*/
        } else {
            message.setSplitCreateStatus(NOT_NEED);
        }

        return message;
    }


    private List<AnchoredInfoMessage> assembleAnchoredResult(BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply, EntryNotifyBizTypeEnum notifyBizType) {
        if (!baseEntryApply.getAnchoredApplyFlag()) {
            return Collections.emptyList();
        }
        AnchoredResultBO anchoredResultBO = anchoredBiz.queryAnchoredResult(baseEntryApply.getApplyNo(), BizSceneEnum.AGG_ENTRY.getDocument());
        if (null == anchoredResultBO) {
            AnchoredInfoMessage anchoredInfoMessage = new AnchoredInfoMessage();
            anchoredInfoMessage.setAnchoredStatus(AnchoredStatus.FAIL.name());
            if (EntryNotifyBizTypeEnum.ENTRY == notifyBizType && EntryTypeEnum.ENTRY_ANCHORED == baseEntryApply.getEntryType()) {
                logger.info("[获取挂靠结果] 未获取到挂靠记录，但是是进件+挂靠场景的进件结果，，挂靠先发成处理中，进件申请编号为{}", baseEntryApply.getApplyNo());
                anchoredInfoMessage.setAnchoredStatus(AnchoredStatus.PROCESSING.name());
            }
            return Lists.newArrayList(anchoredInfoMessage);
        }
        AnchoredApplyDetailResultBO anchoredApplyDetailResultBO = anchoredResultBO.getAnchoredApplyList().get(0);
        if (DocumentedEnum.stringInRightEnums(anchoredApplyDetailResultBO.getAnchoredStatus(), AnchoredStatus.SUCCESS)) {
            return anchoredApplyDetailResultBO.getAnchoredMerchantInfoList().stream().map(e -> {
                AnchoredInfoMessage anchoredInfoMessage = new AnchoredInfoMessage();
                anchoredInfoMessage.setAnchoredStatus(AnchoredStatus.SUCCESS.name());
                anchoredInfoMessage.setAnchoredMerchantNo(e.getAnchoredMerchantNo());
                anchoredInfoMessage.setAnchoredChannelNo(e.getAnchoredChannelNo());
                return anchoredInfoMessage;
            }).collect(Collectors.toList());
        } else if (DocumentedEnum.stringInRightEnums(anchoredApplyDetailResultBO.getAnchoredStatus(), AnchoredStatus.FAIL)) {
            AnchoredInfoMessage anchoredInfoMessage = new AnchoredInfoMessage();
            anchoredInfoMessage.setAnchoredStatus(AnchoredStatus.FAIL.name());
            anchoredInfoMessage.setFailMessage(anchoredApplyDetailResultBO.getFailReason());
            return Lists.newArrayList(anchoredInfoMessage);
        } else {
            AnchoredInfoMessage anchoredInfoMessage = new AnchoredInfoMessage();
            anchoredInfoMessage.setAnchoredStatus(AnchoredStatus.PROCESSING.name());
            return Lists.newArrayList(anchoredInfoMessage);
        }
    }

    private EntryDetailResultNotifyMessage assembleBaseEntryApplyInfo(BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply, EntryNotifyBizTypeEnum notifyBizType) {

        EntryDetailResultNotifyMessage message = new EntryDetailResultNotifyMessage();
        message.setBizScene(baseEntryApply.getBizScene().getDocument());
        message.setNotifyBizType(notifyBizType.getDocument());
        message.setBizApplyNo(baseEntryApply.getBizApplyNo());
        message.setMerchantNo(baseEntryApply.getMerchantInfo().getMerchantNo());
        message.setChannelType(DocumentedEnum.parseName(baseEntryApply.getPayChannel()));
        message.setPayScene(DocumentedEnum.parseName(baseEntryApply.getPayScene()));
        message.setActivityType(DocumentedEnum.parseName(baseEntryApply.getActivityType()));
        SceneAndActivityTypeBO sceneAndPromotionTypeBO = SceneAndActivityUtils.getSceneAndPromotionTypeBO(baseEntryApply.getPayChannel(), baseEntryApply.getPayScene(), baseEntryApply.getActivityType());
        message.setFeeType(sceneAndPromotionTypeBO.getChannelFeeType());
        message.setChannelActivityType(sceneAndPromotionTypeBO.getChannelActivityType());
        message.setEntryType(DocumentedEnum.parseName(baseEntryApply.getEntryType()));
        if (baseEntryApply instanceof DigitalCurrencyEntryApply) {
            message.setBankCode(((DigitalCurrencyEntryApply) baseEntryApply).getBankCode());

        }
        return message;
    }

}
