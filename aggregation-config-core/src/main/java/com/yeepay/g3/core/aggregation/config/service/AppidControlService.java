package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity;

import java.util.List;

/**
 * @Description: appid管控
 * @ClassName: AppidControlService
 * @Author: cong.huo
 * @Date: 2024/12/18 15:47   // 时间
 * @Version: 1.0
 */
public interface AppidControlService {
    /**
     *  根据appid查询管控信息
     * @param appid
     * @return com.yeepay.g3.core.aggregation.config.entity.AddidControlEntity
     * <AUTHOR>
     * @date 2024/12/18 15:50
     */
    AppidControlEntity queryByAppid(String appid);

    /**
     * @param appidControlEntityQuery
     * @param version
     * @return void
     * <AUTHOR>
     * @date 2024/12/18 17:01
     */
    int updateByIdVersion(AppidControlEntity appidControlEntityQuery,long version);

    /**
     *
     * @param appidControlEntity
     * @return void
     * <AUTHOR>
     * @date 2024/12/18 17:01
     */
    void insert(AppidControlEntity appidControlEntity);

    /**
     * 查询管控列表
     * @param appid
     * @param notifyStatus
     * @return
     */
    List<AppidControlEntity> queryByControlList(String appid,String notifyStatus);
}
