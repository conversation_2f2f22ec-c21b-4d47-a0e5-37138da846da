package com.yeepay.g3.core.aggregation.config.external.bo.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 渠道号主体信息
 * auth:dsc
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
@Data
public class ChannelNoSignSubjectBO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 证件号
     */
    @JSONField(name = "tripartiteSign_licenceNo")
    private String licenceNo;

    /**
     * 渠道号
     */
    @JSONField(name = "tripartiteSign_channelNo")
    private String channelNo;

    /**
     * 主体对应的商户编号
     */
    @JSONField(name = "tripartiteSign_channelMerchantNo")
    private String channelMerchantNo;

    /**
     * 签约名
     */
    @JSONField(name = "tripartiteSign_signName")
    private String signName;

    /**
     * 法人姓名
     */
    @JSONField(name = "tripartiteSign_legalName")
    private String legalName;

    /**
     * 法人手机号
     */
    @JSONField(name = "tripartiteSign_legalPhone")
    private String legalPhone;
    /**
     * 法人身份证号
     */
    @JSONField(name = "tripartiteSign_adminIdentNo")
    private String adminIdentNo;

    /**
     * 公司管理员姓名
     */
    @JSONField(name = "tripartiteSign_adminName")
    private String adminName;
    /**
     * 联系人手机号
     */
    @JSONField(name = "tripartiteSign_contactPhone")
    private String contactPhone;
    /**
     * 证件类别
     */
    @JSONField(name = "tripartiteSign_identType")
    private String identType;
    /**
     * 地址（精确到门牌号）
     */
    @JSONField(name = "tripartiteSign_registerAdd")
    private String registerAdd;
    /**
     * 邮编
     */
    @JSONField(name = "tripartiteSign_postalCode")
    private String postalCode;
    /**
     * 电话
     */
    @JSONField(name = "tripartiteSign_phone")
    private String phone;
    /**
     * 开户名
     */
    @JSONField(name = "tripartiteSign_bankAccountName")
    private String bankAccountName;
    /**
     * 开户行
     */
    @JSONField(name = "tripartiteSign_bankName")
    private String bankName;
    /**
     * 开户账号
     */
    @JSONField(name = "tripartiteSign_bankCardNo")
    private String bankCardNo;
    /**
     * 公司网址
     */
    @JSONField(name = "tripartiteSign_companyWebsite")
    private String companyWebsite;

    /**
     * 签章关键字，默认“乙方（签章）”
     */
    private String signKeyWord = "乙方（签章）";
}
