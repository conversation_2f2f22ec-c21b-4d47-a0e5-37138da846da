package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2025/6/4 19:00
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_anchored_relation")
public class AnchoredRelationEntity extends BaseEntity {

    /**
     * 申请单号
     */
    @Column(name = "apply_no")
    private String applyNo;

    /**
     * 商户编号
     */
    @Column(name = "merchant_no")
    private String merchantNo;

    /**
     * 挂靠商户编号
     */
    @Column(name = "anchored_merchant_no")
    private String anchoredMerchantNo;

    /**
     * 支付场景
     */
    @Column(name = "pay_scene")
    private String payScene;

    /**
     * 渠道类型
     */
    @Column(name = "channel_type")
    private String channelType;

    /**
     * 支持的支付方式
     */
    @Column(name = "support_pay_type")
    private long supportPayType;

    /**
     * 状态 启用 禁用
     */
    @Column(name = "status")
    private String status;
}
