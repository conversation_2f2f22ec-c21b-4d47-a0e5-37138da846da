package com.yeepay.g3.core.aggregation.config.external.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryResultEntity;
import com.yeepay.g3.core.aggregation.config.external.convert.DisposalConvert;
import com.yeepay.g3.core.aggregation.config.common.ExternalSystemEnum;
import com.yeepay.g3.core.aggregation.config.common.RemoteProxyFactory;
import com.yeepay.g3.core.aggregation.config.external.ChannelDisposalExternal;
import com.yeepay.g3.facade.aggregation.pay.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.trade.bankcooper.WechatPlatformDisposalFacade;
import com.yeepay.g3.facade.trade.bankcooper.param.WechatPlatformDisposalNotifyQueryParamDTO;
import com.yeepay.g3.facade.trade.bankcooper.result.WechatPlatformDisposalNotifyQueryResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/18 15:14
 */

@Slf4j
@Service
public class ChannelDisposalExternalImpl implements ChannelDisposalExternal {

    // 通道返回成功码
    private static final String BANK_SUCCESS_CODE = "0000";
    private static final String BANK_SUCCESS_EMPTY_CODE = "851054";
    private WechatPlatformDisposalFacade wechatPlatformDisposalFacade = RemoteProxyFactory.getService(WechatPlatformDisposalFacade.class, ExternalSystemEnum.BANKCHANNEL);

    @Override
    public DisposalQueryResultEntity pageQueryDisposalList(DisposalQueryEntity requestEntity) {
        WechatPlatformDisposalNotifyQueryResultDTO resultDTO;
        try {
            WechatPlatformDisposalNotifyQueryParamDTO paramDTO = DisposalConvert.buildWechatDisposalParamDTO(requestEntity);
            resultDTO = wechatPlatformDisposalFacade.wechatPlatformDisposalNotifyQuery(paramDTO);
            log.info("[调用通道处置通知查询] reqDTO={},respDTO={}", JsonUtils.toJSONString(paramDTO), JsonUtils.toJSONString(resultDTO));
        } catch (Exception e) {
            log.error("调用通道处置通知查询异常 reqDto={}", JsonUtils.toJSONString(requestEntity), e);
            throw new BaseException(ResultCode.DISPOSAL_NOTIFY_QUERY_FAIL.getCode(), ErrorCodeEnum.BANK_CHANNEL_WECHAT_DISPOSAL_EMPTY.getDesc() + "，请稍后重试");
        }

        //非0000【成功】和851054【查不到数据】
        if (Objects.nonNull(resultDTO)
            && !BANK_SUCCESS_CODE.equals(resultDTO.getBizCode())
            && !BANK_SUCCESS_EMPTY_CODE.equals(resultDTO.getBizCode())) {
            log.error("调用通道处置通知查询失败, 失败原因 code:{},msg:{}", resultDTO.getBizCode(), resultDTO.getBizMsg());
            throw new BaseException(ResultCode.DISPOSAL_NOTIFY_QUERY_FAIL.getCode(), resultDTO.getBizMsg());
        }
        return DisposalConvert.buildWechatDisposalResultDTO(resultDTO);
    }
}
