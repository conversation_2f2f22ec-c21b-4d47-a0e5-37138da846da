package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:55
 */
public interface EntryApplyHandler<T extends EntryApply<? extends ChannelSpecialParams>> extends ChannelEntryResultHandler<T> {

    void buildChannelNo(final T entryApply);

    void buildBackupInfo(final T entryApply);

    ChannelEntryResult entryApply(final EntryApplyContext<T> entryApplyContext);
    default boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        return false;
    }

    BaseEntryApply<? extends ChannelSpecialParams> buildEntryApply(final ChannelEntryOrderEntity orderEntity,
                                                                   final ChannelEntryApplyDetailEntity detailEntity);
}
