package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.LegalTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.ContactInfoDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 联系人信息
 *
 * <AUTHOR>
 * @since 2025/5/20:18:34
 * Company: 易宝支付(YeePay)
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class ContactInfo implements Serializable {

    private static final long serialVersionUID = -8665106686280556266L;
    /**
     * 联系人姓名
     */
    private final String contactName;

    /**
     * 联系人手机号
     */
    private final String contactMobileNo;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 联系人身份证号
     */
    private final String contactCertNo;

    /**
     * 联系人类型
     */
    private final LegalTypeEnum contactType;

    public static ContactInfo build(ContactInfoDTO contactInfoDTO) {
        Assert.notNull(contactInfoDTO, ResultCode.PARAM_VALID_ERROR, "contactInfoDTO can not be null!");

        return ContactInfo.builder()
                .contactName(contactInfoDTO.getContactName())
                .contactMobileNo(contactInfoDTO.getContactMobileNo())
                .contactEmail(contactInfoDTO.getContactEmail())
                .contactCertNo(contactInfoDTO.getContactCertNo())
                .contactType(DocumentedEnum.fromValue(LegalTypeEnum.class, contactInfoDTO.getContactType()))
                .build();
    }

//    /**
//     * 实名认证-微信校验
//     * // todo-LZX-报错文案
//     */
//    public void validateAuthWechat() {
//        // todo 联系人类型删掉了
//        /* if (LegalTypeEnum.SUPER.name().equals(contactType)) {
//            ValidationUtils.notNull(certType, "联系人类型为经办人时，联系人证件类型必填");
//            if (!IdentificationTypeEnum.getWechatIdentificationType().contains(certType)) {
//                throw new IllegalArgumentException("联系人证件类型不支持");
//            }
//        }*/
//        // 基础校验
//        this.validateAuthBase();
//    }
//
//    /**
//     * 实名认证-支付宝校验
//     * // todo-LZX-报错文案
//     */
//    public void validateAuthAliPay() {
//        /*ValidationUtils.notNull(certType, "支付宝: 联系人证件类型必填");
//        if (!IdentificationTypeEnum.getAliPayIdentificationType().contains(certType)) {
//            throw new IllegalArgumentException("联系人证件类型不支持");
//        }*/
//        // 基础校验
//        this.validateAuthBase();
//    }
//
//    /**
//     * 实名认证-基础校验
//     * // todo-LZX-报错文案
//     */
//    public void validateAuthBase() {
//        //// todo 联系人类型删掉了
//       /* if (!LegalTypeEnum.SUPER.name().equals(contactType)) {
//            return;
//        }
//        ValidationUtils.notNull(cardFrontImg, "联系人类型为经办人时，联系人证件正面照片必填");
//        ValidationUtils.notNull(cardBackImg, "联系人类型为经办人时，联系人证件反面照片必填");
//        ValidationUtils.notNull(effectTime, "联系人类型为经办人时，联系人证件有效期开始时间必填");
//        ValidationUtils.notNull(expireTime, "联系人类型为经办人时，联系人证件有效期结束时间必填");
//        ValidationUtils.notNull(businessAuthorizationLetter, "联系人类型为经办人时，业务办理授权函必填");*/
//
//    }

    @JsonIgnore
    public boolean getIsLegal() {

        // todo return LegalTypeEnum.LEGAL.name().equals(contactType);
        return true;
    }

    @JsonIgnore
    public boolean getIsIdCardCertType() {

        // todo return IdentificationTypeEnum.ID_CARD.equals(this.certType);
        return true;

    }
}

