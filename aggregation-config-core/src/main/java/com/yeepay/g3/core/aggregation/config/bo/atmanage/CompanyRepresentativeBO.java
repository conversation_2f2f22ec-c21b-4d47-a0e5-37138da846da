package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.IdentificationTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.LegalTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.CompanyRepresentativeDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * 描述: 企业代表（法人或经营者）信息 DTO
 *
 * <AUTHOR>
 * @since 2025/5/20
 * Company: 易宝支付(YeePay)
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class CompanyRepresentativeBO implements Serializable {

    private static final long serialVersionUID = 3687773618227567128L;
    /**
     * 证件持有人类型
     * 主体：政府机关、事业单位：可选填经办人
     */
    private final LegalTypeEnum legalType;

    /**
     * 证件类型
     */
    private final IdentificationTypeEnum cardType;

    /**
     * 证件姓名
     */
    private final String name;

    /**
     * 证件号码
     */
    private final String cardNo;

    /**
     * 证件生效期
     */
    private final String effectTime;

    /**
     * 证件失效效期
     */
    private final String expireTime;

    /**
     * 证件正面照片
     */
    private final String cardFrontImg;

    /**
     * 证件反面照片
     */
    private final String cardBackImg;

    /**
     * 证件居住地址
     */
    private final String certAddress;

    /**
     * 授权函照片
     */
    private String businessAuthorizationLetter;

    public static CompanyRepresentativeBO build(CompanyRepresentativeDTO companyRepresentativeDTO) {

        if (CheckUtils.isEmpty(companyRepresentativeDTO)) {
            return null;
        }
        IdentificationTypeEnum cardType = DocumentedEnum.fromValueOfNullable(IdentificationTypeEnum.class, companyRepresentativeDTO.getCardType());
        final LegalTypeEnum legalType = DocumentedEnum.fromValueOfNullable(LegalTypeEnum.class, companyRepresentativeDTO.getLegalType());

        return CompanyRepresentativeBO.builder()
                .legalType(legalType)
                .cardType(cardType)
                .name(companyRepresentativeDTO.getName())
                .cardNo(companyRepresentativeDTO.getCardNo())
                .effectTime(buildEffectTime(companyRepresentativeDTO.getEffectTime()))
                .expireTime(buildExpireTime(companyRepresentativeDTO.getExpireTime()))
                .cardFrontImg(companyRepresentativeDTO.getCardFrontImg())
                .cardBackImg(companyRepresentativeDTO.getCardBackImg())
                .certAddress(companyRepresentativeDTO.getCertAddress())
                .businessAuthorizationLetter(companyRepresentativeDTO.getBusinessAuthorizationLetter())
                .build();
    }

    public static String buildEffectTime(String effectTime) {
        if (StringUtils.isNotBlank(effectTime)) {
            boolean effectTimeIsValid = Const.DATE_PATTERN.matcher(effectTime).matches();
            Assert.isTrue(effectTimeIsValid, ResultCode.PARAM_VALID_ERROR, "证书有效期限开始日期格式错误");
            return effectTime;
        }
        return null;
    }

    public static String buildExpireTime(String expireTime) {
        if (StringUtils.isNotBlank(expireTime)) {
            boolean expireTimeIsValid = Const.DATE_PATTERN.matcher(expireTime).matches()
                    || Const.PERMANENT.equals(expireTime);
            Assert.isTrue(expireTimeIsValid, ResultCode.PARAM_VALID_ERROR, "证书结束日期格式错误");
            return expireTime;
        }
        return null;
    }

    public static CompanyRepresentativeBO buildByChannelNo(CompanyRepresentativeDTO companyRepresentativeDTO) {

        if (CheckUtils.isEmpty(companyRepresentativeDTO)) {
            return null;
        }
        IdentificationTypeEnum cardType = DocumentedEnum.fromValueOfNullable(IdentificationTypeEnum.class, companyRepresentativeDTO.getCardType());
        final LegalTypeEnum legalType = DocumentedEnum.fromValueOfNullable(LegalTypeEnum.class, companyRepresentativeDTO.getLegalType());
        return CompanyRepresentativeBO.builder()
                .legalType(legalType)
                .cardType(cardType)
                .name(companyRepresentativeDTO.getName())
                .cardNo(companyRepresentativeDTO.getCardNo())
                .effectTime(companyRepresentativeDTO.getEffectTime())
                .expireTime(companyRepresentativeDTO.getExpireTime())
                .cardFrontImg(companyRepresentativeDTO.getCardFrontImg())
                .cardBackImg(companyRepresentativeDTO.getCardBackImg())
                .certAddress(companyRepresentativeDTO.getCertAddress())
                .businessAuthorizationLetter(companyRepresentativeDTO.getBusinessAuthorizationLetter())
                .build();
    }

//    /**
//     * 实名认证-微信校验
//     */
//    public void validateAuthWechat() {
//        // todo-LZX-基础校验
//        if (!IdentificationTypeEnum.getWechatIdentificationType().contains(cardType)) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "证件类型不支持");
//        }
//        this.validateAuthBase();
//    }
//
//    /**
//     * 实名认证-支付宝校验
//     */
//    public void validateAuthAliPay() {
//        // todo-LZX-基础校验
//        if (!IdentificationTypeEnum.getAliPayIdentificationType().contains(cardType)) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "证件类型不支持");
//        }
//        this.validateAuthBase();
//    }
//
//    /**
//     * 实名认证-基础校验
//     * // todo-LZX-报错文案
//     */
//    public void validateAuthBase() {
//        if (CheckUtils.isEmpty(legalType) || LegalTypeEnum.LEGAL.equals(legalType)) {
//            ValidationUtils.notNull(cardFrontImg, "法人证件正面照片不能为空");
//            ValidationUtils.notNull(cardBackImg, "法人证件反面照片不能为空");
//        } else {
//            ValidationUtils.notNull(businessAuthorizationLetter, "经办人授权函照片不能为空");
//        }
//    }

    @JsonIgnore
    public boolean getIsIdCardType() {
        return IdentificationTypeEnum.ID_CARD.equals(cardType);
    }

}
