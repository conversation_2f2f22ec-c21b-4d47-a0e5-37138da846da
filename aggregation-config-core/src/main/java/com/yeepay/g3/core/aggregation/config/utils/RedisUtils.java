package com.yeepay.g3.core.aggregation.config.utils;

import com.yeepay.g3.core.aggregation.config.mq.constant.MqConstant;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.utils.lock.utils.RedisCall;
import com.yeepay.utils.lock.utils.RedisClientUtils;
import redis.clients.jedis.Jedis;

import java.util.Arrays;
import java.util.Random;

public class RedisUtils {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtils.class);

    public static final String SET_SUCCESS = "OK";


    private static final Random RANDOM = new Random();

    // 默认最小和最大延迟时间
    private static final int DEFAULT_MIN_DELAY_MS = 300;
    private static final int DEFAULT_MAX_DELAY_MS = 500;

    /**
     * 尝试获取分布式锁，支持重试机制，使用默认的延迟时间（300-500毫秒）。
     *
     * @param key         锁的键后缀（会拼接到 MqConstant.MERCHANT_BIND_SYNC_LOCK 前缀后）
     * @param lockTimeout 锁的超时时间（秒）
     * @param maxRetries  最大重试次数
     * @return true 表示成功获取锁；false 表示最终未能获取锁
     */
    public static boolean tryLockWithRetries(
            String key,
            int lockTimeout,
            int maxRetries
    ) throws InterruptedException {
        return tryLockWithRetries(key, lockTimeout, maxRetries, DEFAULT_MIN_DELAY_MS, DEFAULT_MAX_DELAY_MS);
    }

    /**
     * 尝试获取分布式锁，支持重试机制，延迟时间可配置。
     *
     * @param key         锁的键后缀（会拼接到 MqConstant.MERCHANT_BIND_SYNC_LOCK 前缀后）
     * @param lockTimeout 锁的超时时间（秒）
     * @param maxRetries  最大重试次数
     * @param minDelayMs  每次重试的最小等待时间（毫秒）
     * @param maxDelayMs  每次重试的最大等待时间（毫秒）
     * @return true 表示成功获取锁；false 表示最终未能获取锁
     */
    public static boolean tryLockWithRetries(
            String key,
            int lockTimeout,
            int maxRetries,
            int minDelayMs,
            int maxDelayMs
    ) throws InterruptedException {
        int retryCount = 0;
        // 拼接完整的锁键
        String lockKey = MqConstant.MERCHANT_BIND_SYNC_LOCK + key;

        logger.info("Attempting to acquire lock. LockKey: {}, LockTimeout: {}, MaxRetries: {}", lockKey, lockTimeout, maxRetries);

        while (retryCount <= maxRetries) {
            // 尝试获取锁，key 同时作为锁的键和值
            boolean lockAcquired = RedisClientUtils.call(jedis -> {
                try {
                    String response = jedis.set(lockKey, key, "NX", "EX", lockTimeout);
                    logger.info("Redis SET command response: {}, LockKey: {}", response, lockKey);
                    return "OK".equals(response);
                } catch (Throwable e) {
                    logger.error("Error while setting Redis lock. LockKey: {}", lockKey, e);
                    return false;
                }
            });

            if (lockAcquired) {
                logger.info("Successfully acquired lock. LockKey: {}", lockKey);
                return true; // 成功获取锁
            }

            retryCount++;
            logger.warn("Failed to acquire lock. LockKey: {}, RetryCount: {}/{}", lockKey, retryCount, maxRetries);

            if (retryCount > maxRetries) {
                logger.error("Max retries reached. Failed to acquire lock. LockKey: {}", lockKey);
                break; // 达到最大重试次数，退出
            }

            // 随机等待 minDelayMs 到 maxDelayMs 毫秒
            int waitTime = minDelayMs + RANDOM.nextInt(maxDelayMs - minDelayMs + 1);
            logger.info("Waiting for {}ms before next retry. LockKey: {}", waitTime, lockKey);
            Thread.sleep(waitTime);
        }

        logger.error("Failed to acquire lock after all retries. LockKey: {}", lockKey);
        return false; // 最终未能获取锁
    }

    public static void lock(final String key, final String value, final int expireTime) {
        boolean lock = RedisClientUtils.call(new RedisCall<Boolean>() {
            @Override
            public Boolean run(Jedis jedis) {
                try {
                    String response = jedis.set(key, value, "NX", "EX", expireTime);
                    logger.info("set redis state:{}, key:{}", response, key);
                    return SET_SUCCESS.equals(response);
                } catch (Throwable e) {
                    logger.error("redis set error,key:" + key, e);
                    return false;
                }
            }
        });
        if (!lock) {
            logger.info("通过key={}获取redis锁失败,请求重复", key);
            throw new RuntimeException("获取分布式锁失败,key"+key);
        }
    }

    public static void unlock(final String key, final String value) {
        RedisClientUtils.call(new RedisCall<Boolean>() {
            @Override
            public Boolean run(Jedis jedis) {
                jedis.eval("if redis.call('get',KEYS[1]) == ARGV[1] then \n return redis.call('del',KEYS[1]) \n else return 0 \n end", Arrays.asList(key), Arrays.asList(value));
                logger.info("unlock key :{}",key);
                return true;
            }
        });
    }
}