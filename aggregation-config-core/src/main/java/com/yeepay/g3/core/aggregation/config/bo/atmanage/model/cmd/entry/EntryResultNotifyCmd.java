package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 进件结果通知cmd
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
public class EntryResultNotifyCmd {

    /**
     * 通知业务类型
     * ENTRY：进件结果通知
     * TERMINAL_REPORT：终端报备结果通知
     * SPLIT_CREATE：分账方结果通知
     */
    private EntryNotifyBizTypeEnum notifyBizType;

    /**
     * 业务方编码
     */
    private BizSceneEnum bizScene;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 进件订单号
     */
    private String orderNo;

    /**
     * 进件单笔处理申请号
     */
    private String applyNo;

    /**
     * 商编
     */
    private String merchantNo;

    private EntryTypeEnum entryType;

    private PaySceneEnum payScene;

    private PayChannelEnum payChannel;

    private ActivityTypeEnum activityType;

    /**
     * 费率类型
     */
    private String feeType;

    /**
     * 通道返回的活动类型
     */
    private String channelActivityType;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 挂靠信息 同主体挂靠可能会有多个
     */
    private List<EntryAnchoredInfoNotifyCmd> anchoredInfoList;

    /**
     * 进件信息 分主+多备进件可能会有多个
     */
    private List<EntryDetailNotifyCmd> entryInfoList;

    /**
     * 辅助终端报备状态
     */
    private String terminalReportStatus;

    /**
     * 分账方创建状态
     */
    private String splitCreateStatus;

}
