package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/8 14:15
 */
@Getter
@Setter(AccessLevel.PROTECTED)
@SuperBuilder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class WechatB2BEntrySpecialParams implements ChannelSpecialParams {

    private final SuperAdminInfo superAdminInfo;
    /**
     * 结算账户信息
     */
    private final SettleAccountInfo settleAccountInfo;
    /**
     * 主营商品类型。
     * 通道侧枚举【01：食品 02：饮料 03：乳制品 04：酒水 05：生鲜果蔬 06：调味品 07：日化个护 08：文具 09：鞋服/配饰 10：家居
     * 11：母婴/玩具   12：数码3C 99：其他】
     */
    private final List<String> goodsTypes;
    /**
     * 主要线下销售渠道。
     * 可选项："杂货店", "便利店", "超市", "餐饮店", "母婴店", "烟酒店", "其他"
     * 通道侧枚举：
     * 【A：杂货店
     * B：便利店
     * C：超市
     * D：餐饮店
     * E：母婴店
     * F：烟酒店
     * Z：其他】
     */
    private final List<String> goodsSales;

    /**
     * 商户规模 微信小程序B2B必填
     * LARGE: 大型企业 2000 人以上
     * MIDDLE: 中型企业 150 至 2000 人
     * SMALL: 小型企业 15 至 150 人
     * TINY: 微型企业 15 人以下
     */
    private final String merchantScale;

    /**
     * 门店覆盖数。
     * 可选项："0-5千", "5千-1万", "1万-10万", "10万-50万", "50万以上"
     * 通道侧枚举【N1：0-5千 N2：5千-1万 N3：1万-10万  N4：10万-50万 N5：50万以上】
     */
    private final String coverNum;

    /**
     * 所需服务类型。
     * 可选项："门店订货", "门店促销", "门店活动执行", "门店直播", "其他"
     * 通道侧枚举【S1：门店订货 S2：门店促销 S3：门店活动执行 S4：门店直播 S5：其他】
     */
    private final List<String> services;

    private final String storeEntrancePic;

    /**
     * 小程序信息
     */
    private final MiniProgramInfo miniProgramInfo;

    private final ActivityInfo activityInfo;

    private final String extendInfo;

    public static WechatB2BEntrySpecialParams build(final WxDirectB2BConfigCmd cmd) {
        return WechatB2BEntrySpecialParams.builder()
                .superAdminInfo(cmd.getSuperAdminInfo())
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .goodsTypes(cmd.getGoodsTypes())
                .goodsSales(cmd.getGoodsSales())
                .merchantScale(cmd.getMerchantScale())
                .coverNum(cmd.getCoverNum())
                .services(cmd.getServices())
                .storeEntrancePic(cmd.getStoreEntrancePic())
                .miniProgramInfo(cmd.getMiniProgramInfo())
                .extendInfo(cmd.getExtendInfo())
                .activityInfo(cmd.getActivityInfo())
                .build();

    }
}
