package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.UboInfoBO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.WxDirectConf;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 微信直连
 * <AUTHOR>
 * @date 2025/5/27 21:37
 */
@Setter(AccessLevel.PRIVATE)
@Builder
@Getter
public class WxDirectConfigCmd {

    private final PaySceneEnum payScene;

    private final PayChannelEnum payChannel;

    /**
     * 经营者/法人是否为受益人
     */
    private final Boolean isBenefitPerson;

    /**
     * 受益人列表
     */
    private final List<UboInfoBO> uboInfos;


    private final SettleAccountInfo settleAccountInfo;

    /**
     * 超级管理员信息
     */
    private final SuperAdminInfo superAdminInfo;

    /**
     * 经营资料
     */
    private final SalesScene salesScene;

    private final ActivityInfo activityInfo;

    /**
     * 补充说明信息
     * 若主体为“个人卖家”，该字段必传，则需填写描述“ 该商户已持续从事电子商务经营活动满6个月，且期间经营收入累计超过20万元。
     */
    private final String businessAdditionDesc;
    /**
     *  补充材料
     *  根据实际审核情况，额外要求提供。最多可上传15张照片
     */
    private final String businessAdditionPic;

    /**
     * 进件类型
     */
    private final EntryTypeEnum entryType;

    /**
     * 费用类型
     */
    private final String chargeType;

    /**
     * 扩展信息
     */

    private final String extendInfo;

    /**
     * 补充说明
     */
    private final AdditionInfo additionInfo;


    @Setter(AccessLevel.PRIVATE)
    @Builder
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor(force = true)
    public static class AdditionInfo implements Serializable {

        /**
         * 补充说明信息
         * 若主体为“个人卖家”，该字段必传，则需填写描述“ 该商户已持续从事电子商务经营活动满6个月，且期间经营收入累计超过20万元。
         */
        private final String businessAdditionDesc;
        /**
         * 补充材料
         * 根据实际审核情况，额外要求提供。最多可上传15张照片
         */
        private final List<String> businessAdditionPics;

        /**
         * 请上传法定代表人或负责人亲笔签署的开户承诺函扫描件（下载模板）。亲笔签名承诺函内容清晰可见，不得有涂污，破损，字迹不清晰现象
         */
        private final String legalPersonCommitment;

        /**
         * 1、建议法人按如下话术录制“法人开户意愿视频”：
         * 我是#公司全称#的法定代表人（或负责人），特此证明本公司申请的商户号为我司真实意愿开立且用于XX业务（或XX服务）。我司现有业务符合法律法规及腾讯的相关规定。
         * 2、支持上传20M内的视频，格式可为avi、wmv、mpeg、mp4、mov、mkv、flv、f4v、m4v、rmvb；
         */
        private final String legalPersonVideo;

        public static AdditionInfo build(WxDirectConf.AdditionInfo additionInfo) {
            if (null == additionInfo) return null;
            return AdditionInfo.builder()
                    .businessAdditionDesc(additionInfo.getBusinessAdditionDesc())
                    .businessAdditionPics(additionInfo.getBusinessAdditionPics())
                    .legalPersonCommitment(additionInfo.getLegalPersonCommitment())
                    .legalPersonVideo(additionInfo.getLegalPersonVideo())
                    .build();
        }
    }
    public static WxDirectConfigCmd build(WxDirectConf wxDirectConf) {
        Assert.isFalse(!wxDirectConf.getIsBenefitPerson() && CollectionUtils.isEmpty(wxDirectConf.getUboInfos())
                , ResultCode.PARAM_VALID_ERROR, "微信直连受益人非法人时 受益人列表不能为空");
        return WxDirectConfigCmd.builder()
                .payScene(PaySceneEnum.DIRECT)
                .entryType(EntryTypeEnum.ENTRY)
                .payChannel(PayChannelEnum.WECHAT)
                .isBenefitPerson(wxDirectConf.getIsBenefitPerson())
                .uboInfos(UboInfoBO.buildUboInfos(wxDirectConf.getUboInfos()))
                .settleAccountInfo(SettleAccountInfo.build(wxDirectConf.getSettleAccountInfo()))
                .superAdminInfo(SuperAdminInfo.build(wxDirectConf.getSuperAdminInfo()))
                .additionInfo(AdditionInfo.build(wxDirectConf.getAdditionInfo()))
                .salesScene(SalesScene.build(wxDirectConf.getSalesScene()))
                .activityInfo(ActivityInfo.build(wxDirectConf.getActivityInfo()))
                .chargeType(wxDirectConf.getChargeType())
                .extendInfo(wxDirectConf.getExtendInfo())
                .build();
    }


}
