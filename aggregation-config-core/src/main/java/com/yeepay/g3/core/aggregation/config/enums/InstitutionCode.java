package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/11 14:00
 */
@Getter
@AllArgsConstructor
public enum InstitutionCode implements DocumentedEnum<String> {
    WECHAT("WECHAT", "微信"),
    ALI("ALI", "支付宝"),

    ;

    private final String document;
    private final String desc;
}
