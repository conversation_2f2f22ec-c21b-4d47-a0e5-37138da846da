package com.yeepay.g3.core.aggregation.config.bo.atmanage.auth;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 描述: 辅助证明材料 DTO
 * <AUTHOR>
 * @since 2025/5/20
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@Builder
public class AssistProveBO implements Serializable {

    private static final long serialVersionUID = -5414635792828442945L;

    /**
     * 小微经营类型
     */
    private String microBizType;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店省编码
     */
    private String provinceCode;

    /**
     * 门店省
     */
    private String province;

    /**
     * 门店市编码
     */
    private String cityCode;

    /**
     * 门店市
     */
    private String city;

    /**
     * 门店区编码
     */
    private String districtCode;

    /**
     * 门店区
     */
    private String district;

    /**
     * 门店地址
     */
    private String storeAddress;

    /**
     * 门店门头照片
     */
    private String storeHeaderCopy;

    /**
     * 店内环境照片
     */
    private String storeIndoorCopy;
//
//    public static AssistProveBO build(AssistProveDTO assistProveDTO) {
//        if (assistProveDTO == null) {
//            return null;
//        }
//        return AssistProveBO.builder()
//                .microBizType(assistProveDTO.getMicroBizType())
//                .storeName(assistProveDTO.getStoreName())
//                .provinceCode(assistProveDTO.getProvinceCode())
//                .province(assistProveDTO.getProvince())
//                .cityCode(assistProveDTO.getCityCode())
//                .city(assistProveDTO.getCity())
//                .districtCode(assistProveDTO.getDistrictCode())
//                .district(assistProveDTO.getDistrict())
//                .storeAddress(assistProveDTO.getStoreAddress())
//                .storeHeaderCopy(assistProveDTO.getStoreHeaderCopy())
//                .storeIndoorCopy(assistProveDTO.getStoreIndoorCopy())
//                .build();
//    }

}
