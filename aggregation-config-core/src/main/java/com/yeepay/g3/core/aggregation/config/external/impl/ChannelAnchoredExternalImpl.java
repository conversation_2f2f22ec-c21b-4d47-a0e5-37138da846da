package com.yeepay.g3.core.aggregation.config.external.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.ChannelAnchoredExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.AggDesignateMerchantAnchoredReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.AggSameSubjectAnchoredReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.ChannelAnchoredRecordBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.AggSameReportRespBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.aggregation.pay.dto.AttachReportRequestDTO;
import com.yeepay.g3.facade.aggregation.pay.dto.AttachReportResponseDTO;
import com.yeepay.g3.facade.aggregation.pay.dto.AttachSameReportRequestDTO;
import com.yeepay.g3.facade.aggregation.pay.dto.AttachSameReportResponseDTO;
import com.yeepay.g3.facade.aggregation.pay.facade.AttachReportFacade;
import com.yeepay.g3.facade.aggregation.pay.facade.ReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.OpenPayAsyncReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRelationRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRelationResponseDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 请求通道挂靠服务
 *
 * @author: Mr.yin
 * @date: 2025/6/5  15:40
 */
@Service
public class ChannelAnchoredExternalImpl implements ChannelAnchoredExternal {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 通道挂靠服务服务
     */
    private OpenPayAsyncReportFacade openPayAsyncReportFacade = RemoteServiceFactory.getService(OpenPayAsyncReportFacade.class);
    public static final String CHANNEL_SUCCESS = "0000";


    /**
     * 聚合指定商编挂靠
     */
    private AttachReportFacade attachReportFacade = RemoteServiceFactory.getService(AttachReportFacade.class);

    /**
     * 聚合同主体报备服务
     */
    private ReportFacade reportFacade = RemoteServiceFactory.getService(ReportFacade.class);

    @Override
    public RemoteResult<String> aggregationDesignateMerchantAnchored(AggDesignateMerchantAnchoredReqBO reqBO) {
        AttachReportRequestDTO requestDTO = new AttachReportRequestDTO();
        requestDTO.setBizSystemNo(Const.DEFAULT_SYSTEM_NAME);/*此处聚合有定制*/
        requestDTO.setApplicant(reqBO.getMerchantNo());
        //逗号分割的String
        requestDTO.setMerchantNoList(reqBO.getMerchantNo());
        requestDTO.setMainMerchantNo(reqBO.getAnchoredMerchantNo());
        requestDTO.setGroupName(reqBO.getMerchantNo());/*这玩意底层逻辑走不到*/
        requestDTO.setRequestReasonType("FRANCHISE_RELATIONSHIP");
        long startTimestamp = System.currentTimeMillis();
        AttachReportResponseDTO attachReportResponseDTO = null;
        try {
            logger.info("[聚合指定商编挂靠] 调用开始，请求参数: {}", JsonUtils.toJSONString(requestDTO));
            attachReportResponseDTO = attachReportFacade.attachReport(requestDTO);
            logger.info("[聚合指定商编挂靠] 调用完成, 响应结果: {}, 耗时: {} ms", JsonUtils.toJSONString(attachReportResponseDTO), System.currentTimeMillis() - startTimestamp);
        } catch (Exception e) {
            logger.error(String.format("[聚合指定商编挂靠] 调用异常, 请求参数: {%s}, 异常信息:  ", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (CheckUtils.isEmpty(attachReportResponseDTO) || CheckUtils.isEmpty(attachReportResponseDTO.getCode())) {
            logger.error("[聚合指定商编挂靠] 异常 返回结果为空 requestDTO={}", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (!"00000".equals(attachReportResponseDTO.getCode()) || CheckUtils.isEmpty(attachReportResponseDTO.getCompleteSuccess())
                || !"SUCCESS".equals(attachReportResponseDTO.getCompleteSuccess())) {
            logger.warn("[聚合指定商编挂靠] 异常 结果处理失败 response={},reqBO={}", JsonUtils.toJSONString(attachReportResponseDTO), JsonUtils.toJSONString(reqBO));
            return RemoteResult.fail(attachReportResponseDTO.getCode(), attachReportResponseDTO.getMessage());
        }

        return RemoteResult.success(attachReportResponseDTO.getCode(), attachReportResponseDTO.getMessage());
    }

    @Override
    public RemoteResult<List<AggSameReportRespBO>> aggSameSubjectAnchored(AggSameSubjectAnchoredReqBO reqBO) {
        logger.info("[聚合同商编挂靠]，准备请求reqBO={}", JSON.toJSONString(reqBO));
        AttachSameReportRequestDTO requestDTO = new AttachSameReportRequestDTO();
        requestDTO.setMerchantNo(reqBO.getMerchantNo());
        requestDTO.setChannel(reqBO.getChannelType());
        requestDTO.setScene(reqBO.getPayScene());
        requestDTO.setBizSystemNo(Const.DEFAULT_SYSTEM_NAME);
        if (!ActivityTypeEnum.NORMAL.name().equals(reqBO.getActivityType())) {
            requestDTO.setPromotionType(reqBO.getActivityType());
        }
        requestDTO.setSameScopeMerchantNo(reqBO.getSameSubjectSameScopeMerchantNo());
        AttachSameReportResponseDTO responseDTO = null;
        try {
            logger.info("[remote][请求聚合同主体挂靠]    requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = reportFacade.attachSameReport(requestDTO);
            logger.info("[remote][请求聚合同主体挂靠]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[聚合同商编挂靠] 调用异常, 请求参数: {%s}, 异常信息:  ", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (CheckUtils.isEmpty(responseDTO) || CheckUtils.isEmpty(responseDTO.getCode())) {
            logger.error("[聚合同商编挂靠] 异常 返回结果为空 requestDTO={}", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (!"00000".equals(responseDTO.getCode())) {
            logger.warn("[聚合指定商编挂靠] 异常 结果处理失败 response={},reqBO={}", JsonUtils.toJSONString(responseDTO), JsonUtils.toJSONString(reqBO));
            return RemoteResult.fail(responseDTO.getCode(), responseDTO.getMessage());
        }
        if (CollectionUtils.isEmpty(responseDTO.getAttachReportRecordList())) {
            return RemoteResult.fail(ResultCode.NOT_SUPPORT_ANCHORED_MERCHANT, "无可用的同主体挂靠商编", responseDTO.getCode(), responseDTO.getMessage());
        }
        // 同主体挂靠列表
        List<AggSameReportRespBO> aggSameReportRespBOList = responseDTO.getAttachReportRecordList().stream().map(attachRecord ->
                AggSameReportRespBO.covert(attachRecord)
        ).collect(Collectors.toList());
        return RemoteResult.success(aggSameReportRespBOList, responseDTO.getCode(), responseDTO.getMessage());
    }

    @Override
    public RemoteResult<List<ChannelAnchoredRecordBO>> channelAnchored(AnchoredApply anchoredApply, AggAnchoredMerchantInfo aggAnchoredMerchantInfo) {
        OpenPayAsyncReportRelationRequestDTO requestDTO = null;
        try {
            SceneAndActivityTypeBO sceneAndActivityTypeBO = SceneAndActivityUtils.getSceneAndPromotionTypeBO(anchoredApply.getChannelType(), anchoredApply.getPayScene(), anchoredApply.getActivityType());
            requestDTO = buildChannelAnchoredRequest(anchoredApply, aggAnchoredMerchantInfo, sceneAndActivityTypeBO);
        } catch (BaseException e) {
            logger.error("[remote][请求通道挂靠]  异常 获取场景和活动类型异常 anchoredApply={}", JsonUtils.toJSONString(anchoredApply));
            return RemoteResult.fail(e.getResponseEnum().getCode(), e.getMessage());
        }
        OpenPayAsyncReportRelationResponseDTO responseDTO = null;
        try {
            logger.info("[remote][请求通道挂靠]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = openPayAsyncReportFacade.createOpenPayReportRelation(requestDTO);
            logger.info("[remote][请求通道挂靠]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[remote][请求通道挂靠] 调用异常 requestDTO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (null == responseDTO) {
            logger.error("[remote][请求通道挂靠] 调用异常返回为null requestDTO={%s})", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (!CHANNEL_SUCCESS.equals(responseDTO.getBizCode()) && !"800008".equals(responseDTO.getBizCode())) {
            /*0000：挂靠成功，800008：已挂靠成功，重复挂靠*/
            logger.warn("[remote][请求通道挂靠] 通道处理失败 requestDTO={}", JsonUtils.toJSONString(requestDTO));
            return RemoteResult.fail(responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        if ("800008".equals(responseDTO.getBizCode())) {/*正常是要接一次查询的，现在先这么样吧*/
            ChannelAnchoredRecordBO channelAnchoredRecordBO = new ChannelAnchoredRecordBO();
            channelAnchoredRecordBO.setMerchantNo(aggAnchoredMerchantInfo.getAnchoredMerchantNo());
            channelAnchoredRecordBO.setAnchoredMerchantNo(aggAnchoredMerchantInfo.getAnchoredMerchantNo());
            channelAnchoredRecordBO.setAnchoredMerchantChannelNo(aggAnchoredMerchantInfo.getAnchoredChannelNo());
            channelAnchoredRecordBO.setFeeType(requestDTO.getMaskReportFee());
            channelAnchoredRecordBO.setSuccessTime(new Date());
            logger.info("[remote][请求通道挂靠] 挂靠成功 返回的挂靠  channelAnchoredRecordBO={}", JsonUtils.toJSONString(channelAnchoredRecordBO));
            return RemoteResult.success(Lists.newArrayList(channelAnchoredRecordBO), responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        if (CollectionUtils.isEmpty(responseDTO.getRelationRecordDTOList())) {
            logger.warn("[remote][请求通道挂靠] 通道处理失败 无可挂靠的费率 requestDTO={}", JsonUtils.toJSONString(requestDTO));
            return RemoteResult.fail(ResultCode.NOT_SUPPORT_ANCHORED_MERCHANT, "无可挂靠的费率", responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        List<ChannelAnchoredRecordBO> anchoredList = responseDTO.getRelationRecordDTOList().stream()
                .filter(e -> e.getOpen())
                .map(e -> ChannelAnchoredRecordBO.covert(e)).collect(Collectors.toList());
        logger.info("[remote][请求通道挂靠] 挂靠成功 返回的挂靠list anchoredList={}", JsonUtils.toJSONString(anchoredList));
        return RemoteResult.success(anchoredList, responseDTO.getBizCode(), responseDTO.getBizMsg());
    }

    /**
     * 组装请求通道挂靠参数
     */
    public OpenPayAsyncReportRelationRequestDTO buildChannelAnchoredRequest(AnchoredApply anchoredApply, AggAnchoredMerchantInfo aggAnchoredMerchantInfo, SceneAndActivityTypeBO sceneAndActivityTypeBO) {
        OpenPayAsyncReportRelationRequestDTO requestDTO = new OpenPayAsyncReportRelationRequestDTO();
        requestDTO.setPromotionType(sceneAndActivityTypeBO.getChannelActivityType());
        requestDTO.setMaskReportFee(sceneAndActivityTypeBO.getChannelFeeType());

        requestDTO.setRequireMerchantNo(anchoredApply.getMerchantNo());
        requestDTO.setMaskMerchantNo(aggAnchoredMerchantInfo.getAnchoredMerchantNo());
        requestDTO.setOperator(aggAnchoredMerchantInfo.getRequestNo());
        return requestDTO;
    }
}
