package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredDetailCmd;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.factory.anchored.AnchoredTypeHandlerRouter;
import com.yeepay.g3.core.aggregation.config.utils.SpringContextUtils;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import com.yeepay.g3.utils.common.StringUtils;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 22:57
 */
@SuperBuilder
@Getter
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class AnchoredApply {

    /**
     * 业务场景
     */
    private BizSceneEnum bizScene;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 挂靠请求的顶级商编，目前是为了查询同主体指定跨SAAS配置
     */
    private final String topMerchantNo;

    /**
     * 挂靠请求的父级商编，目前是为了查询同主体指定跨SAAS配置
     */
    private final String parentMerchantNo;

    /**
     * 挂靠申请的订单号
     */
    private final String orderNo;

    /**
     * 挂靠申请的唯一申请号
     */
    private final String applyNo;

    /**
     * 商编
     */
    private final String merchantNo;

    /**
     * 支付场景
     */
    private final PaySceneEnum payScene;

    /**
     * 活动类型
     */
    private final ActivityTypeEnum activityType;

    /**
     * 支付渠道
     */
    private final PayChannelEnum channelType;
    /**
     * 挂靠类型
     */
    private final AnchoredType anchoredType;

    /**
     * 挂靠维度 是否需要跨sass体系
     */
    private Boolean anchoredDimension = Boolean.FALSE;

    /**
     * 挂靠商户编号传参
     * 同主体跨SAAS范围商编
     */
    private String relationMerchantNo;

    /**
     * 授权函文件名称
     */
    private final String authFileName;

    /**
     * 授权函文件地址
     */
    private final String authFileUrl;

    /**
     * 集团名称
     */
    private final String groupName;

    /**
     * 支持的支付类型 默认全部支持
     */
    private Long supportPayType;

    /**
     * 挂靠状态
     */
    private AnchoredStatus anchoredStatus;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 是否走单笔同步返回结果链路
     */
    private Boolean singSync = Boolean.FALSE;

    /**
     * 聚合挂靠成功商编信息
     */
    private List<AggAnchoredMerchantInfo> aggAnchoredMerchantInfoList;

    public void verifyAnchoredApplyParam() {
        AnchoredTypeHandlerRouter anchoredTypeHandlerRouter = SpringContextUtils.getBean("anchoredTypeHandlerRouter", AnchoredTypeHandlerRouter.class);
        anchoredTypeHandlerRouter.getHandler(this.getAnchoredType(), this.getAnchoredDimension())
                .validateAnchoredParams(this);
    }

    /**
     * 正向请求创建出挂靠申请
     */
    public static AnchoredApply covertAnchoredApply(AnchoredApplyEntity anchoredApplyEntity) {
        List<AggAnchoredMerchantInfo> aggAnchoredMerchantInfoList = StringUtils.isBlank(anchoredApplyEntity.getAggAnchoredMerchantText()) ? Lists.newArrayList() :
                JsonUtils.fromJson(anchoredApplyEntity.getAggAnchoredMerchantText(), new TypeReference<List<AggAnchoredMerchantInfo>>() {
                });

        return AnchoredApply.builder()
                .bizScene(DocumentedEnum.fromValueOfNullable(BizSceneEnum.class, anchoredApplyEntity.getBizScene()))
                .bizApplyNo(anchoredApplyEntity.getBizApplyNo())
                .orderNo(anchoredApplyEntity.getOrderNo())
                .applyNo(anchoredApplyEntity.getApplyNo())
                .topMerchantNo(anchoredApplyEntity.getTopMerchantNo())
                .parentMerchantNo(anchoredApplyEntity.getParentMerchantNo())
                .merchantNo(anchoredApplyEntity.getMerchantNo())
                .payScene(DocumentedEnum.fromValueOfNullable(PaySceneEnum.class, anchoredApplyEntity.getPayScene()))
                .channelType(DocumentedEnum.fromValueOfNullable(PayChannelEnum.class, anchoredApplyEntity.getPayChannel()))
                .activityType(DocumentedEnum.fromValueOfNullable(ActivityTypeEnum.class, anchoredApplyEntity.getActivityType()))
                .anchoredType(DocumentedEnum.fromValueOfNullable(AnchoredType.class, anchoredApplyEntity.getAnchoredType()))
                .anchoredDimension(Boolean.valueOf(anchoredApplyEntity.getAnchoredDimension()))
                .relationMerchantNo(anchoredApplyEntity.getRelationMerchantNo())
                .authFileName(anchoredApplyEntity.getAuthFileName())
                .authFileUrl(anchoredApplyEntity.getAuthFileUrl())
                .groupName(anchoredApplyEntity.getGroupName())
                .supportPayType(anchoredApplyEntity.getSupportPayType())
                .anchoredStatus(DocumentedEnum.fromValueOfNullable(AnchoredStatus.class, anchoredApplyEntity.getAnchoredStatus()))
                .finishTime(anchoredApplyEntity.getFinishTime())
                .failReason(anchoredApplyEntity.getFailReason())
                .aggAnchoredMerchantInfoList(aggAnchoredMerchantInfoList)
                .singSync(Boolean.FALSE)
                .build();
    }

    /**
     * 正向请求创建出挂靠申请
     */
    public static AnchoredApply createAnchoredApply(String orderNo, AnchoredCmd anchoredCmd, AnchoredDetailCmd anchoredDetailCmd, String bizApplyNo) {
        String applyNo = UniqueNoGenerateUtils.getUUIDUniqueNo();
        return AnchoredApply.builder()
                .bizScene(anchoredCmd.getBizScene())
                .bizApplyNo(bizApplyNo)
                .orderNo(orderNo)
                .applyNo(applyNo)
                .topMerchantNo(anchoredCmd.getTopLevelMerchantNo())
                .parentMerchantNo(anchoredCmd.getParentMerchantNo())
                .merchantNo(anchoredCmd.getMerchantNo())
                .channelType(anchoredDetailCmd.getPayChannel())
                .payScene(anchoredDetailCmd.getPayScene())
                .activityType(anchoredDetailCmd.getActivityType())
                .anchoredType(anchoredDetailCmd.getAnchoredType())
                .anchoredDimension(null == anchoredDetailCmd.getAnchoredDimension() ? Boolean.FALSE : anchoredDetailCmd.getAnchoredDimension())
                .relationMerchantNo(anchoredDetailCmd.getRelationMerchantNo())
//                .authFileName(anchoredCmd.getAuthFileName())
//                .authFileUrl(anchoredCmd.getAuthFileUrl())
                .groupName(anchoredCmd.getMerchantNo())
//                .supportPayType(anchoredDetailCmd.getSupportPayType())
                .anchoredStatus(AnchoredStatus.INIT)
//                        .finishTime()
//                        .failReason()
                .build();
    }

    /**
     * 正常接口 正向请求创建出挂靠申请
     */
    public static AnchoredApply createAnchoredApply(String orderNo, AnchoredCmd anchoredCmd, AnchoredDetailCmd anchoredDetailCmd) {
        String applyNo = UniqueNoGenerateUtils.getUUIDUniqueNo();
        return AnchoredApply.builder()
                .bizScene(anchoredCmd.getBizScene())
                .bizApplyNo(anchoredCmd.getBizApplyNo())
                .orderNo(orderNo)
                .applyNo(applyNo)
                .topMerchantNo(anchoredCmd.getTopLevelMerchantNo())
                .parentMerchantNo(anchoredCmd.getParentMerchantNo())
                .merchantNo(anchoredCmd.getMerchantNo())
                .channelType(anchoredDetailCmd.getPayChannel())
                .payScene(anchoredDetailCmd.getPayScene())
                .activityType(anchoredDetailCmd.getActivityType())
                .anchoredType(anchoredDetailCmd.getAnchoredType())
                .anchoredDimension(null == anchoredDetailCmd.getAnchoredDimension() ? Boolean.FALSE : anchoredDetailCmd.getAnchoredDimension())
                .relationMerchantNo(anchoredDetailCmd.getRelationMerchantNo())
//                .authFileName(anchoredCmd.getAuthFileName())
//                .authFileUrl(anchoredCmd.getAuthFileUrl())
                .groupName(anchoredCmd.getMerchantNo())
//                .supportPayType(anchoredDetailCmd.getSupportPayType())
                .anchoredStatus(AnchoredStatus.INIT)
//                        .finishTime()
//                        .failReason()
                .build();
    }

    public void buildSameSubjectDesignateSaasRelation(String relationMerchantNo) {
        this.relationMerchantNo = relationMerchantNo;
    }

    /**
     * 更新聚合挂靠结果
     */
    public void buildAggAnchoredResult(RemoteResult<List<AggAnchoredMerchantInfo>> aggAnchoredResult) {
        if (aggAnchoredResult.isSuccess()) {
            this.aggAnchoredMerchantInfoList = aggAnchoredResult.getData();
            this.anchoredStatus = AnchoredStatus.AGG_SUCCESS;
        } else {
            this.anchoredStatus = AnchoredStatus.FAIL;
            this.failReason = aggAnchoredResult.getMessage();
        }
    }

    public void updateChannelAnchoredProcess() {
        this.anchoredStatus = AnchoredStatus.PROCESSING;

    }

    /**
     * 更新渠道挂靠结果
     */
    public void updateChannelAnchoredResult(AnchoredStatus channelAnchoredStatus, String failReason) {
        this.anchoredStatus = channelAnchoredStatus;
        this.failReason = failReason;

    }

    /**
     * 判断当前申请是否终态 是否能更新订单状态
     *
     */
    public static Boolean isFinish(AnchoredStatus anchoredStatus, AnchoredType anchoredType, Boolean anchoredDimension) {
        boolean finish = DocumentedEnum.inRightEnums(anchoredStatus,
                AnchoredStatus.SUCCESS,
                AnchoredStatus.FAIL,
                AnchoredStatus.CHANNEL_FAIL);
        if (finish) {
            return Boolean.TRUE;
        }
        /*判断是否是指定商编+同主体同SAAS 聚合完成也可以完成*/
        return AnchoredStatus.AGG_SUCCESS == anchoredStatus && needSpecialHandleStatus(anchoredType, anchoredDimension);
    }

    /**
     * 对外的展示状态
     */
    public static String covertSyncStatus(AnchoredStatus anchoredStatus, AnchoredType anchoredType, Boolean anchoredDimension) {
        if (anchoredStatus == AnchoredStatus.SUCCESS || anchoredStatus == AnchoredStatus.FAIL)
            return anchoredStatus.getDocument();

        if (needSpecialHandleStatus(anchoredType, anchoredDimension)) {
            if (anchoredStatus == AnchoredStatus.AGG_SUCCESS || anchoredStatus == AnchoredStatus.CHANNEL_FAIL)
                return AnchoredStatus.SUCCESS.getDocument();
        } else {
            if (anchoredStatus == AnchoredStatus.CHANNEL_FAIL) return AnchoredStatus.FAIL.getDocument();
        }
        return AnchoredStatus.PROCESSING.getDocument();

    }

    /**
     * 对外的展示状态
     *
     */
    public static String covertSyncStatus(String anchoredStatus, String anchoredType, Boolean anchoredDimension) {
        if (DocumentedEnum.stringInRightEnums(anchoredStatus, AnchoredStatus.SUCCESS, AnchoredStatus.FAIL))
            return anchoredStatus;

        if (needSpecialHandleStatus(anchoredType, anchoredDimension)) {
            if (DocumentedEnum.stringInRightEnums(anchoredStatus, AnchoredStatus.AGG_SUCCESS, AnchoredStatus.CHANNEL_FAIL)) {
                return AnchoredStatus.SUCCESS.getDocument();
            }

        } else {
            if (DocumentedEnum.stringInRightEnums(anchoredStatus, AnchoredStatus.CHANNEL_FAIL)) {
                return AnchoredStatus.FAIL.getDocument();
            }
        }
        return AnchoredStatus.PROCESSING.getDocument();

    }

    /**
     * 是否需要特殊处理状态，
     * 否则到终态才算终态
     *
     */
    public static boolean needSpecialHandleStatus(AnchoredType anchoredType, Boolean anchoredDimension) {
        return AnchoredType.DESIGNATE_MERCHANT == anchoredType
                || (AnchoredType.SAME_SUBJECT == anchoredType && Boolean.FALSE.equals(anchoredDimension));
    }

    /**
     * 是否需要特殊处理状态，
     * 否则到终态才算终态
     *
     */
    public static boolean needSpecialHandleStatus(String anchoredType, Boolean anchoredDimension) {
        return AnchoredType.DESIGNATE_MERCHANT.getDocument().equals(anchoredType)
                || (AnchoredType.SAME_SUBJECT.getDocument().equals(anchoredType) && Boolean.FALSE.equals(anchoredDimension));
    }

    public void updateSingSync(Boolean singSync) {
        this.singSync = singSync;
    }
}
