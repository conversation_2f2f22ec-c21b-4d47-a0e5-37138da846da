package com.yeepay.g3.core.aggregation.config.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yeepay.g3.core.aggregation.config.common.CacheTypeEnum;
import com.yeepay.g3.utils.smartcache.utils.SmartCacheUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public class CacheUtil {

    private static final Cache<String, CacheEntry> localCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)  // 默认过期时间
            .build();

    @Setter
    @Getter
    @AllArgsConstructor
    private static class CacheEntry {
        private final Object value;
        private final long expireTime;
        private final Class<?> type;

        public boolean isExpired() {
            return System.currentTimeMillis() <= expireTime;
        }
    }

    // 从本地缓存获取数据（使用 TypeReference）
    private static <T> T getFromLocalCache(String key, TypeReference<T> typeReference) {
        CacheEntry entry = localCache.getIfPresent(key);
        if (entry != null && entry.isExpired()) {
            return JsonUtils.convert(entry.getValue(), typeReference);
        }
        return null;
    }

    private static <T> T getFromLocalCache(String key, Class<T> clazz) {
        CacheEntry entry = localCache.getIfPresent(key);
        if (entry != null && entry.isExpired()) {
            if (clazz.isAssignableFrom(entry.getType())) {
                return (T) entry.getValue();
            } else {
                String convert = JsonUtils.convert(entry.getValue());
                return JsonUtils.convert(convert, clazz);
            }
        }
        return null;
    }

    // 从远程缓存获取数据（使用 TypeReference）
    private static <T> T getFromRemoteCache(String key, TypeReference<T> typeReference) {
        Object cached = SmartCacheUtils.get(key);
        if (cached != null) {
            if (cached instanceof CacheEntry) {
                CacheEntry entry = (CacheEntry) cached;  // 强转为 CacheEntry
                if (typeReference.getType().getClass().isAssignableFrom(entry.getType())) {
                    return (T) entry.getValue();  // 类型匹配，直接返回
                } else {
                    return JsonUtils.convert(entry.getValue(), typeReference);
                }
            }
        }
        return null;
    }

    private static <T> T getFromRemoteCache(String key, Class<T> clazz) {
        Object cached = SmartCacheUtils.get(key);
        if (cached != null) {
            if (cached instanceof CacheEntry) {
                CacheEntry entry = (CacheEntry) cached;  // 强转为 CacheEntry
                if (clazz.isAssignableFrom(entry.getType())) {
                    return (T) entry.getValue();  // 类型匹配，直接返回
                } else {
                    // 类型不匹配，进行反序列化
                    String convert = JsonUtils.convert(entry.getValue());
                    return JsonUtils.convert(convert, clazz);
                }
            }
        }
        return null;
    }

    public static <T> T get(String key, CacheTypeEnum cacheType, long expireAfterWriteMillis, Object type) {
        try {
            if (CacheTypeEnum.MIXED.equals(cacheType)) {
                if (type instanceof TypeReference) {
                    return getCache(key, cacheType, expireAfterWriteMillis, (TypeReference<T>) type);
                } else if (type instanceof Class) {
                    return getCache(key, cacheType, expireAfterWriteMillis, (Class<T>) type);
                } else {
                    throw new IllegalArgumentException("type must be either TypeReference or Class");
                }
            }else {
                throw new IllegalArgumentException("CacheType must be MIXED for this method");
            }
        }catch (Throwable e) {
            log.error("Failed to get cache for key: {}", key, e);
        }
        return null;
    }

    public static <T> T get(String key, CacheTypeEnum cacheType, Object type) {
        try {
            // 先判断缓存类型和目标类型
            if (type instanceof TypeReference) {
                return getCache(key, cacheType, (TypeReference<T>) type);
            } else if (type instanceof Class) {
                return getCache(key, cacheType, (Class<T>) type);
            } else {
                throw new IllegalArgumentException("type must be either TypeReference or Class");
            }
        }catch (Throwable e){
            log.error("Failed to get cache for key: {}", key, e);
        }
       return null;
    }

    // 统一的缓存获取方法
    private static <T> T getCache(String key, CacheTypeEnum cacheType, long expireAfterWriteMillis, TypeReference<T> typeReference) {
        if (cacheType == CacheTypeEnum.LOCAL) {
            return getFromLocalCache(key, typeReference);
        } else if (cacheType == CacheTypeEnum.REMOTE) {
            return getFromRemoteCache(key, typeReference);
        } else if (cacheType == CacheTypeEnum.MIXED) {
            T value = getFromLocalCache(key, typeReference);
            if (value != null) {
                return value;
            }
            value = getFromRemoteCache(key, typeReference);
            if (value != null) {
                putToLocalCache(key, value, expireAfterWriteMillis);
            }
            return value;
        } else {
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);
        }
    }

    // 统一的缓存获取方法，支持 Class 类型
    private static <T> T getCache(String key, CacheTypeEnum cacheType, long expireAfterWriteMillis, Class<T> clazz) {
        if (cacheType == CacheTypeEnum.LOCAL) {
            return getFromLocalCache(key, clazz);
        } else if (cacheType == CacheTypeEnum.REMOTE) {
            return getFromRemoteCache(key, clazz);
        } else if (cacheType == CacheTypeEnum.MIXED) {
            T value = getFromLocalCache(key, clazz);
            if (value != null) {
                return value;
            }
            value = getFromRemoteCache(key, clazz);
            if (value != null) {
                putToLocalCache(key, value, expireAfterWriteMillis);
            }
            return value;
        } else {
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);
        }
    }

    public static <T> T getCache(String key, CacheTypeEnum cacheType, TypeReference<T> typeReference) {
        if (cacheType == CacheTypeEnum.LOCAL) {
            return getFromLocalCache(key, typeReference);
        } else if (cacheType == CacheTypeEnum.REMOTE) {
            return getFromRemoteCache(key, typeReference);
        } else {
            throw new IllegalArgumentException("CacheType must be LOCAL or REMOTE for this method");
        }
    }

    public static <T> T getCache(String key, CacheTypeEnum cacheType, Class<T> clazz) {
        if (cacheType == CacheTypeEnum.LOCAL) {
            return getFromLocalCache(key, clazz);
        } else if (cacheType == CacheTypeEnum.REMOTE) {
            return getFromRemoteCache(key, clazz);
        } else {
            throw new IllegalArgumentException("CacheType must be LOCAL or REMOTE for this method");
        }
    }

    // 向本地缓存存入数据，并设置过期时间
    private static <T> void putToLocalCache(String key, T value, long localCacheExpireAfterWrite) {
        long expireTime = System.currentTimeMillis() + localCacheExpireAfterWrite;
        CacheEntry entry = new CacheEntry(value, expireTime, value.getClass());
        localCache.put(key, entry);
    }

    // 向远程缓存存入数据
    private static <T> void putToRemoteCache(String key, T value, int seconds) {
        CacheEntry entry = new CacheEntry(value, System.currentTimeMillis() + seconds * 1000L, value.getClass());
        SmartCacheUtils.set(key, entry, seconds);
    }

    // 向本地或远程缓存存入数据
    public static <T> void put(CacheTypeEnum cacheType, String key, T value, long expireAfterWrite) {
        try {
            if (cacheType == CacheTypeEnum.LOCAL) {
                putToLocalCache(key, value, expireAfterWrite);
            } else if (cacheType == CacheTypeEnum.REMOTE) {
                putToRemoteCache(key, value, (int) expireAfterWrite);
            } else {
                throw new IllegalArgumentException("CacheType must be LOCAL or REMOTE for this method");
            }
        }catch (Throwable e) {
            log.error("Failed to put cache for key: {}", key, e);
        }
    }

    // 向混合缓存存入数据（本地和远程）
    public static <T> void put(CacheTypeEnum cacheType, String key, T value, long localCacheExpireAfterWrite, int remoteCacheExpireAfterWrite) {
        if (cacheType == CacheTypeEnum.MIXED) {
            try {
                putToLocalCache(key, value, localCacheExpireAfterWrite);
            }catch (Throwable e){
                log.error("Failed to put local cache for key: {}", key, e);
            }
            try {
                putToRemoteCache(key, value, remoteCacheExpireAfterWrite);
            }catch (Throwable e){
                log.error("Failed to put remote cache for key: {}", key, e);
            }
        } else {
            throw new IllegalArgumentException("CacheType must be MIXED for this method");
        }
    }

    // 从本地缓存删除数据
    private static void removeFromLocalCache(String key) {
        localCache.invalidate(key);
    }

    // 从远程缓存删除数据
    public static void removeFromRemoteCache(String key) {
        SmartCacheUtils.remove(key);
    }

    // 删除缓存（本地和远程）
    public static void remove(String key) {
        removeFromLocalCache(key);
        removeFromRemoteCache(key);
    }

    // 清空本地缓存
    public static void clearLocalCache() {
        localCache.invalidateAll();
    }

    // 清空远程缓存
    public static void clearRemoteCache() {
        SmartCacheUtils.clear();
    }

    // 清空所有缓存
    public static void clearAllCache() {
        clearLocalCache();
        clearRemoteCache();
    }

    // 检查缓存是否存在
    public static boolean contains(String key, CacheTypeEnum cacheType) {
        if (cacheType == CacheTypeEnum.LOCAL) {
            return localCache.getIfPresent(key) != null;
        } else if (cacheType == CacheTypeEnum.REMOTE) {
            return SmartCacheUtils.contains(key);
        } else if (cacheType == CacheTypeEnum.MIXED) {
            return contains(key, CacheTypeEnum.LOCAL) || contains(key, CacheTypeEnum.REMOTE);
        } else {
            throw new IllegalArgumentException("Unknown cache type: " + cacheType);
        }
    }
}
