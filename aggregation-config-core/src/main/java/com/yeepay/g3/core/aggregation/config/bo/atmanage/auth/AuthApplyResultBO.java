package com.yeepay.g3.core.aggregation.config.bo.atmanage.auth;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * description: 认证申请结果
 * <AUTHOR>
 * @since 2025/5/26:17:44
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class AuthApplyResultBO {

    /**
     * 申请单编号
     */
    private String channelApplyNo;

    /**
     * 返回码
     */
    private String returnCode;

    /**
     * 返回信息
     */
    private String returnMessage;
}
