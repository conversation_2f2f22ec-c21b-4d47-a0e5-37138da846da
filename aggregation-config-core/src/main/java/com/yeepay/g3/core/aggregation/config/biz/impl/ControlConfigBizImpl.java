package com.yeepay.g3.core.aggregation.config.biz.impl;

import com.yeepay.g3.core.aggregation.config.biz.ControlConfigBiz;
import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.service.ControlConfigService;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.aggregation.config.enums.OperateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @description: 管控配置biz业务层
 * @author: xuchen.liu
 * @date: 2024-12-13 15:11
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class ControlConfigBizImpl implements ControlConfigBiz {

    @Autowired
    private ControlConfigService controlConfigService;

    @Override
    public Long configManage(Pair<OperateTypeEnum, ControlConfigEntity> entityPair) {
        OperateTypeEnum operateType = entityPair.getKey();
        ControlConfigEntity controlConfigEntity = entityPair.getRight();
        ControlConfigEntity queryValidByMerchantNo = controlConfigService.queryValidByMerchantNo(
                controlConfigEntity.getMerchantNo() , null);
        if (OperateTypeEnum.CREATE.equals(operateType)) {
            //调用创建配置
            if (queryValidByMerchantNo != null) {
                throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR,"商编已存在配置,不可重复添加");
            }
            controlConfigEntity.setCreateTime(new Date());
            controlConfigEntity.setUpdateTime(new Date());
            controlConfigService.saveConfig(controlConfigEntity);

        } else if (OperateTypeEnum.UPDATE.equals(operateType)) {
            //补充id
            controlConfigEntity.setId(queryValidByMerchantNo.getId());
            controlConfigEntity.setCreateTime(queryValidByMerchantNo.getCreateTime());
            controlConfigEntity.setUpdateTime(new Date());
            controlConfigService.updateConfig(controlConfigEntity);



        }else {
            log.error("未处理的操作类型={}",operateType.name());
        }
        return controlConfigEntity.getId();
    }
}
