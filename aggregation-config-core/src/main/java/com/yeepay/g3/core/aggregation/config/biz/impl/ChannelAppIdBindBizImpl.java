package com.yeepay.g3.core.aggregation.config.biz.impl;

import com.yeepay.g3.core.aggregation.config.biz.ChannelAppIdBindBiz;
import com.yeepay.g3.core.aggregation.config.builder.AppIdMerchantBindBuilder;
import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.BindAllStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelAppIdBindStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.core.aggregation.config.service.AppIdMerchantBindService;
import com.yeepay.g3.core.aggregation.config.service.AttachResultService;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: 绑定关系业务层
 * @author: xuchen.liu
 * @date: 2024-12-16 18:47
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class ChannelAppIdBindBizImpl  implements ChannelAppIdBindBiz {

    private final MerchantCenterExternal merchantCenterExternal;

    private AppIdMerchantBindService appIdMerchantBindService;

    private final AttachResultService attachResultService;

    private final AppIdMerchantBindBuilder appIdMerchantBindBuilder;

    private final AtlasThreadPoolExecutor executor;

    public final ThreadLocal<Boolean> booleanThreadLocal = new ThreadLocal<>();

    private final TransactionTemplate transactionTemplate;

    @Autowired
    public ChannelAppIdBindBizImpl(MerchantCenterExternal merchantCenterExternal,
                                   AppIdMerchantBindService appIdMerchantBindService,
                                   AttachResultService attachResultService,
                                   AppIdMerchantBindBuilder appIdMerchantBindBuilder,
                                   @Qualifier(value = "asyncExecutor") AtlasThreadPoolExecutor executor, TransactionTemplate transactionTemplate) {
        this.merchantCenterExternal = merchantCenterExternal;
        this.appIdMerchantBindService = appIdMerchantBindService;
        this.attachResultService = attachResultService;
        this.appIdMerchantBindBuilder = appIdMerchantBindBuilder;
        this.executor = executor;
        this.transactionTemplate = transactionTemplate;
    }

    @SneakyThrows
    @Override
    public void handle(AppIdMerchantBindEntity appIdMerchantBindEntity) {
        if (appIdMerchantBindService.receiveMqData(appIdMerchantBindEntity)) {
            executor.execute(() -> this.process(appIdMerchantBindEntity));
        }
    }

    @Override
    public void compensateAppIdBind(String appId, String merchantNo) {
        List<AppIdMerchantBindEntity> appIdMerchantBindEntities = appIdMerchantBindService.queryBindByAppid(appId, merchantNo, DataCleanStatusEnum.INIT);
        if (CollectionUtils.isNotEmpty(appIdMerchantBindEntities)) {
            log.info("根据appId 查询到需要补偿的绑定关系={}",JsonUtils.convert(appIdMerchantBindEntities));
            for (AppIdMerchantBindEntity appIdMerchantBindEntity : appIdMerchantBindEntities) {
                appIdMerchantBindEntity.setUpdateTime(new Date());
                this.process(appIdMerchantBindEntity);
            }
        }
    }

    /**
     * 异步处理商户绑定关系数据清洗，并更新相关绑定状态。
     * <p>
     * 该方法负责查询商户组信息、挂靠结果以及待更新的绑定数据，
     * 通过处理待更新数据和新增的挂靠结果，生成新的绑定关系并批量更新。
     * </p>
     *
     * @param appIdMerchantBindEntity 包含商户信息和AppId的实体对象
     */
    private void process(AppIdMerchantBindEntity appIdMerchantBindEntity) {
        try {
            log.info("开始执行任务绑定关系数据清洗={}", JsonUtils.convert(appIdMerchantBindEntity));
            // 初始化新增列表
            List<AttachResultEntity> newAttachResultList;

            List<AttachResultEntity> attachResultList = new ArrayList<>();
            if (attachResultService.updateClearStatus(appIdMerchantBindEntity.getMerchantNo(),DataCleanStatusEnum.USED,DataCleanStatusEnum.SUCCESS)) {
                // 查询挂靠结果
                 attachResultList = attachResultService.queryByWechatAttachResult(appIdMerchantBindEntity.getMerchantNo(),DataCleanStatusEnum.USED);
            }
            // bind  、attach 、 all
            // 查询待更新的全量绑定数据
            List<AppidAllMerchantNoBindEntity> pendingUpdateList = appIdMerchantBindService.queryByAppidAndMerchantNoAndAttachMerchantNo(appIdMerchantBindEntity.getAppId(),
                    appIdMerchantBindEntity.getMerchantNo());

            boolean isInert = true;
            if (CollectionUtils.isNotEmpty(pendingUpdateList)) {
                log.info("待更新全量数据列表={}", JsonUtils.convert(pendingUpdateList));

                // 生成待更新列表中的所有关联商户号
                Set<String> existingMerchantNos = getExistingMerchantNos(pendingUpdateList);

                // 过滤出新增的挂靠结果
                newAttachResultList = attachResultList.stream()
                        .filter(item -> !existingMerchantNos.contains(item.getSubMerchantNo()))
                        .collect(Collectors.toList());

                // 挂靠结果映射
                Map<String, AttachResultEntity> attachResultMap = attachResultList.stream()
                        .flatMap(attachResult -> Stream.of(
                                new AbstractMap.SimpleEntry<>(attachResult.getSubMerchantNo(), attachResult),
                                new AbstractMap.SimpleEntry<>(attachResult.getMainMerchantNo(), attachResult)
                        ))
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (existing, replacement) -> existing
                        ));

                /**
                 * 1、当对 appid 与 商编进行绑定时，需要针对这个 appid 的所有已存在记录做加挂绑定
                 * 2、当 appid 与 商编解绑的时， 需要做的是对这个appid + 主商编已存在的所有记录做解绑
                 */
                String merchantNo = appIdMerchantBindEntity.getMerchantNo();
                ChannelAppIdBindStatusEnum bindStatus = appIdMerchantBindEntity.getBindStatus();
                if (ChannelAppIdBindStatusEnum.FAILED.equals(bindStatus)) {
                    pendingUpdateList = pendingUpdateList.stream()
                            .filter(pending -> pending.getAttachMerchantNo().equals(merchantNo))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(pendingUpdateList)) {
                        isInert = false;
                    }
                }

                //处理待更新的全量绑定数据
                ArrayList<AppidAllMerchantNoBindEntity> temp = new ArrayList<>();
                pendingUpdateList.forEach(pending -> {
                    AttachResultEntity attachResult = attachResultMap.get(pending.getMerchantNo());
                    if (attachResult != null) {
                        pending.setBindStatus(appIdMerchantBindBuilder.getBindAllStatusEnum(attachResult, appIdMerchantBindEntity));
                        pending.setUpdateTime(new Date());
                        temp.add(pending);
                    }
                    if (pending.getMerchantNo().equals(appIdMerchantBindEntity.getMerchantNo())) {
                        pending.setBindStatus(appIdMerchantBindBuilder.getBindAllStatusEnum(appIdMerchantBindEntity));
                        pending.setUpdateTime(new Date());
                        temp.add(pending);
                    }
                });
                pendingUpdateList = temp;

            }else {
                //若待更列表未空，不对挂靠信息做任何处理
                newAttachResultList = attachResultList;
            }
            // 映射对象
            List<AppidAllMerchantNoBindEntity> newAllList = new ArrayList<>();
            if (isInert) {
                newAllList = appIdMerchantBindBuilder.toAllList(newAttachResultList, appIdMerchantBindEntity,pendingUpdateList);
            }
            Pair<List<AppidAllMerchantNoBindEntity>, List<AppidAllMerchantNoBindEntity>> listPair = Pair.of(newAllList, pendingUpdateList);

            // 更新商户绑定关系
            appIdMerchantBindEntity.setCleanStatus(DataCleanStatusEnum.SUCCESS);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    try {
                        attachResultService.updateClearStatus(appIdMerchantBindEntity.getMerchantNo(),DataCleanStatusEnum.SUCCESS,DataCleanStatusEnum.USED);
                        appIdMerchantBindService.batchMerchantAll(appIdMerchantBindEntity, listPair);
                    }catch (Throwable e) {
                        status.setRollbackOnly();
                        throw e;
                    }
                }
            });
            log.info("绑定数据处理完成");
        } catch (Throwable e) {
            String msg = String.format("appid=%s ,merchantNo=%s 处理绑定关系异常,", appIdMerchantBindEntity.getAppId(), appIdMerchantBindEntity.getMerchantNo());
            log.error(msg+ e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取待更新列表中的所有关联商户号
     */
    private Set<String> getExistingMerchantNos(List<AppidAllMerchantNoBindEntity> pendingUpdateList) {
        Set<String> allMerchantNos = new HashSet<>();
        pendingUpdateList.forEach(pending -> {
            allMerchantNos.add(pending.getMerchantNo());
            allMerchantNos.add(pending.getAttachMerchantNo());
        });
        return allMerchantNos;
    }

    /**
     * 异步处理挂靠绑定关系数据清洗并执行更新操作。
     * <p>
     * 该方法会根据传入的挂靠结果实体 {@link AttachResultEntity}，查询主商户和子商户的绑定关系，
     * 如果绑定关系已存在则更新相关信息，如果不存在则创建新的绑定关系。
     * </p>
     *
     * @param attachResultEntity 包含主商户编号、子商户编号及其他挂靠数据的实体对象
     */
    @Override
    public void attachBind(AttachResultEntity attachResultEntity) {
        try {
            log.info("开始执行异步挂靠绑定关系数据清洗={}", JsonUtils.convert(attachResultEntity));
            ChannelTypeEnum channelType = ChannelTypeEnum.valueOf(attachResultEntity.getChannel());
            if (!ChannelTypeEnum.WECHAT.equals(channelType)) {
                return;
            }
            //更新挂靠的数据清洗状态
            attachResultEntity.setCleanStatus(DataCleanStatusEnum.SUCCESS.name());
            // 获取挂靠的主商户编号
            String mainMerchantNo = attachResultEntity.getMainMerchantNo();
            // 获取挂靠的子商户编号列表
            String subMerchantNo = attachResultEntity.getSubMerchantNo();
            List<AppIdMerchantBindEntity>  appidList =  appIdMerchantBindService.queryAppidByMerchantNoAndChannelType(mainMerchantNo, ChannelTypeEnum.WECHAT);
            if (CollectionUtils.isEmpty(appidList)) {
                log.info("根据挂靠主商编查询商户AppId绑定关系，无法查询到数据 ，不进行数据合并, 主商编={}",mainMerchantNo);
                attachResultService.update(attachResultEntity, DataCleanStatusEnum.INIT);
                return;
            }

            List<String> appIdList = appidList.stream().map(AppIdMerchantBindEntity::getAppId).collect(Collectors.toList());
            List<AppidAllMerchantNoBindEntity> allBindList;
            //根据appId+挂靠商编号+交易商编查询，加了子如果没查到，所有主在子不在
            allBindList = appIdMerchantBindService.queryAllByAppIdList(appIdList, mainMerchantNo, subMerchantNo);

            //数据操作二元组 key=新增的数据，value=修改的数据
            Pair<List<AppidAllMerchantNoBindEntity>, List<AppidAllMerchantNoBindEntity>> listPair;
            if (CollectionUtils.isNotEmpty(allBindList)) {
                for (AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity : allBindList) {
                    appidAllMerchantNoBindEntity.setUpdateTime(new Date());
                    appIdMerchantBindBuilder.setBindStatus(attachResultEntity, appidAllMerchantNoBindEntity, appidList);
                }
                listPair = Pair.of(new ArrayList<>(), allBindList);
            }else {
                allBindList = new ArrayList<>();
                Date now = new Date();
                for (AppIdMerchantBindEntity appIdMerchantBindEntity : appidList) {
                    //挂靠子商编组参数
                    AppidAllMerchantNoBindEntity attachMerchantBind = new AppidAllMerchantNoBindEntity();
                    attachMerchantBind.setAppid(appIdMerchantBindEntity.getAppId());
                    attachMerchantBind.setMerchantNo(attachResultEntity.getSubMerchantNo());
                    attachMerchantBind.setMerchantName(attachResultEntity.getSubMerchantName());
                    //查询顶级商编
                    attachMerchantBind.setFirstMerchantNo(attachResultEntity.getSubFirstMerchantNo());
                    attachMerchantBind.setSecondMerchantNo(attachResultEntity.getSubSecondMerchantNo());
                    attachMerchantBind.setThreeMerchantNo(attachResultEntity.getSubThreeMerchantNo());
                    attachMerchantBind.setAttachMerchantNo(attachResultEntity.getMainMerchantNo());
                    BindAllStatusEnum attachMerchantBindAllStatus = appIdMerchantBindBuilder.getBindAllStatusEnum(attachResultEntity, appIdMerchantBindEntity);
                    attachMerchantBind.setBindStatus(attachMerchantBindAllStatus);
                    attachMerchantBind.setCreateTime(now);
                    attachMerchantBind.setUpdateTime(now);
                    //放入list
                    allBindList.add(attachMerchantBind);
                }
                listPair = Pair.of(allBindList, new ArrayList<>());
            }
            attachResultEntity.setLastModifiedTime(new Date());

            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    try {
                        attachResultService.update(attachResultEntity, DataCleanStatusEnum.INIT);
                        //写入数据
                        appIdMerchantBindService.batchMerchantAll(null, listPair);
                    }catch (Throwable e) {
                        status.setRollbackOnly();
                        throw e;
                    }
                }
            });
        }catch (Throwable e){
            log.error("异步处理挂靠绑定关系异常,"+e.getMessage(),e);
        }
    }
}
