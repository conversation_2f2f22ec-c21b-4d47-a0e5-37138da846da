package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryApplyResultReqBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryOrder;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryApplyDetailConvert;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryOrderConvert;
import com.yeepay.g3.core.aggregation.config.dao.entry.ChannelEntryApplyDetailDao;
import com.yeepay.g3.core.aggregation.config.dao.entry.ChannelEntryOrderDao;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/9 22:39
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ChannelEntryOrderServiceImpl implements ChannelEntryOrderService {

    private final ChannelEntryOrderDao channelEntryOrderDao;
    private final ChannelEntryApplyDetailDao channelEntryApplyDetailDao;

    @Override
    @Transactional
    public void saveChannelEntryOrder(final EntryOrder entryOrder) {
        try {
            channelEntryOrderDao.insertSelective(ChannelEntryOrderConvert.convert(entryOrder));
            channelEntryApplyDetailDao.batchInsert(ChannelEntryApplyDetailConvert.convert(entryOrder));
        } catch (DuplicateKeyException exception) {
            throw new BusinessException(ResultCode.ENTRY_APPLY_IDEMPOTENT);
        }
    }

    @Override
    public EntryOrder getChannelEntryOrder(final BizSceneEnum bizScene, final String bizApplyNo) {
        final Weekend<ChannelEntryOrderEntity> weekend = Weekend.of(ChannelEntryOrderEntity.class);
        weekend.weekendCriteria()
                .andEqualTo(ChannelEntryOrderEntity::getBizScene, bizScene.name())
                .andEqualTo(ChannelEntryOrderEntity::getBizApplyNo, bizApplyNo);
        final ChannelEntryOrderEntity channelEntryOrderEntity = channelEntryOrderDao.selectOneByExample(weekend);

        return ChannelEntryOrderConvert.convert(channelEntryOrderEntity);
    }

    @Override
    public List<BaseEntryApply<? extends ChannelSpecialParams>> queryChannelEntryOrder(QueryEntryApplyResultReqBO reqBO) {

        final Weekend<ChannelEntryApplyDetailEntity> weekend = Weekend.of(ChannelEntryApplyDetailEntity.class);
        weekend.weekendCriteria()
                .andEqualTo(ChannelEntryApplyDetailEntity::getMerchantNo, reqBO.getMerchantNo())
                .andEqualTo(ChannelEntryApplyDetailEntity::getPayChannel, CheckUtils.isEmpty(reqBO.getPayChannel()) ? null : reqBO.getPayChannel().getDocument())
                .andEqualTo(ChannelEntryApplyDetailEntity::getPayScene, CheckUtils.isEmpty(reqBO.getPayScene()) ? null : reqBO.getPayScene().getDocument())
                .andEqualTo(ChannelEntryApplyDetailEntity::getActivityType, CheckUtils.isEmpty(reqBO.getActivityType()) ? null : reqBO.getActivityType().getDocument());
        List<ChannelEntryApplyDetailEntity> channelEntryOrderEntities = channelEntryApplyDetailDao.selectByExample(weekend);
        if (CheckUtils.isEmpty(channelEntryOrderEntities)) {
            return Collections.emptyList();
        }
        ConcurrentMap<String, List<ChannelEntryApplyDetailEntity>> orderMap = channelEntryOrderEntities.stream().collect(Collectors.groupingByConcurrent(ChannelEntryApplyDetailEntity::getOrderNo));
        final Weekend<ChannelEntryOrderEntity> orderWeekend = Weekend.of(ChannelEntryOrderEntity.class);
        orderWeekend.weekendCriteria()
                .andIn(ChannelEntryOrderEntity::getOrderNo, orderMap.keySet());
        orderWeekend.orderBy(Const.CREATE_DT).desc();
        List<ChannelEntryOrderEntity> orderEntities = channelEntryOrderDao.selectByExample(orderWeekend);
        ChannelEntryOrderEntity channelEntryOrderEntity = orderEntities.get(0);
        List<ChannelEntryApplyDetailEntity> detailList = orderMap.get(channelEntryOrderEntity.getOrderNo());
        return ChannelEntryApplyDetailConvert.convertToBO(channelEntryOrderEntity, detailList);
    }

    @Override
    public List<BaseEntryApply<? extends ChannelSpecialParams>> queryNotFinishChannelEntryOrderBO(Date startTime, Date endTime, List<String> applyNoList) {
        List<ChannelEntryApplyDetailEntity> notFinishList = channelEntryApplyDetailDao.queryNotFinishChannelEntryOrder(startTime, endTime, applyNoList);
        if (CheckUtils.isEmpty(notFinishList)) {
            return Lists.newArrayList();
        }
        /*查询订单信息*/
        List<String> orderNoList = notFinishList.stream().map(ChannelEntryApplyDetailEntity::getOrderNo).collect(Collectors.toList());
        final Weekend<ChannelEntryOrderEntity> orderWeekend = Weekend.of(ChannelEntryOrderEntity.class);
        orderWeekend.weekendCriteria()
                .andIn(ChannelEntryOrderEntity::getOrderNo, orderNoList);
        orderWeekend.orderBy(Const.CREATE_DT).desc();
        List<ChannelEntryOrderEntity> orderEntities = channelEntryOrderDao.selectByExample(orderWeekend);
        Map<String, ChannelEntryOrderEntity> orderMap = orderEntities.stream().filter(entity -> entity.getOrderNo() != null).collect(Collectors.toMap(
                ChannelEntryOrderEntity::getOrderNo,
                entity -> entity,
                (existing, replacement) -> existing
        ));

        return notFinishList.stream().map(detail -> ChannelEntryApplyDetailConvert.convertToBO(orderMap.get(detail.getOrderNo()), detail)).collect(Collectors.toList());
    }

    @Override
    public BaseEntryApply<? extends ChannelSpecialParams> getChannelEntryApply(String applyNo) {
        final Weekend<ChannelEntryApplyDetailEntity> weekend = Weekend.of(ChannelEntryApplyDetailEntity.class, true, true);
        weekend.weekendCriteria().andEqualTo(ChannelEntryApplyDetailEntity::getApplyNo, applyNo);
        ChannelEntryApplyDetailEntity detailEntity = channelEntryApplyDetailDao.selectOneByExample(weekend);
        if (CheckUtils.isEmpty(detailEntity)) {
            return null;
        }

        final Weekend<ChannelEntryOrderEntity> orderWeekend = Weekend.of(ChannelEntryOrderEntity.class, true, true);
        orderWeekend.weekendCriteria().andEqualTo(ChannelEntryOrderEntity::getOrderNo, detailEntity.getOrderNo());
        ChannelEntryOrderEntity orderEntity = channelEntryOrderDao.selectOneByExample(orderWeekend);
        return ChannelEntryApplyDetailConvert.convertToBO(orderEntity, detailEntity);
    }

    @Override
    public int updateChannelEntryApplyDetailEntryApplyStatus(final BaseEntryApply<? extends ChannelSpecialParams> entryApply) {
        final Weekend<ChannelEntryApplyDetailEntity> weekend = Weekend.of(ChannelEntryApplyDetailEntity.class, true, true);
        weekend.weekendCriteria()
                .andEqualTo(ChannelEntryApplyDetailEntity::getApplyNo, entryApply.getApplyNo())
                .andEqualTo(ChannelEntryApplyDetailEntity::getStatus, entryApply.getOldEntryStatus());
        final ChannelEntryApplyDetailEntity channelEntryApplyDetailEntity = ChannelEntryApplyDetailConvert.convertEntryStatusUpdateEntity(entryApply);
        return channelEntryApplyDetailDao.updateByExampleSelective(channelEntryApplyDetailEntity, weekend);
    }

    @Override
    public int updateChannelEntryApplyDetailFlagStatus(final BaseEntryApply<? extends ChannelSpecialParams> entryApply) {
        final Weekend<ChannelEntryApplyDetailEntity> weekend = Weekend.of(ChannelEntryApplyDetailEntity.class, true, true);
        weekend.weekendCriteria()
                .andEqualTo(ChannelEntryApplyDetailEntity::getApplyNo, entryApply.getApplyNo())
                .andEqualTo(ChannelEntryApplyDetailEntity::getFlagStatus, entryApply.getOldFlagStatus())
                .andEqualTo(ChannelEntryApplyDetailEntity::getStatus, entryApply.getOldEntryStatus());
        final ChannelEntryApplyDetailEntity channelEntryApplyDetailEntity = ChannelEntryApplyDetailConvert.convertToFlagStatusUpdateEntity(entryApply);
        return channelEntryApplyDetailDao.updateByExampleSelective(channelEntryApplyDetailEntity, weekend);
    }

    @Override
    public ChannelEntryApplyDetailEntity selectChannelEntryApplyDetailEntityForUpdate(String applyNo) {
        return channelEntryApplyDetailDao.selectChannelEntryApplyDetailEntityForUpdate(applyNo);
    }
}
