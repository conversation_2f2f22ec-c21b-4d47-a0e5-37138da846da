package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryAnchoredInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryChannelTerminalInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryApplyDetailInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryApplyInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryInfoBO;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 聚合进件查询结果DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:57
 */
@Builder
@Getter
public class QueryEntryResultRespBO implements Serializable {

    private static final long serialVersionUID = -4394005679747440564L;
    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 申请单信息
     */
    private QueryEntryApplyInfoBO entryApplyInfo;

    /**
     * 历史进件列表
     */
    private List<QueryEntryAndAnchoredDetailBO> entryDetailList;

    public static QueryEntryResultRespBO build(String merchantNo,
                                               List<QueryEntryApplyDetailInfoBO> entryApplyList,
                                               List<QueryEntryInfoBO> entryList,
                                               List<QueryAnchoredInfoBO> attachList,
                                               QueryChannelTerminalInfoBO wxTerminalInfo,
                                               QueryChannelTerminalInfoBO aliTerminalInfo,
                                               Map<String, String> splitCreateStatus) {

        Set<String> keySet = new HashSet<>();
        Map<String, QueryEntryInfoBO> entryInfoMap = Optional.ofNullable(entryList)
                .orElse(Collections.emptyList()).stream()
                .filter(bo -> bo.getUniqueKey() != null)
                .collect(Collectors.toMap(QueryEntryInfoBO::getUniqueKey, entry -> entry, QueryEntryResultRespBO::mergeCompare));
        Map<String, List<QueryAnchoredInfoBO>> attachMap = Optional.ofNullable(attachList)
                .orElse(new ArrayList<>()).stream()
                .filter(bo -> bo.getUniqueKey() != null)
                .collect(Collectors.groupingBy(QueryAnchoredInfoBO::getUniqueKey));
        List<QueryAnchoredInfoBO> specialAnchoredInfos = Optional.ofNullable(attachList)
                .orElse(new ArrayList<>()).stream()
                .filter(bo -> bo.getUniqueKey() == null).collect(Collectors.toList());
        attachMap.forEach((key, value) -> value.addAll(specialAnchoredInfos));
        keySet.addAll(entryInfoMap.keySet());
        keySet.addAll(attachMap.keySet());

        List<QueryEntryAndAnchoredDetailBO> detailList = keySet.stream().map(key -> QueryEntryAndAnchoredDetailBO.build(
                key,
                entryInfoMap.get(key),
                attachMap.get(key),
                wxTerminalInfo,
                aliTerminalInfo,
                splitCreateStatus
        )).collect(Collectors.toList());
        QueryEntryApplyInfoBO queryEntryApplyInfoBO = QueryEntryApplyInfoBO.build(entryApplyList);
        return QueryEntryResultRespBO.builder()
                .merchantNo(merchantNo)
                .entryApplyInfo(queryEntryApplyInfoBO)
                .entryDetailList(detailList).build();
    }

    public static QueryEntryInfoBO mergeCompare(QueryEntryInfoBO v1,
                                                QueryEntryInfoBO v2) {
        // 降序排列，优先级高的排前面
        return Arrays.stream(new QueryEntryInfoBO[]{v1, v2}).max((a, b) -> {
            int priorityA = EntryStatusEnum.SUCCESS.getDocument().equals(a.getEntryStatus()) ? 2
                    : EntryStatusEnum.PROCESSING.getDocument().equals(a.getEntryStatus()) ? 1
                    : 0;
            int priorityB = EntryStatusEnum.SUCCESS.getDocument().equals(b.getEntryStatus()) ? 2
                    : EntryStatusEnum.PROCESSING.getDocument().equals(b.getEntryStatus()) ? 1
                    : 0;
            return Integer.compare(priorityA, priorityB);
        }).orElse(v1);
    }
}