package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;

import java.util.List;

/**
 * description: 第三方记录数据访问对象接口
 *
 * <AUTHOR>
 * @since 2025/5/26:11:38
 * Company: 易宝支付(YeePay)
 */
public interface ThirdPartyRecordService {


    /**
     * 保存第三方记录
     */
    void saveThirdPartyRecord(ThirdPartyRecordEntity thirdPartyRecordEntity);

//    /**
//     * 批量插入记录
//     *
//     * @param records 记录列表
//     * @return 插入成功的记录数
//     */
//    Boolean batchInsertThirdPartyRecord(List<ThirdPartyRecordEntity> records);

    /**
     * 查询同一批次请求下的记录
     *
     * @param applyNo
     * @param businessType
     * @return
     */
    List<ThirdPartyRecord> queryThirdPartyRecordByApplyNo(String applyNo, String businessType);

    /**
     * 查询同一批次请求下的记录 未完成的
     * 或者成功的
     *
     * @param applyNo
     * @param businessType
     * @return
     */
    List<ThirdPartyRecord> queryNotFinishOrSuccessThirdPartyRecordByApplyNo(String applyNo, String businessType);


    /**
     * 更新通道调用记录结果
     *
     * @return
     */
    Boolean updateThirdRecordChannelResult(String requestNo, String channelCode, String channelMsg, ThirdPartyRecordStatusEnum statusEnum);

    /**
     * 查询渠道的调用情况
     *
     * @param requestNo
     * @return
     */
    ThirdPartyRecord queryThirdRecordByRequestNo(String requestNo, String businessType);

    int saveThirdPartyRecord(final ThirdPartyRecord thirdPartyRecord);

    Boolean updateByRequestNo(final ThirdPartyRecord thirdPartyRecord);

    /**
     * 依据申请单号批量查询最新请求下的记录
     *
     * @param applyNoList
     * @param businessType
     * @return
     */
    List<ThirdPartyRecordEntity> queryThirdPartyRecordListByApplyNo(List<String> applyNoList, String businessType);

}