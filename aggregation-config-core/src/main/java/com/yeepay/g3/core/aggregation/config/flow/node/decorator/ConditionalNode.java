package com.yeepay.g3.core.aggregation.config.flow.node.decorator;

import com.yeepay.g3.core.aggregation.config.flow.ContextMatcher;
import com.yeepay.g3.core.aggregation.config.flow.FlowContext;
import com.yeepay.g3.core.aggregation.config.flow.node.Node;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/5/27 17:59
 */
@Slf4j
public class ConditionalNode implements Node {
    private final Node delegate;
    private final ContextMatcher matcher;

    public ConditionalNode(Node delegate, ContextMatcher matcher) {
        this.delegate = delegate;
        this.matcher = matcher;
    }

    @Override
    public void execute(final FlowContext context) {
        if (matcher.match(context)) {
            delegate.execute(context);
        }
    }

    @Override
    public Node combine(final Node after) {
        return new ConditionalNode(delegate.combine(after), matcher);
    }
}
