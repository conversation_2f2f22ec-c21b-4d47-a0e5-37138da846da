package com.yeepay.g3.core.aggregation.config.mq.listener;

import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.TerminalReportBiz;
import com.yeepay.g3.core.aggregation.config.mq.event.TerminalReportMessage;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 网点终端报备回调
 * TerminalReportCallBackMessageListener
 */
@Component
public class TerminalReportCallBackListener implements MessageListenerConcurrently {
    private static final Logger LOGGER = LoggerFactory.getLogger(TerminalReportCallBackListener.class);
    private final int maxConsumeCount = 10;

    @Resource
    private TerminalReportBiz terminalReportBiz;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        LogGuidUtil.clearLogContext();
        MessageExt msg = list.get(0);
        int reconsumeTimes = msg.getReconsumeTimes();
        String message = new String(msg.getBody());
        try {
            LOGGER.info("terminalReportCallBackMessageListener接收到了消息[{}]，msgId={}", message, msg.getMsgId());
            TerminalReportMessage callBackBO = JsonUtils.fromJson(message, TerminalReportMessage.class);
            terminalReportBiz.callBackTerminalReport(callBackBO);
        } catch (Exception e) {
            if (reconsumeTimes == maxConsumeCount) {
                LOGGER.error("网点终端报备回调失败,已达最大重试次数,mq不会再进行重试,message=" + message, e);
            } else {
                LOGGER.error("网点终端报备回调失败,mq稍后会进行重试,message=" + message, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
