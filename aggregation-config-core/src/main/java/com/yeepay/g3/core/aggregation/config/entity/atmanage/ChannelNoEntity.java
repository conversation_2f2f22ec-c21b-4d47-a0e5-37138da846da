package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoCreateBO;
import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * description: 商户渠道号信息实体类
 * <AUTHOR>
 * @since 2025/6/2:23:38
 * Company: 易宝支付(YeePay)
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Table(name = "TBL_MERCHANT_CHANNEL_NO")
public class ChannelNoEntity extends BaseEntity {

    /**
     * 商户编码
     */
    @Column(name = "MERCHANT_NO", length = 32)
    private String merchantNo;

    /**
     * 渠道号
     */
    @Column(name = "CHANNEL_NO", length = 32)
    private String channelNo;

    /**
     * 申请单号
     */
    @Column(name = "APPLY_NO", length = 64)
    private String applyNo;

    /**
     * 机构编码(WECHAT、ALIPAY)
     */
    @Column(name = "PAY_CHANNEL", length = 16)
    private String payChannel;

    /**
     * 渠道机构编码(YP：易宝、HK：海科)
     */
    @Column(name = "CHANNEL_INSTITUTION_CODE", length = 16)
    private String channelInstitutionCode;

    /**
     * 支付场景
     */
    @Column(name = "PAY_SCENE", length = 8)
    private String payScene;

    /**
     * 活动类型
     */
    @Column(name = "ACTIVITY_TYPE", length = 16)
    private String activityType;

    /**
     * 状态
     */
    @Column(name = "STATUS", length = 16)
    private String status;


    public static ChannelNoEntity build(ChannelNoCreateBO createBO) {
        ChannelNoEntity entity = ChannelNoEntity.builder().build();
        entity.setMerchantNo(createBO.getMerchantNo());
        entity.setChannelNo(createBO.getChannelNo());
        entity.setApplyNo(createBO.getApplyNo());
        entity.setPayChannel(createBO.getChannelType().getDocument());
        entity.setChannelInstitutionCode(createBO.getInstituteType());
        entity.setPayScene(createBO.getPayScene().getDocument());
        entity.setActivityType(createBO.getPromotionType().getDocument());
        entity.setCreatedBy(createBO.getApplyNo());
        entity.setCreateDt(LocalDateTime.now());
        entity.setUpdatedBy(createBO.getApplyNo());
        return entity;
    }
}
