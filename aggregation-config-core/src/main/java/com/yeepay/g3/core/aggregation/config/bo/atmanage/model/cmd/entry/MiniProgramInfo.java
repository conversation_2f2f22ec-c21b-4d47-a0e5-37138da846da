package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.MiniProgramInfoDTO;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 21:13
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class MiniProgramInfo implements Serializable {

    /**
     * 【服务商小程序AppID】 选填 string(256)
     * 1、服务商小程序AppID与商家小程序AppID，二选一必填；
     * 2、可填写当前服务商商户号已绑定的小程序AppID。
     */
    private final String miniProgramAppId;

    /**
     * 【商家小程序AppID】 选填 string(256)
     * 微信B2B必填
     * 1、服务商小程序AppID与商家小程序AppID，二选一必填；
     * 2、请填写已认证的小程序AppID；
     * 3、完成进件后，系统发起特约商户号与该AppID的绑定（即配置为sub_appid可在发起支付时传入）
     * （1）若AppID主体与商家主体/服务商主体一致，则直接完成绑定；
     * （2）若AppID主体与商家主体/服务商主体不一致，则商户签约时显示《联合营运承诺函》，
     * 并且AppID的管理员需登录公众平台确认绑定意愿。
     */
    private final String miniProgramSubAppId;

    /**
     * 【小程序名称】
     */
    private final String miniProgramName;


    /**
     * 【小程序截图】 选填 array[string(1024)]
     * 微信直连，请提供展示商品/服务的页面截图/设计稿（最多5张），若小程序未建设完善或未上线，则必填
     * 微信小程序B2B必填，一张即可
     */
    private final List<String> miniProgramPics;

    /**
     * 确认订单付款界面截图
     * 微信小程序B2B必填
     */
    private final String orderPayPic;

    /**
     * 小程序-方案概述
     * 微信小程序B2B，必填，长度限制：21-100字
     */
    private final String miniProgramDesc;

    /**
     * 【小程序密钥】 选填 string(1024)
     * 微信小程序B2B，必填，请填写小程序的密钥
     */
    private final String miniProgramAppSecret;

    public static MiniProgramInfo build(MiniProgramInfoDTO miniProgramInfo) {
        return MiniProgramInfo.builder()
                .miniProgramAppId(miniProgramInfo.getMiniProgramAppId())
                .miniProgramSubAppId(miniProgramInfo.getMiniProgramSubAppId())
                .miniProgramName(miniProgramInfo.getMiniProgramAppName())
                .miniProgramPics(miniProgramInfo.getMiniProgramPics())
                .build();
    }

    public static MiniProgramInfo buildWechatB2BMiniProgramInfo(MiniProgramInfoDTO miniProgramInfo) {
        //Assert.notNull(miniProgramInfo.getOrderPayPic(), ResultCode.PARAM_VALID_ERROR, "微信B2B场景确认订单付款界面截图不能为空");
        //Assert.notEmpty(miniProgramInfo.getMiniProgramPics(), ResultCode.PARAM_VALID_ERROR, "微信B2B场景小程序截图不能为空");
        Assert.notNull(miniProgramInfo.getMiniProgramSubAppId(), ResultCode.PARAM_VALID_ERROR, "微信B2B场景小程序AppID不能为空");
        Assert.notNull(miniProgramInfo.getMiniProgramAppSecret(), ResultCode.PARAM_VALID_ERROR, "微信B2B场景小程序密钥不能为空");
        Assert.notNull(miniProgramInfo.getDescription(), ResultCode.PARAM_VALID_ERROR, "微信B2B场景小程序-方案概述不能为空");
        return MiniProgramInfo.builder()
                .miniProgramAppId(miniProgramInfo.getMiniProgramAppId())
                .miniProgramSubAppId(miniProgramInfo.getMiniProgramSubAppId())
                .miniProgramName(miniProgramInfo.getMiniProgramAppName())
                .miniProgramPics(miniProgramInfo.getMiniProgramPics())
                .orderPayPic(miniProgramInfo.getOrderPayPic())
                .miniProgramDesc(miniProgramInfo.getDescription())
                .miniProgramAppSecret(miniProgramInfo.getMiniProgramAppSecret())
                .build();
    }
}
