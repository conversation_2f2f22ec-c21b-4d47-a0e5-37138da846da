package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 进件申请场景子状态枚举
 * <AUTHOR>
 * @since 2025/6/15:22:44
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum EntryApplyMainStatus implements DocumentedEnum<String> {
    INIT("INIT", "初始化"),
    PROCESSING("PROCESSING", "处理中"),
    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    ;
    private final String document;
    private final String desc;
}
