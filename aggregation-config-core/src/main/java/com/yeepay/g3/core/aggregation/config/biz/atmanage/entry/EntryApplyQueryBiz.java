
package com.yeepay.g3.core.aggregation.config.biz.atmanage.entry;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryApplyResultReqBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryResultRespBO;

/**
 * 进件申请查询服务
 * <AUTHOR>
 */
public interface EntryApplyQueryBiz {

    /**
     * 查询商户聚合进件+ 挂靠结果
     * 综合所有请求的当前结果+历史结果
     *
     * @param reqBO
     * @return
     */
    QueryEntryResultRespBO queryMerchantEntryResult(QueryEntryApplyResultReqBO reqBO);
}
