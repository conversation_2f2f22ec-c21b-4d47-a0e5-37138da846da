package com.yeepay.g3.core.aggregation.config.external.impl;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.core.aggregation.config.common.ExternalSystemEnum;
import com.yeepay.g3.core.aggregation.config.common.MessageConst;
import com.yeepay.g3.core.aggregation.config.common.RemoteProxyFactory;
import com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.core.aggregation.config.utils.ConfigUtil;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.business.manage.dto.request.merchant.QueryMerchantInfoFromFmcReqDTO;
import com.yeepay.g3.facade.business.manage.dto.response.merchant.MerchantInfoFromFmcRespDTO;
import com.yeepay.g3.facade.business.manage.facade.merchant.MerchantInfoFacade;
import com.yeepay.g3.facade.merchant_platform.dto.BaseReq;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.TagBaseDTO;
import com.yeepay.g3.facade.merchant_platform.dto.TagQueryRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.QueryMerchantGroupRespDto;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.QueryTagReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customerplatform.QueryMerchantGroupReqDto;
import com.yeepay.g3.facade.merchant_platform.enumtype.customerManagement.MerchantTagValueEnum;
import com.yeepay.g3.facade.merchant_platform.facade.MerchantFacade;
import com.yeepay.g3.facade.merchant_platform.facade.TagFacade;
import com.yeepay.g3.facade.merchant_platform.facade.customermanagement.CustomerMerchantInfoFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description: 客户中心外部领域 实现层
 * @author: xuchen.liu
 * @date: 2024-12-14 20:05
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class MerchantCenterExternalImpl implements MerchantCenterExternal {

    /**
     * 调用客户中心系统名称
     */
    private static final String USER_CENTER_SYSTEM = "aggregation-pay";

    /**
     * 降级查询CODE
     */
    private static final String DEGRADED_QUERY_CODE = "8029";

    /**
     * 降级查询成功CODE
     */
    private static final String SUCCESS_CODE = "BMS00000";

    /**
     * 调用客户中心CharSet
     */
    private static final String USER_CENTER_CHARSET = "UTF-8";

    /**
     * 统一配置客户中心UID键
     */
    private static final String USER_CENTER_UID = "AGGREGATION_PAY_USER_CENTER_UID_KEY";

    /**
     * 客户中心UID本地默认值
     */
    private static final String USER_CENTER_UID_DEFAULT = "4d4dd8c7ebe24ece99b12f8ee344023f";

    /**
     * 调用客户中心产品开通查询及校验接口的成功响应码
     */
    private static final String CUS_SUCCESS_CODE = "0000";

    @Override
    public String getIndustryLineByTopMerchant( String merchantNo) {
        TagFacade tagFacade = RemoteProxyFactory.getService(TagFacade.class, ExternalSystemEnum.MERCHANTCENTER);
        TagQueryRespDTO respDto;
        String industryLine = MessageConst.MESSAGE;
        try {
            QueryTagReqDTO reqDto = new QueryTagReqDTO();
            buildCommonReqDto(reqDto);
            reqDto.setMerchantNo(merchantNo);
            respDto = tagFacade.queryTagByMerchantNo(reqDto);
            if (respDto == null) {
                log.error("查询商户行业线信息异常返回结果为空 merchantNo:{}",merchantNo);

            }
            if (!CUS_SUCCESS_CODE.equalsIgnoreCase(respDto.getRetCode())) {
                log.error("查询商户行业线信息异常返回结果为空 merchantNo:{}",merchantNo);
            }
            List<TagBaseDTO> tagList = respDto.getTagList();
            if(CollectionUtils.isNotEmpty(tagList)){
                industryLine = tagList.get(0).getTag();
            }

        } catch (Throwable e) {
            log.error("查询商户行业线信息异常 merchantNo:"+merchantNo, e);
        }

        return industryLine;
    }
    @Override
    public MerchantGroupEntity queryMerchantGroupByMerchantNo(String merchantNo) {
        MerchantGroupEntity merchantGroupEntity = new MerchantGroupEntity();
        //1 查询客户中心 获取是中台还是非中台   非中台用顶级商编做一级商编
        //2 查询客户中心 中台配
        MerchantRespDTO respDto;
        MerchantFacade merchantFacade = RemoteProxyFactory.getService(MerchantFacade.class,
                ExternalSystemEnum.MERCHANTCENTER);
        MerchantReqDTO merchantReqDTO = new MerchantReqDTO();
        buildCommonReqDto(merchantReqDTO);
        merchantReqDTO.setMerchantNo(merchantNo);
        try {
        log.info("查询商户中台还是非中台 信息入参：{}",merchantReqDTO);
         respDto = merchantFacade.getMerchantUsingCache(merchantReqDTO);
        log.info("查询商户中台还是非中台 信息返回结果：{}",respDto);
        } catch (Throwable e) {
            log.error("查询商户中台还是非中台 信息异常 merchantNo:"+merchantNo, e);
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_GROUP_ERROR);
        }

        // 接口调用是否成功
        if (respDto == null) {
            log.warn("查询商户中台还是非中台返回结果为空 merchantNo:{}",merchantNo);
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_GROUP_ERROR);
        }

        if ("2005".equalsIgnoreCase(respDto.getRetCode())) {
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_NOT_ERROR, respDto.getRetMsg());
        }

        if (!CUS_SUCCESS_CODE.equalsIgnoreCase(respDto.getRetCode())) {
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_GROUP_ERROR, respDto.getRetMsg());
        }


        if(MerchantTagValueEnum.M3gA3g.equals(respDto.getMerchantTag())){
             merchantGroupEntity=  queryMerchantGroupByMerchantNoByMiddle(merchantNo);
        }else{
            //如果topParentNo 没有或者等于本身  一级就是本身  如果不是 一级是topParentNo 二级是本身

            if(StringUtils.isBlank(respDto.getTopParentNo())|| merchantNo.equals(respDto.getTopParentNo())){
                merchantGroupEntity.getGroup().add(merchantNo);
            }else{
                merchantGroupEntity.getGroup().add(respDto.getTopParentNo());
                merchantGroupEntity.getGroup().add(merchantNo);
            }
            merchantGroupEntity.setMerchantName(respDto.getSignName());
            merchantGroupEntity.setMerchantNo(merchantNo);

        }

        return merchantGroupEntity;

    }



    public MerchantGroupEntity queryMerchantGroupByMerchantNoByMiddle(String merchantNo) {

        CustomerMerchantInfoFacade customerMerchantInfoFacade = RemoteProxyFactory.getService(CustomerMerchantInfoFacade.class,
                ExternalSystemEnum.MERCHANTCENTER);
        QueryMerchantGroupRespDto respDto;
        try {
            QueryMerchantGroupReqDto reqDto = new QueryMerchantGroupReqDto();
            buildCommonReqDto(reqDto);
            reqDto.setMerchantNo(merchantNo);
            log.info("查询商户分组信息入参：{}",reqDto);
            respDto = customerMerchantInfoFacade.queryMerchantGroup(reqDto);
            log.info("查询商户分组信息返回结果：{}",respDto);
        } catch (Throwable e) {
            log.error("查询商户分组信息异常 merchantNo:"+merchantNo, e);
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_GROUP_ERROR);
        }
        // 接口调用是否成功
        if (respDto == null) {
            log.warn("调用客户中心查询分组信息返回结果为空 merchantNo:{}",merchantNo);
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_GROUP_ERROR);
        }

        if ("2005".equalsIgnoreCase(respDto.getRetCode())) {
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_NOT_ERROR, respDto.getRetMsg());
        }

        if (!CUS_SUCCESS_CODE.equalsIgnoreCase(respDto.getRetCode()) && !DEGRADED_QUERY_CODE.equalsIgnoreCase(respDto.getRetCode())) {
            throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_GROUP_ERROR, respDto.getRetMsg());
        }
        // 降级查询
        if (DEGRADED_QUERY_CODE.equalsIgnoreCase(respDto.getRetCode())) {
            MerchantInfoFromFmcRespDTO merchantInfoFromFmcRespDTO = this.queryMerchantInfoFromFmc(merchantNo);
            return this.getMerchantGroupEntity(merchantInfoFromFmcRespDTO.getTopMerchantNo(), merchantInfoFromFmcRespDTO.getParentMerchantNo(), merchantInfoFromFmcRespDTO.getMerchantNo(), merchantInfoFromFmcRespDTO.getSignName());
        } else {
            return this.getMerchantGroupEntity(respDto.getFirstParentNo(), respDto.getSecondParentNo(), respDto.getMerchantNo(), respDto.getMerchantName());
        }
    }

    /**
     * @description 降级查询一二三级商编
     * <AUTHOR>
     * @date 17:58 2025/1/17
     * @param merchantNo
     * @return com.yeepay.g3.facade.business.manage.dto.response.merchant.MerchantInfoFromFmcRespDTO
     */
    public MerchantInfoFromFmcRespDTO queryMerchantInfoFromFmc(String merchantNo) {
        MerchantInfoFacade merchantInfoFacade = RemoteProxyFactory.getService(MerchantInfoFacade.class, ExternalSystemEnum.MERCHANTCENTER);
        try {
            QueryMerchantInfoFromFmcReqDTO reqDTO = new QueryMerchantInfoFromFmcReqDTO();
            reqDTO.setMerchantNo(merchantNo);
            log.info("queryMerchantInfoFromFmc入参：{}", JSON.toJSONString(reqDTO));
            MerchantInfoFromFmcRespDTO respDTO = merchantInfoFacade.queryMerchantInfoFromFmc(reqDTO);
            log.info("queryMerchantInfoFromFmc回参：{}", JSON.toJSONString(respDTO));
            if (respDTO == null || !SUCCESS_CODE.equalsIgnoreCase(respDTO.getReturnCode())) {
                throw new AggConfigException(ErrorCodeEnum.QUERY_MERCHANT_GROUP_ERROR);
            }
            return respDTO;
        } catch (Exception e) {
            log.error("queryMerchantInfoFromFmc异常", e);
            throw e;
        }
    }

    /**
     * @description 构建一二三级商编, 如果一级商编没有那么自己就是一级,如果二级没有，他自己就是二级
     *
     * <AUTHOR>
     * @date 17:49 2025/1/17
     * @param firstParentNo
     * @param secondParentNo
     * @param merchantNo
     * @param merchantName
     * @return com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity
     */
    private MerchantGroupEntity getMerchantGroupEntity(String firstParentNo, String secondParentNo, String merchantNo, String merchantName) {
        MerchantGroupEntity merchantGroupEntity = new MerchantGroupEntity();
        merchantGroupEntity.setMerchantName(merchantName);
        merchantGroupEntity.setMerchantNo(merchantNo);
        if (StringUtils.isBlank(firstParentNo)) {
            merchantGroupEntity.getGroup().add(merchantNo);
            return merchantGroupEntity;
        } else if (StringUtils.isBlank(secondParentNo)) {
            merchantGroupEntity.getGroup().add(firstParentNo);
            merchantGroupEntity.getGroup().add(merchantNo);
            return merchantGroupEntity;
        } else {
            merchantGroupEntity.getGroup().add(firstParentNo);
            merchantGroupEntity.getGroup().add(secondParentNo);
            merchantGroupEntity.getGroup().add(merchantNo);
            return merchantGroupEntity;
        }
    }

    private void buildCommonReqDto(BaseReq merchantProductReqDTO) {
        // 新客户平台给每个系统分配的uid
        merchantProductReqDTO.setUid(ConfigUtil.getSysConfigFrom3G(USER_CENTER_UID, USER_CENTER_UID_DEFAULT));
        // 系统在新客户平台的业务方标识，一个system对应一个uid，用来做权限控制
        merchantProductReqDTO.setSystem(USER_CENTER_SYSTEM);
        merchantProductReqDTO.setCharSet(USER_CENTER_CHARSET);
        merchantProductReqDTO.setReqTime(new Date());
    }
}
