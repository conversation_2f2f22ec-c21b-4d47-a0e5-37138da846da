package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;

import java.util.List;

/**
 * @description: 挂靠结果服务层
 * @author: xuchen.liu
 * @date: 2024-12-13 15:15
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface AttachResultService {

    /**
     *  根据唯一主键查询
     * @param attachResultEntity
     * @return com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity
     * <AUTHOR>
     * @date 2024/12/17 17:31
     */
    AttachResultEntity  queryByUniqueKey(AttachResultEntity attachResultEntity);

    /**
     * 补充挂靠实体
     * @param attachResultEntity
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 17:51
     */
    void insert(AttachResultEntity attachResultEntity);


    /**
     * 更新
     *
     * @param attachResultEntity
     * @param dataCleanStatus
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 18:07
     */
    int update(AttachResultEntity attachResultEntity,DataCleanStatusEnum dataCleanStatus);

    /**
     * 根据商编查询挂靠结果列表
     *
     * @param merchantNo 商户编号
     * @param used
     * @return 挂靠结果
     */
    List<AttachResultEntity> queryByWechatAttachResult(String merchantNo, DataCleanStatusEnum cleanStatus);

    /**
     * 更新数据清洗状态
     *
     * @param merchantNo
     * @param dataCleanStatus
     * @return
     */
    Boolean  updateClearStatus(String merchantNo, DataCleanStatusEnum dataCleanStatus, DataCleanStatusEnum oldClearStatus);
}
