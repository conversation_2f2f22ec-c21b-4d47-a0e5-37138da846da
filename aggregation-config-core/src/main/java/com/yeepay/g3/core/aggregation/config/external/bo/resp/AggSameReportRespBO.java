package com.yeepay.g3.core.aggregation.config.external.bo.resp;

import com.yeepay.g3.facade.aggregation.pay.dto.AttachSameReportRecordDTO;
import lombok.Data;

import java.util.Date;

/**
 * @author: Mr.yin
 * @date: 2025/6/8  19:10
 */
@Data
public class AggSameReportRespBO {
    /**
     * 商编
     */
    private String merchantNo;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 场景
     */
    private String payScene;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 挂靠商编
     */
    private String anchoredMerchantNo;
    
    /**
     * 挂靠商编的渠道号
     */
    private String anchoredChannelNo;

    /**
     * 挂靠商编的报备商户号
     */
    private String institutionMchNo;

    /**
     * 挂靠状态
     * 此处底层都是挂靠成功的
     */
    private String attachStatus;

    /**
     * 挂靠成功时间
     */
    private Date attachSuccessTime;
    
    public static AggSameReportRespBO covert(AttachSameReportRecordDTO attachRecord) {
        AggSameReportRespBO aggSameReportRespBO = new AggSameReportRespBO();
        aggSameReportRespBO.setMerchantNo(attachRecord.getMerchantNo());
        aggSameReportRespBO.setChannelType(attachRecord.getChannel());
        aggSameReportRespBO.setPayScene(attachRecord.getScene());
        aggSameReportRespBO.setActivityType(attachRecord.getPromotionType());
        aggSameReportRespBO.setAnchoredChannelNo(attachRecord.getMaskChannelNo());
        aggSameReportRespBO.setInstitutionMchNo(attachRecord.getMaskReportMerchantNo());
        aggSameReportRespBO.setAnchoredMerchantNo(attachRecord.getMaskMerchantNo());
        aggSameReportRespBO.setAttachStatus(attachRecord.getAttachStatus());
        aggSameReportRespBO.setAttachSuccessTime(attachRecord.getAttachSuccessTime());
        return aggSameReportRespBO;
    }
    
}
