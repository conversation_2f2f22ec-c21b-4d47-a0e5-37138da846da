package com.yeepay.g3.core.aggregation.config.enums;

/**
 * <AUTHOR>
 * @date 2025/6/9 23:40
 */
public interface BitEnum {
    long getBit();

    /**
     * 判断指定的组合值是否包含该枚举项
     *
     * @param combined 组合后的整数值
     * @return true 表示包含该枚举项
     */
    default boolean isSet(long combined) {
        return (combined & this.getBit()) == this.getBit();
    }


    /**
     * 将多个 BitEnum 枚举项组合
     *
     * @param enums 要组合的 BitEnum 枚举数组
     * @return 组合后的整数值
     */
    static long combine(BitEnum... enums) {
        long result = 0;
        for (BitEnum e : enums) {
            result |= e.getBit();
        }
        return result;
    }

    /**
     * 将指定的标志添加到组合值中
     */
    static long addFlag(long combined, BitEnum flag) {
        return combined | flag.getBit();
    }

    /**
     * 从组合值中移除指定的标志
     */
    static long removeFlag(long combined, BitEnum flag) {
        return combined & ~flag.getBit();
    }

    /**
     * 判断指定的组合值是否包含某个 BitEnum 枚举项
     *
     * @param combined 组合后的flag
     * @param flag     要判断的 BitEnum 枚举项
     * @return true 表示包含该枚举项
     */
    static boolean hasFlag(long combined, BitEnum flag) {
        return flag.isSet(combined);
    }

}
