package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.UboInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/8 14:42
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@SuperBuilder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class WechatDirectEntrySpecialParams implements ChannelSpecialParams {

    /**
     * 经营者/法人是否为受益人
     */
    private final Boolean isBenefitPerson;

    /**
     * 受益人列表
     */
    private final List<UboInfoBO> uboInfos;


    private final SettleAccountInfo settleAccountInfo;

    /**
     * 超级管理员信息
     */
    private final SuperAdminInfo superAdminInfo;

    /**
     * 经营资料
     */
    private final SalesScene salesScene;

    /**
     * 活动信息
     */
    private final ActivityInfo activityInfo;

    /**
     * 补充说明
     */
    private final WxDirectConfigCmd.AdditionInfo additionInfo;

    private String signUrl;

    private final String chargeType;

    private final String extendInfo;

    public static WechatDirectEntrySpecialParams build(WxDirectConfigCmd wxDirectConfigCmd) {
        return WechatDirectEntrySpecialParams.builder()
                .isBenefitPerson(wxDirectConfigCmd.getIsBenefitPerson())
                .uboInfos(wxDirectConfigCmd.getUboInfos())
                .settleAccountInfo(wxDirectConfigCmd.getSettleAccountInfo())
                .superAdminInfo(wxDirectConfigCmd.getSuperAdminInfo())
                .salesScene(wxDirectConfigCmd.getSalesScene())
                .additionInfo(wxDirectConfigCmd.getAdditionInfo())
                .chargeType(wxDirectConfigCmd.getChargeType())
                .extendInfo(wxDirectConfigCmd.getExtendInfo())
                .activityInfo(wxDirectConfigCmd.getActivityInfo())
                .build();
    }

    public void signUrl(final String signUrl) {
        this.signUrl = signUrl;
    }
}
