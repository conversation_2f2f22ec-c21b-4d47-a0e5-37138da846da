package com.yeepay.g3.core.aggregation.config.utils;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.gmcrypt.utils.SMUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 国密加密
 */
public class GMUtils {

    private static final Logger logger = LoggerFactory.getLogger(GMUtils.class);

    /**
     * 加密
     */
    public static String encrypt(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        try {
            return SMUtils.encrypt(content);
        } catch (Exception e) {
            logger.error("国密加密失败，加密的数据=" + content, e);
            return content;
        }
    }

    /**
     * 解密
     */
    public static String decrypt(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        try {
            return SMUtils.decrypt(content);
        } catch (Exception e) {
            logger.error("国密解密失败，解密的数据=" + content, e);
            return content;
        }
    }

}
