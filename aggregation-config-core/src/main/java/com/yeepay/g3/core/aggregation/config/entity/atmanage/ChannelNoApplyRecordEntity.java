package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.ChannelNoApplyStatusEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * description: 商户渠道号申请记录实体类
 * <AUTHOR>
 * @since 2025/6/2:23:38
 * Company: 易宝支付(YeePay)
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Table(name = "TBL_CHANNEL_NO_APPLY_RECORD")
public class ChannelNoApplyRecordEntity extends BaseEntity {

    /**
     * 商户编码
     */
    @Column(name = "MERCHANT_NO", length = 16)
    private String merchantNo;

    /**
     * 商户检查
     */
    @Column(name = "MERCHANT_SHORT_NAME", length = 128)
    private String merchantShortName;

    /**
     * 业务场景
     */
    @Column(name = "BIZ_SCENE", length = 16)
    private String bizScene;

    /**
     * 业务申请单号
     */
    @Column(name = "BIZ_APPLY_NO", length = 32)
    private String bizApplyNo;

    /**
     * 业务单号
     */
    @Column(name = "BIZ_ORDER_NO", length = 32)
    private String bizOrderNo;

    /**
     * 申请单号
     */
    @Column(name = "APPLY_NO", length = 32)
    private String applyNo;

    /**
     * 机构编码(WECHAT、ALIPAY)
     */
    @Column(name = "PAY_CHANNEL", length = 16)
    private String payChannel;

    /**
     * 渠道号
     */
    @Column(name = "CHANNEL_NO", length = 32)
    private String channelNo;

    /**
     * 渠道标识
     */
    @Column(name = "CHANNEL_IDENTIFIER", length = 32)
    private String channelIdentifier;

    /**
     * 支付场景
     */
    @Column(name = "PAY_SCENE", length = 16)
    private String payScene;

    /**
     * 活动类型
     */
    @Column(name = "ACTIVITY_TYPE", length = 32)
    private String activityType;

    /**
     * 状态
     */
    @Column(name = "STATUS", length = 16)
    private String status;

    /**
     * 销售名称
     */
    @Column(name = "SALES_NAME", length = 64)
    private String salesName;

    /**
     * 行业名称
     */
    @Column(name = "INDUSTRY_NAME", length = 64)
    private String industryName;

    /**
     * 是否使用易宝渠道号
     */
    @Column(name = "IS_USE_YP_CHANNEL_NO")
    private String isUseYpChannelNo;

    /**
     * 资金清算类型
     */
    @Column(name = "FUND_LIQUIDATION_TYPE", length = 16)
    private String fundLiquidationType;

    /**
     * 客服电话
     */
    @Column(name = "SERVICE_PHONE", length = 32)
    private String servicePhone;

    /**
     * 数币银行代码
     */
    @Column(name = "DIGITAL_CURRENCY_BANK_CODE", length = 16)
    private String digitalCurrencyBankCode;

    /**
     * 联系人信息
     */
    @Column(name = "CONTACT_INFO", length = 1024)
    private String contactInfo;

    /**
     * 公司经办人/法人信息
     */
    @Column(name = "COMPANY_REPRESENTATIVE_INFO", length = 1024)
    private String companyRepresentativeInfo;

    /**
     * 证书信息
     */
    @Column(name = "CERTIFICATE_INFO", length = 1024)
    private String certificateInfo;

    /**
     * 结算账户信息
     */
    @Column(name = "SETTLE_ACCOUNT_INFO", length = 1024)
    private String settleAccountInfo;

    /**
     * 扩展信息
     */
    @Column(name = "EXTERNAL_INFO", length = 1024)
    private String externalInfo;

    /**
     * 申请结果描述
     */
    @Column(name = "APPLY_RESULT_DESC", length = 1024)
    private String applyResultDesc;

    public static ChannelNoApplyRecordEntity build(ChannelNoApplyBO channelNoApplyBO) {
        String applyNo = Const.MERCHANT_CHANNEL_APPLY_ORDER_PREFIX + UniqueNoGenerateUtils.getUniqueNo();
        ChannelNoApplyRecordEntity entity = ChannelNoApplyRecordEntity.builder().build();
        entity.setMerchantNo(channelNoApplyBO.getMerchantNo());
        entity.setMerchantShortName(channelNoApplyBO.getShortName());
        entity.setBizScene(channelNoApplyBO.getBizScene().getDocument());
        entity.setBizApplyNo(channelNoApplyBO.getBizApplyNo());
        entity.setBizOrderNo(channelNoApplyBO.getBizOrderNo());
        entity.setApplyNo(applyNo);
        entity.setPayChannel(channelNoApplyBO.getPayChannel().getDocument());
        entity.setChannelIdentifier(channelNoApplyBO.getMerchantNo());
        entity.setPayScene(channelNoApplyBO.getPayScene().getDocument());
        entity.setActivityType(channelNoApplyBO.getActivityType().getDocument());
        entity.setStatus(ChannelNoApplyStatusEnum.WAIT_SUBMIT.getDocument());
        entity.setSalesName(channelNoApplyBO.getSalesName());
        entity.setIndustryName(channelNoApplyBO.getIndustryName());
        entity.setIsUseYpChannelNo(channelNoApplyBO.getIsUseYpChannelNo());
        entity.setFundLiquidationType(channelNoApplyBO.getFundLiquidationType());
        entity.setServicePhone(channelNoApplyBO.getServicePhone());
        entity.setDigitalCurrencyBankCode(channelNoApplyBO.getDigitalCurrencyBankCode());
        entity.setContactInfo(JsonUtils.toJSONString(channelNoApplyBO.getSuperAdminInfo()));
        entity.setCompanyRepresentativeInfo(JsonUtils.toJSONString(channelNoApplyBO.getCompanyRepresentativeInfo()));
        entity.setCertificateInfo(JsonUtils.toJSONString(channelNoApplyBO.getCertificateInfo()));
        entity.setSettleAccountInfo(JsonUtils.toJSONString(channelNoApplyBO.getSettleAccount()));
        entity.setExternalInfo(channelNoApplyBO.getExternalInfo());
        entity.setCreatedBy(Const.SYSTEM);
        entity.setUpdatedBy(Const.SYSTEM);
        entity.setCreateDt(LocalDateTime.now());
        return entity;
    }

    public static ChannelNoApplyRecordEntity buildUpdateStatus(String applyNo, ChannelNoApplyStatusEnum channelStatus, ChannelNoApplyResultBO result) {
        ChannelNoApplyRecordEntity channelApplyEntity = ChannelNoApplyRecordEntity.builder().build();
        channelApplyEntity.setApplyNo(applyNo);
        channelApplyEntity.setStatus(channelStatus.getDocument());
        channelApplyEntity.setChannelNo(result.getChannelNo());
        channelApplyEntity.setApplyResultDesc(result.getApplyResultDesc());
        return channelApplyEntity;
    }
}
