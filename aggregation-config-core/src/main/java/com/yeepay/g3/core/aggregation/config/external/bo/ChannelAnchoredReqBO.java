package com.yeepay.g3.core.aggregation.config.external.bo;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import lombok.Data;

/**
 * 查询渠道商户的进件结果
 *
 * @author: Mr.yin
 * @date: 2025/6/4  20:23
 */
@Data
public class ChannelAnchoredReqBO {
    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 挂靠商户编号
     */
    private String anchoredMerchantNo;

    /**
     * 场景
     */
    private PayChannelEnum channelType;


    /**
     * 场景
     */
    private String payScene;

    /**
     * 活动类型
     */
    private String activityType;
}
