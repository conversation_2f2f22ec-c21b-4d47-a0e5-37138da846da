package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * description: 聚合记录实体类
 * <AUTHOR>
 * @since 2025/5/21:15:07
 * Company: 易宝支付(YeePay)
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Table(name = "TBL_THIRD_PARTY_RECORD")
public class ThirdPartyRecordEntity extends BaseEntity {

    /**
     * 申请编号
     */
    @Column(name = "APPLY_NO", length = 32)
    private String applyNo;

    /**
     * 请求编号
     */
    @Column(name = "REQUEST_NO", length = 32)
    private String requestNo;

    /**
     * 业务类型
     */
    @Column(name = "BUSINESS_TYPE", length = 32)
    private String businessType;

    /**
     * 状态
     * INIT - 初始化
     * SUCCESS - 成功
     * FAIL - 失败
     * PROCESSING - 处理中
     */
    @Column(name = "STATUS", length = 16)
    private String status;


    @Column(name = "FINISH_TIME")
    private LocalDateTime finishTime;

    /**
     * 内部渠道申请编号
     */
    @Column(name = "INNER_CHANNEL_APPLY_NO", length = 32)
    private String innerChannelApplyNo;

    /**
     * 渠道申请编号
     */
    @Column(name = "CHANNEL_APPLY_NO", length = 32)
    private String channelApplyNo;

    /**
     * 渠道响应码
     */
    @Column(name = "CHANNEL_RESPONSE_CODE", length = 16)
    private String channelResponseCode;

    /**
     * 渠道响应消息
     */
    @Column(name = "CHANNEL_RESPONSE_MESSAGE", length = 256)
    private String channelResponseMessage;
}

