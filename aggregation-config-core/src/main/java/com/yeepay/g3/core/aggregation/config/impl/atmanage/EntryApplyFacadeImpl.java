package com.yeepay.g3.core.aggregation.config.impl.atmanage;

import com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.EntryApplyBiz;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.EntryApplyQueryBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryAndAnchoredDetailBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryResultRespBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryApplyResultReqBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryApplyInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryOrder;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.EntryApplyCmd;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryOrderConvert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.EntryApplyRequest;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.QueryEntryAndAnchoredRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.EntryApplyDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.QueryEntryAndAnchoredResponseDTO;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.EntryApplyFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: 描述
 *
 * <AUTHOR>
 * @since 2025/6/11:17:44
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EntryApplyFacadeImpl implements EntryApplyFacade {

    private final EntryApplyQueryBiz entryApplyQueryBiz;
    private final EntryApplyBiz entryApplyBiz;

    @Override
    public EntryApplyDTO entryApply(final EntryApplyRequest entryApplyRequest) {
        final EntryApplyCmd entryApplyCmd = EntryApplyCmd.create(entryApplyRequest);
        final EntryOrder entryOrder = entryApplyBiz.handle(entryApplyCmd);
        return ChannelEntryOrderConvert.convertToDTO(entryOrder);
    }

    @Override
    public QueryEntryAndAnchoredResponseDTO queryMerchantEntryAndAnchoredInfo(QueryEntryAndAnchoredRequestDTO reqDTO) {
        QueryEntryApplyResultReqBO reqBO = QueryEntryApplyResultReqBO.build(reqDTO);
        QueryEntryResultRespBO queryEntryResultRespBO = entryApplyQueryBiz.queryMerchantEntryResult(reqBO);
        QueryEntryAndAnchoredResponseDTO responseDTO = new QueryEntryAndAnchoredResponseDTO();
        responseDTO.setMerchantNo(reqBO.getMerchantNo());
        responseDTO.setEntityApplyInfo(QueryEntryApplyInfoBO.buildDTO(queryEntryResultRespBO.getEntryApplyInfo()));
        responseDTO.setEntryDetailList(QueryEntryAndAnchoredDetailBO.buildList(queryEntryResultRespBO.getEntryDetailList()));
        return responseDTO;
    }
}
