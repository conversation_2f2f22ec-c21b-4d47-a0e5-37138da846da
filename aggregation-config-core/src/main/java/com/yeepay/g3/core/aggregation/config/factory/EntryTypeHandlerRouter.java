package com.yeepay.g3.core.aggregation.config.factory;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.g3.core.aggregation.config.annotation.EntryApplyProcess;
import com.yeepay.g3.core.aggregation.config.service.EntryOrderHandler;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/6/8 18:56
 */
@Component
public class EntryTypeHandlerRouter implements InitializingBean {

    private final Map<EntryTypeEnum, EntryOrderHandler> handlerMap = new ConcurrentHashMap<>();

    private final List<EntryOrderHandler> handlers;

    public EntryTypeHandlerRouter(List<EntryOrderHandler> handlers) {
        this.handlers = handlers;
    }

    @Override
    public void afterPropertiesSet() {
        for (EntryOrderHandler handler : handlers) {
            Class<?> handlerClass = handler.getClass();
            if (handlerClass.isAnnotationPresent(EntryApplyProcess.class)) {
                EntryApplyProcess annotation = handlerClass.getAnnotation(EntryApplyProcess.class);
                EntryTypeEnum type = annotation.value();
                handlerMap.put(type, handler);
            }
        }
    }

    public EntryOrderHandler getHandler(EntryTypeEnum type) {
        return handlerMap.get(type);
    }
}
