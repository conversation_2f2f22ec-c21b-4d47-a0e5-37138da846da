package com.yeepay.g3.core.aggregation.config.flow.node.decorator;

import com.yeepay.g3.core.aggregation.config.flow.FlowContext;
import com.yeepay.g3.core.aggregation.config.flow.node.Node;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2025/5/27 17:50
 */
public class AsyncNode implements Node {

    private final Node delegate;
    /**
     * 执行器
     */
    private final Executor executor;

    public AsyncNode(Node delegate, Executor executor) {
        this.delegate = delegate;
        this.executor = executor;
    }

    @Override
    public void execute(final FlowContext context) {

        CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                delegate.execute(context), executor);
        // 可选：添加异常处理
        future.exceptionally(ex -> {
            //TODO 执行异常
            return null;
        });
    }

    @Override
    public Node combine(final Node after) {
        return delegate.combine(new AsyncNode(after, executor));
    }
}
