package com.yeepay.g3.core.aggregation.config.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName: ExternalInterfaceEnum
 * @Description: 外部接口枚举类
 */
@Getter
@AllArgsConstructor
public enum ExternalInterfaceEnum {

    USER_CENTER_VERIFY("客户中心校验"),
    OPR_CREATE("OPR下单"),
    OPR_QUERY("OPR查询"),
    OPR_CALLBACK("OPR回调"),
    BANK_PREPARE("银子预下单"),
    BANK_PASSIVE("银子被扫"),
    BANK_QUERY("银子查询"),
    BANK_CLOSE("银子关单"),
    RISK_SYNC("风控同步"),
    RISK_ASYNC("风控异步"),
    USER_CENTER_QUERY_GROUP("客户中心查询分组信息"),
    USER_CENTER_QUERY_INFO("客户中心查询基本信息"),
    BANK_PRE_ROUTE("银子预路由"),
    BANK_UNION_USER_AUTH("银子银联用户授权"),
    USER_CENTER_QUERY_INFO_CACHE("客户中心查询基本信息缓存"),
    WECHAT_COMPLAINT_FEEDBACK("微信投诉提交回复"),
    WECHAT_COMPLAINT_IMG("微信投诉图片上传"),
    WECHAT_COMPLAINT_SUCCESS("微信投诉商户反馈处理完成"),
    WECHAT_COMPLAINT_NEGOTIATION_HISTORY("微信投诉协商历史查询"),
    WECHAT_COMPLAINT_DOWNLOAD_PICTURE("微信投诉图片下载"),
    WECHAT_COMPLAINT_UPDATE_REFUND_PROCESS("微信投诉更新退款审批结果"),
    MARKET_DISCOUNT("营销扣减"),
    USER_CENTER_VERIFY_RELATION("客户中心校验商户关系"),
    USER_CENTER_QUERY_PRODUCT_TIME("客户中心查询商户开通产品时间"),
    USER_CENTER_QUERY_PRODUCT_LIST("客户中心查询商户开通产品列表"),
    BANK_QUERY_OPENID("银子查询OPENID"),
    AGG_SYNC_ACCESS_TOKEN("同步accessToken"),
    OPR_PRE_PROCESS("OPR预处理"),
    BANK_BUSINESS_QUERY("支付宝前置咨询"),
    BANK_SHARE_TOKEN("生成吱口令"),
    BANK_AUTH_INFO("刷脸付获取调用凭证"),
    BANK_CREDIT("信用分"),
    USER_CENTER_VERIFY_MERCHANT_RELATION_BATCH("客户中心批量校验商户关系")

    ;

    private final String desc;
}
