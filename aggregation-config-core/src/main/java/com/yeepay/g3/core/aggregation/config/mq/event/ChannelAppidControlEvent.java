package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-16 16:14
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Data
public class ChannelAppidControlEvent implements Serializable {

    private static final long serialVersionUID = 733131723782889469L;

    /**
     * appid
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 管控开始时间
     */
    @NotBlank(message = "管控开始时间不能为空")
    private String controlledTime;

    /**
     * 管控结束时间
     */
    private String uncontrolledTime;

    /**
     *  通知类型  新增/更新
     */
    @NotBlank(message = "渠道类型不能为空")
    private String notifyType;
}
