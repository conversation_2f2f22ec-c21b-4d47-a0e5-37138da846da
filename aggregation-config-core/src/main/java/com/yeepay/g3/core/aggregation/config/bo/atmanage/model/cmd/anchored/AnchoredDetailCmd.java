package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request.AnchoredApplyDetailDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.AnchoredInfoDTO;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/6/2 17:27
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
public class AnchoredDetailCmd {

    /**
     * 挂靠类型
     */
    private final AnchoredType anchoredType;

    /**
     * 挂靠维度 是否需要跨sass体系
     */
    private final Boolean anchoredDimension;

    /**
     * 挂靠商户编号
     * 指定商编时需要填写
     */
    private String relationMerchantNo;

    /**
     * 聚合场景
     */
    private final PaySceneEnum payScene;

    /**
     * 聚合渠道
     */
    private final PayChannelEnum payChannel;

    /**
     * 聚合活动类型
     */
    private final ActivityTypeEnum activityType;

    public static AnchoredDetailCmd build(final AnchoredInfoDTO anchoredInfoDTO,
                                          final PaySceneEnum payScene,
                                          final PayChannelEnum payChannel,
                                          final ActivityTypeEnum activityType) {
        final AnchoredType anchoredType = DocumentedEnum.fromValue(AnchoredType.class, anchoredInfoDTO.getAnchoredType());
        Assert.isFalse(AnchoredType.DESIGNATE_MERCHANT == anchoredType
                && StringUtils.isBlank(anchoredInfoDTO.getAnchoredMerchantNo()), ResultCode.PARAM_VALID_ERROR, "挂靠类型不合法");

        return builder()
                .anchoredType(anchoredType)
                .anchoredDimension(anchoredInfoDTO.getAnchoredDimension())
                .relationMerchantNo(anchoredInfoDTO.getAnchoredMerchantNo())
                .payScene(payScene)
                .payChannel(payChannel)
                .activityType(activityType)
                .build();
    }

    public static AnchoredDetailCmd build(AnchoredApplyDetailDTO reqDTO) {
        final AnchoredType anchoredType = DocumentedEnum.fromValue(AnchoredType.class, reqDTO.getAnchoredType());
        Assert.isFalse(AnchoredType.DESIGNATE_MERCHANT == anchoredType
                && StringUtils.isBlank(reqDTO.getAnchoredMerchantNo()), ResultCode.PARAM_VALID_ERROR, "挂靠类型不合法");

        return builder()
                .anchoredType(DocumentedEnum.fromValue(AnchoredType.class, reqDTO.getAnchoredType()))
                .relationMerchantNo(reqDTO.getAnchoredMerchantNo())
                .anchoredDimension(reqDTO.getAnchoredDimension())
                .payScene(DocumentedEnum.fromValue(PaySceneEnum.class, reqDTO.getPayScene()))
                .payChannel(DocumentedEnum.fromValue(PayChannelEnum.class, reqDTO.getChannelType()))
                .activityType(DocumentedEnum.fromValueOfNullable(ActivityTypeEnum.class, reqDTO.getActivityType()))
                .build();
    }

}
