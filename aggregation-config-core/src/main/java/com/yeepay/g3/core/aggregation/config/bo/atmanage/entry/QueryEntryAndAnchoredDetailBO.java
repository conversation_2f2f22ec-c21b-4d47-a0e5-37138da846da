package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryAnchoredInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryChannelTerminalInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryInfoBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryAndAnchoredDetailDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * description: 查询商户进件结果
 * <AUTHOR>
 * @since 2025/6/10:14:11
 * Company: 易宝支付(YeePay)
 */
@Builder
@Getter
public class QueryEntryAndAnchoredDetailBO implements Serializable {


    private static final long serialVersionUID = 4509795264022477064L;
    /**
     * 渠道类型
     */
    private PayChannelEnum payChannel;

    /**
     * 场景类型
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;

    /**
     * 费率类型
     */
    private String feeType;

    /**
     * 通道返回的活动类型
     */
    private String channelActivityType;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 挂靠信息
     */
    private List<QueryAnchoredInfoBO> anchoredInfoList;

    /**
     * 进件信息
     */
    private QueryEntryInfoBO entryInfoResult;

    /**
     * 辅助终端报备状态
     */
    private String terminalReportStatus;

    /**
     * 辅助终端报备失败原因
     */
    private String terminalReportFailMessage;

    /**
     * 创建分账方状态
     */
    private String splitCreateStatus;

    public static QueryEntryAndAnchoredDetailBO build(String key,
                                                      QueryEntryInfoBO entryInfo,
                                                      List<QueryAnchoredInfoBO> attachList,
                                                      QueryChannelTerminalInfoBO wxTerminalInfo,
                                                      QueryChannelTerminalInfoBO aliTerminalInfo,
                                                      Map<String, String> receiverStatusMap) {
        if (CheckUtils.isEmpty(key)) {
            return null;
        }
        SceneAndActivityTypeBO sceneAndActivityTypeBO = SceneAndActivityUtils.getSceneByUniqueKey(key);

        SceneAndActivityTypeBO sceneByUniqueKey = SceneAndActivityUtils.getSceneByUniqueKey(key);
        // 挂靠 微信直连/数币不返回制定商编挂靠
        if (PayChannelEnum.WECHAT.equals(sceneByUniqueKey.getChannel())
                && (DocumentedEnum.inRightEnums(sceneByUniqueKey.getPayScene(), PaySceneEnum.STORE_ASST, PaySceneEnum.DIRECT, PaySceneEnum.DIRECT_STANDARD))) {
            attachList = Optional.ofNullable(attachList)
                    .orElse(new ArrayList<>()).stream()
                    .filter(bo -> bo.getUniqueKey() != null).collect(Collectors.toList());
        } else if (PayChannelEnum.BANK.equals(sceneByUniqueKey.getChannel())
                && PaySceneEnum.DIGITAL_CURRENCY.equals(sceneByUniqueKey.getPayScene())) {
            attachList = Optional.ofNullable(attachList)
                    .orElse(new ArrayList<>()).stream()
                    .filter(bo -> bo.getUniqueKey() != null).collect(Collectors.toList());
        }
        // 挂靠商编去重
        List<QueryAnchoredInfoBO> distinctAttachList = new ArrayList<>(Optional.ofNullable(attachList)
                .orElse(new ArrayList<>()).stream()
                .filter(bo -> bo.getAnchoredMerchantNo() != null)
                .collect(Collectors.toMap(
                        QueryAnchoredInfoBO::getAnchoredMerchantNo,
                        bo -> bo,
                        (existing, replacement) -> existing // 保留第一个出现的
                )).values());
        // 终端
        QueryChannelTerminalInfoBO terminalInfoBO = null;
        if (PayChannelEnum.WECHAT.equals(sceneAndActivityTypeBO.getChannel())) {
            terminalInfoBO = wxTerminalInfo;
        } else if (PayChannelEnum.ALIPAY.equals(sceneAndActivityTypeBO.getChannel())) {
            terminalInfoBO = aliTerminalInfo;
        }

        // 银行编码
        String bankCode = CheckUtils.isEmpty(entryInfo) ? null : entryInfo.getBankCode();

        // 分账方状态
        boolean needReceiver = PayChannelEnum.WECHAT.equals(sceneAndActivityTypeBO.getChannel())
                && PaySceneEnum.DIRECT.equals(sceneAndActivityTypeBO.getPayScene())
                && !CheckUtils.isEmpty(entryInfo);
        String receiverStatus = needReceiver ? receiverStatusMap.get(entryInfo.getChannelNo()) : null;

        return QueryEntryAndAnchoredDetailBO.builder()
        .payChannel(sceneAndActivityTypeBO.getChannel())
        .payScene(sceneAndActivityTypeBO.getPayScene())
        .activityType(sceneAndActivityTypeBO.getActivityType())
        .feeType(sceneAndActivityTypeBO.getChannelFeeType())
        .channelActivityType(sceneAndActivityTypeBO.getChannelActivityType())
        .bankCode(bankCode)
        .entryInfoResult(entryInfo)
                .anchoredInfoList(distinctAttachList)
        .terminalReportStatus(CheckUtils.isEmpty(terminalInfoBO) ? null : terminalInfoBO.getTotalTerminalStatus())
        .terminalReportFailMessage(CheckUtils.isEmpty(terminalInfoBO) ? null : terminalInfoBO.getTotalTerminalFailMsg())
        .splitCreateStatus(receiverStatus).build();
    }

    public static List<QueryEntryAndAnchoredDetailDTO> buildList(List<QueryEntryAndAnchoredDetailBO> detailList) {
        if (CheckUtils.isEmpty(detailList)) {
            return new ArrayList<>();
        }

        return detailList.stream().map(e ->
            QueryEntryAndAnchoredDetailDTO.builder()
                    .payChannel(e.getPayChannel().getDocument())
                    .payScene(e.getPayScene().getDocument())
                    .activityType(e.getActivityType().getDocument())
                    .feeType(e.getFeeType())
                    .channelActivityType(e.getChannelActivityType())
                    .digitalCurrencyBankCode(e.getBankCode())
                    .entryInfo(QueryEntryInfoBO.buildDTO(e.getEntryInfoResult()))
                    .anchoredInfoList(QueryAnchoredInfoBO.buildListDTO(e.getAnchoredInfoList()))
                    .terminalReportStatus(e.getTerminalReportStatus())
                    .terminalReportFailMessage(e.getTerminalReportFailMessage())
                    .splitCreateStatus(e.getSplitCreateStatus()).build()
        ).collect(Collectors.toList());
    }
}
