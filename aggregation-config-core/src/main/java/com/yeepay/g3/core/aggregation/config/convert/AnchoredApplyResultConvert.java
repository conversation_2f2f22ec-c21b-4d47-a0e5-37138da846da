package com.yeepay.g3.core.aggregation.config.convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyDetailResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredMerchantInfoResultBO;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity;
import com.yeepay.g3.core.aggregation.config.mq.event.AnchoredApplyResultMessage;
import com.yeepay.g3.core.aggregation.config.mq.event.AnchoredMerchantInfoMessage;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 挂靠相关外部需要信息转换
 *
 * @author: Mr.yin
 * @date: 2025/6/30  18:41
 */
public class AnchoredApplyResultConvert {


    /**
     * 组装出外部想要的 查询结果
     *
     * @return
     */
    public static AnchoredApplyDetailResultBO covertAnchoredApplyQueryResult(AnchoredApplyEntity anchoredApplyEntity) {
        AnchoredApplyDetailResultBO message = new AnchoredApplyDetailResultBO();
        message.setApplyNo(anchoredApplyEntity.getApplyNo());
        message.setMerchantNo(anchoredApplyEntity.getMerchantNo());
        message.setPayScene(anchoredApplyEntity.getPayScene());
        message.setActivityType(anchoredApplyEntity.getActivityType());
        message.setChannelType(anchoredApplyEntity.getPayChannel());
        message.setAnchoredType(anchoredApplyEntity.getAnchoredType());
        message.setAnchoredDimension(Boolean.valueOf(anchoredApplyEntity.getAnchoredDimension()));

        message.setAnchoredStatus(AnchoredApply.covertSyncStatus(anchoredApplyEntity.getAnchoredStatus(), anchoredApplyEntity.getAnchoredType(), Boolean.valueOf(anchoredApplyEntity.getAnchoredDimension())));
        if (DocumentedEnum.stringInRightEnums(message.getAnchoredStatus(), AnchoredStatus.SUCCESS)) {
            List<AggAnchoredMerchantInfo> aggAnchoredMerchantInfoList = StringUtils.isBlank(anchoredApplyEntity.getAggAnchoredMerchantText()) ? Lists.newArrayList() :
                    JsonUtils.fromJson(anchoredApplyEntity.getAggAnchoredMerchantText(), new TypeReference<List<AggAnchoredMerchantInfo>>() {
                    });
            if (CollectionUtils.isNotEmpty(aggAnchoredMerchantInfoList)) {
                if (AnchoredApply.needSpecialHandleStatus(anchoredApplyEntity.getAnchoredType(), Boolean.valueOf(anchoredApplyEntity.getAnchoredDimension()))) {
                    List<AnchoredMerchantInfoResultBO> anchoredMerchantInfoList = aggAnchoredMerchantInfoList.stream()
                            .map(e -> AnchoredMerchantInfoResultBO.covert(e, anchoredApplyEntity.getMerchantNo())).collect(Collectors.toList());
                    message.setAnchoredMerchantInfoList(anchoredMerchantInfoList);
                } else {/*渠道终态的这种，要以渠道为准*/
                    List<AnchoredMerchantInfoResultBO> anchoredMerchantInfoList = aggAnchoredMerchantInfoList.stream()
                            .filter(e -> Boolean.TRUE.toString().equalsIgnoreCase(e.getChannelStatus()))
                            .map(e -> AnchoredMerchantInfoResultBO.covert(e, anchoredApplyEntity.getMerchantNo())).collect(Collectors.toList());
                    message.setAnchoredMerchantInfoList(anchoredMerchantInfoList);
                }
            }
        } else if (DocumentedEnum.stringInRightEnums(message.getAnchoredStatus(), AnchoredStatus.FAIL)) {
            message.setFailReason(anchoredApplyEntity.getFailReason());
        }

        return message;
    }


    /**
     * 转换成消息
     */
    public static AnchoredApplyResultMessage covertAnchoredApplyResultMessage(AnchoredApply anchoredApply, AnchoredOrderEntity anchoredOrderEntity) {
        AnchoredApplyResultMessage message = new AnchoredApplyResultMessage();
        message.setBizScene(anchoredOrderEntity.getBizScene());
        message.setBizApplyNo(anchoredOrderEntity.getBizApplyNo());
        message.setOrderNo(anchoredOrderEntity.getOrderNo());
        message.setApplyNo(anchoredApply.getApplyNo());
        message.setMerchantNo(anchoredApply.getMerchantNo());
        message.setPayScene(null == anchoredApply.getPayScene() ? "" : anchoredApply.getPayScene().getDocument());
        message.setActivityType(null == anchoredApply.getActivityType() ? "" : anchoredApply.getActivityType().getDocument());
        message.setChannelType(null == anchoredApply.getChannelType() ? "" : anchoredApply.getChannelType().getDocument());
        message.setAnchoredType(anchoredApply.getAnchoredType().getDocument());
        message.setAnchoredDimension(null == anchoredApply.getAnchoredDimension() ? "false" : anchoredApply.getAnchoredDimension().toString());
        message.setAnchoredStatus(AnchoredApply.covertSyncStatus(anchoredApply.getAnchoredStatus(), anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension()));
        if (DocumentedEnum.inRightEnums(anchoredApply.getAnchoredStatus(), AnchoredStatus.SUCCESS)) {
            if (CollectionUtils.isNotEmpty(anchoredApply.getAggAnchoredMerchantInfoList())) {
                if (AnchoredApply.needSpecialHandleStatus(anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension())) {
                    List<AnchoredMerchantInfoMessage> anchoredMerchantInfoList = anchoredApply.getAggAnchoredMerchantInfoList().stream()
                            .map(e -> AnchoredMerchantInfoMessage.covert(e, anchoredApply.getMerchantNo())).collect(Collectors.toList());
                    message.setAnchoredMerchantInfoList(anchoredMerchantInfoList);
                } else {/*渠道终态的这种，要以渠道为准*/
                    List<AnchoredMerchantInfoMessage> anchoredMerchantInfoList = anchoredApply.getAggAnchoredMerchantInfoList().stream()
                            .filter(e -> Boolean.TRUE.toString().equalsIgnoreCase(e.getChannelStatus()))
                            .map(e -> AnchoredMerchantInfoMessage.covert(e, anchoredApply.getMerchantNo())).collect(Collectors.toList());
                    message.setAnchoredMerchantInfoList(anchoredMerchantInfoList);
                }
            }
        } else if (DocumentedEnum.inRightEnums(anchoredApply.getAnchoredStatus(), AnchoredStatus.FAIL)) {
            message.setFailReason(anchoredApply.getFailReason());
        }

        return message;
    }

}
