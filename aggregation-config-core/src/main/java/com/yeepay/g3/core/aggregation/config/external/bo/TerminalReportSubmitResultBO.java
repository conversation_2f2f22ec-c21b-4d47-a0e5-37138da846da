package com.yeepay.g3.core.aggregation.config.external.bo;

import com.yeepay.g3.core.aggregation.config.enums.TerminalReportStatus;
import lombok.Data;

import java.io.Serializable;

/**
 * 终端报备提交结果
 *
 * @author: Mr.yin
 * @date: 2025/6/24  11:15
 */
@Data
public class TerminalReportSubmitResultBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 下游的traceID
     */
    private String channelOrderNo;

    /**
     * 异步提交状态
     * 下游可能是
     * 终态成功
     * 提交失败
     * 提交成功处理中
     */
    private TerminalReportStatus status;

}
