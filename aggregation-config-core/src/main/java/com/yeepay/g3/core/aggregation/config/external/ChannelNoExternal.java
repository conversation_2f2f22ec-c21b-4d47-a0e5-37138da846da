package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoQueryBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoQueryResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.QueryChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelNoSignSubjectBO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.ChannelInfoResponsesDTO;

import java.util.List;

/**
 * description: 渠道号申请外部接口
 * <AUTHOR>
 * @since 2025/5/27:18:02
 * Company: 易宝支付(YeePay)
 */
public interface ChannelNoExternal {

    /**
     * 申请渠道号
     * @param channelInfo 渠道号申请信息
     */
    void applyChannelInfo(String requestNo, ChannelNoApplyBO channelInfo);

    /**
     * 查询渠道号申请结果通知
     */
    ChannelNoApplyResultBO queryApplyChannelResult(QueryChannelNoApplyResultBO resultBO);

    /**
     * 查询渠道号信息
     * @param queryBO 查询条件
     * @return 渠道号信息列表
     */
    List<ChannelNoQueryResultBO> queryChannelInfo(ChannelNoQueryBO queryBO);

    /**
     * 根据业务线查询渠道号
     *
     * @param industryLine 行业线
     * @param paySceneEnum 支付场景
     * @return 渠道号
     */
    String queryChannelNoByBusinessLine(String industryLine, PaySceneEnum paySceneEnum);


    /**
     * @description 根据【渠道号】查询渠道主体信息
     */
    RemoteResult<ChannelNoSignSubjectBO> queryChannelSubjectByChannelNo(String channelNo);

    /**
     * 查询渠道号Info
     *
     * @return 渠道号信息
     */
    ChannelInfoResponsesDTO queryChannelNoInfoByChannelNo(List<String> channelNoList);

}
