package com.yeepay.g3.core.aggregation.config.factory.decorator;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.auth.AuthApplyBO;
import com.yeepay.g3.core.aggregation.config.factory.handle.MerchantInfoStrategy;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;

/**
 * description: 商户信息装饰器
 * <AUTHOR>
 * @since 2025/5/22:11:11
 * Company: 易宝支付(YeePay)
 */
public class MerchantInfoDecorator implements MerchantInfoStrategy {

    MerchantInfoStrategy merchantInfoStrategy;

    public MerchantInfoDecorator(MerchantInfoStrategy merchantInfoStrategy) {
        this.merchantInfoStrategy = merchantInfoStrategy;
    }

    @Override
    public AuthApplyBO buildAndValidateAuth(AuthApplyReqDTO authApplyReqDTO) {
        // 业务逻辑处理
        return merchantInfoStrategy.buildAndValidateAuth(authApplyReqDTO);
    }

}
