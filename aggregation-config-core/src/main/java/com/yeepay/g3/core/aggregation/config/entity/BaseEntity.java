package com.yeepay.g3.core.aggregation.config.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Id;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/15 17:38
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseEntity {
    /**
     * 唯一ID
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 删除标志 0=正常，1=删除
     */
    @Column(name = "del_flag", length = 1)
    private Integer delFlag;

    /**
     * 最后更新人
     */
    @Column(name = "updated_by", length = 64)
    private String updatedBy;

    /**
     * 最后更新时间
     */
    @Column(name = "update_dt")
    private LocalDateTime updateDt;

    /**
     * 创建人
     */
    @Column(name = "created_by", length = 64)
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(name = "create_dt")
    private LocalDateTime createDt;
}
