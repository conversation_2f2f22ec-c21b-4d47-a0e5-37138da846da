package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.QualificationDTO;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 15:06
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class Qualification {
    /**
     * 行业类目id
     */
    private final String categoryId;

    /**
     * 行业经营许可证资质照片列表
     */
    private final List<String> operationCopyList;

    public static Qualification build(final QualificationDTO qualificationDTO) {
        if (qualificationDTO == null) return null;
        return Qualification.builder()
                .categoryId(qualificationDTO.getCategoryId())
                .operationCopyList(qualificationDTO.getOperationCopyList())
                .build();
    }
}
