package com.yeepay.g3.core.aggregation.config.bo.atmanage.auth;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.AtMerchantInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ContactInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.UboInfoBO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * description: 认证申请请求 DTO
 * <AUTHOR>
 * @since 2025/5/21:10:55
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class AuthApplyBO extends AtMerchantInfoBO implements Serializable {

    private static final long serialVersionUID = 5823893800853389621L;
    /**
     * 商户编码（必填）
     */
    private String merchantNo;

    /**
     * 渠道类型（必填）
     */
    private PayChannelEnum channelType;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 渠道标识（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelIdentifier;

    /**
     * 渠道号（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelNo;

    /**
     * 辅助证明信息（可选）
     */
    private AssistProveBO assistProveInfo;

    @Builder
    public AuthApplyBO(String merchantNo, PayChannelEnum channelType, PaySceneEnum payScene, String channelIdentifier, String channelNo,
                       String bizApplyNo, ContactInfo contactInfo, SubjectInfo subjectInfo, List<UboInfoBO> uboInfoList,
                       AssistProveBO assistProveInfo) {
        super(bizApplyNo, contactInfo, subjectInfo, uboInfoList);
        this.merchantNo = merchantNo;
        this.channelType = channelType;
        this.payScene = payScene;
        this.channelIdentifier = channelIdentifier;
        this.channelNo = channelNo;
        this.assistProveInfo = assistProveInfo;
    }

//
//    public static AuthApplyBO buildAndValidateAuthApplyBO(AuthApplyReqDTO applyDTO) {
//        if (applyDTO == null) {
//            return null;
//        }
//        // 渠道类型
//        PayChannelEnum payChannelEnum = DocumentedEnum.fromValue(PayChannelEnum.class, applyDTO.getChannelType());
//        // 判断主体类型
//        SubjectTypeEnum subjectTypeEnum = DocumentedEnum.fromValue(SubjectTypeEnum.class, applyDTO.getSubjectInfo().getSubjectType());
//
//        MerchantInfoDecorator merchantInfoDecorator = MerchantInfoFactory.getMerchantInfoDecorator(payChannelEnum, subjectTypeEnum);
//        if (merchantInfoDecorator == null) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "未找到对应策略");
//        }
//        return merchantInfoDecorator.buildAndValidateAuth(applyDTO);
//    }
//
//    public static AuthApplyBO buildAuthApplyBO(AuthApplyReqDTO applyDTO) {
//        ContactInfo contactInfo = ContactInfo.build(applyDTO.getContactInfo());
//        SubjectInfo subjectInfo = SubjectInfo.buildAuth(applyDTO.getSubjectInfo());
//        List<UboInfoBO> uboInfoBOS = UboInfoBO.buildUboInfos(applyDTO.getUboInfoList());
//        PayChannelEnum payChannelEnum = DocumentedEnum.fromValue(PayChannelEnum.class, applyDTO.getChannelType());
//        PaySceneEnum paySceneEnum = DocumentedEnum.fromValue(PaySceneEnum.class, applyDTO.getPayScene());
//        // 业务逻辑处理
//        return AuthApplyBO.builder()
//                .merchantNo(applyDTO.getMerchantNo())
//                .channelType(payChannelEnum)
//                .payScene(paySceneEnum)
//                .channelIdentifier(applyDTO.getChannelIdentifier())
//                .channelNo(applyDTO.getChannelNo())
//                .bizApplyNo(applyDTO.getBizApplyNo())
//                .contactInfo(contactInfo)
//                .subjectInfo(subjectInfo)
//                .uboInfoList(uboInfoBOS)
//                .assistProveInfo(AssistProveBO.build(applyDTO.getAssistProveInfo()))
//                .build();
//    }
//
//    /**
//     * 实名认证-基础校验
//     */
//    public void validateAuthBase() {
//        if (CheckUtils.isEmpty(channelNo) && CheckUtils.isEmpty(channelIdentifier)) {
//            // 渠道号和渠道标识 二选一必填
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "渠道号和渠道标识 二选一必填");
//        }
//    }
}

