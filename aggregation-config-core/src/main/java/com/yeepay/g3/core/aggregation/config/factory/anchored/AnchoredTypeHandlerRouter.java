package com.yeepay.g3.core.aggregation.config.factory.anchored;

/**
 * 挂靠类型+维度
 */

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AnchoredTypeHandlerRouter {

    private final List<AnchoredApplyHandler> anchoredApplyHandlerList;

    public AnchoredTypeHandlerRouter(final List<AnchoredApplyHandler> anchoredApplyHandlerList) {
        this.anchoredApplyHandlerList = anchoredApplyHandlerList;
    }

    public AnchoredApplyHandler getHandler(final AnchoredType anchoredType, final Boolean anchoredDimension) {
        return anchoredApplyHandlerList.stream()
                .filter(handler -> handler.isSupport(anchoredType, anchoredDimension))
                .findFirst()
                .orElseThrow(() -> new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "暂不支持的挂靠类型+维度"));
    }
}
