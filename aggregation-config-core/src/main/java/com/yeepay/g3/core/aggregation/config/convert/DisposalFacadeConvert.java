package com.yeepay.g3.core.aggregation.config.convert;

import com.google.common.collect.Lists;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalRecordEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryResultEntity;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalRecordDto;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryResponseDTO;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 20:27
 */
public class DisposalFacadeConvert {

    public static DisposalNotifyQueryResponseDTO buildWechatDisposalResultDTO(DisposalQueryResultEntity resultEntity) {
        DisposalNotifyQueryResponseDTO responseDTO = new DisposalNotifyQueryResponseDTO();
        List<DisposalRecordDto> disposalRecordDtoList = buildPunishRecordEntityList(resultEntity.getDisposalRecordEntityList());
        responseDTO.setDisposalRecordDtoList(disposalRecordDtoList);
        responseDTO.setTotalCount(resultEntity.getTotalCount());
        return responseDTO;
    }

    private static DisposalRecordDto buildPunishRecordEntity(DisposalRecordEntity disposalRecordEntity) {
        DisposalRecordDto disposalRecordDto = new DisposalRecordDto();
        disposalRecordDto.setYeepayMerchantNo(disposalRecordEntity.getYeepayMerchantNo());
        disposalRecordDto.setYeepayMerchantName(disposalRecordEntity.getYeepayMerchantName());
        disposalRecordDto.setCompanyName(disposalRecordEntity.getCompanyName());
        disposalRecordDto.setChannelNo(disposalRecordEntity.getChannelNo());
        disposalRecordDto.setReportMerchantNo(disposalRecordEntity.getReportMerchantNo());
        disposalRecordDto.setPunishPlan(disposalRecordEntity.getPunishPlan());
        disposalRecordDto.setLimitRecoverFunction(disposalRecordEntity.getLimitRecoverFunction());
        disposalRecordDto.setPunishType(disposalRecordEntity.getPunishType());
        disposalRecordDto.setPunishTime(disposalRecordEntity.getPunishTime());
        return disposalRecordDto;
    }


    private static List<DisposalRecordDto> buildPunishRecordEntityList(List<DisposalRecordEntity> disposalRecordEntityList) {
        List<DisposalRecordDto> disposalRecordDtoList = Lists.newArrayList();
        for (DisposalRecordEntity disposalRecordEntity : disposalRecordEntityList) {
            if (disposalRecordEntity == null) {
                continue;
            }
            DisposalRecordDto recordDto = buildPunishRecordEntity(disposalRecordEntity);
            disposalRecordDtoList.add(recordDto);
        }

        return disposalRecordDtoList;

    }

    public static DisposalQueryEntity buildPunishQueryEntity(DisposalNotifyQueryRequestDTO requestDTO) {
        DisposalQueryEntity paramEntity = new DisposalQueryEntity();

        paramEntity.setChannelType(requestDTO.getChannelType());
        paramEntity.setReportMerchantNo(requestDTO.getReportMerchantNo());
        paramEntity.setYeepayMerchantNo(requestDTO.getYeepayMerchantNo());
        paramEntity.setChannelNo(requestDTO.getChannelNo());
        paramEntity.setTopMerchantNo(requestDTO.getTopMerchantNo());
        paramEntity.setAgentMerchantNo(requestDTO.getAgentMerchantNo());

        paramEntity.setStartTime(requestDTO.getStartTime());
        paramEntity.setEndTime(requestDTO.getEndTime());
        paramEntity.setPageNum(requestDTO.getPageNum());
        paramEntity.setPageSize(requestDTO.getPageSize());

        paramEntity.setPunishPlan(requestDTO.getPunishPlan());
        paramEntity.setPunishType(requestDTO.getPunishType());
        paramEntity.setCompanyName(requestDTO.getCompanyName());

        paramEntity.setQueryType(requestDTO.getQueryType());
        paramEntity.setWithSelf(requestDTO.getWithSelf());
        return paramEntity;
    }


}
