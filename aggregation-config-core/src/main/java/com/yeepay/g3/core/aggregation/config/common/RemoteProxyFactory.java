package com.yeepay.g3.core.aggregation.config.common;

import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.lang.reflect.Proxy;

/**
 * @Title : com.yeepay.g3.core.refund.processor.external.proxy
 * @Description : 远程调用Facade动态代理工厂, 主要用于实现外部facade调用日志监控
 * @Company : 易宝支付(Yeepay)
 * <AUTHOR> jiafu.wu
 * @Since: 2020/6/2
 * @Version : 1.0.0
 */
@Slf4j
public class RemoteProxyFactory {

    public static <T> T getService(Class<T> clz, ExternalSystemEnum system) {
        Assert.notNull(clz, "clz must be not null");
        Assert.notNull(system, "system must be specified");
        Assert.isTrue(clz.isInterface(), "clz must be interface");
        T target = RemoteServiceFactory.getService(clz);
        RemoteInvocationHandler invocationHandler = new RemoteInvocationHandler(target, system.getSysName());
        return (T) Proxy.newProxyInstance(Thread.currentThread().getContextClassLoader(), target.getClass().getInterfaces(), invocationHandler);
    }

    /**
     * 指定超时时间
     * <AUTHOR>
     * @param clz
     * @param system
     * @param connTimeout
     * @param readTimeout
     */
    public static <T> T getService(Class<T> clz, ExternalSystemEnum system, int connTimeout, int readTimeout) {
        Assert.notNull(clz, "clz must be not null");
        Assert.notNull(system, "system must be specified");
        Assert.isTrue(clz.isInterface(), "clz must be interface");
        T target = RemoteServiceFactory.getService(clz, connTimeout, readTimeout);
        RemoteInvocationHandler invocationHandler = new RemoteInvocationHandler(target, system.getSysName());
        return (T) Proxy.newProxyInstance(Thread.currentThread().getContextClassLoader(), target.getClass().getInterfaces(), invocationHandler);
    }
}
