package com.yeepay.g3.core.aggregation.config.factory.anchored.impl;

import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.external.ChannelAnchoredExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.AggDesignateMerchantAnchoredReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 指定商编挂靠处理
 */
@Component
public class DesignateMerchantAnchoredApplyHandler extends BaseAnchoredApplyHandler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ChannelAnchoredExternal channelAnchoredExternal;

    @Override
    public boolean isSupport(AnchoredType anchoredType, Boolean anchoredDimension) {
        return AnchoredType.DESIGNATE_MERCHANT == anchoredType;
    }

    @Override
    public void validateAnchoredParams(AnchoredApply anchoredApply) {
        Assert.isTrue(StringUtils.isNotBlank(anchoredApply.getRelationMerchantNo()), ResultCode.PARAM_VALID_ERROR, "指定商编挂靠，指定商编必填");
        SceneAndActivityUtils.getSceneAndPromotionTypeBO(anchoredApply.getChannelType(), anchoredApply.getPayScene(), anchoredApply.getActivityType());
    }

    /**
     * 聚合指定商编挂靠，只要成功就成功
     */
    @Override
    public RemoteResult<List<AggAnchoredMerchantInfo>> aggregationAnchoredApply(AnchoredApply anchoredApplyBO) {
        logger.info("[指定商编挂靠] 处理开始 anchoredApplyBO={}", JsonUtils.toJSONString(anchoredApplyBO));
        /*请求指定商编挂靠*/
        AggDesignateMerchantAnchoredReqBO reqBO = new AggDesignateMerchantAnchoredReqBO();
        reqBO.setMerchantNo(anchoredApplyBO.getMerchantNo());
        reqBO.setAnchoredMerchantNo(anchoredApplyBO.getRelationMerchantNo());
        reqBO.setGroupName(anchoredApplyBO.getGroupName());
        RemoteResult<String> result = channelAnchoredExternal.aggregationDesignateMerchantAnchored(reqBO);
        if (result.isSuccess()) {
            AggAnchoredMerchantInfo anchoredMerchantInfo = AggAnchoredMerchantInfo.covert(anchoredApplyBO.getRelationMerchantNo());
            return RemoteResult.success(Lists.newArrayList(anchoredMerchantInfo), result.getChannelCode(), result.getChannelMessage());
        } else {
            return RemoteResult.fail(result.getChannelCode(), result.getChannelMessage());
        }
    }
}
