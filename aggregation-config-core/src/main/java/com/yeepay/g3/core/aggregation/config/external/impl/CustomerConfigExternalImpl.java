package com.yeepay.g3.core.aggregation.config.external.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.external.CustomerConfigExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.CustomerConfigResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.facade.business.manage.dto.request.merchant.QueryMerchantBizConfigReqDto;
import com.yeepay.g3.facade.business.manage.dto.response.merchant.QueryMerchantBizConfigRespDTO;
import com.yeepay.g3.facade.business.manage.facade.merchant.MerchantBizConfigFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 渠道微信直连 分账方相关
 *
 * @author: Mr.yin
 * @date: 2025/6/13  18:14
 */
@Service
@Slf4j
public class CustomerConfigExternalImpl implements CustomerConfigExternal {

    private MerchantBizConfigFacade merchantBizConfigFacade = RemoteServiceFactory.getService(MerchantBizConfigFacade.class);


    @Override
    public RemoteResult<CustomerConfigResultBO> queryCustomerConfig(String merchantNo, String configKey) {
        QueryMerchantBizConfigReqDto requestDTO = new QueryMerchantBizConfigReqDto();
        requestDTO.setMerchantNo(merchantNo);
        requestDTO.setConfigType(configKey);
        QueryMerchantBizConfigRespDTO responseDTO;
        try {
            log.info("[remote][查询客户中心配置结果]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = merchantBizConfigFacade.queryMerchantBizConfig(requestDTO);
            log.info("[remote][查询客户中心配置结果]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            log.error("[remote][查询客户中心配置结果] 调用异常", e);
            throw new BusinessException(ResultCode.QUERY_CUSTOMER_CONFIG_ERROR);
        }
        if (null == responseDTO) {
            log.error("[remote][查询客户中心配置结果] 调用异常返回为null)");
            throw new BusinessException(ResultCode.QUERY_CUSTOMER_CONFIG_ERROR);
        }
        if (!responseDTO.isSuccess()) {
            return RemoteResult.fail(responseDTO.getReturnCode(), responseDTO.getReturnMsg());
        }
        CustomerConfigResultBO resultBO = new CustomerConfigResultBO();
        resultBO.setConfigStatus(responseDTO.getConfigStatus());
        resultBO.setConfigValue(responseDTO.getConfigValue());
        log.info("[remote][查询客户中心配置结果] 返回结果 result={}", JsonUtils.toJSONString(resultBO));
        return RemoteResult.success(resultBO, responseDTO.getReturnCode(), responseDTO.getReturnMsg());
    }
}
