package com.yeepay.g3.core.aggregation.config.flow.node;

import com.yeepay.g3.core.aggregation.config.flow.FlowContext;

/**
 * <AUTHOR>
 * @date 2025/5/26 21:29
 */
@FunctionalInterface
public interface Node {

   Node EMPTY = context -> {
   };

    void execute(FlowContext context);

    /**
     * 合并下一个处理器
     */
    default Node combine(Node after) {
        return context -> {
            this.execute(context);
            after.execute(context);
        };
    }
}
