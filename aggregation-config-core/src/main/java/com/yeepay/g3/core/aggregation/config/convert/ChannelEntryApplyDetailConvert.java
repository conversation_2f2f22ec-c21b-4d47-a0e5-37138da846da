package com.yeepay.g3.core.aggregation.config.convert;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.DigitalCurrencyEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryOrder;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.factory.EntryApplyFactory;
import com.yeepay.g3.core.aggregation.config.utils.GMUtils;
import com.yeepay.g3.core.aggregation.config.utils.SpringContextUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/9 23:06
 */
public class ChannelEntryApplyDetailConvert {

    private static final EntryApplyFactory entryApplyFactory = SpringContextUtils.getBean(EntryApplyFactory.class);

    private ChannelEntryApplyDetailConvert() {
    }

    public static List<ChannelEntryApplyDetailEntity> convert(final EntryOrder entryOrder) {

        return entryOrder.getEntryApplies().stream()
                .map(entryApply -> {
                    String bankCode = "";
                    if (entryApply instanceof DigitalCurrencyEntryApply) {
                        bankCode = ((DigitalCurrencyEntryApply) entryApply).getBankCode();
                    }
                    return ChannelEntryApplyDetailEntity.builder()
                            .applyNo(entryApply.getApplyNo())
                            .orderNo(entryOrder.getOrderNo())
                            .merchantNo(entryApply.getMerchantInfo().getMerchantNo())
                            .merchantName(entryApply.getMerchantInfo().getMerchantName())
                            .channelNo(Optional.ofNullable(entryApply.getChannelNo()).orElse(""))
                            .channelIdentifier(Optional.ofNullable(entryApply.getChannelIdentifier()).orElse(""))
                            .entryType(entryApply.getEntryType().getDocument())
                            .payScene(entryApply.getPayScene().getDocument())
                            .payChannel(entryApply.getPayChannel().getDocument())
                            .bankCode(bankCode)
                            .activityType(entryApply.getActivityType().getDocument())
                            .status(entryApply.getEntryStatus().getDocument())
                            .auditStatus(entryApply.getEntryAuditStatus().getDocument())
                            .backupCount(Optional.ofNullable(entryApply.getBackupCounts()).orElse(0))
                            .channelParams(GMUtils.encrypt(JsonUtils.toJSONString(entryApply.getChannelSpecialParams())))
                            .flag(entryApply.getFlag())
                            .flagStatus(entryApply.getFlagStatus())
                            .extendInfo(entryApply.getExtendInfo())
                            .build();
                }).collect(Collectors.toList());
    }

    public static ChannelEntryApplyDetailEntity convertEntryStatusUpdateEntity(final BaseEntryApply<? extends ChannelSpecialParams> entryApply) {
        String failReason = entryApply.getFailReason();
        if (StringUtils.isNotBlank(failReason) && failReason.length() > 85) {
            failReason = failReason.substring(0, 85);
        }
        return ChannelEntryApplyDetailEntity.builder()
                .applyNo(entryApply.getApplyNo())
                .channelNo(entryApply.getChannelNo())
                .channelIdentifier(entryApply.getChannelIdentifier())
                .status(entryApply.getEntryStatus() == null ? null : entryApply.getEntryStatus().getDocument())
                .auditStatus(entryApply.getEntryAuditStatus() == null ? null : entryApply.getEntryAuditStatus().getDocument())
                .backupCount(entryApply.getBackupCounts())
                .channelMchNos(entryApply.getChannelMchNos())
                .finishTime(entryApply.getFinishTime())
                .signUrl(entryApply.getSignUrl())
                .failReason(failReason)
                .channelGatewayCode(entryApply.getChannelGatewayCode() == null ? null : entryApply.getChannelGatewayCode().getDocument())
                .build();
    }

    public static ChannelEntryApplyDetailEntity convertToFlagStatusUpdateEntity(final BaseEntryApply<? extends ChannelSpecialParams> entryApply) {
        return ChannelEntryApplyDetailEntity.builder()
                .flagStatus(entryApply.getFlagStatus())
                .anchoredOrderNo(entryApply.getAnchoredApplyNo())
                .status(entryApply.getEntryStatus().getDocument())
                .build();
    }

    public static BaseEntryApply<ChannelSpecialParams> convertToBO(final ChannelEntryOrderEntity entryOrder,
                                                                   final ChannelEntryApplyDetailEntity detail) {
        PayChannelEnum payChannelEnum = DocumentedEnum.fromValue(PayChannelEnum.class, detail.getPayChannel());
        PaySceneEnum paySceneEnum = DocumentedEnum.fromValue(PaySceneEnum.class, detail.getPayScene());

        return entryApplyFactory.getHandler(payChannelEnum, paySceneEnum).buildEntryApply(entryOrder, detail);
    }

    public static List<BaseEntryApply<? extends ChannelSpecialParams>> convertToBO(ChannelEntryOrderEntity order, List<ChannelEntryApplyDetailEntity> detailList) {
        List<BaseEntryApply<? extends ChannelSpecialParams>> resultList = new ArrayList<>();
        for (ChannelEntryApplyDetailEntity detailEntity : detailList) {
            resultList.add(convertToBO(order, detailEntity));
        }
        return resultList;
    }
}
