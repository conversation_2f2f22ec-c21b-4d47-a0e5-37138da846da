package com.yeepay.g3.core.aggregation.config.convert;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryAuditStatus;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.enums.ChannelGatewayCode;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportQueryResponseDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportResponseDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayReportRecordDTO;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/11 11:08
 */
public class ChannelResultConvert {

    private static final String CHANNEL_ENTRY_SUCCESS = "SUCCESS";
    private static final String CHANNEL_ENTRY_FAILED = "FAILED";


    public static ChannelEntryResult convert(final OpenPayAsyncReportResponseDTO response, final String requestNo) {

        final ChannelEntryResult.ChannelEntryResultBuilder builder = ChannelEntryResult.builder()
                .requestNo(requestNo);

        final List<ChannelEntryResult.ChannelEntryDetail> entryDetails = response.getOpenPayReportRecordDTOs().stream()
                .filter(Objects::nonNull)
                .map(ChannelResultConvert::convertToDetail)
                .collect(Collectors.toList());
        builder.channelEntryDetails(entryDetails);

        return builder.build();
    }

    public static ChannelEntryResult convert(final OpenPayAsyncReportQueryResponseDTO response, final String requestNo) {

        final ChannelEntryResult.ChannelEntryResultBuilder builder = ChannelEntryResult.builder()
                .requestNo(requestNo);

        final List<ChannelEntryResult.ChannelEntryDetail> entryDetails = response.getOpenPayReportRecordDTOs().stream()
                .filter(Objects::nonNull)
                .map(ChannelResultConvert::convertToDetail)
                .collect(Collectors.toList());
        builder.channelEntryDetails(entryDetails);

        return builder.build();
    }

    private static ChannelEntryResult.ChannelEntryDetail convertToDetail(final OpenPayReportRecordDTO record) {
        if (CHANNEL_ENTRY_SUCCESS.equals(record.getReportStatus())) {
            return ChannelEntryResult.ChannelEntryDetail.success(record);
        } else if (CHANNEL_ENTRY_FAILED.equals(record.getReportStatus())) {
            return ChannelEntryResult.ChannelEntryDetail.fail(record);
        } else {
            return ChannelEntryResult.ChannelEntryDetail.processing(record);
        }
    }

    public static EntryAuditStatus convertAuditStatus(final String auditStatus, final String reportStatus) {
        //间连没有auditStatus， 待实现
        if (StringUtils.isBlank(auditStatus)) {
            return EntryAuditStatus.INIT;
        }
        switch (auditStatus) {
            case "APPLYMENT_STATE_EDITTING":
                return EntryAuditStatus.EDITING;
            case "APPLYMENT_STATE_AUDITING":
                return EntryAuditStatus.AUDITING;
            case "APPLYMENT_STATE_REJECTED":
                return EntryAuditStatus.REJECTED;
            case "APPLYMENT_STATE_TO_BE_CONFIRMED":
                return EntryAuditStatus.TO_BE_CONFIRMED;
            case "APPLYMENT_STATE_TO_BE_SIGNED":
                return EntryAuditStatus.TO_BE_SIGNED;
            case "APPLYMENT_STATE_SIGNING":
                return EntryAuditStatus.SIGNING;
            case "APPLYMENT_STATE_FINISHED":
                return EntryAuditStatus.FINISHED;
            case "APPLYMENT_STATE_CANCELED":
                return EntryAuditStatus.CANCELED;
            default:
                return EntryAuditStatus.INIT;
        }
    }

    public static ChannelGatewayCode convertChannelGatewayCode(final String reportType, final String bankCode) {
        if (StringUtils.isBlank(reportType)) {
            return null;
        }
        switch (reportType) {
            case "UP":
                return ChannelGatewayCode.UNIONPAY;
            case "NUCC":
                return ChannelGatewayCode.NUCC;
            case "UPANDNUCC":
                return ChannelGatewayCode.UNIONPAY_NUCC;
            case "DIRECT":
                if ("WECHAT".equals(bankCode)) {
                    return ChannelGatewayCode.WECHAT;
                }
                throw new IllegalArgumentException("不支持的渠道报备类型");
            case "DRMB":
                return ChannelGatewayCode.BANK;
            default:
                throw new IllegalArgumentException("不支持的渠道报备类型");
        }
    }
}



