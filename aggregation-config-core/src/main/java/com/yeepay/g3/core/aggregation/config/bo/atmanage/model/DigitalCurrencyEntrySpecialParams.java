package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.DigitalCurrencyConfigCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/6/8 14:10
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class DigitalCurrencyEntrySpecialParams implements ChannelSpecialParams {
    /**
     * 银行编码
     */
    private final String bankCode;

    private final SettleAccountInfo settleAccountInfo;

    private final String extendInfo;


    public static DigitalCurrencyEntrySpecialParams build(DigitalCurrencyConfigCmd cmd) {
        return DigitalCurrencyEntrySpecialParams.builder()
                .bankCode(cmd.getBankCode())
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .extendInfo(cmd.getExtendInfo())
                .build();
    }
}
