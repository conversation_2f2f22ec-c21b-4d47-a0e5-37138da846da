package com.yeepay.g3.core.aggregation.config.factory.handle;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.auth.AuthApplyBO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;

/**
 * description: 支付宝商户信息处理类
 * <AUTHOR>
 * @since 2025/5/22:11:06
 * Company: 易宝支付(YeePay)
 */
public class AliPayMerchantInfoStrategy implements MerchantInfoStrategy {

    @Override
    public AuthApplyBO buildAndValidateAuth(AuthApplyReqDTO applyDTO) {
//        AuthApplyBO authApplyBO = AuthApplyBO.buildAuthApplyBO(applyDTO);
//        // 校验实名认证基础参数
//        authApplyBO.validateAuthBase();
//
//        //  校验联系人信息
//        authApplyBO.getContactInfo().validateAuthAliPay();
//
//        // 校验主体信息
//        authApplyBO.getSubjectInfo().validateAuthBase();
//        // 校验主体信息-法人信息
//        authApplyBO.getSubjectInfo().getCompanyRepresentativeInfo().validateAuthAliPay();
//
//        // 校验 UBO 信息
//        List<UboInfoBO> uboInfoList = authApplyBO.getUboInfoList();
//        if (!CheckUtils.isEmpty(uboInfoList)) {
//            uboInfoList.forEach(UboInfoBO::validateAuthAliPay);
//        }
//        return authApplyBO;
        return null;
    }
}
