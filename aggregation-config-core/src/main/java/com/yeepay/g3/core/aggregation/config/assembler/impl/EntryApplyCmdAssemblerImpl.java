package com.yeepay.g3.core.aggregation.config.assembler.impl;

import com.yeepay.g3.core.aggregation.config.assembler.EntryApplyCmdAssembler;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.EntryApplyCmd;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.EntryApplyRequest;

/**
 * <AUTHOR>
 * @date 2025/5/23 14:29
 */
public class EntryApplyCmdAssemblerImpl implements EntryApplyCmdAssembler {
    @Override
    public EntryApplyCmd assemble(final EntryApplyRequest entryApplyRequest) {

        return EntryApplyCmd.create(entryApplyRequest);
    }
}
