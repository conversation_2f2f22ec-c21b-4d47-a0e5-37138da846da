package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-16 18:28
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface AppIdMerchantBindService {

    /**
     * 保存
     *
     * @param appIdMerchantBindEntity appIdMerchantBindEntity
     * @return 返回是否需要处理
     */
    Boolean receiveMqData(AppIdMerchantBindEntity appIdMerchantBindEntity);

    /**
     * 批量保存all表
     *
     * @param appIdMerchantBindEntity appIdMerchantBindEntity
     * @param listPair                    list
     */
    void batchMerchantAll(AppIdMerchantBindEntity appIdMerchantBindEntity, Pair<List<AppidAllMerchantNoBindEntity>, List<AppidAllMerchantNoBindEntity>> listPair);

    /**
     * 根据 appId 和商户编号查询 AppidMerchantBind 记录
     * @param appId 应用ID
     * @param merchantNo 商户编号
     * @return 查询到的实体对象，如果没有则返回 null
     */
    AppIdMerchantBindEntity queryByAppIdAndMerchantNo(String appId, String merchantNo);

    /**
     * 根据appId 和顶级商户编号查询
     * @param appId appid
     * @param firstMerchantNo 顶级商户编号
     * @return  list
     */
    List<AppidAllMerchantNoBindEntity> queryAllMerchantNoBind(String appId, String firstMerchantNo);

    /**
     * 根据 appid 和挂靠商编查询
     * @param appId appid
     * @param attachMerchantNo 挂靠商编
     * @return
     */
    List<AppidAllMerchantNoBindEntity> queryByAppidAndMerchantNoAndAttachMerchantNo(String appId,String attachMerchantNo);

    /**
     * 根据商编查询appId和商编的绑定关系
     *
     * @param appidList appid列表
     * @param attachMerchantNo 挂靠商编
     * @return 绑定关系
     */
    List<AppidAllMerchantNoBindEntity> queryAllByAppIdList(List<String> appidList,String attachMerchantNo , String merchantNo);

    /**
     * 查询appi列表
     * @param merchantNo  商户编号
     * @return list
     */
    List<AppIdMerchantBindEntity> queryAppidByMerchantNoAndChannelType(String merchantNo, ChannelTypeEnum channelType);

    /**
     * 根据appid 查询所有的一级商编
     * @param appid
     * @param warningStatus
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/18 18:30
     */
    List<String> queryByAppidGroupByFirstMerchantNo(String appid,String warningStatus);


    /**
     * 更新报警通知为已经成功
     * @param appid
     * @param topMerchants
     * @return int
     * <AUTHOR>
     * @date 2024/12/19 16:48
     */
    int updateWaringStatus(String appid, List<String> topMerchants);


    /**
     * 根据appid和配置通知的 查询顶级商编
     * @param appid
     * @param notifyStatus
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/19 17:14
     */
    List<String> getTopMerchantsByAppidAndConfigAndEmail(String appid, String notifyStatus,int merchantGrade);

    /**
     * 根据appid和配置通知的 查询顶级商编
     * @param appid
     * @param notifyStatus
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/12/19 17:14
     */
    List<String> getTopMerchantsByAppidAndConfigAndUrl(String appid, String notifyStatus,int merchantGrade);

    /**
     * 更新通知状态
     * @param topMerchant
    * @param appid
    * @param subMerchantNos
    * @param emailNotifyStatus
     * @return void
     * <AUTHOR>
     * @date 2024/12/19 19:20
     */
    void updateNotifyStatusEmail(String topMerchant, String appid,List<String> subMerchantNos, String emailNotifyStatus, int emailLevel ,int merchantGrade);

    /**
     * 更新接口通知状态
     * @param topMerchant
     * @param appid
     * @param subMerchantNos
     * @param urlNotifyStatus
     * @return void
     * <AUTHOR>
     * @date 2024/12/19 19:20
     */
    void updateNotifyStatusUrl(String topMerchant, String appid, List<String> subMerchantNos, String urlNotifyStatus, int urlLevel, int merchantGrade);

    /**
     * 根据 appid 查询绑定数据
     *
     * @param appId      appid
     * @param merchantNo
     * @return
     */
    List<AppIdMerchantBindEntity> queryBindByAppid(String appId,String merchantNo, DataCleanStatusEnum cleanStatus);

    /**
     * 根据等级查询all表
     * @param appid
    * @param merchantGradeNo
    * @param merchantGrade
     * @return java.util.List<com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity>
     * <AUTHOR>
     * @date 2024/12/27 12:54
     */
    List<AppidAllMerchantNoBindEntity> queryAllMerchantNoBindByMerchantGrade(String appid, String merchantGradeNo, int merchantGrade);

    /**
     * 更新appid对应的级别
     * @param appid
     * @return void
     * <AUTHOR>
     * @date 2024/12/31 11:19
     */
    void updateLevelByAppid(String appid);
}
