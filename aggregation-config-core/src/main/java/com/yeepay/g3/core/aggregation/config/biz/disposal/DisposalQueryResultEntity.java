package com.yeepay.g3.core.aggregation.config.biz.disposal;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 17:27
 */

@Setter
@Getter
public class DisposalQueryResultEntity implements Serializable {

    private static final long serialVersionUID = 5436544829345304822L;

    private Integer totalCount;

    private List<DisposalRecordEntity> disposalRecordEntityList;


}
