package com.yeepay.g3.core.aggregation.config.bo.atmanage.auth;

import com.yeepay.aggregation.config.share.kernel.enumerate.AuthStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * description: 认证结果
 * <AUTHOR>
 * @since 2025/5/26:19:13
 * Company: 易宝支付(YeePay)
 */

@Getter
@Setter
@Builder
public class AuthResultBO implements Serializable {
    private static final long serialVersionUID = 4924869989276436014L;

    /**
     * 认证状态
     */
    private AuthStatusEnum authStatusEnum;

    /**
     * 二维码数据
     */
    private String qrcodeData;

    /**
     * 认证驳回原因列表
     */
    private List<AuthRejectReasonBO> rejectReasonList;
}
