package com.yeepay.g3.core.aggregation.config.flow;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/27 18:01
 */
@FunctionalInterface
public interface ContextMatcher {

    /**
     * 是否匹配上下文
     */
    boolean match(FlowContext context);

    /**
     * 与另一个匹配器与
     */
    default ContextMatcher and(ContextMatcher other) {
        Objects.requireNonNull(other);
        return c -> match(c) && other.match(c);
    }

    /**
     * 与另一个匹配器或
     */
    default ContextMatcher or(ContextMatcher other) {
        Objects.requireNonNull(other);
        return c -> match(c) || other.match(c);
    }

    /**
     * 反向
     */
    default ContextMatcher negate() {
        return c -> !match(c);
    }
}
