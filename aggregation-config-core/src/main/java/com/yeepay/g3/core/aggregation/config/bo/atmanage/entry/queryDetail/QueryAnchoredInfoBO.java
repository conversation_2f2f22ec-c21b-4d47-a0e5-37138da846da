package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryAnchoredInfoDTO;
import com.yeepay.g3.facade.aggregation.pay.dto.AttachRecordInfoDTO;
import com.yeepay.g3.facade.aggregation.pay.enums.AttachConfigStatusEnum;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayReportRelationRecordDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description: 查询挂靠信息查询结果
 *
 * <AUTHOR>
 * @since 2025/6/10:14:14
 * Company: 易宝支付(YeePay)
 */
@Builder
@Getter
public class QueryAnchoredInfoBO implements Serializable {

    private static final long serialVersionUID = -4826540282716761072L;
    /**
     * 挂靠状态
     */
    private AnchoredStatus anchoredStatus;

    /**
     * 关联申请单号
     */
    private String bizApplyNo;

    /**
     * 关联失败原因
     */
    private String failMessage;

    /**
     * 挂靠商编
     */
    private String anchoredMerchantNo;

    /**
     * 挂靠渠道号
     */
    private String anchoredChannelNo;

    /**
     * 支付渠道（必填）
     */
    private PayChannelEnum payChannel;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型（必填）
     */
    private ActivityTypeEnum activityType;

    public static List<QueryAnchoredInfoBO> buildList(List<AttachRecordInfoDTO> infoDTOList) {
        if (CheckUtils.isEmpty(infoDTOList)) {
            return new ArrayList<>();
        }
        return infoDTOList.stream().map(e -> QueryAnchoredInfoBO.builder()
                .anchoredStatus(getAnchoredStatusByAgg(e.getConfigStatus()))
                .failMessage(e.getFailureReason())
                .anchoredMerchantNo(e.getMainMerchantNo())
                .payChannel(DocumentedEnum.fromValueOfNullable(PayChannelEnum.class, e.getChannel()))
                .payScene(DocumentedEnum.fromValueOfNullable(PaySceneEnum.class, e.getScene()))
                .activityType(CheckUtils.isEmpty(DocumentedEnum.fromValueOfNullable(ActivityTypeEnum.class, e.getPromotionType()))
                        ? ActivityTypeEnum.NORMAL
                        : DocumentedEnum.fromValueOfNullable(ActivityTypeEnum.class, e.getPromotionType()))
                .build()).collect(Collectors.toList());
    }

    private static AnchoredStatus getAnchoredStatusByAgg(String configStatus) {
        if (AttachConfigStatusEnum.SUCCESS.name().equals(configStatus)) {
            return AnchoredStatus.SUCCESS;
        } else if (AttachConfigStatusEnum.PENDING.name().equals(configStatus)) {
            return AnchoredStatus.PROCESSING;
        } else {
            return AnchoredStatus.FAIL;
        }
    }

    public static List<QueryAnchoredInfoBO> buildChannelList(List<OpenPayReportRelationRecordDTO> infoDTOList) {
        if (CheckUtils.isEmpty(infoDTOList)) {
            return new ArrayList<>();
        }
        return infoDTOList.stream()
                .filter(e -> e.getMaskMerchantNo() != null
                        && e.getRequireMerchantNo() != null
                        && !e.getMaskMerchantNo().startsWith(e.getRequireMerchantNo()))
                .map(e -> {
                    SceneAndActivityTypeBO sceneAndPromotion = SceneAndActivityUtils.getSceneAndPromotion(e.getMaskReportFee(), Const.CHANNEL_ACTIVITY_NORMAL);
                    return QueryAnchoredInfoBO.builder()
                            .anchoredStatus(AnchoredStatus.SUCCESS)
                            .anchoredMerchantNo(e.getMaskMerchantNo())
                            .anchoredChannelNo(e.getMaskChannelNo())
                            .payChannel(sceneAndPromotion.getChannel())
                            .payScene(sceneAndPromotion.getPayScene())
                            .activityType(sceneAndPromotion.getActivityType())
                            .build();
                }).collect(Collectors.toList());
    }

    public String getUniqueKey() {
        return SceneAndActivityUtils.getUniqueKey(payChannel, payScene, activityType);
    }

    public static List<QueryAnchoredInfoDTO> buildListDTO(List<QueryAnchoredInfoBO> infoBOList) {
        if (CheckUtils.isEmpty(infoBOList)) {
            return new ArrayList<>();
        }
        return infoBOList.stream().map(e -> QueryAnchoredInfoDTO.builder()
                .anchoredStatus(e.getAnchoredStatus().getDocument())
                .bizApplyNo(e.getBizApplyNo())
                .failMessage(e.getFailMessage())
                .anchoredMerchantNo(e.getAnchoredMerchantNo())
                .anchoredChannelNo(e.getAnchoredChannelNo())
                .build()).collect(Collectors.toList());
    }

}
