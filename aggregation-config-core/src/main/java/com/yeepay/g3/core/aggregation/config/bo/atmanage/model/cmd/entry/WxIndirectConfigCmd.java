package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.MerchantInfoDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.WxIndirectConf;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 微信间连
 *
 * <AUTHOR>
 * @date 2025/6/2 17:14
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
public class WxIndirectConfigCmd {

    /**
     * 支付场景
     */
    private final PaySceneEnum payScene;

    /**
     * 支付渠道
     */
    private final PayChannelEnum payChannel;

    /**
     * 活动信息
     */
    private ActivityInfo activityInfo;

    /**
     * 微信商户经营类目
     */
    private final String mcc;

    /**
     * 手续费规则id 微信费率规则号
     */
    private final String feeRuleId;

    /**
     * 微信渠道号
     */
    private final String channelNo;

    /**
     * 渠道标识 易宝侧商编 会根据此商编的渠道号下进行进件
     */
    private final String channelIdentifier;

    /**
     * 进件类型
     */
    private final EntryTypeEnum entryType;

    /**
     * 挂靠信息
     */
    private final AnchoredCmd anchoredInfo;

    /**
     * 是否需要终端报备
     */
    private Boolean isNeedTerminalReport;

    /**
     * 备份数量
     */
    private final Integer backupCount;

    private SettleAccountInfo settleAccountInfo;

    private String extendInfo;

    public static WxIndirectConfigCmd build(final WxIndirectConf wxIndirectConf, final MerchantInfoDTO merchantInfo) {

        Assert.notNull(wxIndirectConf, ResultCode.PARAM_VALID_ERROR, "微信间连进件配置不能为空");
        final EntryTypeEnum entryType = DocumentedEnum.fromValue(EntryTypeEnum.class, wxIndirectConf.getEntryType());
        final PaySceneEnum payScene = DocumentedEnum.fromValue(PaySceneEnum.class, wxIndirectConf.getPayScene());
        final ActivityInfo activityInfo = ActivityInfo.build(wxIndirectConf.getActivityInfo());
        final WxIndirectConfigCmdBuilder builder = WxIndirectConfigCmd.builder()
                .payScene(payScene)
                .payChannel(PayChannelEnum.WECHAT)
                .activityInfo(activityInfo)
                .mcc(wxIndirectConf.getMcc())
                .feeRuleId(wxIndirectConf.getFeeRuleId())
                .channelNo(wxIndirectConf.getChannelNo())
                .channelIdentifier(wxIndirectConf.getChannelIdentifier())
                .settleAccountInfo(SettleAccountInfo.build(wxIndirectConf.getSettleAccountInfo()))
                .entryType(entryType)
                .backupCount(wxIndirectConf.getBackupCount())
                .isNeedTerminalReport(wxIndirectConf.getIsNeedTerminalReport())
                .extendInfo(wxIndirectConf.getExtendInfo());

        if (Boolean.TRUE.equals(EntryTypeEnum.needAnchored(entryType))) {
            Assert.notNull(wxIndirectConf.getAnchoredInfo(), ResultCode.PARAM_VALID_ERROR, "微信间连进件需要挂靠时挂靠信息不能为空");
            final AnchoredCmd anchoredInfo = AnchoredCmd.build(wxIndirectConf.getAnchoredInfo(), merchantInfo, payScene,
                    PayChannelEnum.WECHAT, activityInfo.getActivityType());
            builder.anchoredInfo(anchoredInfo);
        }


        return builder.build();
    }
}
