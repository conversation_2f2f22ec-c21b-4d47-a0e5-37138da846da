package com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: Mr.yin
 * @date: 2025/6/30  16:25
 */
@Data
public class ChannelAnchoredResultBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 挂靠订单请求通道请求号
     */
    private String requestNo;

    /**
     * 挂靠结果
     * SUCCESS/FAIL
     */
    private String status;

    public static ChannelAnchoredResultBO success(String requestNo) {
        ChannelAnchoredResultBO channelAnchoredResultBO = new ChannelAnchoredResultBO();
        channelAnchoredResultBO.setRequestNo(requestNo);
        channelAnchoredResultBO.setStatus("SUCCESS");
        return channelAnchoredResultBO;
    }

    public static ChannelAnchoredResultBO fail(String requestNo) {
        ChannelAnchoredResultBO channelAnchoredResultBO = new ChannelAnchoredResultBO();
        channelAnchoredResultBO.setRequestNo(requestNo);
        channelAnchoredResultBO.setStatus("FAIL");
        return channelAnchoredResultBO;
    }

}
