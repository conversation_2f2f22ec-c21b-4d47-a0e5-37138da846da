package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity;

/**
 * @description: 客户中心外部域
 * @author: xuchen.liu
 * @date: 2024-12-14 20:04
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface MerchantCenterExternal {

    /**
     * 查询商户行业线
     * @param firstMerchantNo  一级商编
     */
    String getIndustryLineByTopMerchant(String firstMerchantNo);

    /**
     * 根据商编查询商户分组
     * @param merchantNo 商户编号
     */
    MerchantGroupEntity queryMerchantGroupByMerchantNo(String merchantNo);
}
