package com.yeepay.g3.core.aggregation.config.factory.anchored.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.ChannelAnchoredResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity;
import com.yeepay.g3.core.aggregation.config.enums.OrderStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.external.ChannelAnchoredExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.ChannelAnchoredRecordBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.factory.anchored.AnchoredApplyHandler;
import com.yeepay.g3.core.aggregation.config.mq.producer.impl.AnchoredResultProducerImpl;
import com.yeepay.g3.core.aggregation.config.service.AnchoredApplyService;
import com.yeepay.g3.core.aggregation.config.service.AnchoredOrderService;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 挂靠申请处理
 *
 * @author: Mr.yin
 * @date: 2025/6/6  18:05
 */
@Component
public abstract class BaseAnchoredApplyHandler implements AnchoredApplyHandler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ChannelAnchoredExternal channelAnchoredExternal;

    @Resource
    private AnchoredApplyService anchoredApplyService;

    @Resource
    private AnchoredOrderService anchoredOrderService;

    @Resource
    private ThirdPartyRecordService thirdPartyRecordService;

    @Resource
    private AnchoredResultProducerImpl anchoredResultProducer;

    @Resource
    @Qualifier("anchoredHandleExecutor")
    private AtlasThreadPoolExecutor anchoredHandleExecutor;

    /**
     * 挂靠申请公共逻辑处理
     *
     */
    @Override
    public void anchoredApplyHandle(AnchoredApply anchoredApply) {
        logger.info("[anchoredApplyHandle] anchoredApply={} ", JsonUtils.toJSONString(anchoredApply));
        if (AnchoredStatus.INIT == anchoredApply.getAnchoredStatus()) {
            anchoredApplyService.updateAnchoredProcess(anchoredApply);
        }
        /*2.请求聚合挂靠*/
        if (AnchoredStatus.PROCESSING == anchoredApply.getAnchoredStatus()) {
            RemoteResult<List<AggAnchoredMerchantInfo>> aggAnchoredResult = aggregationAnchoredApply(anchoredApply);
            /*2.1持久化聚合结果*/
            aggAnchoredResultHandle(anchoredApply, aggAnchoredResult);
        }
        Boolean finishTag = AnchoredApply.isFinish(anchoredApply.getAnchoredStatus(), anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension());
        logger.info("[聚合挂靠]执行后结果 finishTag={}, anchoredApplyNo={} ", finishTag, anchoredApply.getApplyNo());
        /*3.请求通道挂靠*/
        if (DocumentedEnum.inRightEnums(anchoredApply.getAnchoredStatus(), AnchoredStatus.AGG_SUCCESS)) {
            if (Boolean.TRUE.equals(finishTag) && Boolean.TRUE.equals(anchoredApply.getSingSync())) {/*如果逻辑上已经闭环，则后边的异步处理*/
                CompletableFuture.runAsync(() -> {
                    channelAnchoredApply(anchoredApply);
                }, anchoredHandleExecutor);
            } else {
                channelAnchoredApply(anchoredApply);
            }
        }

    }

    /**
     * 聚合挂靠结果更新
     *
     */
    private void aggAnchoredResultHandle(AnchoredApply anchoredApply, RemoteResult<List<AggAnchoredMerchantInfo>> aggAnchoredResult) {
        /*更新聚合挂靠状态*/
        anchoredApply.buildAggAnchoredResult(aggAnchoredResult);
        if (!aggAnchoredResult.isSuccess()) {
            logger.warn("[聚合挂靠结果更新] 聚合结果更新失败 anchoredApplyNo={} ", anchoredApply.getApplyNo());
            anchoredApplyService.updateAggAnchoredResultFail(anchoredApply.getApplyNo(), aggAnchoredResult.getMessage(), aggAnchoredResult.getCode());
            /*查询是不是全完成了*/
            checkAnchoredOrderApplyAllFinish(anchoredApply);
        } else {
            /*同时落一下三方调用记录，根据他来决定结果 这里因为同主体的关系，会膨胀为列表*/
            anchoredApplyService.updateAggAnchoredResultSuccess(anchoredApply);
        }
        logger.info("[聚合挂靠结果更新] 检查一下部分场景已经可以发送消息 anchoredApplyNo={} ", anchoredApply.getApplyNo());
        //发送消息通知挂靠完成；有一些聚合成功的也发
        anchoredResultProducer.sendAnchoredApplyFinishMessage(anchoredApply);

    }


    /**
     * 请求通道挂靠
     *
     */
    @Override
    public void channelAnchoredApply(AnchoredApply anchoredApply) {
        logger.info("[请求通道挂靠] 异步处理 anchoredApply={} ", JsonUtils.toJSONString(anchoredApply));
        Boolean finishTag = AnchoredApply.isFinish(anchoredApply.getAnchoredStatus(), anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension());
        if (Boolean.TRUE.equals(anchoredApply.getSingSync()) && Boolean.FALSE.equals(finishTag)) {
            if (anchoredApply.getAggAnchoredMerchantInfoList().size() == 1) {
                channelAnchoredApply(anchoredApply, anchoredApply.getAggAnchoredMerchantInfoList().get(0));
                return;
            }
            multiThreadHandleChannelResult(anchoredApply);
        } else {/*上边本身就是异步了，这里就不需要异步了*/
            anchoredApply.getAggAnchoredMerchantInfoList().forEach(aggAnchoredMerchantInfo -> channelAnchoredApply(anchoredApply, aggAnchoredMerchantInfo));
        }

    }

    /**
     * 针对需要拿到同步结果的场景，多线程处理结果
     */
    private void multiThreadHandleChannelResult(AnchoredApply anchoredApply) {
        logger.info("[multiThreadHandleChannelResult] 线程处理开始 anchoredApply={} ", JsonUtils.toJSONString(anchoredApply));
        // 1. 创建任意一个成功即完成的条件
        CompletableFuture<Object> firstSuccessOrAllFailed = new CompletableFuture<>();
        AtomicBoolean successFlag = new AtomicBoolean(false); // 标记是否已有成功
        // 2.使用CompletableFuture实现多线程处理,在每个任务上迈上线程埋点

        // 3. 所有任务完成后检查是否无成功
        CompletableFuture.allOf(anchoredApply.getAggAnchoredMerchantInfoList().stream()
                        .map(aggAnchoredMerchantInfo -> CompletableFuture.supplyAsync(() -> {
                            ChannelAnchoredResultBO result = channelAnchoredApply(anchoredApply, aggAnchoredMerchantInfo);
                            if ("SUCCESS".equals(result.getStatus())) {
                                logger.info("[multiThreadHandleChannelResult] 单条线程处理成功 requestNo={},Status={} ", result.getRequestNo(), result.getStatus());
                                // 发现成功且尚未处理过
                                if (successFlag.compareAndSet(false, true)) {
                                    logger.info("[multiThreadHandleChannelResult] 任务处理成功 requestNo={},Status={} ", result.getRequestNo(), result.getStatus());
                                    firstSuccessOrAllFailed.complete("SUCCESS"); // 触发主流程继续
                                }
                            }
                            return result;
                        }, anchoredHandleExecutor)).toArray(CompletableFuture[]::new))
                .whenComplete((v, ex) -> {
                    if (!successFlag.get()) {
                        logger.info("[multiThreadHandleChannelResult] 所有线程都已完成，但是没等到成功结果");
                        firstSuccessOrAllFailed.complete("CHANNEL_FAIL");
                    }
                });

        // 4. 主线程等待：第一个成功 或 全部失败
        try {
            String status = (String) firstSuccessOrAllFailed.get(); // 阻塞直到任一成功或全部失败
            anchoredApply.updateChannelAnchoredResult(DocumentedEnum.fromValueOfNullable(AnchoredStatus.class, status), null);
            logger.info("[multiThreadHandleChannelResult] 线程处理完成拿到同步结果 anchoredApply={} ", JsonUtils.toJSONString(anchoredApply));
        } catch (Exception e) {
            logger.error(String.format("[通道挂靠][多线程处理] 获取处理结果异常 anchoredApply={%s}) cased by", JsonUtils.toJSONString(anchoredApply)), e);
            throw new BusinessException(ResultCode.SERVER_ERROR, "系统异常");
        }
    }

    /**
     * 调用通道进行挂靠
     */
    private ChannelAnchoredResultBO channelAnchoredApply(AnchoredApply anchoredApply, AggAnchoredMerchantInfo aggAnchoredMerchantInfo) {
        logger.info("[调用通道进行挂靠] 处理开始 aggAnchoredMerchantInfo={} aggAnchoredMerchantInfo={} ", JsonUtils.toJSONString(aggAnchoredMerchantInfo), JsonUtils.toJSONString(anchoredApply));
        ThirdPartyRecord thirdPartyRecord = thirdPartyRecordService.queryThirdRecordByRequestNo(aggAnchoredMerchantInfo.getRequestNo(), ThirdPartyBusinessTypeEnum.CHANNEL_ANCHORED.getDocument());
        if (DocumentedEnum.inRightEnums(thirdPartyRecord.getStatus(), ThirdPartyRecordStatusEnum.SUCCESS, ThirdPartyRecordStatusEnum.FAIL)) {
            /*查询其他订单是否完成*/
            logger.info("[调用通道进行挂靠] 该记录已完成 thirdPartyRecord={} ", JsonUtils.toJSONString(thirdPartyRecord));
            checkAnchoredApplyChannelFinish(anchoredApply);
            ChannelAnchoredResultBO channelAnchoredResultBO = new ChannelAnchoredResultBO();
            channelAnchoredResultBO.setRequestNo(aggAnchoredMerchantInfo.getRequestNo());
            channelAnchoredResultBO.setStatus(DocumentedEnum.inRightEnums(thirdPartyRecord.getStatus(), ThirdPartyRecordStatusEnum.SUCCESS) ? "SUCCESS" : "FAIL");
            return channelAnchoredResultBO;
        }
        RemoteResult<List<ChannelAnchoredRecordBO>> channelAnchoredResult = channelAnchoredExternal.channelAnchored(anchoredApply, aggAnchoredMerchantInfo);
        /*更新三方记录状态*/
        if (channelAnchoredResult.isSuccess()) {
            /*更新一下返回的挂靠渠道号*/
            aggAnchoredMerchantInfo.channelAnchoredSuccess(channelAnchoredResult.getData().get(0));
            anchoredApplyService.updateChannelAnchoredInfo(anchoredApply.getApplyNo(), aggAnchoredMerchantInfo);
            thirdPartyRecordService.updateThirdRecordChannelResult(aggAnchoredMerchantInfo.getRequestNo(),
                    channelAnchoredResult.getChannelCode(), channelAnchoredResult.getChannelMessage(), ThirdPartyRecordStatusEnum.SUCCESS);
            logger.info("[调用通道进行挂靠] 通道挂靠成功，更新三方记录，更新当前apply内的该条挂靠的挂靠商编信息 aggAnchoredMerchantInfo={}", JsonUtils.toJSONString(aggAnchoredMerchantInfo));
        } else {
            thirdPartyRecordService.updateThirdRecordChannelResult(aggAnchoredMerchantInfo.getRequestNo(),
                    channelAnchoredResult.getChannelCode(), channelAnchoredResult.getChannelMessage(), ThirdPartyRecordStatusEnum.FAIL);
            logger.info("[调用通道进行挂靠] 通道挂靠失败，更新当前三方记录为失败 requestNo={}", aggAnchoredMerchantInfo.getRequestNo());
        }
        /*查询其他订单是否完成*/
        checkAnchoredApplyChannelFinish(anchoredApply);
        ChannelAnchoredResultBO channelAnchoredResultBO = new ChannelAnchoredResultBO();
        channelAnchoredResultBO.setRequestNo(aggAnchoredMerchantInfo.getRequestNo());
        channelAnchoredResultBO.setStatus(channelAnchoredResult.isSuccess() ? "SUCCESS" : "FAIL");
        return channelAnchoredResultBO;
    }

    /**
     * 判断挂靠申请的 通道挂靠完成
     */
    public void checkAnchoredApplyChannelFinish(AnchoredApply anchoredApply) {
        /*查询其他订单是否完成*/
        List<ThirdPartyRecord> allRecords = thirdPartyRecordService.queryThirdPartyRecordByApplyNo(anchoredApply.getApplyNo(), ThirdPartyBusinessTypeEnum.CHANNEL_ANCHORED.getDocument());
        if (CollectionUtils.isEmpty(allRecords)) {
            return;
        }
        logger.info("[请求通道挂靠] 检验是否全部调用通道完成 allRecords={}", JsonUtils.toJSONString(allRecords));
        Map<String, List<ThirdPartyRecord>> groupedMap = allRecords.stream()
                .collect(Collectors.groupingBy(record ->
                        DocumentedEnum.inRightEnums(record.getStatus(), ThirdPartyRecordStatusEnum.SUCCESS, ThirdPartyRecordStatusEnum.FAIL, ThirdPartyRecordStatusEnum.CLOSED)
                                ? record.getStatus().name() : ThirdPartyRecordStatusEnum.PROCESSING.getDocument()
                ));
        if (!CollectionUtils.isEmpty(groupedMap.get(ThirdPartyRecordStatusEnum.PROCESSING.getDocument()))) {
            return;
        } else if (!CollectionUtils.isEmpty(groupedMap.get(ThirdPartyRecordStatusEnum.SUCCESS.getDocument()))) {
            anchoredApply.updateChannelAnchoredResult(AnchoredStatus.SUCCESS, null);
        } else if (!CollectionUtils.isEmpty(groupedMap.get(ThirdPartyRecordStatusEnum.FAIL.getDocument()))) {
            anchoredApply.updateChannelAnchoredResult(AnchoredStatus.CHANNEL_FAIL, groupedMap.get(ThirdPartyRecordStatusEnum.FAIL.getDocument()).get(0).getChannelResponseMessage());
        }
        anchoredApplyService.updateChannelAnchoredResult(anchoredApply.getApplyNo(), anchoredApply.getAnchoredStatus(), anchoredApply.getFailReason());
        logger.info("[请求通道挂靠] 确认全部调用通道完成 ", JsonUtils.toJSONString(anchoredApply));
        //发送消息通知挂靠完成
        anchoredResultProducer.sendAnchoredApplyFinishMessage(anchoredApply);
        /*查询是不是全完成了呢*/
        checkAnchoredOrderApplyAllFinish(anchoredApply);

    }


    /**
     * 检查同订单下的 挂靠申请是否全处理完成
     *
     * @param anchoredApply
     */
    public void checkAnchoredOrderApplyAllFinish(AnchoredApply anchoredApply) {

        AnchoredOrderEntity anchoredOrder = anchoredOrderService.queryAnchoredOrderByOrderNo(anchoredApply.getOrderNo());
        if (OrderStatusEnum.FINISH.getDocument().equals(anchoredOrder.getOrderStatus())) {
            return;
        }
        /*查询其他订单是否完成*/
        List<AnchoredApplyEntity> recordList = anchoredApplyService.queryAllAnchoredApplyByOrderNo(anchoredApply.getOrderNo());
        logger.info("[挂靠订单] 获取所有同订单下的挂靠申请结果 recordList={}", JsonUtils.toJSONString(recordList));
        boolean finishTag = recordList.stream().allMatch(record ->
                DocumentedEnum.stringInRightEnums(record.getAnchoredStatus(),
                        AnchoredStatus.SUCCESS,
                        AnchoredStatus.FAIL,
                        AnchoredStatus.CHANNEL_FAIL));
        if (finishTag) {
            logger.info("[挂靠订单] 同订单下的所有挂靠申请都处理完成 OrderNo={}", anchoredApply.getOrderNo());
            anchoredOrderService.updateAnchoredOrderFinish(anchoredApply.getOrderNo());
        }
    }

}
