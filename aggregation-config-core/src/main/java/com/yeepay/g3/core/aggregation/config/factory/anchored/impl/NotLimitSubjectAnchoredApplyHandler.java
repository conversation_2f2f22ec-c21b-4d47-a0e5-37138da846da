package com.yeepay.g3.core.aggregation.config.factory.anchored.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 基础挂靠申请处理器
 */
@Component
public class NotLimitSubjectAnchoredApplyHandler extends BaseAnchoredApplyHandler {


    @Override
    public boolean isSupport(AnchoredType anchoredType, Boolean anchoredDimension) {
        return AnchoredType.NOT_LIMIT_SUBJECT == anchoredType;
    }

    @Override
    public void validateAnchoredParams(AnchoredApply anchoredApply) {
        SceneAndActivityUtils.getSceneAndPromotionTypeBO(anchoredApply.getChannelType(), anchoredApply.getPayScene(), anchoredApply.getActivityType());

    }

    @Override
    public RemoteResult<List<AggAnchoredMerchantInfo>> aggregationAnchoredApply(AnchoredApply anchoredApply) {

        return RemoteResult.fail(ResultCode.NOT_SUPPORT_BIZ, "暂不支持的业务");
    }
}
