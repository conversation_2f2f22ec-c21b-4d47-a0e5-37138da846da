package com.yeepay.g3.core.aggregation.config.mq.listener;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.factory.EntryApplyFactory;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelEntryResultEvent;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * description: 进件结果监听
 *
 * <AUTHOR>
 * @since 2025/6/15:20:30
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ChannelEntryResultListener implements MessageListenerConcurrently {

    private final ThirdPartyRecordService thirdPartyRecordService;
    private final ChannelEntryOrderService channelEntryOrderService;
    private final EntryApplyFactory entryApplyFactory;


    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        LogGuidUtil.clearLogContext();
        MessageExt msg = list.get(0);
        String message = new String(msg.getBody());
        log.info("ChannelEntryResultListener接收到了消息[{}]，msgId={}", message, msg.getMsgId());
        ChannelEntryResultEvent callBackBO = JsonUtils.fromJson(message, ChannelEntryResultEvent.class);
        this.handleMessage(callBackBO);
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void handleMessage(ChannelEntryResultEvent message) {

        // 1. 依据 message - > ChannelEntryResult
        ChannelEntryResult channelEntryResult = getChannelEntryResult(message);
        log.info("[ChannelEntryResultListener] 处理进件结果回调, requestOrderNo: {}, channelEntryResult: {}", message.getRequestOrderNo(), JsonUtils.toJSONString(channelEntryResult));
        ThirdPartyRecord thirdPartyRecord = thirdPartyRecordService.queryThirdRecordByRequestNo(
                message.getRequestOrderNo(),
                ThirdPartyBusinessTypeEnum.CHANNEL_ENTRY.name());

        // 2. 查询 BaseEntryApply
        BaseEntryApply<? extends ChannelSpecialParams> channelEntryOrder = channelEntryOrderService.getChannelEntryApply(thirdPartyRecord.getApplyNo());
        log.info("[ChannelEntryResultListener] 获取进件申请单, applyNo: {}, channelEntryOrder{}", channelEntryOrder.getApplyNo(), JsonUtils.toJSONString(channelEntryOrder));

        // 3. 构建 EntryApplyContext
        EntryApplyContext entryApplyEntryApplyContext = new EntryApplyContext<>(channelEntryOrder);
        entryApplyEntryApplyContext.buildThirdPartyRecord(thirdPartyRecord);

        // 4. 处理回调
        log.info("[ChannelEntryResultListener] 处理进件结果回调, requestOrderNo: {}, channelEntryResult: {}", message.getRequestOrderNo(), JsonUtils.toJSONString(channelEntryResult));
        entryApplyFactory.getHandler(channelEntryOrder.getPayChannel(), channelEntryOrder.getPayScene())
                .resultHandle(entryApplyEntryApplyContext, channelEntryResult);
    }

    private ChannelEntryResult getChannelEntryResult(ChannelEntryResultEvent message) {
        switch (message.getStatus()) {
            case "SUCCESS":
                return ChannelEntryResult.builder().requestNo(message.getRequestOrderNo())
                        .innerChannelOrderNo(message.getBankOrderNo())
                        .channelEntryDetails(Collections.singletonList(ChannelEntryResult.ChannelEntryDetail.success(message)))
                        .build();
            case "AUDITING":
                return ChannelEntryResult.builder().requestNo(message.getRequestOrderNo())
                        .innerChannelOrderNo(message.getBankOrderNo())
                        .channelEntryDetails(Collections.singletonList(ChannelEntryResult.ChannelEntryDetail.processing(message)))
                        .build();
            case "FAILED":
                if (ChannelResultCode.getEntryFailedWaitRetryCode().contains(message.getErrCode())) {
                    return ChannelEntryResult.builder().requestNo(message.getRequestOrderNo())
                            .innerChannelOrderNo(message.getBankOrderNo())
                            .channelEntryDetails(Collections.singletonList(ChannelEntryResult.ChannelEntryDetail.processing(message)))
                            .build();
                }
                return ChannelEntryResult.builder().requestNo(message.getRequestOrderNo())
                        .innerChannelOrderNo(message.getBankOrderNo())
                        .channelEntryDetails(Collections.singletonList(ChannelEntryResult.ChannelEntryDetail.fail(message)))
                        .build();
            default:
                log.error("[ChannelEntryResultListener] 渠道结果处理异常, 状态码: {}", message.getStatus());
                throw new BusinessException(ResultCode.PARAM_VALID_ERROR, "接收到处理不了的状态!");
        }
    }
}
