package com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model;

import com.yeepay.g3.core.aggregation.config.external.bo.ChannelAnchoredRecordBO;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.AggSameReportRespBO;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 聚合返回的挂靠商编信息
 */
@Getter
@Setter(AccessLevel.PRIVATE)
public class AggAnchoredMerchantInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 挂靠商户的渠道号
     */
    private String anchoredChannelNo;

    /**
     * 挂靠商户的商户号
     */
    private String institutionMchNo;

    /**
     * 挂靠商户编号
     */
    private String anchoredMerchantNo;

    /**
     * 请求通道的订单号
     */
    private String requestNo;

    /**
     * 通道的挂靠成功标记 true
     */
    private String channelStatus;

    /**
     * 同主体转换出挂靠商编
     *
     * @param aggSameReportRespBO
     * @return
     */
    public static AggAnchoredMerchantInfo covert(AggSameReportRespBO aggSameReportRespBO) {
        AggAnchoredMerchantInfo payAnchoredMerchantInfo = new AggAnchoredMerchantInfo();
        payAnchoredMerchantInfo.setAnchoredChannelNo(aggSameReportRespBO.getAnchoredChannelNo());
        payAnchoredMerchantInfo.setInstitutionMchNo(aggSameReportRespBO.getInstitutionMchNo());
        payAnchoredMerchantInfo.setAnchoredMerchantNo(aggSameReportRespBO.getAnchoredMerchantNo());
        return payAnchoredMerchantInfo;
    }

    public static AggAnchoredMerchantInfo covert(String anchoredMerchantNo){
        AggAnchoredMerchantInfo aggAnchoredMerchantInfo = new AggAnchoredMerchantInfo();
        aggAnchoredMerchantInfo.setAnchoredMerchantNo(anchoredMerchantNo);
        return aggAnchoredMerchantInfo;
    }

    public void buildChannelRequestNo(String requestNo){
        this.requestNo = requestNo;
    }

    public void channelAnchoredSuccess(ChannelAnchoredRecordBO channelAnchoredRecordBO) {
        this.anchoredChannelNo = channelAnchoredRecordBO.getAnchoredMerchantChannelNo();
        this.institutionMchNo = channelAnchoredRecordBO.getAnchoredInstitutionMerchantNo();
        this.channelStatus = Boolean.TRUE.toString();
    }
}
