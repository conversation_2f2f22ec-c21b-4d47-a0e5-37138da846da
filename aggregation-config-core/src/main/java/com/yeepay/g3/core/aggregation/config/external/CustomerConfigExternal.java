package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.external.bo.CustomerConfigResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;

/**
 * 客户中心配置信息查询
 */

public interface CustomerConfigExternal {


    /**
     * 查询客户中心配置信息
     *
     * @return
     */
    RemoteResult<CustomerConfigResultBO> queryCustomerConfig(String merchantNo, String configKey);
}
