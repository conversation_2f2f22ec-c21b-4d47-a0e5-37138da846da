package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryNotifyBizTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.DivideReceiverBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.MerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WechatDirectEntryApply;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:11
 */
@Slf4j
@Service
public class WechatDirectEntryApplyHandler implements EntryApplyHandler<WechatDirectEntryApply> {

    private final ChannelNoBiz channelNoBiz;
    private final EntryApplyHandler<WechatDirectEntryApply> baseEntryApplyHandler;
    private final DivideReceiverBiz divideReceiverBiz;
    private final EntryResultProducer entryResultProducer;


    @Autowired
    public WechatDirectEntryApplyHandler(@Qualifier("baseEntryApplyHandler") final EntryApplyHandler<WechatDirectEntryApply> baseEntryApplyHandler,
                                         final ChannelNoBiz channelNoBiz,
                                         final DivideReceiverBiz divideReceiverBiz,
                                         final EntryResultProducer entryResultProducer) {
        this.baseEntryApplyHandler = baseEntryApplyHandler;
        this.channelNoBiz = channelNoBiz;
        this.divideReceiverBiz = divideReceiverBiz;
        this.entryResultProducer = entryResultProducer;
    }

    @Override
    public void buildChannelNo(final WechatDirectEntryApply entryApply) {
        MerchantInfo merchantInfo = entryApply.getMerchantInfo();
        // 1、微信直连
        String channelNo = channelNoBiz.queryChannelNoByDirect(PayChannelEnum.WECHAT,
                merchantInfo.getIndustryLine(), entryApply.getPayScene());
        entryApply.buildChannelNo(channelNo);
    }

    @Override
    public void buildBackupInfo(final WechatDirectEntryApply entryApply) {
        if (log.isDebugEnabled()) {
            log.debug("微信直连无备份");
        }
    }

    @Override
    public ChannelEntryResult entryApply(final EntryApplyContext<WechatDirectEntryApply> entryApplyContext) {
        return baseEntryApplyHandler.entryApply(entryApplyContext);
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {

        return PayChannelEnum.WECHAT == payChannel
                && PaySceneEnum.DIRECT == payScene;
    }

    @Override
    public WechatDirectEntryApply buildEntryApply(ChannelEntryOrderEntity orderEntity, ChannelEntryApplyDetailEntity detailEntity) {
        return new WechatDirectEntryApply(orderEntity, detailEntity);
    }

    @Override
    public void resultHandle(final EntryApplyContext<WechatDirectEntryApply> context,
                             final ChannelEntryResult channelEntryResult) {

        baseEntryApplyHandler.resultHandle(context, channelEntryResult);
        if (!context.getEntryApply().getEntryStatus().finalStatus()) {
            entryResultProducer.sendEntryNotifyMessage(context.getEntryApply(), EntryNotifyBizTypeEnum.ENTRY);
        }
        //判断是否需要创建分账方
        if (EntryStatusEnum.SUCCESS == context.getEntryApply().getEntryStatus()
                && context.getEntryApply().getWxDirectCreateSplitterFlag()) {
            divideReceiverBiz.createDivideReceiver(context.getEntryApply());
        }

    }
}
