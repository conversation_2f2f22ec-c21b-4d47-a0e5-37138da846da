package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.annotation.EntryApplyProcess;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.AnchoredBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyRespBO;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.EntryOrderHandler;
import com.yeepay.g3.core.aggregation.config.service.impl.ChannelEntryApplyService;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2025/6/5 20:24
 */
@Slf4j
@Service
@EntryApplyProcess(EntryTypeEnum.ENTRY_ANCHORED)
public class EntryAnchoredOrderHandler implements EntryOrderHandler {
    private final ChannelEntryApplyService channelEntryApplyService;
    private final AnchoredBiz anchoredBiz;
    private final AtlasThreadPoolExecutor anchoredHandleExecutor;
    private final ChannelEntryOrderService channelEntryOrderService;
    private final TransactionTemplate transactionTemplate;
    private final EntryResultProducer entryResultProducer;

    @Autowired
    public EntryAnchoredOrderHandler(final ChannelEntryApplyService channelEntryApplyService,
                                     final AnchoredBiz anchoredBiz,
                                     @Qualifier("anchoredHandleExecutor") final AtlasThreadPoolExecutor anchoredHandleExecutor,
                                     final ChannelEntryOrderService channelEntryOrderService,
                                     final TransactionTemplate transactionTemplate,
                                     final EntryResultProducer entryResultProducer) {
        this.channelEntryApplyService = channelEntryApplyService;
        this.anchoredBiz = anchoredBiz;
        this.anchoredHandleExecutor = anchoredHandleExecutor;
        this.channelEntryOrderService = channelEntryOrderService;
        this.transactionTemplate = transactionTemplate;
        this.entryResultProducer = entryResultProducer;
    }

    @Override
    public void handle(final EntryApplyContext<? extends EntryApply<? extends ChannelSpecialParams>> context) {
        log.info("进件类型：ENTRY_ANCHORED 开始处理: 请求参数:{}", JsonUtils.toJSONString(context.getEntryApply()));
        Assert.notNull(context.getEntryApply().getAnchoredOrder(), "进件+挂靠类型,挂靠信息不能为空");
        //挂靠
        anchoredHandleExecutor.submit(() -> {
            try {
                LogGuidUtil.clearLogContext();
                log.info("进件类型：ENTRY_ANCHORED  挂靠开始处理: 挂靠参数:{}", JsonUtils.toJSONString(context.getEntryApply().getAnchoredOrder()));
                final AnchoredApplyRespBO anchoredApply = anchoredBiz.anchoredApply(context.getEntryApply().getAnchoredOrder());
                final BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply = (BaseEntryApply<? extends ChannelSpecialParams>) context.getEntryApply();
                if (AnchoredStatus.SUCCESS.getDocument().equals(anchoredApply.getApplyStatus())) {
                    transactionTemplate.execute(transactionStatus -> {
                        /*需要加查询锁更新一下*/
                        ChannelEntryApplyDetailEntity currentEntity = channelEntryOrderService.selectChannelEntryApplyDetailEntityForUpdate(baseEntryApply.getApplyNo());
                        baseEntryApply.flashCurrentFlagStatusAndEntryStatus(currentEntity.getFlagStatus(), DocumentedEnum.fromValueOfNullable(EntryStatusEnum.class, currentEntity.getStatus()));
                        baseEntryApply.anchoredSuccess(anchoredApply.getOrderNo());
                        if (0 == channelEntryOrderService.updateChannelEntryApplyDetailFlagStatus(baseEntryApply)) {
                            //todo 更新挂靠结果失败,补偿变更OR生成补偿查询任务
                            log.warn("[挂靠],成功，进件申请更新失败, 并发操作 baseEntryApply={}", JsonUtils.toJSONString(baseEntryApply));
                            throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
                        }
                        return null;
                    });
                    //进件+挂靠发消息？
                    log.info("进件类型：ENTRY_ANCHORED 挂靠成功，发送消息 进件申请单:{}", context.getEntryApply().getApplyNo());
                    entryResultProducer.sendEntryNotifyMessage((BaseEntryApply<? extends ChannelSpecialParams>) context.getEntryApply(), EntryNotifyBizTypeEnum.ENTRY_ANCHORED);

                }
            } catch (Exception e) {
                log.error(String.format("进件类型：ENTRY_ANCHORED 挂靠失败，进件申请单:{%s} cased by", context.getEntryApply().getApplyNo()), e);
            }
        });


        // 进件
        log.info("进件类型：ENTRY_ANCHORED  进件开始处理: 进件申请单:{}", context.getEntryApply().getApplyNo());
        channelEntryApplyService.entryApply(context);
    }

}
