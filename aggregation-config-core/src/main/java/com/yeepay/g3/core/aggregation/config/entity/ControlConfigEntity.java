package com.yeepay.g3.core.aggregation.config.entity;


import com.yeepay.g3.facade.aggregation.config.enums.AccessEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ControlNotifyWayEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ControlStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-13 15:14
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Data
public class ControlConfigEntity implements Serializable {

    private static final long serialVersionUID = 4066109444616255992L;

    /**
     * 主键ID，自动生成
     */
    private Long id;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 控制通知方式
     */
    private ControlNotifyWayEnum controlNotifyWay;

    /**
     * 是否下发（标志位）
     */
    private AccessEnum isLower;

    /**
     * 邮件地址（可选）
     */
    private List<String> emailAddress;

    /**
     *  邮件抄送地址（可选）
     */
    private List<String> ccAddress;

    /**
     * 回调URL（可选）
     */
    private String callBackUrl;

    /**
     * 应用密钥（可选）
     */
    private String appKey;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 备注信息（可选）
     */
    private String remark;

    /**
     * 状态（例如：启用/禁用）
     */
    private ControlStatusEnum status;

    /**
     * 是否删除标志（例如：'Y'表示删除，'N'表示未删除）
     */
    private AccessEnum isDel;

    /**
     * 创建时间，默认当前时间
     */
    private Date createTime;

    /**
     * 最后更新时间，默认当前时间
     */
    private Date updateTime;


}
