package com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.CompanyRepresentativeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.RegistrationCertificateBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SettleAccountBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SuperAdminInfo;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoApplyRecordEntity;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelNoApplyReqDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.SubmitApplyChannelInfoRequestDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

/**
 * description: 渠道号申请业务对象
 * <AUTHOR>
 * @since 2025/5/27:16:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class ChannelNoApplyBO implements Serializable {

    private static final long serialVersionUID = 5199459893352069611L;

    /**
     * 商户编码（必填）
     */
    private String merchantNo;

    /**
     * 商户简称（可选）
     */
    private String shortName;

    /**
     * 业务场景（必填）
     */
    private BizSceneEnum bizScene;

    /**
     * 业务申请单号（必填）
     */
    private String bizApplyNo;

    /**
     * 业务单号
     */
    private String bizOrderNo;

    /**
     * 支付渠道类型（必填）
     */
    private PayChannelEnum payChannel;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型（必填）
     */
    private ActivityTypeEnum activityType;

    /**
     * 销售名称（必填）
     */
    private String salesName;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 是否使用易宝渠道号（必填）
     */
    private String isUseYpChannelNo;

    /**
     * 资金清算类型
     */
    private String fundLiquidationType;

    /**
     * 客服电话
     */
    private String servicePhone;

    /**
     * 数币银行代码（可选）
     */
    private String digitalCurrencyBankCode;

    /**
     * 联系人信息
     */
    private SuperAdminInfo superAdminInfo;

    /**
     * 公司经办人/法人信息
     */
    private CompanyRepresentativeBO companyRepresentativeInfo;

    /**
     * 证书信息
     */
    private RegistrationCertificateBO certificateInfo;

    /**
     * 结算账户信息
     */
    private SettleAccountBO settleAccount;

    /**
     * 扩展信息
     */
    private String externalInfo;

    public static ChannelNoApplyBO build(ChannelNoApplyReqDTO reqDTO) {
        SuperAdminInfo superAdminInfo = SuperAdminInfo.buildByChannelNo(reqDTO.getSuperAdminInfo());
        RegistrationCertificateBO registrationCertificateBO = RegistrationCertificateBO.buildByChannelNoApply(reqDTO.getCertificateInfo());
        SettleAccountBO settleAccountBO = SettleAccountBO.build(reqDTO.getSettleAccountInfo());
        CompanyRepresentativeBO representativeBO = CompanyRepresentativeBO.buildByChannelNo(reqDTO.getCompanyRepresentativeInfo());

        return ChannelNoApplyBO.builder()
                .merchantNo(reqDTO.getMerchantNo())
                .shortName(reqDTO.getShortName())
                .bizScene(DocumentedEnum.fromValue(BizSceneEnum.class, reqDTO.getBizScene()))
                .bizApplyNo(reqDTO.getBizApplyNo())
                .bizOrderNo(reqDTO.getBizOrderNo())
                .payChannel(DocumentedEnum.fromValue(PayChannelEnum.class, reqDTO.getPayChannel()))
                .payScene(DocumentedEnum.fromValue(PaySceneEnum.class, reqDTO.getPayScene()))
                .activityType(DocumentedEnum.fromValue(ActivityTypeEnum.class, reqDTO.getActivityType()))
                .salesName(reqDTO.getSalesName())
                .industryName(reqDTO.getIndustryName())
                .isUseYpChannelNo(reqDTO.getIsUseYpChannelNo())
                .fundLiquidationType(reqDTO.getFundLiquidationType())
                .servicePhone(reqDTO.getServicePhone())
                .superAdminInfo(superAdminInfo)
                .companyRepresentativeInfo(representativeBO)
                .certificateInfo(registrationCertificateBO)
                .settleAccount(settleAccountBO)
                .externalInfo(reqDTO.getExtendInfo())
                .digitalCurrencyBankCode(reqDTO.getDigitalCurrencyBankCode())
                .build();
    }

    public static SubmitApplyChannelInfoRequestDTO buildChannelDTO(String requestNo, ChannelNoApplyBO channelInfo) {

        SceneAndActivityTypeBO sceneAndPromotion = SceneAndActivityUtils.getSceneAndPromotionTypeBO(
                channelInfo.getPayChannel(), channelInfo.getPayScene(), channelInfo.getActivityType());

        SuperAdminInfo superAdminInfo = channelInfo.getSuperAdminInfo();
        RegistrationCertificateBO certificateInfo = channelInfo.getCertificateInfo();
        SettleAccountBO settleAccount = channelInfo.getSettleAccount();
        CompanyRepresentativeBO representativeInfo = channelInfo.getCompanyRepresentativeInfo();

        SubmitApplyChannelInfoRequestDTO reqDTO = new SubmitApplyChannelInfoRequestDTO();
        reqDTO.setRequestOrderNo(requestNo);
        reqDTO.setOpSource(Const.DEFAULT_SYSTEM_NAME);
        reqDTO.setFeeType(sceneAndPromotion.getChannelFeeType());
        reqDTO.setPromotionType(sceneAndPromotion.getChannelActivityType());
        reqDTO.setOpSerialNo(channelInfo.getBizOrderNo());
        reqDTO.setChannelIdentifier(channelInfo.getMerchantNo());
        reqDTO.setSeller(channelInfo.getSalesName());
        reqDTO.setIndustryName(channelInfo.getIndustryName());
        reqDTO.setContactName(superAdminInfo.getContactName());
        reqDTO.setContactPhone(superAdminInfo.getContactMobileNo());
        reqDTO.setContactEmail(superAdminInfo.getContactEmail());
        reqDTO.setMerchantShortName(channelInfo.getShortName());
        reqDTO.setMerchantBusinessName(certificateInfo.getCertMerchantName());
        reqDTO.setMerchantBusinessLicense(certificateInfo.getCertNumber());
        reqDTO.setMerchantBusinessAddress(certificateInfo.getCertCompanyAddress());
        reqDTO.setMerchantBusinessLicensePicUrl(certificateInfo.getCertCopy());
        reqDTO.setMerAccNo(settleAccount.getCardNo());
        reqDTO.setMerAccName(settleAccount.getCardName());
        reqDTO.setMerAccBankName(settleAccount.getBankName());
        reqDTO.setMerAccSubBankName(settleAccount.getBankBranchName());
        reqDTO.setMerAccBankProvince(settleAccount.getProvName());
        reqDTO.setMerAccBankCity(settleAccount.getCityName());
        reqDTO.setExternalInfo(CheckUtils.isEmpty(channelInfo.getExternalInfo()) ? null : JsonUtils.convert(channelInfo.getExternalInfo(), Map.class));
        reqDTO.setMerAccBankCode(settleAccount.getBankCode());
        reqDTO.setBankCode(channelInfo.getDigitalCurrencyBankCode());
        reqDTO.setFundLiquidationType(channelInfo.getFundLiquidationType());
        reqDTO.setContactIdFrontPicUrl(superAdminInfo.getCardFrontImg());
        reqDTO.setContactIdBackPicUrl(superAdminInfo.getCardBackImg());
        reqDTO.setBusiAuthorizationPicUrl(superAdminInfo.getBusinessAuthorizationLetter());
        reqDTO.setServicePhone(channelInfo.getServicePhone());
        reqDTO.setContactIdType(CheckUtils.isEmpty(superAdminInfo.getCertType()) ? null : superAdminInfo.getCertType().getApplyChannelNoIdentificationType());
        reqDTO.setIsUseYpChannelNo(channelInfo.getIsUseYpChannelNo());
        if (!CheckUtils.isEmpty(representativeInfo)) {
            reqDTO.setLegalFrontPicUrl(representativeInfo.getCardFrontImg());
            reqDTO.setLegalBackPicUrl(representativeInfo.getCardBackImg());
        }

        return reqDTO;
    }


    public static ChannelNoApplyBO buildByEntry(ChannelNoApplyRecordEntity applyRecordEntity) {
        SuperAdminInfo superAdminInfo = JsonUtils.convert(applyRecordEntity.getContactInfo(), SuperAdminInfo.class);
        RegistrationCertificateBO registrationCertificateBO = JsonUtils.convert(applyRecordEntity.getCertificateInfo(), RegistrationCertificateBO.class);
        SettleAccountBO settleAccountBO = JsonUtils.convert(applyRecordEntity.getSettleAccountInfo(), SettleAccountBO.class);
        CompanyRepresentativeBO representativeBO = JsonUtils.convert(applyRecordEntity.getCompanyRepresentativeInfo(), CompanyRepresentativeBO.class);
        return ChannelNoApplyBO.builder()
                .merchantNo(applyRecordEntity.getMerchantNo())
                .shortName(applyRecordEntity.getMerchantShortName())
                .bizScene(DocumentedEnum.fromValue(BizSceneEnum.class, applyRecordEntity.getBizScene()))
                .bizApplyNo(applyRecordEntity.getBizApplyNo())
                .bizOrderNo(applyRecordEntity.getBizOrderNo())
                .payChannel(DocumentedEnum.fromValue(PayChannelEnum.class, applyRecordEntity.getPayChannel()))
                .payScene(DocumentedEnum.fromValue(PaySceneEnum.class, applyRecordEntity.getPayScene()))
                .activityType(DocumentedEnum.fromValue(ActivityTypeEnum.class, applyRecordEntity.getActivityType()))
                .salesName(applyRecordEntity.getSalesName())
                .industryName(applyRecordEntity.getIndustryName())
                .isUseYpChannelNo(applyRecordEntity.getIsUseYpChannelNo())
                .fundLiquidationType(applyRecordEntity.getFundLiquidationType())
                .servicePhone(applyRecordEntity.getServicePhone())
                .superAdminInfo(superAdminInfo == null ? SuperAdminInfo.builder().build() : superAdminInfo)
                .companyRepresentativeInfo(representativeBO == null ? CompanyRepresentativeBO.builder().build() : representativeBO)
                .certificateInfo(registrationCertificateBO == null ? RegistrationCertificateBO.builder().build() : registrationCertificateBO)
                .settleAccount(settleAccountBO == null ? SettleAccountBO.builder().build() : settleAccountBO)
                .externalInfo(applyRecordEntity.getExternalInfo())
                .digitalCurrencyBankCode(applyRecordEntity.getDigitalCurrencyBankCode())
                .build();
    }
}
