package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryAuditStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.UboInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.*;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlag;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlagStatus;
import com.yeepay.g3.core.aggregation.config.utils.GMUtils;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/8 14:42
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@SuperBuilder
public class WechatDirectEntryApply extends BaseEntryApply<WechatDirectEntrySpecialParams> {


    /**
     * 经营者/法人是否为受益人
     */
    private final Boolean isBenefitPerson;

    /**
     * 受益人列表
     */
    private final List<UboInfoBO> uboInfos;


    private final SettleAccountInfo settleAccountInfo;

    /**
     * 超级管理员信息
     */
    private final SuperAdminInfo superAdminInfo;

    /**
     * 经营资料
     */
    private final SalesScene salesScene;

    /**
     * 费用类型
     * 差价模式：COMMISSION_CHARGE
     * 返佣模式：REBATE
     */
    private final String chargeType;

    private final ActivityInfo activityInfo;

    /**
     * 补充说明
     */
    private final WxDirectConfigCmd.AdditionInfo additionInfo;

    public WechatDirectEntryApply(ChannelEntryOrderEntity order, ChannelEntryApplyDetailEntity detailEntity) {
        super(order, detailEntity);
        WechatDirectEntrySpecialParams specialParams = JsonUtils.fromJson(GMUtils.decrypt(detailEntity.getChannelParams()), WechatDirectEntrySpecialParams.class);
        this.channelSpecialParams = specialParams;
        this.isBenefitPerson = specialParams.getIsBenefitPerson();
        this.uboInfos = specialParams.getUboInfos();
        this.settleAccountInfo = specialParams.getSettleAccountInfo();
        this.superAdminInfo = specialParams.getSuperAdminInfo();
        this.salesScene = specialParams.getSalesScene();
        this.additionInfo = specialParams.getAdditionInfo();
        this.chargeType = specialParams.getChargeType();
        this.activityInfo = specialParams.getActivityInfo();

    }

    public static WechatDirectEntryApply createEntryApply(final WxDirectConfigCmd cmd, final String orderNo,
                                                          final EntryApplyCmd entryApplyCmd) {
        final String applyNo = "WXD" + UniqueNoGenerateUtils.getUniqueNo();
        final boolean commissionCharge = "COMMISSION_CHARGE".equals(cmd.getChargeType());

        return WechatDirectEntryApply.builder()
                .orderNo(orderNo)
                .applyNo(applyNo)
                .bizApplyNo(entryApplyCmd.getBizApplyNo())
                .bizScene(entryApplyCmd.getBizScene())
                .merchantInfo(entryApplyCmd.getMerchantInfo())
                .subjectInfo(entryApplyCmd.getSubjectInfo())
                .contactInfo(entryApplyCmd.getContactInfo())
                .isBenefitPerson(cmd.getIsBenefitPerson())
                .uboInfos(cmd.getUboInfos())
                .entryType(cmd.getEntryType())
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .payScene(cmd.getPayScene())
                .payChannel(cmd.getPayChannel())
                .superAdminInfo(cmd.getSuperAdminInfo())
                .salesScene(cmd.getSalesScene())
                .additionInfo(cmd.getAdditionInfo())
                .channelSpecialParams(WechatDirectEntrySpecialParams.build(cmd))
                .entryAuditStatus(EntryAuditStatus.INIT)
                .entryStatus(EntryStatusEnum.INIT)
                .chargeType(cmd.getChargeType())
                .flag(commissionCharge ? addWxDirectCreateSplitterFlag() : EntryFlag.NONE.getBit())
                .flagStatus(EntryFlagStatus.NONE.getBit())
                .activityInfo(cmd.getActivityInfo())
                .activityType(cmd.getActivityInfo().getActivityType())
                .extendInfo(cmd.getExtendInfo())
                .build();
    }
}
