package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 挂靠申请响应DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/5  14:04
 */
@Data
public class AnchoredApplyDetailResultBO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 处理单号
     */
    private String applyNo;

    /**
     * 商户编号
     */
    private String merchantNo;

    private String payScene;

    private String activityType;

    private String channelType;

    /**
     * 挂靠类型
     */
    private String anchoredType;

    /**
     * 挂靠维度 是否需要跨sass体系
     */
    private Boolean anchoredDimension;

    /**
     * 挂靠状态
     */
    private String anchoredStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 挂靠商户信息
     */
    private List<AnchoredMerchantInfoResultBO> anchoredMerchantInfoList;


}
