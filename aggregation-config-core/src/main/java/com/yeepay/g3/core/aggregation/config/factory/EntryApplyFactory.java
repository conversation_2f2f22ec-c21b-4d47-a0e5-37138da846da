package com.yeepay.g3.core.aggregation.config.factory;

/**
 * <AUTHOR>
 * @date 2025/6/8 19:01
 */

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EntryApplyFactory {

    private final List<EntryApplyHandler<? extends EntryApply<? extends ChannelSpecialParams>>> entryApplyHandler;

    public EntryApplyFactory(final List<EntryApplyHandler<? extends EntryApply<? extends ChannelSpecialParams>>> entryApplyHandler) {
        this.entryApplyHandler = entryApplyHandler;
    }

    public EntryApplyHandler getHandler(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        return entryApplyHandler.stream()
                .filter(handler -> handler.isSupport(payChannel, payScene))
                .findFirst()
                .orElseThrow(() -> new UnsupportedOperationException("不支持的支付渠道或支付场景"));
    }
}
