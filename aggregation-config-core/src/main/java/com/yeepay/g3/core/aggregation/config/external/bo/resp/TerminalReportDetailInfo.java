package com.yeepay.g3.core.aggregation.config.external.bo.resp;

import lombok.Data;

/**
 * 终端报备详细信息
 *
 * @author: Mr.yin
 * @date: 2025/6/13  17:35
 */
@Data
public class TerminalReportDetailInfo {

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 渠道报备商户号
     */
    private String channelMchNo;

    /**
     * SUCCESS: 成功
     * FAIL: 失败
     */
    private String terminalStatus;

    /**
     * 终端设备类型
     * 01：自动柜员机（含ATM和CDM）和多媒体自助终端；
     * 02：传统POS；
     * 03：mPOS；
     * 04：智能POS；
     * 05：II型固定电话；
     * 06：云闪付终端；
     * 07：保留使用；
     * 08：手机POS；
     * 09：刷脸付终端；
     * 10：条码支付受理终端；
     * 11：条码支付辅助受理终端；
     */
    private String terminalType;

    /**
     * 终端设备编号，易宝为终端设备分配的编号
     */
    private String terminalId;

    /**
     * 失败时候的失败原因
     */
    private String code;

    private String message;


}
