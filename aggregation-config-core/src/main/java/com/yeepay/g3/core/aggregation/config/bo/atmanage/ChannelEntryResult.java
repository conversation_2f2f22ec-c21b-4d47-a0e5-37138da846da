package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryAuditStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.g3.core.aggregation.config.convert.ChannelResultConvert;
import com.yeepay.g3.core.aggregation.config.enums.ChannelGatewayCode;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelEntryResultEvent;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayReportRecordDTO;
import lombok.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 21:22
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
public class ChannelEntryResult {
    private String requestNo;
    private String innerChannelOrderNo;
    private List<ChannelEntryDetail> channelEntryDetails;


    @Setter(AccessLevel.PRIVATE)
    @Getter
    @Builder
    public static class ChannelEntryDetail {

        private final ThirdPartyRecordStatusEnum status;

        private final EntryAuditStatus entryAuditStatus;

        private SuccessBuilder successBuilder;

        private FailBuilder failBuilder;

        private ProcessBuilder processBuilder;

        public static ChannelEntryDetail success(final OpenPayReportRecordDTO reportRecord) {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.SUCCESS)
                    .entryAuditStatus(EntryAuditStatus.FINISHED)
                    .successBuilder(SuccessBuilder.successBuilder(reportRecord))
                    .build();
        }

        public static ChannelEntryDetail success(final ChannelEntryResultEvent resultEvent) {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.SUCCESS)
                    .entryAuditStatus(EntryAuditStatus.FINISHED)
                    .successBuilder(SuccessBuilder.successBuilder(resultEvent))
                    .build();
        }


        public static ChannelEntryDetail fail(final String code, final String message) {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.FAIL)
                    .failBuilder(FailBuilder.failBuilder(code, message))
                    .build();
        }

        public static ChannelEntryDetail fail(final OpenPayReportRecordDTO entryRecord) {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.FAIL)
                    .entryAuditStatus(ChannelResultConvert.convertAuditStatus(entryRecord.getAuditStatus(), entryRecord.getReportStatus()))
                    .failBuilder(FailBuilder.failBuilder(entryRecord))
                    .build();
        }

        public static ChannelEntryDetail fail(final ChannelEntryResultEvent resultEvent) {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.FAIL)
                    .entryAuditStatus(ChannelResultConvert.convertAuditStatus(resultEvent.getAuditStatus(), resultEvent.getStatus()))
                    .failBuilder(FailBuilder.failBuilder(resultEvent))
                    .build();
        }

        public static ChannelEntryDetail processing(final OpenPayReportRecordDTO entryRecord) {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.PROCESSING)
                    .entryAuditStatus(ChannelResultConvert.convertAuditStatus(entryRecord.getAuditStatus(), "AUDITING"))
                    .processBuilder(ProcessBuilder.processBuilder(entryRecord.getSignUrl()))
                    .build();
        }

        public static ChannelEntryDetail processing(final ChannelEntryResultEvent resultEvent) {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.PROCESSING)
                    .entryAuditStatus(ChannelResultConvert.convertAuditStatus(resultEvent.getAuditStatus(), "AUDITING"))
                    .processBuilder(ProcessBuilder.processBuilder(resultEvent.getSignUrl()))
                    .build();
        }

        // 异常场景 补偿查询 没有签约链接
        public static ChannelEntryDetail processing() {
            return ChannelEntryDetail.builder()
                    .status(ThirdPartyRecordStatusEnum.PROCESSING)
                    .processBuilder(ProcessBuilder.processBuilder(""))
                    .build();
        }


    }

    @Builder
    @Getter
    @ToString
    @Setter(AccessLevel.PRIVATE)
    public static class SuccessBuilder {
        private ChannelGatewayCode channelGatewayCode;
        private boolean isMain;
        private String channelNo;
        private String channelMchNo;
        private String feeType;
        private String channelActiveType;
        private LocalDateTime finishTime;

        public static SuccessBuilder successBuilder(final ChannelEntryResultEvent resultEvent) {
            return SuccessBuilder.builder()
                    .channelGatewayCode(ChannelResultConvert.convertChannelGatewayCode(resultEvent.getReportType(), resultEvent.getBankCode()))
                    .isMain(resultEvent.getMerchantNo().equals(resultEvent.getReportMerchantNo()))
                    .channelNo(resultEvent.getChannelNo())
                    .channelMchNo(resultEvent.getReportMerchantNo())
                    .feeType(resultEvent.getReportFee())
                    .channelActiveType(resultEvent.getActivityType())
                    .finishTime(LocalDateTime.now())
                    .build();
        }

        public static SuccessBuilder successBuilder(final OpenPayReportRecordDTO entryRecord) {
            return SuccessBuilder.builder()
                    .channelGatewayCode(ChannelResultConvert.convertChannelGatewayCode(entryRecord.getReportType(), entryRecord.getBankCode()))
                    .isMain(entryRecord.getMainMerchantNo().equals(entryRecord.getReportMerchantNo()))
                    .channelNo(entryRecord.getChannelNo())
                    .channelMchNo(entryRecord.getReportMerchantNo())
                    .feeType(entryRecord.getReportFee())
                    .channelActiveType(entryRecord.getPromotionType())
                    .finishTime(entryRecord.getSuccessTime() == null ? LocalDateTime.now() :
                            entryRecord.getSuccessTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .build();
        }
    }

    @Builder
    @Getter
    @ToString
    @Setter(AccessLevel.PRIVATE)
    public static class FailBuilder {
        private String channelResponseCode;
        private String channelResponseMessage;
        private LocalDateTime finishTime;
        private String failReason;

        public static FailBuilder failBuilder(final OpenPayReportRecordDTO entryRecord) {
            return FailBuilder.builder()
                    .channelResponseCode(entryRecord.getErrCode())
                    .channelResponseMessage(entryRecord.getErrMsg())
                    .finishTime(entryRecord.getSuccessTime() == null ? LocalDateTime.now() :
                            entryRecord.getSuccessTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .failReason(entryRecord.getErrMsg())
                    .build();
        }

        public static FailBuilder failBuilder(final ChannelEntryResultEvent resultEvent) {
            return FailBuilder.builder()
                    .channelResponseCode(resultEvent.getErrCode())
                    .channelResponseMessage(resultEvent.getErrMsg())
                    .finishTime(LocalDateTime.now())
                    .failReason(resultEvent.getErrMsg())
                    .build();
        }

        public static FailBuilder failBuilder(final String code, final String message) {
            return FailBuilder.builder()
                    .channelResponseCode(code)
                    .channelResponseMessage(message)
                    .finishTime(LocalDateTime.now())
                    .failReason(message)
                    .build();
        }
    }

    @Builder
    @Getter
    @ToString
    @Setter(AccessLevel.PRIVATE)
    public static class ProcessBuilder {
        private String signUrl;

        public static ProcessBuilder processBuilder(final String signUrl) {
            return ProcessBuilder.builder()
                    .signUrl(signUrl)
                    .build();
        }
    }

}
