package com.yeepay.g3.core.aggregation.config.flow.node.impl;

import com.yeepay.g3.core.aggregation.config.flow.ContextMatcher;
import com.yeepay.g3.core.aggregation.config.flow.FlowContext;
import com.yeepay.g3.core.aggregation.config.flow.enums.Mark;
import com.yeepay.g3.core.aggregation.config.flow.node.Node;
import lombok.Getter;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.concurrent.Executor;

/**
* <AUTHOR>
* @date 2025/5/27 14:33
*/
@Getter
public class MarkedNode implements Node {
    private Mark mark;
    private TransactionTemplate transactionTemplate;
    private Executor executor;
    private ContextMatcher matcher;

    private MarkedNode() {
    }

    public static MarkedNode startTransaction(TransactionTemplate transactionTemplate) {
        MarkedNode handler = new MarkedNode();
        handler.mark = Mark.TRANSACTION_START;
        handler.transactionTemplate = transactionTemplate;
        return handler;
    }

    public static MarkedNode startAsync(Executor executor) {
        MarkedNode handler = new MarkedNode();
        handler.mark = Mark.ASYNC_START;
        handler.executor = executor;
        return handler;
    }

    public static MarkedNode startCondition(ContextMatcher matcher) {
        MarkedNode handler = new MarkedNode();
        handler.mark = Mark.CONDITION_START;
        handler.matcher = matcher;
        return handler;
    }

    @Override
    public void execute(final FlowContext context) {
        // do nothing, only for mark
    }
}
