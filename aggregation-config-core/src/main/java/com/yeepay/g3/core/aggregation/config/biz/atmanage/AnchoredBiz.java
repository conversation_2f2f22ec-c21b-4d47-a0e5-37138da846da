package com.yeepay.g3.core.aggregation.config.biz.atmanage;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredOrder;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyRespBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredResultBO;

import java.util.Date;
import java.util.List;

/**
 * 挂靠业务
 *
 * @author: Mr.yin
 * @date: 2025/6/5  14:33
 */
public interface AnchoredBiz {

    /**
     * 挂靠申请
     *
     * @param anchoredCmd
     */
    AnchoredApplyRespBO anchoredApply(AnchoredCmd anchoredCmd);

    /**
     * 挂靠申请- 单笔同步
     *
     * @param anchoredOrder
     */
    AnchoredApplyRespBO anchoredApply(AnchoredOrder anchoredOrder);

    /**
     * 补偿未完成的挂靠订单
     */
    void compensateAnchoredOrder(Date startTime, Date endTime, List<String> orderNo);

    /**
     * 查询挂靠结果
     *
     * @param bizApplyNo
     */
    AnchoredResultBO queryAnchoredResult(String bizApplyNo, String bizScene);
}
