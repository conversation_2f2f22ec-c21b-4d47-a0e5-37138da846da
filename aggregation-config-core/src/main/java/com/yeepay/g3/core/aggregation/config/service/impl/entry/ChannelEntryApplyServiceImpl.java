package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.factory.EntryApplyFactory;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import com.yeepay.g3.core.aggregation.config.service.impl.ChannelEntryApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/19 13:03
 */

@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ChannelEntryApplyServiceImpl implements ChannelEntryApplyService {

    private final EntryApplyFactory entryApply;

    @SuppressWarnings("unchecked")
    @Override
    public void entryApply(final EntryApplyContext<? extends EntryApply<? extends ChannelSpecialParams>> entryApplyContext) {
        final EntryApplyHandler handler = entryApply.getHandler(entryApplyContext.getEntryApply().getPayChannel(), entryApplyContext.getEntryApply().getPayScene());

        handler.buildChannelNo(entryApplyContext.getEntryApply());
        handler.buildBackupInfo(entryApplyContext.getEntryApply());

        final ChannelEntryResult channelEntryResult = handler.entryApply(entryApplyContext);
        handler.resultHandle(entryApplyContext, channelEntryResult);
    }
}
