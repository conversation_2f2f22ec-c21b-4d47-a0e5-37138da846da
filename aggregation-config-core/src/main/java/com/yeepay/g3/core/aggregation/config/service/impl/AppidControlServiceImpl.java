package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.g3.core.aggregation.config.dao.AppidControlDao;
import com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity;
import com.yeepay.g3.core.aggregation.config.service.AppidControlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:appid管控
 * @ClassName: AppidControlServiceImpl
 * @Author: cong.huo
 * @Date: 2024/12/18 15:48   // 时间
 * @Version: 1.0
 */
@Service
public class AppidControlServiceImpl implements AppidControlService {

    @Resource
    private AppidControlDao appidControlDao;

    /**
     * 根据appid管控
     * @param appid
     * @return com.yeepay.g3.core.aggregation.config.entity.AddidControlEntity
     * <AUTHOR>
     * @date 2024/12/18 15:51
     */
    @Override
    public AppidControlEntity queryByAppid(String appid) {
        return appidControlDao.queryByAppid(appid);
    }

    @Override
    public int updateByIdVersion(AppidControlEntity appidControlEntity, long version) {
        return appidControlDao.updateByIdVersion(appidControlEntity,version);
    }

    @Override
    public void insert(AppidControlEntity appidControlEntity) {
         appidControlDao.insertSelective(appidControlEntity);
    }

    @Override
    public List<AppidControlEntity> queryByControlList(String appid, String notifyStatus) {
        return appidControlDao.queryByControlList(appid,notifyStatus);
    }
}
