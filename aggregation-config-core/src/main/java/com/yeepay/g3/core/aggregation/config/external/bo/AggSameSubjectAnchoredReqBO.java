package com.yeepay.g3.core.aggregation.config.external.bo;

import lombok.Data;

/**
 * 查询渠道商户的进件结果
 *
 * @author: Mr.yin
 * @date: 2025/6/4  20:23
 */
@Data
public class AggSameSubjectAnchoredReqBO {
    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 场景
     */
    private String payScene;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 聚合渠道类型
     */
    private String channelType;

    /**
     * 同主体挂靠限定范围标识商编
     */
    private String sameSubjectSameScopeMerchantNo;
}
