package com.yeepay.g3.core.aggregation.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 数据清洗状态枚举
 * @author: xuchen.liu
 * @date: 2024-12-17 15:03
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum DataCleanStatusEnum {

    INIT("初始化"),

    USED("待使用"),

    SUCCESS("成功");

    /**
     * 描述
     */
    private final String desc;

}
