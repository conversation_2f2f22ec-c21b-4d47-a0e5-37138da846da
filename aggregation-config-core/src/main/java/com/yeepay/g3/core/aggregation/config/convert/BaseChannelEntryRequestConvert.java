package com.yeepay.g3.core.aggregation.config.convert;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.SubjectTypeEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.CompanyRepresentativeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ContactInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.RegistrationCertificateBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SalesInfo;
import com.yeepay.g3.core.aggregation.config.external.constant.ChannelConst;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/18 18:46
 */
@Service("baseChannelEntryRequestConvert")
@Slf4j
public class BaseChannelEntryRequestConvert<C extends BaseEntryApply<? extends ChannelSpecialParams>> implements ChannelEntryRequestConvert<C> {
    private static final String INDIVIDUAL = "个体户";

    @Override
    public OpenPayAsyncReportRequestDTO buildChannelRequest(final EntryApplyContext<C> context) {
        final SceneAndActivityTypeBO sceneAndPromotionTypeBO = SceneAndActivityUtils.getSceneAndPromotionTypeBO(context.getEntryApply().getPayChannel(),
                context.getEntryApply().getPayScene(), context.getEntryApply().getActivityType());
        String reportMerchantName = context.getEntryApply().getMerchantInfo().getMerchantName();
        String shortName = context.getEntryApply().getMerchantInfo().getShortName();
        OpenPayAsyncReportRequestDTO request = new OpenPayAsyncReportRequestDTO();
        request.setRequestOrderNo(context.getThirdPartyRecord().getRequestNo());
        request.setMerchantNo(context.getEntryApply().getMerchantInfo().getMerchantNo());
        request.setTopMerchantNo(context.getEntryApply().getMerchantInfo().getTopLevelMerchantNo());
        request.setMerchantName(context.getEntryApply().getMerchantInfo().getMerchantName());
        request.setSpecialFlag(context.getEntryApply().getMerchantInfo().getIsExitArea());
        request.setBackupCount(Optional.ofNullable(context.getEntryApply().getBackupCounts()).orElse(0));

        // 个体户需要拼上个体户
        request.setReportMerchantName(reportMerchantName);
        request.setReportMerchantAlias(shortName);
        final String channelNo = context.getEntryApply().getChannelNo();
        request.setChannelNo(StringUtils.isBlank(channelNo) ? null : channelNo);
        final String channelIdentifier = context.getEntryApply().getChannelIdentifier();
        request.setChannelIdentifier(StringUtils.isBlank(channelIdentifier) ? null : channelIdentifier);
        request.setReportFee(sceneAndPromotionTypeBO.getChannelFeeType());
        request.setPromotionType(sceneAndPromotionTypeBO.getChannelActivityType());
        request.setSourceName(Const.DEFAULT_SYSTEM_NAME);
        request.setIndustryName(context.getEntryApply().getMerchantInfo().getIndustryLine());
        //联系人信息
        final ContactInfo contactInfo = context.getEntryApply().getContactInfo();
        request.setContactMobile(contactInfo.getContactMobileNo());
        request.setContactEmail(contactInfo.getContactEmail());
        request.setContactName(contactInfo.getContactName());
        request.setContactIdCardNo(contactInfo.getContactCertNo());
        request.setContactType(contactInfo.getContactType().getChannelLegalType());

        //主体信息
        final SubjectInfo subjectInfo = context.getEntryApply().getSubjectInfo();
        //经营信息
        final SalesInfo salesInfo = subjectInfo.getSalesInfo();
        request.setSecondCategoryCode(null != salesInfo.getQualificationInfo() ? salesInfo.getQualificationInfo().getCategoryId() : null);
        request.setServiceTel(salesInfo.getServicePhone());
        // 经营信息 经营地址
        SalesInfo.SalesAddress salesAddress = salesInfo.getSalesAddress();
        request.setMerchantProvince(salesAddress.getProvince());
        request.setMerchantCity(salesAddress.getCity());
        request.setMerchantDistrict(salesAddress.getDistrict());
        request.setMerchantAddress(salesAddress.getAddress());

        final SubjectTypeEnum subjectType = subjectInfo.getSubjectType();
        request.setMerchantType(subjectType.getChannelSubjectType());

        final CompanyRepresentativeBO companyRepresentativeInfo = subjectInfo.getCompanyRepresentativeInfo();
        if (SubjectTypeEnum.MICRO != subjectType) {
            final RegistrationCertificateBO certificateInfo = subjectInfo.getCertificateInfo();

            request.setMerchantBusinessLicenseType(certificateInfo.getCertType().getChannelCertificateType());
            request.setMerchantBusinessLicense(certificateInfo.getCertNumber());
            request.setMerchantBusinessLicensePic(certificateInfo.getCertCopy());
            request.setMerchantBusinessRegisterAddress(certificateInfo.getCertCompanyAddress());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            if (StringUtils.isNotBlank(certificateInfo.getEffectTime())) {
                LocalDate effectTime = LocalDate.parse(certificateInfo.getEffectTime(), formatter);
                request.setMerchantBusinessLicenseStartTime(Date.from(effectTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }

            if (StringUtils.isNotBlank(certificateInfo.getExpireTime())) {
                final String expireTime = certificateInfo.getExpireTime();
                LocalDate expireTimeLocaldate = Const.PERMANENT.equals(expireTime) ? LocalDate.parse(ChannelConst.EXPIRE_TIME_LONG, formatter)
                        : LocalDate.parse(certificateInfo.getExpireTime(), formatter);
                request.setMerchantBusinessLicenseEndTime(Date.from(expireTimeLocaldate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }

            if (SubjectTypeEnum.INDIVIDUAL == subjectType) {
                if (reportMerchantName.equals(companyRepresentativeInfo.getName())) {
                    reportMerchantName = INDIVIDUAL + reportMerchantName;
                    shortName = INDIVIDUAL + shortName;
                }
                request.setReportMerchantName(reportMerchantName);
                request.setReportMerchantAlias(individualShortNameHandle(shortName));
            }

        } else {
            request.setMerchantBusinessLicenseType(ChannelConst.IDENTITY_CARD);
            request.setMerchantBusinessLicense(companyRepresentativeInfo.getCardNo());
        }
        return request;
    }

    /**
     * 个体户简称处理 copy 入网之前逻辑
     *
     * @param shortName 简称
     * @return shortName
     */
    private String individualShortNameHandle(String shortName) {
        if (StringUtils.length(shortName) <= 20) {
            return shortName;
        }
        if (!shortName.matches(".*[（(]个体工商户[）)]$")) {
            return shortName;
        }
        log.info("命中聚合报备商户简称替换规则shortNameReplace：替换前：{}", shortName);
        shortName = shortName.replaceAll("[（(]个体工商户[）)]$", "");

        log.info("命中聚合报备商户简称替换规则shortNameReplace：替换后：{}", shortName);
        return shortName;
    }
}
