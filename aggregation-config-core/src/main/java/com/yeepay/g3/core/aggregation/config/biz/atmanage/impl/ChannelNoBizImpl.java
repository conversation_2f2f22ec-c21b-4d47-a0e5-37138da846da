package com.yeepay.g3.core.aggregation.config.biz.atmanage.impl;

import com.alibaba.druid.support.json.JSONUtils;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.*;
import com.yeepay.g3.core.aggregation.config.convert.ThirdPartyRecordConvert;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoApplyRecordEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.external.ChannelNoExternal;
import com.yeepay.g3.core.aggregation.config.service.ChannelNoService;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.core.aggregation.config.utils.ConfigUtil;
import com.yeepay.g3.core.aggregation.config.utils.ScheduleUtils;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.ChannelInfoResponsesDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * description: 渠道号管理业务实现类
 *
 * <AUTHOR>
 * @since 2025/5/27:16:57
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service("channelInfoBiz")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelNoBizImpl implements ChannelNoBiz {

    private final ChannelNoExternal channelNoExternal;

    private final ChannelNoService channelService;

    private final ThirdPartyRecordService thirdPartyRecordService;

    @Override
    public void applyChannelNo(ChannelNoApplyBO channelInfo) {
        ChannelNoApplyRecordEntity channelApplyEntity = channelService.queryChannelNoApplyByBizApplyNo(
                channelInfo.getBizScene(),
                channelInfo.getBizApplyNo()
        );

        Assert.isTrue(CheckUtils.isEmpty(channelApplyEntity), ResultCode.CHANNEL_APPLY_RECORD_REPEAT);

        // 保存商户渠道号申请记录
        String applyNo = channelService.saveChannelApplyRecord(channelInfo);

        // 保存第三方记录
        ThirdPartyRecordEntity thirdPartyRecordEntity = ThirdPartyRecordConvert.convert(applyNo,
                ThirdPartyBusinessTypeEnum.MERCHANT_CHANNEL_APPLY, Const.SYSTEM);
        thirdPartyRecordService.saveThirdPartyRecord(thirdPartyRecordEntity);

        // 调用外部渠道号申请接口
        this.applyExternalChannelNo(thirdPartyRecordEntity.getRequestNo(), channelInfo);
        log.info("商户渠道号申请成功，申请编号：{}", thirdPartyRecordEntity.getRequestNo());
    }

    @Override
    public void applyChannelResultNotify(ChannelNoApplyResultNotifyBO notifyBO) {
        /*查询其他订单是否完成*/
        ThirdPartyRecord allRecord = thirdPartyRecordService.queryThirdRecordByRequestNo(notifyBO.getRequestNo(), ThirdPartyBusinessTypeEnum.MERCHANT_CHANNEL_APPLY.name());
        if (CheckUtils.isEmpty(allRecord)) {
            log.error("商户渠道号申请记录不存在，notifyBO：{}", JSONUtils.toJSONString(notifyBO));
            return;
        }

        // 更新申请结果
        channelService.updateChannelApplyResult(allRecord.getRequestNo(),
                ChannelNoApplyResultBO.builder()
                        .channelNo(notifyBO.getChannelNo())
                        .applyResult(notifyBO.getApplyResult())
                        .applyResultDesc(notifyBO.getApplyResultDesc())
                        .build());

        // 绑定商户渠道关系
        ChannelNoApplyStatusEnum applyStatusEnum = ChannelNoApplyStatusEnum.getEnumByChannelStatus(notifyBO.getApplyResult());
        if (ChannelNoApplyStatusEnum.APPLY_SUCCESS.equals(applyStatusEnum)) {
            ChannelNoApplyRecordEntity channelNoApplyRecord = channelService.queryChannelNoApply(allRecord.getApplyNo());
            ChannelNoCreateBO channelNoCreateBO = ChannelNoCreateBO.build(notifyBO, channelNoApplyRecord);
            channelService.saveMerchantChannelRelation(channelNoCreateBO);
        }
    }

    @Override
    public ChannelNoApplyResultBO queryChannelApplyResult(QueryChannelNoApplyResultBO queryBO) {
        return channelNoExternal.queryApplyChannelResult(queryBO);
    }

    @Override
    public List<ChannelNoQueryResultBO> queryChannelNoList(ChannelNoQueryBO queryBO) {
        return channelNoExternal.queryChannelInfo(queryBO);
    }

    @Override
    public void handleChannelNoApplyResult(String requestNo, Date startTime, Date endTime) {

        // 1.查询需要处理的认证申请记录
        List<ChannelNoApplyRecordEntity> needHandleList = this.getNeedQueryHandList(requestNo, startTime, endTime);
        if (CheckUtils.isEmpty(needHandleList)) {
            log.info("handleChannelNoApplyResult: 未查询到需要处理的认证申请记录");
            return;
        }
        List<String> applyNoList = needHandleList.stream().map(ChannelNoApplyRecordEntity::getApplyNo).collect(Collectors.toList());
        List<ThirdPartyRecordEntity> recordEntities = thirdPartyRecordService.queryThirdPartyRecordListByApplyNo(
                applyNoList, ThirdPartyBusinessTypeEnum.MERCHANT_CHANNEL_APPLY.name());
        Map<String, String> applyNoMap = recordEntities.stream().collect(
                Collectors.toMap(ThirdPartyRecordEntity::getApplyNo,
                        ThirdPartyRecordEntity::getRequestNo, (v1, v2) -> v1));


        // 2.遍历需要处理的认证申请记录，查询并更新认证结果
        List<ChannelNoApplyRecordEntity> needRetryList = new ArrayList<>();
        for (ChannelNoApplyRecordEntity applyRecordEntity : needHandleList) {
            ChannelNoApplyRecordEntity channelNoApplyRecordEntity = this.queryAndUpdateApplyResult(applyNoMap.get(applyRecordEntity.getApplyNo()), applyRecordEntity);
            if (channelNoApplyRecordEntity != null) {
                needRetryList.add(channelNoApplyRecordEntity);
            }
        }

        // 3.处理未完成的认证申请记录
        if (CheckUtils.isEmpty(needRetryList)) {
            return;
        }
        for (ChannelNoApplyRecordEntity applyRecordEntity : needRetryList) {
            String needRetryRequestNo = applyNoMap.get(applyRecordEntity.getApplyNo());
            ChannelNoApplyBO channelNoApplyBO = ChannelNoApplyBO.buildByEntry(applyRecordEntity);
            // 调用外部渠道号申请接口
            try {
                this.applyExternalChannelNo(needRetryRequestNo, channelNoApplyBO);
            } catch (Exception e) {
                log.error("商户渠道号申请失败，申请编号：{}", needRetryRequestNo, e);
            }
        }
    }

    /**
     * 查询直连渠道号
     */
    @Override
    public String queryChannelNoByDirect(PayChannelEnum payChannelEnum, String industryLine, PaySceneEnum paySceneEnum) {
        Assert.isTrue(PayChannelEnum.WECHAT.equals(payChannelEnum), ResultCode.PARAM_VALID_ERROR, "暂时仅支持微信直连");
        boolean canUseBusinessLineChannelNo = DocumentedEnum.inRightEnums(paySceneEnum,
                PaySceneEnum.DIRECT, PaySceneEnum.DIRECT_STANDARD);
        Assert.isTrue(canUseBusinessLineChannelNo, ResultCode.PARAM_VALID_ERROR, "该支付场景不支持查询行业线对应渠道号");
        return channelNoExternal.queryChannelNoByBusinessLine(industryLine, paySceneEnum);
    }

    /**
     * 查询微信B2B渠道号
     */
    @Override
    public String queryChannelNoByWechatB2B() {
        final String channelNoWeChatB2B = ConfigUtil.getChannelNoWeChatB2B();
        Assert.notBlank(channelNoWeChatB2B, ResultCode.ENTRY_ROUTE_UN_FIND_CHANNEL_NO_ERROR);
        return channelNoWeChatB2B;
    }

    /**
     * 查询数字货币渠道号
     *
     * @return 数字货币渠道号
     */
    @Override
    public String queryChannelNoByDigitalCurrency() {
        final String channelNoDigitalCurrency = ConfigUtil.getChannelNoDigitalCurrency();
        Assert.notBlank(channelNoDigitalCurrency, ResultCode.ENTRY_ROUTE_UN_FIND_CHANNEL_NO_ERROR);
        return channelNoDigitalCurrency;
    }

    /**
     * 获取默认渠道号
     *
     * @param payChannel   渠道
     * @param payScene     业务场景
     * @param activityType 活动类型
     */
    @Override
    public String queryChannelNoBySceneAndActivity(PayChannelEnum payChannel, PaySceneEnum payScene, ActivityTypeEnum activityType, String industryLine) {
        return Const.HL_INDUSTRY_LINE.contains(industryLine) ?
                ConfigUtil.getChannelNoDefaultHl(payChannel, payScene, activityType)
                : ConfigUtil.getChannelNoDefaultNormal(payChannel, payScene, activityType);
    }

    @Override
    public List<ChannelNoInfoBO> getChannelNoInfo(String channelNo) {
        ChannelInfoResponsesDTO response = channelNoExternal.queryChannelNoInfoByChannelNo(
                Collections.singletonList(channelNo));
        if (CheckUtils.isEmpty(response) || CheckUtils.isEmpty(response.getOpenPayChannelInfoDTOList())) {
            return new ArrayList<>();
        }
        return ChannelNoInfoBO.buildList(response.getOpenPayChannelInfoDTOList());
    }

    @Override
    public ChannelNoInfoBO getChannelNoInfo(String channelNo, PayChannelEnum payChannel, PaySceneEnum payScene,
                                            ActivityTypeEnum activityType) {
        ChannelInfoResponsesDTO response = channelNoExternal.queryChannelNoInfoByChannelNo(
                Collections.singletonList(channelNo));
        if (CheckUtils.isEmpty(response) || CheckUtils.isEmpty(response.getOpenPayChannelInfoDTOList())) {
            return null;
        }
        return ChannelNoInfoBO.buildList(response.getOpenPayChannelInfoDTOList()).stream().filter(
                        info -> info.getPayChannel().equals(payChannel)
                                && info.getPayScene().equals(payScene)
                                && info.getActivityType().equals(activityType))
                .findFirst()
                .orElse(null);
    }

    @Override
    public Boolean validateChannelNo(String channelNo) {
        ChannelInfoResponsesDTO response = channelNoExternal.queryChannelNoInfoByChannelNo(
                Collections.singletonList(channelNo));
        return !CheckUtils.isEmpty(response) && !CheckUtils.isEmpty(response.getOpenPayChannelInfoDTOList());
    }

    private void queryAndUpdateApplyResult(QueryChannelNoApplyResultBO applyResultBO) {
        // 1.调用渠道查询认证结果接口
        ChannelNoApplyResultBO result = channelNoExternal.queryApplyChannelResult(applyResultBO);

        // 2.更新申请结果
        channelService.updateChannelApplyResult(applyResultBO.getRequestNo(), result);

        // 3.返回认证结果
        log.info("商户渠道号申请结果查询成功，申请编号：{}, 结果：{}", applyResultBO.getRequestNo(), result);
    }


    /**
     * 获取需要处理的申请记录列表
     */
    private List<ChannelNoApplyRecordEntity> getNeedQueryHandList(String requestNo, Date startTime, Date endTime) {
        ThirdPartyRecord thirdPartyRecord = null;
        if (!CheckUtils.isEmpty(requestNo)) {
            thirdPartyRecord = thirdPartyRecordService.queryThirdRecordByRequestNo(requestNo, ThirdPartyBusinessTypeEnum.MERCHANT_CHANNEL_APPLY.name());

        }
        return ScheduleUtils.getScheduleList(thirdPartyRecord,
                () -> channelService.queryUnfinishedApplyRecord(startTime, endTime),
                channelService::queryChannelNoApply);

    }

    /**
     * 查询并更新申请结果
     * 返回需要重试补单的列表
     */
    private ChannelNoApplyRecordEntity queryAndUpdateApplyResult(String requestNo, ChannelNoApplyRecordEntity applyRecordEntity) {
        try {
            queryAndUpdateApplyResult(QueryChannelNoApplyResultBO.builder()
                    .requestNo(requestNo)
                    .merchantNo(applyRecordEntity.getMerchantNo())
                    .payChannel(DocumentedEnum.fromValue(PayChannelEnum.class, applyRecordEntity.getPayChannel()))
                    .payScene(DocumentedEnum.fromValue(PaySceneEnum.class, applyRecordEntity.getPayScene()))
                    .activityType(DocumentedEnum.fromValue(ActivityTypeEnum.class, applyRecordEntity.getActivityType()))
                    .digitalCurrencyBankCode(applyRecordEntity.getDigitalCurrencyBankCode())
                    .build());
        } catch (BaseException e) {
            log.error("商户渠道号申请结果查询失败，申请编号：{}，异常信息：{}", applyRecordEntity.getApplyNo(), e.getMessage(), e);
            if (ResultCode.CHANNEL_NO_APPLY_NOT_EXIST.getCode().equals(e.getResponseEnum().getCode())) {
                thirdPartyRecordService.updateThirdRecordChannelResult(requestNo,
                        e.getResponseEnum().getCode(),
                        e.getMessage(),
                        ThirdPartyRecordStatusEnum.NOT_EXIST);
                return applyRecordEntity;
            } else {
                channelService.updateChannelApplyResult(requestNo,
                        ChannelNoApplyResultBO.builder()
                                .applyResult(ChannelNoApplyStatusEnum.APPLY_FAIL.getChannelStatus())
                                .applyResultDesc(e.getMessage())
                                .build());
            }

        } catch (Exception e) {
            log.error("商户渠道号申请结果查询失败，申请编号：{}，异常信息：{}", applyRecordEntity.getApplyNo(), e.getMessage(), e);
            channelService.updateChannelApplyResult(requestNo,
                    ChannelNoApplyResultBO.builder()
                            .applyResult(ChannelNoApplyStatusEnum.APPLY_FAIL.getChannelStatus())
                            .applyResultDesc(e.getMessage())
                            .build());
        }
        return null;
    }

    /**
     * 调用外部申请渠道号
     */
    private void applyExternalChannelNo(String requestNo, ChannelNoApplyBO channelInfo) {
        // 调用外部渠道号申请接口
        try {
            channelNoExternal.applyChannelInfo(requestNo, channelInfo);
        } catch (BaseException e) {
            ChannelNoApplyResultBO result = ChannelNoApplyResultBO.builder()
                    .applyResult(ChannelNoApplyStatusEnum.APPLY_FAIL.getChannelStatus())
                    .applyResultDesc(e.getResponseEnum().getCode() + ":" + e.getMessage())
                    .build();
            channelService.updateChannelApplyResult(requestNo, result);
            log.error("商户渠道号申请失败，申请编号：{}，异常信息：{}", requestNo, e.getMessage(), e);
            throw e;
        }

        // 更新申请结果
        ChannelNoApplyResultBO result = ChannelNoApplyResultBO.builder()
                .applyResult(ChannelNoApplyStatusEnum.APPLYING.getChannelStatus())
                .build();
        channelService.updateChannelApplyResult(requestNo, result);
        log.info("商户渠道号申请成功，申请编号：{}", requestNo);
    }


}
