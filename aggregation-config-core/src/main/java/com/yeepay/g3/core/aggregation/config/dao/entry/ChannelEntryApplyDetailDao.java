package com.yeepay.g3.core.aggregation.config.dao.entry;

import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9 22:45
 */
public interface ChannelEntryApplyDetailDao extends Mapper<ChannelEntryApplyDetailEntity> {
    void batchInsert(List<ChannelEntryApplyDetailEntity> channelEntryApplyDetailEntity);

    /**
     * 加锁
     *
     * @param applyNo
     * @return
     */
    ChannelEntryApplyDetailEntity selectChannelEntryApplyDetailEntityForUpdate(@Param("applyNo") String applyNo);

    /**
     * 查询未完成订单
     * 终端报备/分账方创建/进件申请
     *
     * @param startTime
     * @param endTime
     * @param applyNoList
     * @return
     */
    List<ChannelEntryApplyDetailEntity> queryNotFinishChannelEntryOrder(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("applyNoList") List<String> applyNoList);

}
