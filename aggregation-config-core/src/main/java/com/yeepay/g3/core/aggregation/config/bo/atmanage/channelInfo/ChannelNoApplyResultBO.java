package com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelApplyResultRespDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.QueryApplyChannelResultResponseDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 渠道号申请业务对象
 * <AUTHOR>
 * @since 2025/5/27:16:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class ChannelNoApplyResultBO implements Serializable {

    private static final long serialVersionUID = 7328086457959957884L;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道号申请结果
     */
    private String applyResult;

    /**
     * 渠道号申请结果描述
     */
    private String applyResultDesc;


    /**
     * 构建 ChannelApplyResultBO 对象
     * @param dto 请求 DTO
     * @return ChannelApplyResultBO 对象
     */

    public static ChannelNoApplyResultBO build(QueryApplyChannelResultResponseDTO dto) {
        return ChannelNoApplyResultBO.builder()
                .channelNo(dto.getChannelNo())
                .applyResult(dto.getApplyResult())
                .applyResultDesc(dto.getApplyResultDes())
                .build();
    }

    /**
     * 构建 ChannelApplyResultRespDTO 对象
     * @param dto 请求 DTO
     * @return ChannelApplyResultRespDTO 对象
     */
    public static ChannelApplyResultRespDTO buildDTO(ChannelNoApplyResultBO dto) {
        ChannelApplyResultRespDTO respDTO = new ChannelApplyResultRespDTO();
        respDTO.setChannelNo(dto.getChannelNo());
        respDTO.setApplyResult(dto.getApplyResult());
        respDTO.setApplyResultDesc(dto.getApplyResultDesc());
        return respDTO;
    }
}
