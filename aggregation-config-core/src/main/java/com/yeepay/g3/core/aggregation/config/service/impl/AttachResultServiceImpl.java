package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.g3.core.aggregation.config.dao.AttachResultDao;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.AttachConfigStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.SourceEnum;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.service.AttachResultService;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-13 15:15
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Service
public class AttachResultServiceImpl implements AttachResultService {

    @Autowired
    private AttachResultDao attachResultDao;

    @Override
    public AttachResultEntity queryByUniqueKey(AttachResultEntity attachResultEntity) {
        return attachResultDao.queryByUniqueKey(attachResultEntity);

    }

    /**
     *  保存挂靠实体
     * @param attachResultEntity
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 17:51
     */
    @Override
    public void insert(AttachResultEntity attachResultEntity) {
        attachResultDao.insert(attachResultEntity);
    }

    @Override
    public int update(AttachResultEntity attachResultEntity, DataCleanStatusEnum dataCleanStatus) {
        attachResultEntity.setOldClearStatus(dataCleanStatus.name());
        int result = attachResultDao.updateSelective(attachResultEntity);
        if (result == 0) {
            throw new AggConfigException(ErrorCodeEnum.DATABASE_OPERATE_ERROR,"修改绑定结果影响行数异常");
        }
        return result;
    }

    @Override
    public List<AttachResultEntity> queryByWechatAttachResult(String merchantNo, DataCleanStatusEnum cleanStatus) {
        return attachResultDao.queryByMerchantNoAndStatus(merchantNo, AttachConfigStatusEnum.SUCCESS,
                SourceEnum.CHANNEL, ChannelTypeEnum.WECHAT,cleanStatus);
    }

    @Override
    public Boolean updateClearStatus(String merchantNo, DataCleanStatusEnum dataCleanStatus, DataCleanStatusEnum oldClearStatus) {
        int res = attachResultDao.updateClearStatus(merchantNo, dataCleanStatus, oldClearStatus);
        if (res == 0){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
