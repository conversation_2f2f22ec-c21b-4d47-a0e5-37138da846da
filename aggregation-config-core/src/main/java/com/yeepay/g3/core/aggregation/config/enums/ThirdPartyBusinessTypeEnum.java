package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 三方业务类型枚举
 * <AUTHOR>
 * @since 2025/5/26:16:42
 * Company: 易宝支付(YeePay)
 */

@Getter
@AllArgsConstructor
public enum ThirdPartyBusinessTypeEnum implements DocumentedEnum<String> {

    MERCHANT_CHANNEL_APPLY("MERCHANT_CHANNEL_APPLY", "商户渠道申请"),

    MERCHANT_AUTH("MERCHANT_AUTH", "商户意愿认证"),

    AGG_ANCHORED("AGG_ANCHORED", "聚合挂靠"),

    CHANNEL_ANCHORED("CHANNEL_ANCHORED", "渠道挂靠"),

    CHANNEL_ENTRY("CHANNEL_ENTRY", "渠道入驻"),

    CHANNEL_TERMINAL_REPORT("CHANNEL_TERMINAL_REPORT", "渠道终端报备"),

    CHANNEL_DIVIDE_CREATE("CHANNEL_DIVIDE_CREATE", "渠道分帐方创建"),
    ;

    private final String document;

    private final String desc;

}
