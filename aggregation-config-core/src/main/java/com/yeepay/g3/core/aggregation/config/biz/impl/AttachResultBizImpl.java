package com.yeepay.g3.core.aggregation.config.biz.impl;

import com.yeepay.g3.core.aggregation.config.biz.AttachResultBiz;
import com.yeepay.g3.core.aggregation.config.common.MessageConst;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity;
import com.yeepay.g3.core.aggregation.config.enums.AttachConfigStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.SourceEnum;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAttachEvent;
import com.yeepay.g3.core.aggregation.config.service.AttachResultService;
import com.yeepay.g3.utils.common.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;

/**
 * @Description:
 * @ClassName: AttachResultBizImpl
 * @Author: cong.huo
 * @Date: 2024/12/17 17:08   // 时间
 * @Version: 1.0
 */
@Slf4j
@Service
public class AttachResultBizImpl implements AttachResultBiz {


    @Autowired
    private AttachResultService attachResultService;

    @Autowired
    private MerchantCenterExternal merchantCenterExternal;


    @Override
    public Pair<Boolean, AttachResultEntity> handle(ChannelAttachEvent message, MerchantGroupEntity merchantGroupEntity) throws ParseException {
        /**
         * 1 参数转换
         * 2 查询挂靠关系
         * 3 如果不存在 插入  来源是通道
         * 4 如果存在 更新
         */
        final AttachResultEntity attachResultEntity = buildByMessage(message,merchantGroupEntity);
        AttachResultEntity attachResultEntityQuery = attachResultService.queryByUniqueKey(attachResultEntity);
        if(null == attachResultEntityQuery) {
            //保存
            attachResultService.insert(attachResultEntity);
        } else {
            //判断状态是否一样
            String queryConfigStatus = attachResultEntityQuery.getConfigStatus();
            if(!queryConfigStatus.equalsIgnoreCase(attachResultEntity.getConfigStatus())) {
                //如果更新
                attachResultEntityQuery.setConfigStatus(attachResultEntity.getConfigStatus());
                attachResultEntityQuery.setCleanStatus(DataCleanStatusEnum.INIT.name());
                attachResultService.update(attachResultEntityQuery, DataCleanStatusEnum.SUCCESS);
                return Pair.of(Boolean.TRUE,attachResultEntityQuery);
            }else {
                return Pair.of(Boolean.FALSE,null);
            }
        }
        return Pair.of(Boolean.TRUE,attachResultEntity);
    }

    private AttachResultEntity buildByMessage(ChannelAttachEvent message, MerchantGroupEntity merchantGroupEntityBySub) throws ParseException {
        Date date = new Date();
        AttachResultEntity attachResultEntity = new AttachResultEntity();
        attachResultEntity.setMainMerchantNo(message.getMaskMainMerchantNo());
        attachResultEntity.setSubMerchantNo(message.getYeepayMerchantNo());
        //查询子商户
        attachResultEntity.setSubFirstMerchantNo(merchantGroupEntityBySub.getGroup().get(0));
        attachResultEntity.setSubSecondMerchantNo(merchantGroupEntityBySub.getGroup().size() > 1 ? merchantGroupEntityBySub.getGroup().get(1) : "");
        attachResultEntity.setSubThreeMerchantNo(merchantGroupEntityBySub.getGroup().size() > 2 ? merchantGroupEntityBySub.getGroup().get(2) : "");
        attachResultEntity.setSubMerchantName(merchantGroupEntityBySub.getMerchantName());

        String source = message.getSource();
        if (StringUtils.isBlank(source)) {
            attachResultEntity.setSource(SourceEnum.CHANNEL.name());
            attachResultEntity.setApplyReason(MessageConst.CHANNEL_NOTIFY);
        }else {
            attachResultEntity.setSource(SourceEnum.AGG.name());
            attachResultEntity.setApplyReason(MessageConst.AGG_NOTIFY);
        }

        attachResultEntity.setChannel(message.getChannelType());
        attachResultEntity.setApprovalResult(MessageConst.SUCCESS);
        attachResultEntity.setApplyTime(date);
        //配置状态 需要转换通道的状态
        AttachConfigStatusEnum configStatus;
        String status = message.getStatus();
        if ("1".equals(status)) {
            configStatus = AttachConfigStatusEnum.SUCCESS;
        }
        else if ("0".equals(status)) {
            configStatus = AttachConfigStatusEnum.RELIEVE;
        }
        else {
            configStatus = AttachConfigStatusEnum.RELIEVE;
        }
        attachResultEntity.setConfigStatus(configStatus.name());
        if (StringUtils.isNotBlank(message.getSuccessTime())) {
            attachResultEntity.setConfigTime(DateUtils.parseDate(message.getSuccessTime(),DateUtils.DATE_FORMAT_DATETIME));
        }else {
            attachResultEntity.setConfigTime(date);
        }
        attachResultEntity.setCreatedTime(date);
        attachResultEntity.setLastModifiedTime(date);
        attachResultEntity.setMerchantScan("Y");
        attachResultEntity.setUserScan("Y");
        attachResultEntity.setMiniProgram("Y");
        //区分渠道类型是支付宝还是微信
        if (ChannelTypeEnum.WECHAT.name().equals(message.getChannelType())) {
            attachResultEntity.setWechatOffiaccount("Y");
            attachResultEntity.setAlipayLife("N");
        }else{
            attachResultEntity.setWechatOffiaccount("N");
            attachResultEntity.setAlipayLife("Y");
        }
        attachResultEntity.setCleanStatus(DataCleanStatusEnum.INIT.name());
        return attachResultEntity;


    }
}
