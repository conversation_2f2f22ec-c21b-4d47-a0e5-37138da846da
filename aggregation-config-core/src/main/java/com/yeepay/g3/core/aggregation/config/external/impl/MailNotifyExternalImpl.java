package com.yeepay.g3.core.aggregation.config.external.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.core.aggregation.config.common.MessageConst;
import com.yeepay.g3.core.aggregation.config.entity.*;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.MailNotifyExternal;
import com.yeepay.g3.core.aggregation.config.utils.ConfigUtil;
import com.yeepay.g3.core.aggregation.config.utils.ExcelUtil;
import com.yeepay.g3.core.aggregation.config.utils.FileUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.notifier.NotifyFacade;
import com.yeepay.g3.facade.notifier.dto.MailNotifyRequest;
import com.yeepay.g3.facade.notifier.dto.NotifyResponse;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.encrypt.Digest;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

/**
 * @Description:
 * @ClassName: MailNotifyExternalImpl
 * @Author: cong.huo
 * @Date: 2024/12/16 17:56   // 时间
 * @Version: 1.0
 */
@Service
@Slf4j
public class MailNotifyExternalImpl implements MailNotifyExternal {

    /**
     * 通知facade
     */
    private NotifyFacade notifyFacade = RemoteServiceFactory.getService(NotifyFacade.class);

    @Override
    public boolean sendMailMessage(MailMessageEntity mailMessage) {
        try {
            String dot = "";
            String recipients = "";
            for (String m : mailMessage.getRecipientsList()) {
                recipients += dot + m;
                dot = ",";
            }
            String sign = Digest.md5Digest(mailMessage.getUserName() + mailMessage.getNotifyRuleName() + recipients + mailMessage.getSecretKey());
            MailNotifyRequest mnr = MailNotifyRequest.Builder.aMailNotifyRequest().withUserName(mailMessage.getUserName()).withNotifyRuleName(mailMessage.getNotifyRuleName())
                    .withSign(sign).withMessageParams(mailMessage.getMessageParams()).withRecipients(mailMessage.getRecipientsList()).build();
            if(CollectionUtils.isNotEmpty(mailMessage.getCc()) && !(mailMessage.getCc().size() == 1  && StringUtils.isBlank(mailMessage.getCc().get(0)))){
                mnr.setBcc(mailMessage.getCc());
            }
            if(null!=mailMessage.getMailFile()){
                Map<String, Object> attachment = new HashMap<>();
                byte[] fileBytes = FileUtils.getFileBytes(mailMessage.getMailFile());
                attachment.put(MessageConst.MAIL_FILE_NAME,fileBytes);
                mnr.setAttachments(attachment);
            }
            log.info("发送邮件请求参数：request:{}", mnr.toString());
            NotifyResponse mailResponse = notifyFacade.mailNotify(mnr);
            log.info("发送邮件返回参数, status:{}, response:{}：", mailResponse.getStatus(), mailResponse.toString());
            if(mailResponse.getStatus() == null){
                log.error("发送邮件失败", mailResponse);
                throw new AggConfigException(ErrorCodeEnum.BANKCHENNEL_QUERY_ERROR);
            }
            if(MessageConst.SUCCESS.equals(mailResponse.getStatus())|| MessageConst.SUBMIT_SUCCESS.equals(mailResponse.getStatus())){
                log.info("发送邮件成功");

            }else{
                log.error("发送邮件失败", mailResponse);
                throw new AggConfigException(ErrorCodeEnum.BANKCHENNEL_QUERY_ERROR);
            }
            return true;
        } catch (YeepayBizException e) {
            log.error("发送邮件失败", e);
            return false;
        }
    }

    @Override
    public boolean createMailMessageByAppidAndSend(String topMerchant, List<AppidAllMerchantNoBindEntity> subMerchantNos, ControlConfigEntity controlConfigEntity, AppidControlEntity appidControlEntity) {

        /**
         * 1 获取邮件配置信息
         * 2 创建邮件入参
         *
         */
        File file =null ;
        boolean flag = false;
        if(CollectionUtils.isEmpty(subMerchantNos)){
            return false;
        }
        try {
            Map<String, String> mailNotifyConfig = ConfigUtil.getMailNotifyConfig();
            MailMessageEntity mailMessage = new MailMessageEntity();
            mailMessage.setUserName(mailNotifyConfig.get(ConfigUtil.USER_NAME));
            mailMessage.setNotifyRuleName(mailNotifyConfig.get(ConfigUtil.NOTIFY_RULE_NAME));
            mailMessage.setSecretKey(mailNotifyConfig.get(ConfigUtil.SECRET_KEY));
            mailMessage.setRecipientsList(controlConfigEntity.getEmailAddress());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(controlConfigEntity.getCcAddress())) {
                mailMessage.setCc(controlConfigEntity.getCcAddress());
            }
            HashMap<String, Object> messageParams = Maps.newHashMap();
            String sendMailTime = DateUtils.toString(new Date(), DateUtils.DATE_FORMAT_DATETIME);
            String title = sendMailTime+"微信管控appid易宝通知";
            messageParams.put("title", title);
            String message = createMessage();
            messageParams.put("message", message);
            mailMessage.setMessageParams(messageParams);

            //创建文件列表
            List<AppidAllMerchantNoMailEntity> appidAllMerchantNoMailEntities = new ArrayList<>();
            //  创建文件

            for (AppidAllMerchantNoBindEntity subMerchantNo : subMerchantNos) {
                AppidAllMerchantNoMailEntity appidAllMerchantNoMailEntity = new AppidAllMerchantNoMailEntity();
                appidAllMerchantNoMailEntity.setAppid(subMerchantNo.getAppid());
                appidAllMerchantNoMailEntity.setMerchantNo(subMerchantNo.getMerchantNo());
                appidAllMerchantNoMailEntity.setControlStartTime(DateUtils.toString(appidControlEntity.getControlStartTime(), DateUtils.DATE_FORMAT_DATETIME));
                if(appidControlEntity.getControlEndTime() != null) {
                    appidAllMerchantNoMailEntity.setControlEndTime(DateUtils.toString(appidControlEntity.getControlEndTime(), DateUtils.DATE_FORMAT_DATETIME));
                }else{
                    appidAllMerchantNoMailEntity.setControlEndTime("-");
                }
                appidAllMerchantNoMailEntity.setMerchantName(subMerchantNo.getMerchantName());
                appidAllMerchantNoMailEntities.add(appidAllMerchantNoMailEntity);
            }
             file = ExcelUtil.writeDataToExcel(appidAllMerchantNoMailEntities, title+".xlsx", "Sheet1", AppidAllMerchantNoMailEntity.class);
             mailMessage.setMailFile(file);

             //调用
             flag = this.sendMailMessage(mailMessage);

        }catch (Exception e){
            log.error("发送邮件失败 topMerchant:"+topMerchant,e);

        }finally {
                FileUtils.deleteFile(file);
        }
        return flag;
    }


    public String  createMessage() {


        String title = "尊敬的客户：<br>" +
                "您好！微信已通知对下文appid进行订单发货管理管控，管控开始后将无法交易，需按照微信要求对接订单发货管理。<br>" +
                "如您未对接订单发货管理，请尽早处理；如您接受延迟结算，可联系易宝加入白名单。具体情况可联系易宝销售咨询~<br>" +
                "<br>" +
                "微信管控appid的影响的易宝商编请查看附件：<br>" +
                "————————————————————————<br>"+
                "<br>";

        return title;
    }

}
