package com.yeepay.g3.core.aggregation.config.convert;

import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ContactInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryOrder;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.MerchantInfo;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.utils.GMUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.EntryApplyDTO;

/**
 * <AUTHOR>
 * @date 2025/6/9 22:06
 */
public class ChannelEntryOrderConvert {

    public static ChannelEntryOrderEntity convert(final EntryOrder entryOrder) {

        return ChannelEntryOrderEntity.builder()
                .orderNo(entryOrder.getOrderNo())
                .bizApplyNo(entryOrder.getBizApplyNo())
                .bizScene(entryOrder.getBizScene().getDocument())
                .merchantNo(entryOrder.getMerchantInfo().getMerchantNo())
                .merchantName(entryOrder.getMerchantInfo().getMerchantName())
                .shortName(entryOrder.getMerchantInfo().getShortName())
                .merchantInfo(JsonUtils.toJSONString(entryOrder.getMerchantInfo()))
                .subjectInfo(GMUtils.encrypt(JsonUtils.toJSONString(entryOrder.getSubjectInfo())))
                .contactInfo(GMUtils.encrypt(JsonUtils.toJSONString(entryOrder.getContactInfo())))
                .extendInfo(entryOrder.getExtendInfo())
                .build();
    }

    public static EntryOrder convert(final ChannelEntryOrderEntity entryOrderEntity) {
        if (entryOrderEntity == null) {
            return null;
        }
        return EntryOrder.builder()
                .orderNo(entryOrderEntity.getOrderNo())
                .bizScene(DocumentedEnum.fromValue(BizSceneEnum.class, entryOrderEntity.getBizScene()))
                .bizApplyNo(entryOrderEntity.getBizApplyNo())
                .merchantInfo(JsonUtils.fromJson(entryOrderEntity.getMerchantInfo(), MerchantInfo.class))
                .subjectInfo(JsonUtils.fromJson(GMUtils.decrypt(entryOrderEntity.getSubjectInfo()), SubjectInfo.class))
                .contactInfo(JsonUtils.fromJson(GMUtils.decrypt(entryOrderEntity.getContactInfo()), ContactInfo.class))
                .extendInfo(entryOrderEntity.getExtendInfo())
                .build();

    }

    public static EntryApplyDTO convertToDTO(final EntryOrder entryOrder) {
        EntryApplyDTO entryApplyDTO = new EntryApplyDTO();
        entryApplyDTO.setApplyNo(entryOrder.getOrderNo());
        entryApplyDTO.setBizApplyNo(entryOrder.getBizApplyNo());
        entryApplyDTO.setMerchantInfo(MerchantInfo.convert(entryOrder.getMerchantInfo()));
        return entryApplyDTO;
    }
}
