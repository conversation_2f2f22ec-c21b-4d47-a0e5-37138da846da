package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.g3.core.aggregation.config.dao.ControlConfigDao;
import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.service.ControlConfigService;
import com.yeepay.g3.facade.aggregation.config.enums.AccessEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ControlStatusEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @description: 管控配置底层服务
 * @author: xuchen.liu
 * @date: 2024-12-13 15:12
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Service
public class ControlConfigServiceImpl implements ControlConfigService {

    @Autowired
    private ControlConfigDao controlConfigDao;

    @Override
    public void saveConfig(ControlConfigEntity controlConfigEntity) {
        controlConfigEntity.setIsDel(AccessEnum.N);
        int result = controlConfigDao.insertSelective(controlConfigEntity);
        if (result == 0) {
            throw new AggConfigException(ErrorCodeEnum.DATABASE_OPERATE_ERROR,"新增管控配置异常");
        }
    }

    @Transactional(rollbackFor = Throwable.class,timeout=3000)
    @Override
    public void updateConfig(ControlConfigEntity controlConfigEntity) {
        Long updateId = controlConfigEntity.getId();
        //先保存
       this.saveConfig(controlConfigEntity);
        //修改数据
        ControlConfigEntity update = new ControlConfigEntity();
        update.setId(updateId);
        update.setIsDel(AccessEnum.Y);
        update.setUpdateTime(new Date());
        int updateResult = controlConfigDao.updateSelective(update);
        if (updateResult == 0) {
            throw new AggConfigException(ErrorCodeEnum.DATABASE_OPERATE_ERROR,"修改管控配置异常");
        }
    }

    @Override
    public ControlConfigEntity queryById(Long id) {
        return controlConfigDao.queryById(id);
    }

    @Override
    public ControlConfigEntity queryValidByMerchantNo(String merchantNo, ControlStatusEnum controlStatus) {
        return controlConfigDao.queryByMerchantNoAndStatus(merchantNo, controlStatus != null ? controlStatus.name() : null);
    }
}
