package com.yeepay.g3.core.aggregation.config.impl.atmanage;

import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.QueryChannelNoApplyResultBO;
import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelApplyResultRespDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelNoApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.QueryChannelApplyResultReqDTO;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.ChannelNoFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: 渠道号管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/5/27:16:55
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelNoFacadeImpl implements ChannelNoFacade {

    private final ChannelNoBiz channelNoBiz;
    @Override
    public BaseResponseDTO applyChannelNo(ChannelNoApplyReqDTO reqDTO) {
        log.info("渠道号申请开始,请求参数:{}", JsonUtils.toJSONString(reqDTO));
        ChannelNoApplyBO channelNoApplyBO = ChannelNoApplyBO.build(reqDTO);
        channelNoBiz.applyChannelNo(channelNoApplyBO);
        log.info("渠道号申请结束");
        return new BaseResponseDTO();
    }

    @Override
    public ChannelApplyResultRespDTO queryChannelApplyResult(QueryChannelApplyResultReqDTO reqDTO) {
        log.info("查询渠道号申请结果开始,请求参数:{}", JsonUtils.toJSONString(reqDTO));
        QueryChannelNoApplyResultBO requestBO =  QueryChannelNoApplyResultBO.build(reqDTO);
        ChannelNoApplyResultBO channelNoApplyResultBO = channelNoBiz.queryChannelApplyResult(requestBO);
        ChannelApplyResultRespDTO respDTO = ChannelNoApplyResultBO.buildDTO(channelNoApplyResultBO);
        log.info("查询渠道号申请结果结束,响应参数:{}", JsonUtils.toJSONString(respDTO));
        return respDTO;
    }
}
