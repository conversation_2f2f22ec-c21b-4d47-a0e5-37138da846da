package com.yeepay.g3.core.aggregation.config.service;

/**
 * description: 商户认证服务Service
 * <AUTHOR>
 * @since 2025/5/26:11:28
 * Company: 易宝支付(YeePay)
 */
public interface MerchantAuthService {

//    /**
//     * 保存认证申请记录
//     */
//    MerchantAuthApplyEntity queryAuthRecord(AuthQueryBO authQueryBO);
//
//    /**
//     * 保存认证申请记录
//     * @param authApplyBO 认证申请业务对象
//     */
//    String saveAuthRecord(AuthApplyBO authApplyBO);
//
//    /**
//     * 更新认证申请记录-结果
//     */
//    void updateAuthApplyResult(String requestNo, AuthApplyResultBO result);
//
//    /**
//     * 更新认证取消记录-结果
//     * @param requestNo 认证请求号
//     */
//    void cancelAuthApply(String requestNo);
//
//    /**
//     * 更新认证结果
//     * @param requestNo 认证请求号
//     */
//    void updateAuthResult(String requestNo, AuthResultBO authResultBO);
//
//    /**
//     * 查询未完成的认证申请记录
//     */
//    List<MerchantAuthApplyEntity> queryUnfinishedAuthApply(Date startTime, Date endTime);
//
//    /**
//     * 查询认证申请记录
//     */
//    MerchantAuthApplyEntity queryAuthApplyByApplyNo(String applyNo);
}
