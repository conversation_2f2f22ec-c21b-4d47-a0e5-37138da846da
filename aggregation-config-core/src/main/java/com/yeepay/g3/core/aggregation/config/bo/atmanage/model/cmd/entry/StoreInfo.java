package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.StoreInfoDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 21:00
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class StoreInfo implements Serializable {

    /**
     * 微信直连 ：
     * 1、 长度为1-50个字符；
     * 2、前后不能有空格、制表符、换行符；
     * 3、不能仅含数字、特殊字符；
     * 4、仅能填写数字、英文字母、汉字及特殊字符；
     * 5、仅支持utf-8格式。
     */
    private final String storeName;

    /**
     *  微信直连
     * 【线下场所地址】 请填写详细的经营场所信息，如有多个场所，选择一个主要场所填写即可。
     * 1、长度为4-512个字符；
     * 2、前后不能有空格、制表符、换行符；
     * 3、不能仅含数字、特殊字符；
     * 4、仅能填写数字、英文字母、汉字及特殊字符；
     * 5、仅支持utf-8格式。
     */
    private final String storeAddress;

    /**
     *  微信直连
     * 【线下场所省市编码】 必填
     */
    private final String storeAddressCode;


    /**
     * 【线下场所门头照片】 必填
     * store_entrance_pic
     */
    private final List<String> storeEntrancePics;

    /**
     * 【线下场所内部照片】必填
     */
    private final List<String> storeInnerPics;

    public static StoreInfo build(StoreInfoDTO storeInfo) {
        Assert.notNull(storeInfo, ResultCode.PARAM_VALID_ERROR, "storeInfo can not be null!");
        return StoreInfo.builder()
                .storeName(storeInfo.getStoreName())
                .storeAddress(storeInfo.getStoreAddress())
                .storeAddressCode(storeInfo.getStoreAddressCode())
                .storeEntrancePics(storeInfo.getStoreEntrancePics())
                .storeInnerPics(storeInfo.getStoreInnerPics())
                .build();
    }

}
