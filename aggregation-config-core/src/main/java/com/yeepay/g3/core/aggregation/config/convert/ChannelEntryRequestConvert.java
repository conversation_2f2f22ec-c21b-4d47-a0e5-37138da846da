package com.yeepay.g3.core.aggregation.config.convert;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;

/**
 * <AUTHOR>
 * @date 2025/6/18 18:41
 */
public interface ChannelEntryRequestConvert<T extends BaseEntryApply<? extends ChannelSpecialParams>> {
    OpenPayAsyncReportRequestDTO buildChannelRequest(final EntryApplyContext<T> context);

    default boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        return false;
    }
}
