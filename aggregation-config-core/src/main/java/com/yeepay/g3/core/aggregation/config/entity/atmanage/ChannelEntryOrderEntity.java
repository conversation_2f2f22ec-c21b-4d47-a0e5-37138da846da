package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2025/6/2 15:25
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_channel_entry_order")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelEntryOrderEntity extends BaseEntity {
    /**
     * 订单号
     */
    @Column(name = "order_no", length = 32)
    private String orderNo;

    /**
     * 业务场景
     */
    @Column(name = "biz_scene", length = 16)
    private String bizScene;
    /**
     * 业务申请单号
     */
    @Column(name = "biz_apply_no", length = 32)
    private String bizApplyNo;

    /**
     * 商户编号
     */
    @Column(name = "merchant_no", length = 16)
    private String merchantNo;

    /**
     * 商户名称
     * 必须与企业证照上的名称一致；
     * 个体工商户的营业执照如没有名称，名称为“*”或空，则商户名称应填 “个体户XXX”（XXX为营业执照上经营者姓名），如“个体户张三”，汉字以2个字符计算 ；
     */
    @Column(name = "merchant_name", length = 128)
    private String merchantName;

    /**
     * 商户信息
     */
    @Column(name = "merchant_info", columnDefinition = "text")
    private String merchantInfo;

    /**
     * 简称
     */
    @Column(name = "short_name", length = 128)
    private String shortName;

    /**
     * 主体信息
     */
    @Column(name = "subject_info", columnDefinition = "text")
    private String subjectInfo;

    /**
     * 联系信息
     */
    @Column(name = "contact_info", columnDefinition = "text")
    private String contactInfo;


    @Column(name = "extend_info")
    private String extendInfo;

    /**
     * 备注
     */
    @Column(name = "remark", length = 128)
    private String remark;
}
