package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * description: 渠道号申请结果消费体
 * <AUTHOR>
 * @since 2025/6/15:20:31
 * Company: 易宝支付(YeePay)
 */
@Data
public class ChannelNoApplyResultEvent implements Serializable {

    private static final long serialVersionUID = -5921555641532447578L;

    /**
     * 请求流水号
     */
    @NotBlank(message = "请求流水号不能为空")
    private String requestOrderNo;

    /**
     * 渠道号申请结果
     */
    private String applyResult;

    /**
     * 渠道号申请结果描述
     */
    private String applyResultDes;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道标识
     */
    private String channelIdentifier;

    /**
     * 费率类型
     */
    private String feeType;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 活动类型
     */
    private String promotionType;

    /**
     * 渠道状态
     */
    private String status;

    /**
     * 操作流水号
     */
    private String opSerialNo;

    /**
     * 销售
     */
    private String seller;

    /**
     * 最后修改时间
     */
    private String lastModifyTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 渠道订单号
     */
    private String bankOrderNo;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 联系人信息
     */
    private String contactInfoStr;

    /**
     * 商户信息
     */
    private String merchantInfoStr;

    /**
     * 结算信息
     */
    private String settleInfoStr;

    /**
     * 扩展信息
     */
    private String externalInfoStr;

    /**
     * 渠道类型
     */
    private String fundLiquidationType;

    /**
     * 是否交易
     */
    private String opSource;
}
