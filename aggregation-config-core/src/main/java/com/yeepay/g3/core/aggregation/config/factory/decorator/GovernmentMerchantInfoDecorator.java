package com.yeepay.g3.core.aggregation.config.factory.decorator;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.auth.AuthApplyBO;
import com.yeepay.g3.core.aggregation.config.factory.handle.MerchantInfoStrategy;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;

/**
 * description: 政府机关商户信息装饰器
 * <AUTHOR>
 * @since 2025/5/22:11:19
 * Company: 易宝支付(YeePay)
 */
public class GovernmentMerchantInfoDecorator extends MerchantInfoDecorator {

    public GovernmentMerchantInfoDecorator(MerchantInfoStrategy MerchantInfoStrategy) {
        super(MerchantInfoStrategy);
    }

    @Override
    public AuthApplyBO buildAndValidateAuth(AuthApplyReqDTO authApplyReqDTO) {

        AuthApplyBO authApplyBO = super.buildAndValidateAuth(authApplyReqDTO);

//        // 主体：政府机关、事业单位：可选填经办人
//        String legalType = authApplyReqDTO.getSubjectInfo().getCompanyRepresentativeInfo().getLegalType();
//        LegalTypeEnum legalTypeEnum = DocumentedEnum.fromValue(LegalTypeEnum.class, legalType);
//        //todo authApplyBO.getSubjectInfo().getCompanyRepresentativeInfo().setLegalType(legalTypeEnum);
//
//        if (PayChannelEnum.ALIPAY.equals(authApplyBO.getChannelType())) {
//            ValidationUtils.isTrue(authApplyBO.getContactInfo().getIsIdCardCertType(), "政府机关商户必须提供身份证件类型");
//            ValidationUtils.isTrue(authApplyBO.getSubjectInfo().getCompanyRepresentativeInfo().getIsIdCardType(), "政府机关商户必须提供身份证件类型");
//        }
        return authApplyBO;
    }

}
