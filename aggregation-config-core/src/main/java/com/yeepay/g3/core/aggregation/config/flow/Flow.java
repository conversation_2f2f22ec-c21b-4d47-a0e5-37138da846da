package com.yeepay.g3.core.aggregation.config.flow;

import com.yeepay.g3.core.aggregation.config.flow.enums.FlowStatus;
import com.yeepay.g3.core.aggregation.config.flow.node.Node;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 21:28
 */
@Getter
public class Flow {
    private final List<Node> nodes;
    private FlowStatus flowStatus;
    private FlowContext context;

    public Flow(List<Node> nodes) {
        this.nodes = nodes;
        this.flowStatus = FlowStatus.WAIT;
    }


}
