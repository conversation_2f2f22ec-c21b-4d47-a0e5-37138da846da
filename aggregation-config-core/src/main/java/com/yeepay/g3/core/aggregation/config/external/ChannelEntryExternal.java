package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryChannelTerminalInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.external.bo.QueryEntryProcessBaseReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;

import java.util.List;

/**
 * 通道进件远程服务
 *
 * @author: Mr.yin
 * @date: 2025/6/4  20:17
 */
public interface ChannelEntryExternal {

    /**
     * 查询通道进件结果
     * @param reqBO
     * @return
     */
    List<QueryEntryInfoBO> queryMerchantEntryResult(QueryEntryProcessBaseReqBO reqBO);

    /**
     * 查询通道终端报备结果
     */
    QueryChannelTerminalInfoBO queryTerminalEntryResult(String merchantNo, PayChannelEnum payChannel);

    /**
     * 按照单号 查询通道进件结果
     *
     * @param reqBO
     * @return
     */
    RemoteResult<ChannelEntryResult> queryEntryResultByRequestOrderNo(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo);
}
