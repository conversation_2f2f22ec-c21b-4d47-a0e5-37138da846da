package com.yeepay.g3.core.aggregation.config.builder;

import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.AttachConfigStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.BindAllStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelAppIdBindStatusEnum;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppIdBindEvent;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * @description: 参数转换
 * @author: xuchen.liu
 * @date: 2024-12-16 18:51
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Mapper(componentModel = "spring")
public interface AppIdMerchantBindBuilder {

    AppIdMerchantBindBuilder INSTANCE = Mappers.getMapper(AppIdMerchantBindBuilder.class);

    /**
     * 通知事件转换为内部实体
     * @param channelAppIdBindEvent channelAppIdBindEvent
     * @return AppIdMerchantBindEntity
     */
    @Mapping(source = "status",target = "bindStatus")
    @Mapping(target = "operateTime", ignore = true)
    @Mapping(source = "yeepayMerchantNo",target = "merchantNo")
    AppIdMerchantBindEntity eventToEntity(ChannelAppIdBindEvent channelAppIdBindEvent);

    default List<AppidAllMerchantNoBindEntity> toAllList(List<AttachResultEntity> resultList, AppIdMerchantBindEntity appIdMerchantBindEntity,List<AppidAllMerchantNoBindEntity> pending) {
        List<AppidAllMerchantNoBindEntity> allList = new ArrayList<>();
        Set<String> uniqueMerchantNos = new HashSet<>();
        AppidAllMerchantNoBindEntity bindMerchant = getAppidAllMerchantNoBindEntity(appIdMerchantBindEntity);
        if (CollectionUtils.isNotEmpty(pending)) {
            long count = pending.stream()
                    .filter(all -> all.getMerchantNo().equals(bindMerchant.getMerchantNo()))
                    .filter(all -> all.getAppid().equals(bindMerchant.getAppid()))
                    .count();
            if (count == 0) {
                allList.add(bindMerchant);
            }
        }else {
            allList.add(bindMerchant);
        }
        uniqueMerchantNos.add(appIdMerchantBindEntity.getMerchantNo());
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (AttachResultEntity attachResultEntity : resultList) {
                String mainMerchantNo = attachResultEntity.getMainMerchantNo();
                String subMerchantNo = attachResultEntity.getSubMerchantNo();
                if (mainMerchantNo.equals(bindMerchant.getAttachMerchantNo())) {
                    bindMerchant.setBindStatus(getBindAllStatusEnum(attachResultEntity,appIdMerchantBindEntity));
                }
                if (subMerchantNo.equals(bindMerchant.getMerchantNo())) {
                    bindMerchant.setBindStatus(getBindAllStatusEnum(attachResultEntity,appIdMerchantBindEntity));
                }
                if (uniqueMerchantNos.add(subMerchantNo)) {
                    AppidAllMerchantNoBindEntity entity = new AppidAllMerchantNoBindEntity();
                    entity.setFirstMerchantNo(attachResultEntity.getSubFirstMerchantNo());
                    entity.setSecondMerchantNo(attachResultEntity.getSubSecondMerchantNo());
                    entity.setThreeMerchantNo(attachResultEntity.getSubThreeMerchantNo());
                    entity.setAppid(appIdMerchantBindEntity.getAppId());
                    entity.setMerchantName(attachResultEntity.getSubMerchantName());
                    entity.setCreateTime(new Date());
                    entity.setUpdateTime(new Date());
                    entity.setAttachMerchantNo(attachResultEntity.getMainMerchantNo());
                    entity.setMerchantNo(subMerchantNo);
                    entity.setBindStatus(getBindAllStatusEnum(attachResultEntity,appIdMerchantBindEntity));
                    allList.add(entity);
                }
            }
        }
        return allList;
    }

    default AppidAllMerchantNoBindEntity getAppidAllMerchantNoBindEntity(AppIdMerchantBindEntity appIdMerchantBindEntity) {
        AppidAllMerchantNoBindEntity bindMerchant = new AppidAllMerchantNoBindEntity();
        bindMerchant.setFirstMerchantNo(appIdMerchantBindEntity.getFirstMerchantNo());
        bindMerchant.setSecondMerchantNo(appIdMerchantBindEntity.getSecondMerchantNo());
        bindMerchant.setThreeMerchantNo(appIdMerchantBindEntity.getThreeMerchantNo());
        bindMerchant.setMerchantName(appIdMerchantBindEntity.getMerchantName());
        bindMerchant.setAppid(appIdMerchantBindEntity.getAppId());
        bindMerchant.setCreateTime(new Date());
        bindMerchant.setUpdateTime(new Date());
        bindMerchant.setAttachMerchantNo(appIdMerchantBindEntity.getMerchantNo());
        bindMerchant.setMerchantNo(appIdMerchantBindEntity.getMerchantNo());
        bindMerchant.setBindStatus(getBindAllStatusEnum(appIdMerchantBindEntity));
        return bindMerchant;
    }

    default BindAllStatusEnum getBindAllStatusEnum(AppIdMerchantBindEntity appIdMerchantBindEntity) {
        BindAllStatusEnum bindAllStatus;
        if (appIdMerchantBindEntity.getBindStatus().equals(ChannelAppIdBindStatusEnum.SUCCESS)) {
            bindAllStatus = BindAllStatusEnum.BIND;
        } else {
            bindAllStatus = BindAllStatusEnum.RELIEVE;
        }
        return bindAllStatus;
    }

    default  BindAllStatusEnum getBindAllStatusEnum(AttachResultEntity attachResultEntity, AppIdMerchantBindEntity appIdMerchantBind) {
        BindAllStatusEnum bindAllStatus;
        ChannelAppIdBindStatusEnum channelAppIdBindStatus = appIdMerchantBind.getBindStatus();
        //当通道绑定关系与挂靠关系全部为可用时，真实绑定关系=绑定
        if (ChannelAppIdBindStatusEnum.SUCCESS.equals(channelAppIdBindStatus) &&
                AttachConfigStatusEnum.SUCCESS.name().equalsIgnoreCase(attachResultEntity.getConfigStatus())) {
            bindAllStatus = BindAllStatusEnum.BIND;
        }else {
            bindAllStatus = BindAllStatusEnum.RELIEVE;
        }
        return bindAllStatus;
    }

    default void setBindStatus(AttachResultEntity attachResultEntity, AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity, List<AppIdMerchantBindEntity> appidList) {
        for (AppIdMerchantBindEntity appIdMerchantBind : appidList) {
            if (appidAllMerchantNoBindEntity.getAppid().equalsIgnoreCase(appIdMerchantBind.getAppId())) {
                BindAllStatusEnum bindAllStatus = this.getBindAllStatusEnum(attachResultEntity, appIdMerchantBind);
                appidAllMerchantNoBindEntity.setBindStatus(bindAllStatus);
                break;
            }
        }
    }
}
