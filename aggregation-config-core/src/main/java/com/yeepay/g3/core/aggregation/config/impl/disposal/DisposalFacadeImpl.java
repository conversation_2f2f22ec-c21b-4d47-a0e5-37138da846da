package com.yeepay.g3.core.aggregation.config.impl.disposal;

import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalBiz;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryResultEntity;
import com.yeepay.g3.core.aggregation.config.convert.DisposalFacadeConvert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryResponseDTO;
import com.yeepay.g3.facade.aggregation.config.facade.disposal.DisposalFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * 处罚接口实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DisposalFacadeImpl implements DisposalFacade {

    private final DisposalBiz disposalBiz;

    @Override
    public DisposalNotifyQueryResponseDTO pageQueryDisposalList(DisposalNotifyQueryRequestDTO requestDTO) {
        log.info("[分页查询处罚信息] reqDTO={}", JsonUtils.toJSONString(requestDTO));
        DisposalQueryEntity requestEntity = DisposalFacadeConvert.buildPunishQueryEntity(requestDTO);
        DisposalQueryResultEntity disposalQueryResultEntity = disposalBiz.pageQueryDisposalList(requestEntity);
        DisposalNotifyQueryResponseDTO responseDTO = DisposalFacadeConvert.buildWechatDisposalResultDTO(disposalQueryResultEntity);
        log.info("[分页查询处罚信息] respDTO={}", JsonUtils.toJSONString(responseDTO));
        return responseDTO;
    }


}
