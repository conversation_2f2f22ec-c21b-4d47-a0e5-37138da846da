package com.yeepay.g3.core.aggregation.config.common;

import com.yeepay.g3.utils.common.json.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.util.StopWatch;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;

/**
 * @Title : com.yeepay.g3.core.refund.processor.external.proxy
 * @Description :
 * @Company : 易宝支付(Yeepay)
 * <AUTHOR> jiafu.wu
 * @Since: 2020/6/2
 * @Version : 1.0.0
 */
@Slf4j
public class RemoteInvocationHandler implements InvocationHandler {

    /**
     * 目标对象
     */
    private final Object target;

    /**
     * 外部系统
     */
    private final String sysName;

    /**
     * 构造方法
     *
     * @param target 目标对象
     */
    public RemoteInvocationHandler(Object target, String sysName) {
        this.target = target;
        this.sysName = sysName;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
        StringBuilder logBuilder = new StringBuilder("[outer] - [入参] - [").append(sysName).append("] - [").
                append(methodName).append("] - ").append(JSONUtils.toJsonString(args));
        try {
            Object result = AopUtils.invokeJoinpointUsingReflection(target, method, args);
            logBuilder.append(" --- [返回结果] - ").append(JSONUtils.toJsonString(result));
            return result;
        } catch (Throwable e) {
            log.warn("[outer] - [异常] - [" + sysName + "] - [" + methodName + "]", e);
            throw e;
        } finally {
            stopWatch.stop();
            logBuilder.append(" --- [执行时间] - ").append("耗时[").append(stopWatch.getTotalTimeMillis()).append("]毫秒");
            log.info(logBuilder.toString());
        }
    }

}



