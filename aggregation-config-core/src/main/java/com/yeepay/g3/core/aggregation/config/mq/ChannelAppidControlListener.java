package com.yeepay.g3.core.aggregation.config.mq;

import com.yeepay.g3.core.aggregation.config.biz.ChannelAppidControlBiz;
import com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppidControlEvent;
import com.yeepay.g3.core.aggregation.config.utils.SpringContextUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2025-01-02 13:44
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
public class ChannelAppidControlListener extends MqBaseListener<ChannelAppidControlEvent>{

    public ChannelAppidControlListener(Class<ChannelAppidControlEvent> channelAppidControlEventClass) {
        super(channelAppidControlEventClass);
    }

    @Override
    public String topicName() {
        return "AppId管控通知";
    }

    @Override
    public void onMessage(ChannelAppidControlEvent message) throws Exception {
        AppidControlEntity appidControlEntity;
        try {
            appidControlEntity = SpringContextUtils.getBean(ChannelAppidControlBiz.class).handle(message);
        }catch (Throwable e) {
            log.error("管控消息落库发生异常,e=",e);
            throw new AggConfigException(ErrorCodeEnum.MQ_CONSUMER_ERROR,e.getMessage());
        }
        SpringContextUtils.getBean(ChannelAppidControlBiz.class).notify(appidControlEntity);
    }
}
