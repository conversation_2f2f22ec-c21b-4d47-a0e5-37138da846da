package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 挂靠申请响应DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/5  14:04
 */
@Data
public class AnchoredResultBO implements Serializable {
    private static final long serialVersionUID = 1L;


    private String bizScene;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 处理订单号
     */
    private String orderNo;

    /**
     * 处理中
     * 完成
     */
    private String orderStatus;

    /**
     * 挂靠申请列表
     */
    private List<AnchoredApplyDetailResultBO> anchoredApplyList;


}
