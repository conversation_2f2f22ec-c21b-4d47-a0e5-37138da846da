package com.yeepay.g3.core.aggregation.config.utils;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

/**
 * description: IP工具类
 * <AUTHOR>
 * @since 2025/5/26:11:29
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@UtilityClass
public class IPUtils {

    /**
     * 获取IP地址的int值
     *
     * @return IP地址32位二进制数转换为int的值
     */
    public static Integer getRealLocalIpNum() {
        InetAddress realInetAddress = getRealLocalInetAddress();
        if (realInetAddress == null) {
            return 99999999;
        }
        byte[] address = realInetAddress.getAddress();
        int addrIntValue = 0;
        for (int i = 0; i < address.length; i++) {
            addrIntValue += (address[i] & 0xFF) << (8 * (address.length - i - 1));
        }
        return addrIntValue;
    }

    /**
     * 获取本机真实IP地址
     *
     * @return InetAddress
     */
    public static InetAddress getRealLocalInetAddress() {
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress inetAddress = null;
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> address = ni.getInetAddresses();
                while (address.hasMoreElements()) {
                    InetAddress ip = address.nextElement();
                    log.info("获取到ip={}", ip.getHostAddress());
                    if (ip.isSiteLocalAddress() && !ip.isLoopbackAddress()) {
                        inetAddress = ip;
                    }
                }
            }
            return inetAddress;
        } catch (Exception e) {
            return null;
        }
    }

}
