package com.yeepay.g3.core.aggregation.config.bo.atmanage.auth;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 认证驳回原因
 * <AUTHOR>
 * @since 2025/5/27:14:40
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class AuthRejectReasonBO implements Serializable {


    private static final long serialVersionUID = -8155252505738533048L;
    /**
     * 驳回参数
     */
    private String rejectParam;

    /**
     * 驳回原因
     */
    private String rejectReason;


}
