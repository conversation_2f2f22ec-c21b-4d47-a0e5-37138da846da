package com.yeepay.g3.core.aggregation.config.mq.producer;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryNotifyBizTypeEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;

/**
 * @author: Mr.yin
 * @date: 2025/6/10  18:29
 */
public interface EntryResultProducer {

    /**
     * 发送进件消息
     *
     */
    void sendEntryNotifyMessage(BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply, EntryNotifyBizTypeEnum notifyBizType);
}
