package com.yeepay.g3.core.aggregation.config.convert.decorator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.BankAccountTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.IdentificationTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.CompanyRepresentativeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WechatB2BEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.ActivityInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.MiniProgramInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SuperAdminInfo;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryRequestConvert;
import com.yeepay.g3.core.aggregation.config.external.constant.ChannelConst;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/18 19:04
 */
@Slf4j
@Service
public class WechatB2BChannelEntryRequestConvertImpl implements ChannelEntryRequestConvert<WechatB2BEntryApply> {
    private final ChannelEntryRequestConvert<WechatB2BEntryApply> baseChannelEntryRequestConvert;

    public WechatB2BChannelEntryRequestConvertImpl(@Qualifier("baseChannelEntryRequestConvert") final ChannelEntryRequestConvert<WechatB2BEntryApply> baseChannelEntryRequestConvert) {
        this.baseChannelEntryRequestConvert = baseChannelEntryRequestConvert;
    }

    @Override
    public OpenPayAsyncReportRequestDTO buildChannelRequest(final EntryApplyContext<WechatB2BEntryApply> context) {
        final OpenPayAsyncReportRequestDTO request = baseChannelEntryRequestConvert.buildChannelRequest(context);
        request.setCoverNum(context.getEntryApply().getCoverNum());
        request.setMerchantScale(context.getEntryApply().getMerchantScale());
        request.setGoodsTypeList(context.getEntryApply().getGoodsTypes());
        request.setGoodsSaleList(context.getEntryApply().getGoodsSales());
        request.setServiceList(context.getEntryApply().getServices());

        request.setOnlinePayFilePic(context.getEntryApply().getMiniProgramInfo().getOrderPayPic());
        request.setMerchantScale(context.getEntryApply().getMerchantScale());

        //法人信息
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        final SubjectInfo subjectInfo = context.getEntryApply().getSubjectInfo();
        final CompanyRepresentativeBO companyRepresentativeInfo = subjectInfo.getCompanyRepresentativeInfo();
        final IdentificationTypeEnum cardType = companyRepresentativeInfo.getCardType();
        request.setIdentityInfoName(companyRepresentativeInfo.getName());
        request.setIdentityInfoIdName(companyRepresentativeInfo.getName());
        request.setIdentityInfoIdHolderType(companyRepresentativeInfo.getLegalType().getDocument());
        request.setIdentityInfoIdType(cardType == null ? null : cardType.getChannelIdentificationType());
        request.setIdentityInfoAuthorizePic(companyRepresentativeInfo.getBusinessAuthorizationLetter());
        request.setIdentityInfoIdNo(companyRepresentativeInfo.getCardNo());
        request.setIdentityInfoIdFrontPic(companyRepresentativeInfo.getCardFrontImg());
        request.setIdentityInfoIdBackPic(companyRepresentativeInfo.getCardBackImg());
        request.setIdentityInfoIdAddress(companyRepresentativeInfo.getCertAddress());

        if (StringUtils.isNotBlank(companyRepresentativeInfo.getEffectTime())) {
            LocalDate effectTime = LocalDate.parse(companyRepresentativeInfo.getEffectTime(), formatter);
            request.setIdentityInfoIdStartTime(Date.from(effectTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }
        final String expireTime = companyRepresentativeInfo.getExpireTime();
        if (StringUtils.isNotBlank(expireTime)) {
            LocalDate expireLocalDate = Const.PERMANENT.equals(expireTime) ?
                    LocalDate.parse(ChannelConst.EXPIRE_TIME_LONG, formatter) : LocalDate.parse(expireTime, formatter);
            request.setIdentityInfoIdEndTime(Date.from(expireLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }

        // 超级管理员信息
        final SuperAdminInfo superAdminInfo = context.getEntryApply().getSuperAdminInfo();
        final IdentificationTypeEnum certType = superAdminInfo.getCertType();

        request.setContactName(superAdminInfo.getContactName());
        request.setContactPhone(superAdminInfo.getContactMobileNo());
        request.setContactIdCardNo(superAdminInfo.getContactCertNo());
        request.setContactEmail(superAdminInfo.getContactEmail());
        request.setContactType(superAdminInfo.getContactType().getChannelLegalType());
        request.setContactIdType(Objects.isNull(superAdminInfo.getCertType()) ? null : certType.getChannelIdentificationType());
        request.setContactIdFrontPic(superAdminInfo.getCardFrontImg());
        request.setContactIdBackPic(superAdminInfo.getCardBackImg());

        if (StringUtils.isNotBlank(superAdminInfo.getEffectTime())) {
            LocalDate contactIdEffectTime = LocalDate.parse(superAdminInfo.getEffectTime(), formatter);
            request.setContactIdStartTime(Date.from(contactIdEffectTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }

        final String contactIdExpireTime = superAdminInfo.getExpireTime();
        if (StringUtils.isNotBlank(contactIdExpireTime)) {
            LocalDate expireTimeLocalDate = Const.PERMANENT.equals(contactIdExpireTime) ? LocalDate.parse(ChannelConst.EXPIRE_TIME_LONG, formatter)
                    : LocalDate.parse(contactIdExpireTime, formatter);
            request.setContactIdEndTime(Date.from(expireTimeLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }
        request.setContactAuthorizationPic(superAdminInfo.getBusinessAuthorizationLetter());

        // 结算卡账户
        final SettleAccountInfo settleAccountInfo = context.getEntryApply().getSettleAccountInfo();
        final BankAccountTypeEnum bankAccountType = settleAccountInfo.getBankAccountType();
        request.setMerAccBank(settleAccountInfo.getBankName());
        request.setMerAccBankName(settleAccountInfo.getBankBranchName());
        request.setMerAccBankCode(settleAccountInfo.getBankCode());
        request.setMerAccBankNo(settleAccountInfo.getBranchCode());
        request.setMerAccBankType(bankAccountType == null ? null : bankAccountType.getChannelBankAccountType());
        request.setMerAccNo(settleAccountInfo.getCardNo());
        request.setMerAccName(settleAccountInfo.getCardName());
        request.setMerAccBankDistinctCode(settleAccountInfo.getDistinctCode());

        final MiniProgramInfo miniProgramInfo = context.getEntryApply().getMiniProgramInfo();
        request.setMiniProgramDes(miniProgramInfo.getMiniProgramDesc());
        request.setMiniProgramAppId(miniProgramInfo.getMiniProgramSubAppId());
        request.setMiniProgramAppSecret(miniProgramInfo.getMiniProgramAppSecret());
        request.setMiniProgramAppName(miniProgramInfo.getMiniProgramName());
        request.setMiniProgramPicList(miniProgramInfo.getMiniProgramPics());

        final ActivityInfo activityInfo = context.getEntryApply().getActivityInfo();
        request.setActivitiesId(activityInfo.getActivitiesId());
        request.setDebitActivitiesRate(activityInfo.getDebitActivitiesRate());
        request.setCreditActivitiesRate(activityInfo.getCreditActivitiesRate());

        request.setStoreEntrancePicList(Collections.singletonList(context.getEntryApply().getStoreEntrancePic()));
        request.setExternalInfo(JsonUtils.fromJson(context.getEntryApply().getExtendInfo(), new TypeReference<Map<String, String>>() {
        }));
        return request;
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {

        return PayChannelEnum.WECHAT == payChannel
                && PaySceneEnum.STORE_ASST == payScene;
    }
}
