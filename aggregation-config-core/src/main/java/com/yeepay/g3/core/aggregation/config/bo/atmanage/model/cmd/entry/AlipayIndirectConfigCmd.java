package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.AliIndirectConf;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.MerchantInfoDTO;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/2 17:14
 */

@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
public class AlipayIndirectConfigCmd {
    //  结算账户信息
    private final SettleAccountInfo settleAccountInfo;

    /**
     * 支付场景
     */
    private final PaySceneEnum payScene;

    /**
     * 支付渠道
     */
    private final PayChannelEnum payChannel;

    /**
     * 活动信息
     */
    private final ActivityInfo activityInfo;

    /**
     * 支付宝商户经营类目
     */
    private final String mcc;

    /**
     * 支付宝渠道号
     */
    private final String channelNo;

    /**
     * 渠道标识 易宝侧商编 会根据此商编的渠道号下进行进件
     */
    private final String channelIdentifier;

    /**
     * 进件类型
     */
    private final EntryTypeEnum entryType;

    /**
     * 备份数量
     */
    private final Integer backupCount;

    /**
     * 商户等级
     * INDIRECT_LEVEL_M1
     * INDIRECT_LEVEL_M2
     * INDIRECT_LEVEL_M3
     * INDIRECT_LEVEL_M4
     * 支付宝必填；拟申请的间连商户等级，银联根据 申请等级决定转发报文要素，具体 申请结果以支付宝结果为准
     */
    private final String merchantLevel;

    /**
     * 站点信息
     * 支付宝线上报备费率时必填
     */
    private final List<SiteInfo> siteInfos;

    /**
     * 挂靠信息
     */
    private final AnchoredCmd anchoredCmd;


    /**
     * 是否需要终端报备
     */
    private final Boolean isNeedTerminalReport;

    /**
     * 扩展信息
     */
    private final String extendInfo;


    public static AlipayIndirectConfigCmd build(final AliIndirectConf aliIndirectConf,
                                                final MerchantInfoDTO merchantInfo) {
        Assert.notNull(aliIndirectConf, ResultCode.PARAM_VALID_ERROR, "支付宝间连进件配置不能为空");
        final EntryTypeEnum entryType = DocumentedEnum.fromValue(EntryTypeEnum.class, aliIndirectConf.getEntryType());

        Assert.isFalse(EntryTypeEnum.needAnchored(entryType) && aliIndirectConf.getAnchoredInfo() == null, ResultCode.PARAM_VALID_ERROR, "支付宝进件需要挂靠时挂靠信息不能为空");
        final PaySceneEnum payScene = DocumentedEnum.fromValue(PaySceneEnum.class, aliIndirectConf.getPayScene());
        final ActivityInfo activityInfo = ActivityInfo.build(aliIndirectConf.getActivityInfo());
        return AlipayIndirectConfigCmd.builder()
                .payScene(payScene)
                .payChannel(PayChannelEnum.ALIPAY)
                .activityInfo(activityInfo)
                .mcc(aliIndirectConf.getMcc())
                .channelNo(aliIndirectConf.getChannelNo())
                .channelIdentifier(aliIndirectConf.getChannelIdentifier())
                .entryType(entryType)
                .backupCount(aliIndirectConf.getBackupCount())
                .merchantLevel(aliIndirectConf.getMerchantLevel())
                .siteInfos(SiteInfo.build(aliIndirectConf.getSiteInfo()))
                .isNeedTerminalReport(aliIndirectConf.getIsNeedTerminalReport())
                .anchoredCmd(Boolean.TRUE.equals(EntryTypeEnum.needAnchored(entryType)) ?
                        AnchoredCmd.build(aliIndirectConf.getAnchoredInfo(), merchantInfo, payScene,
                                PayChannelEnum.ALIPAY, activityInfo.getActivityType()) : null)
                .settleAccountInfo(SettleAccountInfo.build(aliIndirectConf.getSettleAccountInfo()))
                .extendInfo(aliIndirectConf.getExtendInfo())
                .build();
    }
}
