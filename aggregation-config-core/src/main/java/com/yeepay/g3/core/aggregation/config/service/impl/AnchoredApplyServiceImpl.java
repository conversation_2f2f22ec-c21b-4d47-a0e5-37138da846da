package com.yeepay.g3.core.aggregation.config.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.convert.ThirdPartyRecordConvert;
import com.yeepay.g3.core.aggregation.config.dao.AnchoredApplyDao;
import com.yeepay.g3.core.aggregation.config.dao.ThirdPartyRecordDao;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.service.AnchoredApplyService;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 挂靠申请服务实现类
 *
 * @author: Mr.yin
 * @date: 2025/6/8  16:31
 */
@Service
public class AnchoredApplyServiceImpl implements AnchoredApplyService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private AnchoredApplyDao anchoredApplyDao;

    @Resource
    private ThirdPartyRecordDao thirdPartyRecordDao;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public Boolean batchCreateAnchoredApply(List<AnchoredApplyEntity> anchoredApplyList) {
        return anchoredApplyList.size() == anchoredApplyDao.batchSaveAnchoredApply(anchoredApplyList);
    }

    @Override
    public void updateAnchoredProcess(AnchoredApply anchoredApply) {
        ThirdPartyRecordEntity thirdPartyRecord = ThirdPartyRecordConvert.convert(anchoredApply.getApplyNo(), ThirdPartyBusinessTypeEnum.AGG_ANCHORED, Const.SYSTEM);
        transactionTemplate.execute(transactionStatus -> {
            if (1 != anchoredApplyDao.updateAnchoredProcess(anchoredApply.getApplyNo())) {
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            thirdPartyRecordDao.batchInsertThirdPartyRecord(Lists.newArrayList(thirdPartyRecord));
            return null;
        });
        anchoredApply.updateChannelAnchoredProcess();
    }

    @Override
    public void updateAggAnchoredResultSuccess(AnchoredApply anchoredApply) {
        String applyNo = anchoredApply.getApplyNo();
        ThirdPartyRecordEntity aggAnchoredRecord = ThirdPartyRecordConvert.convertUpdate(
                null, null,
                ThirdPartyRecordStatusEnum.SUCCESS);
        Weekend<ThirdPartyRecordEntity> aggAnchoredRecordWeekend = Weekend.of(ThirdPartyRecordEntity.class);
        WeekendCriteria<ThirdPartyRecordEntity, Object> weekendCriteria = aggAnchoredRecordWeekend.weekendCriteria();
        weekendCriteria.andEqualTo(ThirdPartyRecordEntity::getApplyNo, applyNo);

        List<AggAnchoredMerchantInfo> aggAnchoredMerchantInfoList = anchoredApply.getAggAnchoredMerchantInfoList();
        // 保存第三方记录
        // 生成请求号号和申请编号
        List<ThirdPartyRecordEntity> thirdPartyRecordEntityList = aggAnchoredMerchantInfoList.stream().map(aggAnchoredMerchantInfo -> {
            ThirdPartyRecordEntity recordEntity = ThirdPartyRecordConvert.convert(applyNo, ThirdPartyBusinessTypeEnum.CHANNEL_ANCHORED, Const.SYSTEM);
            aggAnchoredMerchantInfo.buildChannelRequestNo(recordEntity.getRequestNo());
            return recordEntity;
        }).collect(Collectors.toList());

        Boolean result = transactionTemplate.execute(status -> {
            if (1 != anchoredApplyDao.updateAggAnchoredResultSuccess(applyNo, JsonUtils.toJSONString(aggAnchoredMerchantInfoList))) {
                status.setRollbackOnly();
                return false;
            }
            thirdPartyRecordDao.updateByExampleSelective(aggAnchoredRecord, aggAnchoredRecordWeekend);
            thirdPartyRecordDao.batchInsertThirdPartyRecord(thirdPartyRecordEntityList);
            return true;
        });
        if (!result) {
            logger.warn("[更新聚合挂靠结果] 并发操作 applyNo={},aggAnchoredMerchantInfoList={}", applyNo, JsonUtils.toJSONString(aggAnchoredMerchantInfoList));
            throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
        }

    }

    @Override
    public void updateAggAnchoredResultFail(String applyNo, String failReason, String channelCode) {
        // 更新第三方记录基础：渠道返回码、渠道返回信息
        ThirdPartyRecordEntity aggAnchoredRecord = ThirdPartyRecordConvert.convertUpdate(
                channelCode, failReason,
                ThirdPartyRecordStatusEnum.FAIL);
        if (StringUtils.isNotBlank(failReason) && failReason.length() > 85) {
            failReason = failReason.substring(0, 85);
        }
        Weekend<ThirdPartyRecordEntity> thirdPartyRecordEntityWeekend = Weekend.of(ThirdPartyRecordEntity.class);
        WeekendCriteria<ThirdPartyRecordEntity, Object> weekendCriteria = thirdPartyRecordEntityWeekend.weekendCriteria();
        weekendCriteria.andEqualTo(ThirdPartyRecordEntity::getApplyNo, applyNo);
        String finalFailReason = failReason;
        transactionTemplate.execute(status -> {
            if (1 != anchoredApplyDao.updateAggAnchoredResultFail(applyNo, finalFailReason)) {
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            thirdPartyRecordDao.updateByExampleSelective(aggAnchoredRecord, thirdPartyRecordEntityWeekend);
            return null;
        });
    }

    @Override
    public void updateChannelAnchoredInfo(String applyNo, AggAnchoredMerchantInfo aggAnchoredMerchantInfo) {
        transactionTemplate.execute(status -> {
            AnchoredApplyEntity anchoredApply = anchoredApplyDao.queryAnchoredApplyByApplyNoForUpdate(applyNo);
            //找到那一条，然后更新
            String requestNo = aggAnchoredMerchantInfo.getRequestNo();
            List<AggAnchoredMerchantInfo> aggAnchoredMerchantInfoList = StringUtils.isBlank(anchoredApply.getAggAnchoredMerchantText()) ? Lists.newArrayList() :
                    JsonUtils.fromJson(anchoredApply.getAggAnchoredMerchantText(), new TypeReference<List<AggAnchoredMerchantInfo>>() {
                    });
            boolean updated = false;
            for (int i = 0; i < aggAnchoredMerchantInfoList.size(); i++) {
                AggAnchoredMerchantInfo info = aggAnchoredMerchantInfoList.get(i);
                if (requestNo.equals(info.getRequestNo())) {
                    aggAnchoredMerchantInfoList.set(i, aggAnchoredMerchantInfo);
                    updated = true;
                    break;
                }
            }
            if (updated) {
                anchoredApplyDao.updateChannelAnchoredInfo(applyNo, JsonUtils.toJSONString(aggAnchoredMerchantInfoList));
            }
            return true;
        });
    }

    @Override
    public Boolean updateChannelAnchoredResult(String applyNo, AnchoredStatus anchoredStatus, String failReason) {
        if (StringUtils.isNotBlank(failReason) && failReason.length() > 85) {
            failReason = failReason.substring(0, 85);
        }
        return 1 == anchoredApplyDao.updateChannelAnchoredResult(applyNo, anchoredStatus, failReason);
    }

    @Override
    public List<AnchoredApplyEntity> queryAllAnchoredApplyByOrderNo(String orderNo) {
        return anchoredApplyDao.queryAllAnchoredApplyByOrderNo(orderNo);
    }



}
