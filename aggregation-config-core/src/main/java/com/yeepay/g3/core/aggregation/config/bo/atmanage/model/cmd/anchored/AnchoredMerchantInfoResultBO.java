package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 聚合返回的挂靠商编信息
 */
@Data
public class AnchoredMerchantInfoResultBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商编
     */
    private String merchantNo;

    /**
     * 挂靠商户的渠道号
     */
    private String anchoredChannelNo;

    /**
     * 挂靠商户的商户号
     */
    private String anchoredInstitutionMchNo;

    /**
     * 挂靠商户编号
     */
    private String anchoredMerchantNo;

    /**
     * 同主体转换出挂靠商编
     *
     * @param aggSameReportRespBO
     * @return
     */
    public static AnchoredMerchantInfoResultBO covert(AggAnchoredMerchantInfo aggSameReportRespBO, String merchantNo) {
        AnchoredMerchantInfoResultBO payAnchoredMerchantInfo = new AnchoredMerchantInfoResultBO();
        payAnchoredMerchantInfo.setMerchantNo(merchantNo);
        payAnchoredMerchantInfo.setAnchoredChannelNo(aggSameReportRespBO.getAnchoredChannelNo());
        payAnchoredMerchantInfo.setAnchoredInstitutionMchNo(aggSameReportRespBO.getInstitutionMchNo());
        payAnchoredMerchantInfo.setAnchoredMerchantNo(aggSameReportRespBO.getAnchoredMerchantNo());
        return payAnchoredMerchantInfo;
    }

}
