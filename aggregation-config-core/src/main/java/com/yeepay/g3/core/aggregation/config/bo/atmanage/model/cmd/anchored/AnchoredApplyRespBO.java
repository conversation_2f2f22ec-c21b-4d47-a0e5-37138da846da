package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored;

import lombok.Data;

import java.io.Serializable;

/**
 * 挂靠申请响应DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/5  14:04
 */
@Data
public class AnchoredApplyRespBO implements Serializable {
    private static final long serialVersionUID = 1L;


    private String bizScene;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 处理订单号
     */
    private String orderNo;

    /**
     * 单个场景+渠道+互动类型的 申请状态
     * PROCESSING:处理中
     *
     * SUCCESS:成功（只有同步返回结果才会有）
     * FAIL:失败（只有同步返回结果才会有）
     */
    private String applyStatus;

}
