package com.yeepay.g3.core.aggregation.config.flow.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/26 21:40
 */
@Getter
@AllArgsConstructor
public enum FlowBusinessType implements DocumentedEnum<String> {

    ;
    private final String document;
    private final String desc;
}
