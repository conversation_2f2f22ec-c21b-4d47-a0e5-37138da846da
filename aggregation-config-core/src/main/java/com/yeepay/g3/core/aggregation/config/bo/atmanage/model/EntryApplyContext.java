package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:44
 */
@Getter
@Setter(AccessLevel.PRIVATE)
public class EntryApplyContext<T extends EntryApply<? extends ChannelSpecialParams>> {

    private final T entryApply;
    private ThirdPartyRecord thirdPartyRecord;

    public EntryApplyContext(final T entryApply) {
        this.entryApply = entryApply;
    }

    public void buildThirdPartyRecord(final ThirdPartyRecord thirdPartyRecord) {
        this.thirdPartyRecord = thirdPartyRecord;
    }
}
