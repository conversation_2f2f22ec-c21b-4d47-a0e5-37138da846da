package com.yeepay.g3.core.aggregation.config.factory.anchored.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.external.ChannelAnchoredExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.AggSameSubjectAnchoredReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.AggSameReportRespBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 同主体同SAAS渠道
 */
@Component
public class SameSAASSubjectAnchoredApplyHandler extends BaseAnchoredApplyHandler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ChannelAnchoredExternal channelAnchoredExternal;

    @Override
    public boolean isSupport(AnchoredType anchoredType, Boolean anchoredDimension) {
        /*同主体 同SAAS*/
        return AnchoredType.SAME_SUBJECT == anchoredType && !anchoredDimension;
    }

    @Override
    public void validateAnchoredParams(AnchoredApply anchoredApply) {
        SceneAndActivityUtils.getSceneAndPromotionTypeBO(anchoredApply.getChannelType(), anchoredApply.getPayScene(), anchoredApply.getActivityType());
    }

    @Override
    public RemoteResult<List<AggAnchoredMerchantInfo>> aggregationAnchoredApply(AnchoredApply anchoredApplyBO) {
        logger.info("[同主体挂靠][同SAAS] 处理开始 anchoredApplyBO={}", JsonUtils.toJSONString(anchoredApplyBO));
        /*请求指定商编挂靠*/
        AggSameSubjectAnchoredReqBO reqBO = new AggSameSubjectAnchoredReqBO();
        reqBO.setMerchantNo(anchoredApplyBO.getMerchantNo());
        reqBO.setPayScene(anchoredApplyBO.getPayScene().getDocument());
        reqBO.setChannelType(anchoredApplyBO.getChannelType().getDocument());
        reqBO.setActivityType(null == anchoredApplyBO.getActivityType() ? null : anchoredApplyBO.getActivityType().getDocument());
        RemoteResult<List<AggSameReportRespBO>> result = channelAnchoredExternal.aggSameSubjectAnchored(reqBO);
        if (result.isSuccess()) {
            List<AggAnchoredMerchantInfo> anchoredMerchantInfoList = result.getData().stream()
                    .map(AggAnchoredMerchantInfo::covert).collect(Collectors.toList());
            return RemoteResult.success(anchoredMerchantInfoList, result.getChannelCode(), result.getChannelMessage());
        } else {

            return RemoteResult.fail(result.getCode(), result.getMessage());
        }
    }


}
