package com.yeepay.g3.core.aggregation.config.external.bo;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayTerminalReportQueryRequestDTO;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * description: 查询渠道终端报备结果
 * <AUTHOR>
 * @since 2025/6/9:19:34
 * Company: 易宝支付(YeePay)
 */
@Builder
@Getter
public class QueryChannelTerminalEntryReqBO implements Serializable {

    private static final long serialVersionUID = -7302060627872723661L;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 支付渠道
     */
    private PayChannelEnum payChannel;

    public static OpenPayTerminalReportQueryRequestDTO buildChannelDTO(QueryChannelTerminalEntryReqBO reqBO) {

        Assert.isFalse(PayChannelEnum.BANK.equals(reqBO.getPayChannel()), ResultCode.ENTRY_QUERY_TERMINAL_ERROR, "仅支持微信/支付宝");
        OpenPayTerminalReportQueryRequestDTO reqDTO = new OpenPayTerminalReportQueryRequestDTO();
        reqDTO.setMerchantNo(reqBO.getMerchantNo());
        reqDTO.setBankCode(reqBO.getPayChannel().getDocument());
        return reqDTO;
    }
}
