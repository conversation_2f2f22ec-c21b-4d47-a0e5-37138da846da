package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail;

import com.yeepay.aggregation.config.share.kernel.enumerate.QueryBaseStatusEnum;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayTerminalReportDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayTerminalReportQueryResponseDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * description: 查询渠道终端报备信息结果
 *
 * <AUTHOR>
 * @since 2025/6/11:15:55
 * Company: 易宝支付(YeePay)
 */
@Builder
@Getter
public class QueryChannelTerminalInfoBO implements Serializable {
    private static final long serialVersionUID = -6977270427183954145L;

    private String totalTerminalStatus;

    private String totalTerminalFailMsg;

    public static QueryChannelTerminalInfoBO build(OpenPayTerminalReportQueryResponseDTO responseDTO) {
        List<OpenPayTerminalReportDTO> openPayTerminalReportDTOList = responseDTO.getOriOpenPayTerminalReportDTOList();
        if (CheckUtils.isEmpty(openPayTerminalReportDTOList)) {
            return QueryChannelTerminalInfoBO.builder()
                    .totalTerminalStatus(covertStatus(responseDTO.getTotalTerminalStatus()))
                    .totalTerminalFailMsg(responseDTO.getTotalTerminalFailMsg()).build();
        }
        boolean anySuccess = openPayTerminalReportDTOList.stream().anyMatch(e -> QueryBaseStatusEnum.SUCCESS.getDocument().equals(covertStatus(e.getTerminalStatus())));
        boolean allFail = openPayTerminalReportDTOList.stream().allMatch(e -> QueryBaseStatusEnum.FAIL.getDocument().equals(covertStatus(e.getTerminalStatus())));
        if (allFail) {
            return QueryChannelTerminalInfoBO.builder()
                    .totalTerminalStatus(QueryBaseStatusEnum.FAIL.getDocument())
                    .totalTerminalFailMsg(responseDTO.getTotalTerminalFailMsg()).build();
        } else {
            return QueryChannelTerminalInfoBO.builder()
                    .totalTerminalStatus(anySuccess ? QueryBaseStatusEnum.SUCCESS.getDocument() : QueryBaseStatusEnum.PROCESSING.getDocument())
                    .build();
        }

    }

    private static String covertStatus(String channelStatus) {
        switch (channelStatus) {
            case "SUCCESS":
                return QueryBaseStatusEnum.SUCCESS.getDocument();
            case "FAIL":
                return QueryBaseStatusEnum.FAIL.getDocument();
            default:
                return QueryBaseStatusEnum.PROCESSING.getDocument();
        }
    }

}
