package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;

import java.io.Serializable;

/**
 * 分帐接收方消息
 *
 * @author: Mr.yin
 * @date: 2025/6/16  10:55
 * {@link com.yeepay.g3.facade.trade.bankcooper.dto.async.message.CallbackBankOpenDivideResultMessageFroRocket}
 */
@Data
public class DivideReceiverMessage implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 发起方易宝商编
     */
    private String yeepayMerchantNo;

    /**
     * 状态
     * SUCCESS
     * FAIL
     */
    private String status;

    /**
     * 申请结果描述
     */
    private String errMsg;

    /**
     * 请求流水号
     */
    private String requestOrderNo;

    /**
     * 分帐接收方商编
     */
    private String receiveMerchantno;

    private String id;
    private String yeepayMerchantName;
    private String reportMerchantno;
    private String receiveReportMerchantno;
    private String receiveReportMerchantname;
    private String relationType;
    private String industryName;
    private String reportType;
    private String feeType;
    private String channelNo;
    private String channelIdentifier;
    private String bankCode;
    private String mchAppId;
    private String open;
    private String source;
    private String errCode;


}
