package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/4 22:28
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum implements DocumentedEnum<String> {
    INIT("INIT", "初始化"),
    PROCESSING("PROCESSING", "处理中"),
    FINISH("FINISH", "完成"),
    ;
    private final String document;
    private final String desc;
}
