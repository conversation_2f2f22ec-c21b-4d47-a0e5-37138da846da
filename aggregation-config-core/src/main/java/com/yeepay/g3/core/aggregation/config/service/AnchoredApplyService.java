package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;

import java.util.List;

/**
 * 挂靠订单
 *
 * @author: Mr.yin
 * @date: 2025/6/8  16:25
 */
public interface AnchoredApplyService {

    /**
     * 批量插入挂靠申请
     *
     * @param anchoredApplyList
     * @return
     */
    Boolean batchCreateAnchoredApply(List<AnchoredApplyEntity> anchoredApplyList);

    /**
     * 更新申请为  处理中
     *
     * @param anchoredApply
     * @return
     */
    void updateAnchoredProcess(AnchoredApply anchoredApply);

    /**
     * 更新聚合挂靠成功
     *
     * @param anchoredApply
     * @return
     */
    void updateAggAnchoredResultSuccess(AnchoredApply anchoredApply);

    /**
     * 更新聚合挂靠失败
     *
     * @return
     */
    void updateAggAnchoredResultFail(String applyNo, String failReason, String channelCode);

    /**
     * 更新渠道调用成功标志
     *
     * @param applyNo
     */
    void updateChannelAnchoredInfo(String applyNo, AggAnchoredMerchantInfo aggAnchoredMerchantInfo);

    /**
     * 更新渠道调用最终结果
     *
     * @param applyNo
     * @param anchoredStatus
     * @param failReason
     */
    Boolean updateChannelAnchoredResult(String applyNo, AnchoredStatus anchoredStatus, String failReason);

    /**
     * 查询同一批次下的所有挂靠申请
     *
     * @param orderNo
     * @return
     */
    List<AnchoredApplyEntity> queryAllAnchoredApplyByOrderNo(String orderNo);


}
