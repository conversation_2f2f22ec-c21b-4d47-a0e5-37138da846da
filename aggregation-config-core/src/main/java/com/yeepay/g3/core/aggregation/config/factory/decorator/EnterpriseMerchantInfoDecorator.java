package com.yeepay.g3.core.aggregation.config.factory.decorator;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.auth.AuthApplyBO;
import com.yeepay.g3.core.aggregation.config.factory.handle.MerchantInfoStrategy;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;

/**
 * description: 企业商户信息装饰器
 * <AUTHOR>
 * @since 2025/5/22:11:19
 * Company: 易宝支付(YeePay)
 */
public class EnterpriseMerchantInfoDecorator extends MerchantInfoDecorator {

    public EnterpriseMerchantInfoDecorator(MerchantInfoStrategy merchantInfoStrategy) {
        super(merchantInfoStrategy);
    }
    @Override
    public AuthApplyBO buildAndValidateAuth(AuthApplyReqDTO authApplyReqDTO) {
        // 业务逻辑处理
        AuthApplyBO authApplyBO = super.buildAndValidateAuth(authApplyReqDTO);
//        // 处理企业商户信息
//        SubjectInfo subjectInfo = authApplyBO.getSubjectInfo();
//        ValidationUtils.isTrue(null != subjectInfo.getCertificateInfo() && subjectInfo.getCertificateInfo().isUnifiedCreditCertificate(), "企业商户必须提供统一社会信用代码证");
//        ValidationUtils.notNull(subjectInfo.getCompanyRepresentativeInfo().getCertAddress(), "企业商户必须提供法人证件地址");
////        ValidationUtils.notNull(subjectInfo.getCompanyRepresentativeInfo().isBenefitPerson(), "企业商户必须提供法人是否为实际收益人");
        return authApplyBO;
    }

}
