package com.yeepay.g3.core.aggregation.config.convert;

/**
 * <AUTHOR>
 * @date 2025/6/8 19:01
 */

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ChannelEntryRequestConvertFactory {

    private final List<ChannelEntryRequestConvert<? extends EntryApply<? extends ChannelSpecialParams>>> converts;

    public ChannelEntryRequestConvertFactory(final List<ChannelEntryRequestConvert<? extends EntryApply<? extends ChannelSpecialParams>>> converts) {
        this.converts = converts;
    }

    public ChannelEntryRequestConvert<? extends EntryApply<? extends ChannelSpecialParams>> getHandler(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        return converts.stream()
                .filter(handler -> handler.isSupport(payChannel, payScene))
                .findFirst()
                .orElseThrow(() -> new UnsupportedOperationException("不支持的支付渠道或支付场景"));
    }
}
