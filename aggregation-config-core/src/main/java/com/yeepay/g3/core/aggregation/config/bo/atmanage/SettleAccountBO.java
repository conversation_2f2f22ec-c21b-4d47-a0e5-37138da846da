package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.SettleAccountDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 结算账户业务对象
 * <AUTHOR>
 * @since 2025/5/27:17:46
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class SettleAccountBO implements Serializable {
    private static final long serialVersionUID = 5838998065643720589L;

    /**
     * 银行账户
     */
    private String cardNo;

    /**
     * 银行账户名称
     */
    private String cardName;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 总行名称
     */
    private String bankName;

    /**
     * 支行名称
     */
    private String bankBranchName;

    /**
     * 省份编码
     */
    private String provName;

    /**
     * 市编码
     */
    private String cityName;

    public static SettleAccountBO build(SettleAccountDTO accountDTO) {
        if (accountDTO == null) {
            return null;
        }
        return SettleAccountBO.builder()
                .cardNo(accountDTO.getCardNo())
                .cardName(accountDTO.getCardName())
                .bankCode(accountDTO.getBankCode())
                .bankName(accountDTO.getBankName())
                .bankBranchName(accountDTO.getBankBranchName())
                .provName(accountDTO.getProvName())
                .cityName(accountDTO.getCityName())
                .build();
    }
}
