package com.yeepay.g3.core.aggregation.config.enums;

/**
 * @Description:
 * @ClassName: MerchantGradeEnum
 * @Author: cong.huo
 * @Date: 2024/12/27 10:52   // 时间
 * @Version: 1.0
 */
public enum MerchantGradeEnum {


    THREE(3,"三级"),
    TWO(2,"二级"),
    ONE(1,"二级");

    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String desc;

    MerchantGradeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;

    }
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
