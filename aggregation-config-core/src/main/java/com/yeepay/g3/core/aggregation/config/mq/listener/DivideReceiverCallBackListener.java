package com.yeepay.g3.core.aggregation.config.mq.listener;

import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.DivideReceiverBiz;
import com.yeepay.g3.core.aggregation.config.mq.event.DivideReceiverMessage;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 微信直连分账接收方设置回调监听
 * wechatDirectDivideReceiverMessageListener
 */
@Component
public class DivideReceiverCallBackListener implements MessageListenerConcurrently {
    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Resource
    private DivideReceiverBiz divideReceiverBiz;

    private final int maxConsumeCount = 10;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        LogGuidUtil.clearLogContext();
        MessageExt msg = list.get(0);
        int reconsumeTimes = msg.getReconsumeTimes();
        String message = new String(msg.getBody());
        try {
            LOGGER.info("[聚合分帐方创建]接收到了消息[{}]，msgId={}", message, msg.getMsgId());
            DivideReceiverMessage callBackBO = JsonUtils.fromJson(message, DivideReceiverMessage.class);
            divideReceiverBiz.callBackDivideReceiver(callBackBO);
        } catch (Exception e) {
            if (reconsumeTimes == maxConsumeCount) {
                LOGGER.error("[短信报警],微信直连开通分账方回调失败,已达最大重试次数,mq不会再进行重试,message=" + message, e);
            } else {
                LOGGER.error("[普通报警],微信直连开通分账方回调失败,mq稍后会进行重试,message=" + message, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
