package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryResultEntity;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 17:16
 */
public interface ChannelDisposalExternal {

    DisposalQueryResultEntity pageQueryDisposalList(DisposalQueryEntity requestEntity);

}
