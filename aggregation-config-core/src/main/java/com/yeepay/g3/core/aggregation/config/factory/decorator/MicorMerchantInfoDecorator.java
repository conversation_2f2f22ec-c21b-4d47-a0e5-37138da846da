package com.yeepay.g3.core.aggregation.config.factory.decorator;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.auth.AuthApplyBO;
import com.yeepay.g3.core.aggregation.config.factory.handle.MerchantInfoStrategy;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;

/**
 * description: 企业商户信息装饰器
 * <AUTHOR>
 * @since 2025/5/22:11:19
 * Company: 易宝支付(YeePay)
 */
public class MicorMerchantInfoDecorator extends MerchantInfoDecorator {

    public MicorMerchantInfoDecorator(MerchantInfoStrategy MerchantInfoStrategy) {
        super(MerchantInfoStrategy);
    }

    @Override
    public AuthApplyBO buildAndValidateAuth(AuthApplyReqDTO authApplyReqDTO) {
        // 业务逻辑处理
        AuthApplyBO authApplyBO = super.buildAndValidateAuth(authApplyReqDTO);
//        // todo-LZX-主体类型为小微商户时，辅助证明材料信息必填
//        ValidationUtils.notNull(authApplyBO.getAssistProveInfo(), "主体类型为小微商户时，辅助证明材料信息必填");
//
//        // 联系人信息
//        ContactInfo contactInfo = authApplyBO.getContactInfo();
//        ValidationUtils.isTrue(contactInfo.getIsLegal(), "主体类型为小微商户时，联系人类型必须为经营者/法人");
//        if (PayChannelEnum.ALIPAY.equals(authApplyBO.getChannelType())) {
//            ValidationUtils.isTrue(contactInfo.getIsIdCardCertType(), "支付宝-主体类型为小微商户时，联系人证件类型必须为身份证");
//            ValidationUtils.isTrue(authApplyBO.getSubjectInfo().getCompanyRepresentativeInfo().getIsIdCardType(), "支付宝-主体类型为小微商户时，法人/经办人证件类型必须为身份证");
//        }

        return authApplyBO;
    }

}
