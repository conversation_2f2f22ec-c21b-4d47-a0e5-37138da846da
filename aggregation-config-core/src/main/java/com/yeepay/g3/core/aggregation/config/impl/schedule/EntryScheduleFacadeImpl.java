package com.yeepay.g3.core.aggregation.config.impl.schedule;

import com.google.common.collect.Lists;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.EntryApplyCompensateBiz;
import com.yeepay.g3.facade.aggregation.config.facade.schedule.EntryScheduleFacade;
import com.yeepay.g3.utils.common.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: Mr.yin
 * @date: 2025/6/11  17:57
 */
@Slf4j
@Component
public class EntryScheduleFacadeImpl implements EntryScheduleFacade {

    @Resource
    private EntryApplyCompensateBiz entryApplyCompensateBiz;

    @Override
    public void compensateEntryApply(Integer startTime, Integer endTime, String startDateStr, String endDateStr, String applyNoListStr) {
        log.info("[补偿][处理未闭环的进件订单]  startTime={}, endTime={}，startDateStr={},endDateStr={},applyNoListStr={}", startTime, endTime, startDateStr, endDateStr, applyNoListStr);
        Date startTimeDate = null;
        Date endTimeDate = null;
        try {
            if (StringUtils.hasLength(startDateStr)) {
                startTimeDate = DateUtils.parseDate(startDateStr, DateUtils.DATE_FORMAT_DATETIME);
                endTimeDate = DateUtils.parseDate(endDateStr, DateUtils.DATE_FORMAT_DATETIME);
            } else if (null != startTime) {
                Date now = new Date();
                startTimeDate = DateUtils.addDay(now, -startTime);
                endTimeDate = DateUtils.addDay(now, -endTime);
            }

        } catch (Exception e) {
            log.warn("[补偿][处理未闭环的进件订单]  时间转换异常 startTime={}, endTime={}，startDateStr={},endDateStr={},orderNo={}  异常", startTime, endTime, startDateStr, endDateStr, applyNoListStr, e);
            throw new IllegalArgumentException("时间转换异常");
        }
        List<String> applyNoList = null;
        if (StringUtils.hasLength(applyNoListStr)) {
            applyNoList = Lists.newArrayList(applyNoListStr.split(","));
        }
        if (CollectionUtils.isEmpty(applyNoList) && null == startTimeDate) {
            log.warn("[补偿][处理未闭环的进件订单]  时间或者单号二选一必填");
            throw new IllegalArgumentException("时间或者单号二选一必填");
        }
        entryApplyCompensateBiz.compensateEntryApply(startTimeDate, endTimeDate, applyNoList);
    }
}
