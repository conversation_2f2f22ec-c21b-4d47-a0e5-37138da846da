package com.yeepay.g3.core.aggregation.config.dao;

import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-13 15:15
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Mapper
public interface ControlConfigDao  {

    /**
     * 根据主键查询单条记录
     * @param id 主键ID
     * @return Optional<ControlConfigEntity> 对象
     */
    ControlConfigEntity queryById(@Param("id") Long id);

    /**
     * 插入一条记录（选择性插入）
     * @param controlConfigEntity 插入的对象
     * @return 插入的记录数
     */
    int insertSelective(ControlConfigEntity controlConfigEntity);

    /**
     * 更新一条记录（根据ID选择性更新）
     * @param controlConfigEntity 更新的对象
     * @return 更新的记录数
     */
    int updateSelective(ControlConfigEntity controlConfigEntity);

    /**
     * 根据商户编号和状态查询记录（排除已软删除的记录）
     * @param merchantNo 商户编号
     * @param status 状态
     * @return 符合条件的记录列表
     */
    ControlConfigEntity queryByMerchantNoAndStatus(@Param("merchantNo") String merchantNo, @Param("status") String status);
}
