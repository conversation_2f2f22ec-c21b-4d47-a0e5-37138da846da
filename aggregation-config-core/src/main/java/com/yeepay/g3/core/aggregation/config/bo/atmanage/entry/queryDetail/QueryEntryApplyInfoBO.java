package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail;

import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryAndAnchoredDetailBO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryAndAnchoredDetailDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryApplyInfoDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryApplyInfoDetailDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 聚合进件查询结果DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:57
 */
@Builder
@Getter
public class QueryEntryApplyInfoBO implements Serializable {

    private static final long serialVersionUID = -8322453140001340271L;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 业务场景
     */
    private BizSceneEnum bizScene;

    /**
     * 明细信息
     */
    private List<QueryEntryApplyDetailInfoBO> detailInfoList;

    public static QueryEntryApplyInfoBO build(List<QueryEntryApplyDetailInfoBO> detailInfoList) {
        if (detailInfoList == null || detailInfoList.isEmpty()) {
            return null;
        }
        return QueryEntryApplyInfoBO.builder()
                .bizApplyNo(detailInfoList.get(0).getBizApplyNo())
                .bizScene(detailInfoList.get(0).getBizScene())
                .detailInfoList(detailInfoList)
                .build();
    }

    public static QueryEntryApplyInfoDTO buildDTO(QueryEntryApplyInfoBO queryEntryApplyInfoBO) {
        if (CheckUtils.isEmpty(queryEntryApplyInfoBO)) {
            return null;
        }
        List<QueryEntryApplyInfoDetailDTO> infoDetails = queryEntryApplyInfoBO.getDetailInfoList().stream()
                .map(QueryEntryApplyDetailInfoBO::buildDTO)
                .collect(Collectors.toList());

        return QueryEntryApplyInfoDTO.builder()
                .bizApplyNo(queryEntryApplyInfoBO.getBizApplyNo())
                .bizScene(queryEntryApplyInfoBO.getBizScene().getDocument())
                .detailList(infoDetails)
                .build();
    }
}