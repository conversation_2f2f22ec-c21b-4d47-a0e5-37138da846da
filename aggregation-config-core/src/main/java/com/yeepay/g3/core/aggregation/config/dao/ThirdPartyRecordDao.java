package com.yeepay.g3.core.aggregation.config.dao;

import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * description: 第三方记录数据访问对象接口
 *
 * <AUTHOR>
 * @since 2025/5/26:11:38
 * Company: 易宝支付(YeePay)
 */
public interface ThirdPartyRecordDao extends Mapper<ThirdPartyRecordEntity> {

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 插入成功的记录数
     */
    int batchInsertThirdPartyRecord(@Param("list") List<ThirdPartyRecordEntity> records);

    /**
     * 查询同一批次请求下的记录
     *
     * @param applyNo
     * @param businessType
     * @return
     */
    List<ThirdPartyRecordEntity> queryThirdPartyRecordByApplyNo(@Param("applyNo") String applyNo, @Param("businessType") String businessType);

    /**
     * 查询单条请求记录
     *
     * @param requestNo
     * @param businessType
     * @return
     */
    ThirdPartyRecordEntity queryThirdRecordByRequestNo(@Param("requestNo") String requestNo, @Param("businessType") String businessType);
}