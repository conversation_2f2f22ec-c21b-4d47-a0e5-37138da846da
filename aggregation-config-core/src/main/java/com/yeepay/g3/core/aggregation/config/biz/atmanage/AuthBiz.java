package com.yeepay.g3.core.aggregation.config.biz.atmanage;

/**
 * description: 认证业务接口
 *
 * <AUTHOR>
 * @since 2025/5/21:15:01
 * Company: 易宝支付(YeePay)
 */


public interface AuthBiz {

//    /**
//     * 实名认证申请
//     */
//    String authApply(AuthApplyBO authApplyBO);
//
//    /**
//     * 取消实名认证申请
//     * @param authCancelBO
//     */
//    void authCancel(AuthCancelBO authCancelBO);
//
//    /**
//     * 查询实名认证结果
//     * @param authQueryBO
//     * @return
//     */
//    AuthResultBO queryAuthResult(AuthQueryBO authQueryBO);
//
//    /**
//     * 认证结果处理
//     * @param requestNo 请求号
//     */
//    void handleAuthResult(String requestNo, Date startTime, Date endTime);
}
