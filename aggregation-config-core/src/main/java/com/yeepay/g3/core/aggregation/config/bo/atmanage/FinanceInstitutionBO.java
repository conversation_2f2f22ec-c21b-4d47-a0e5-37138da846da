package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.FinanceInstitutionDTO;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * description: 金融机构信息DTO
 *
 * <AUTHOR>
 * @since 2025/5/20:18:58
 * Company: 易宝支付(YeePay)
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class FinanceInstitutionBO implements Serializable {

    private static final long serialVersionUID = -3379729278737698282L;
    /**
     * 金融机构类型
     */
    private final String financeType;

    /**
     * 金融机构许可证图片
     */
    private final List<String> financeLicensePics;

    public static FinanceInstitutionBO build(FinanceInstitutionDTO institutionDTO) {

        Assert.notBlank(institutionDTO.getFinanceType(), ResultCode.PARAM_VALID_ERROR, "金融机构类型不能为空");
        return FinanceInstitutionBO.builder()
                .financeType(institutionDTO.getFinanceType())
                .financeLicensePics(institutionDTO.getFinanceLicensePics())
                .build();
    }
}
