package com.yeepay.g3.core.aggregation.config.impl.schedule;

import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.facade.aggregation.config.facade.schedule.ChannelNoScheduleFacade;
import com.yeepay.g3.utils.common.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * description: 渠道号调度服务实现类
 * <AUTHOR>
 * @since 2025/6/5:16:12
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelNoScheduleFacadeImpl implements ChannelNoScheduleFacade {

    private final ChannelNoBiz channelNoBiz;
    @Override
    public void handleChannelNoApplyResult(String requestNo, Integer startTime, Integer endTime) {
        log.info("handleChannelNoApplyResult: 开始处理渠道号申请结果定时任务, requestNo={}, startTime={}, endTime={}", requestNo, startTime, endTime);
        Date now = new Date();
        Date startTimeDate = DateUtils.addDay(now, -startTime);
        Date endTimeDate = DateUtils.addDay(now, -endTime);
        channelNoBiz.handleChannelNoApplyResult(requestNo, startTimeDate, endTimeDate);
    }
}
