package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity;
import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.core.aggregation.config.entity.MailMessageEntity;

import java.util.List;

/**
 * @Description: 邮件通知
 * @ClassName: MailNotifyExternal
 * @Author: cong.huo
 * @Date: 2024/12/16 17:53   // 时间
 * @Version: 1.0
 */
public interface MailNotifyExternal {
    /**
     * 邮件通知
     * @param mailMessage
     * @return void
     * <AUTHOR>
     * @date 2024/12/10 16:04
     */
    boolean  sendMailMessage(MailMessageEntity mailMessage) ;

    /**
     * 创建文件并发送
     * @param topMerchant
     * @param subMerchantNos
     * @param controlConfigEntity
     * @param appidControlEntity
     * @return boolean
     * <AUTHOR>
     * @date 2024/12/19 18:59
     */
    boolean createMailMessageByAppidAndSend(String topMerchant, List<AppidAllMerchantNoBindEntity> subMerchantNos, ControlConfigEntity controlConfigEntity, AppidControlEntity appidControlEntity);
}
