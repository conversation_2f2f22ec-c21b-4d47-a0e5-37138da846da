package com.yeepay.g3.core.aggregation.config.external;

/**
 * description: 通道授权外部域
 * <AUTHOR>
 * @since 2025/5/23:16:14
 * Company: 易宝支付(YeePay)
 */
public interface ChannelAuthExternal {

//    /**
//     * 实名认证申请
//     * @param authApplyBO 认证申请对象
//     * @return 认证申请结果
//     */
//    AuthApplyResultBO submitIdentityAuth(String requestNo, AuthApplyBO authApplyBO);
//
//    /**
//     * 取消实名认证申请
//     * @param authCancelBO 取消认证申请对象
//     */
//    void cancelIdentityAuth(String requestNo, AuthCancelBO authCancelBO);
//
//    /**
//     * 查询实名认证结果
//     * @param authQueryBO 查询认证结果对象
//     * @return 认证结果对象
//     */
//    AuthResultBO queryIdentityAuth(String requestNo, AuthQueryBO authQueryBO);
}
