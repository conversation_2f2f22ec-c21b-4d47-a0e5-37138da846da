package com.yeepay.g3.core.aggregation.config.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yeepay.g3.core.aggregation.config.enums.BindAllStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.WaringStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 邮件通知累
 * @className AppipAllMerchantNoBindEntity
 * <AUTHOR>
 * @date 2024/12/17 14:40
 * @version 1.0
 */
@Data
public class AppidAllMerchantNoMailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户编号
     */
    @ExcelProperty("商户编号")
    private String merchantNo;

    /**
     * 商户名称
     */
    @ExcelProperty("商户名称（全称）")
    private String merchantName;

    /**
     * APPID
     */
    @ExcelProperty("管控小程序appid")
    private String appid;



    /**
     * 管控开始时间
     */
    @ExcelProperty("管控appid开始时间")
    private String controlStartTime;

    /**
     * 管控结束时间
     */
    @ExcelProperty("管控appid结束时间")
    private String controlEndTime;



}
