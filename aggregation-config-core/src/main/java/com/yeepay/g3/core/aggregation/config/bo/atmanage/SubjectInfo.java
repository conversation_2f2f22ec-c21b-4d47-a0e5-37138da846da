package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.CertificateTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.enumerate.SubjectTypeEnum;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SalesInfo;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.RegistrationCertificateDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.SubjectInfoDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * description: 主体信息 DTO
 *
 * <AUTHOR>
 * @since 2025/5/21:10:52
 * Company: 易宝支付(YeePay)
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class SubjectInfo {

    private final String subjectId;

    /**
     * 主体类型
     */
    private final SubjectTypeEnum subjectType;

    /**
     * 单位证明函照片（条件必填）
     */
    private final String certificateLetterCopy;

    /**
     * 是否金融机构
     * 未传入将默认填写：false
     */
    private final Boolean financeInstitution;

    /**
     * 登记证书信息
     */
    private final RegistrationCertificateBO certificateInfo;

    /**
     * 经办人/法人身份信息（必填）
     */
    private final CompanyRepresentativeBO companyRepresentativeInfo;

    /**
     * 金融机构许可证
     */
    private final FinanceInstitutionBO financeInstitutionInfo;

    private SalesInfo salesInfo;


//    public static SubjectInfo buildAuth(SubjectInfoDTO subjectInfoDTO) {
//        if (subjectInfoDTO == null) {
//            return null;
//        }
//        // 空时默认 false
//        Boolean financeInstitution = !CheckUtils.isEmpty(subjectInfoDTO.getFinanceInstitution()) && subjectInfoDTO.getFinanceInstitution();
//        return SubjectInfo.builder()
//                .subjectType(DocumentedEnum.fromValue(SubjectTypeEnum.class, subjectInfoDTO.getSubjectType()))
//                .certificateLetterCopy(subjectInfoDTO.getCertificateLetterCopy())
//                .financeInstitution(financeInstitution)
//                .certificateInfo(RegistrationCertificateBO.build(subjectInfoDTO.getCertificateInfo()))
//                .companyRepresentativeInfo(CompanyRepresentativeBO.build(subjectInfoDTO.getCompanyRepresentativeInfo()))
//                .financeInstitutionInfo(FinanceInstitutionBO.build(subjectInfoDTO.getFinanceInstitutionInfo()))
//                .salesInfo(SalesInfo.build(subjectInfoDTO.getSalesInfo()))
//                .build();
//    }

    public static SubjectInfo build(SubjectInfoDTO subjectInfoDTO) {
        Assert.isFalse(subjectInfoDTO.getFinanceInstitution()
                        && Objects.isNull(subjectInfoDTO.getFinanceInstitutionInfo()), ResultCode.PARAM_VALID_ERROR,
                "金额机构时金融机构许可证信息必填");
        final SubjectTypeEnum subjectTypeEnum = DocumentedEnum.fromValue(SubjectTypeEnum.class, subjectInfoDTO.getSubjectType());

        certTypeCheck(subjectInfoDTO.getCertificateInfo(), subjectTypeEnum);
        RegistrationCertificateBO certificate = null;
        if (!subjectTypeEnum.isMicro()) {
            certificate = RegistrationCertificateBO.build(subjectInfoDTO.getCertificateInfo());
        }

        return SubjectInfo.builder()
                .subjectId(subjectInfoDTO.getSubjectId())
                .subjectType(DocumentedEnum.fromValue(SubjectTypeEnum.class, subjectInfoDTO.getSubjectType()))
                .certificateLetterCopy(subjectInfoDTO.getCertificateLetterCopy())
                .certificateInfo(certificate)
                .financeInstitution(subjectInfoDTO.getFinanceInstitution())
                .companyRepresentativeInfo(CompanyRepresentativeBO.build(subjectInfoDTO.getCompanyRepresentativeInfo()))
                .financeInstitutionInfo(Boolean.FALSE.equals(subjectInfoDTO.getFinanceInstitution()) ? null :
                        FinanceInstitutionBO.build(subjectInfoDTO.getFinanceInstitutionInfo()))
                .salesInfo(SalesInfo.build(subjectInfoDTO.getSalesInfo()))
                .build();

    }

    private static void certTypeCheck(RegistrationCertificateDTO certificateDTO, SubjectTypeEnum subjectType) {
        if (null == certificateDTO) {
            return;
        }
        final CertificateTypeEnum certificateTypeEnum = DocumentedEnum.fromValue(CertificateTypeEnum.class,
                certificateDTO.getCertType());
        DocumentedEnum.fromValue(CertificateTypeEnum.class, certificateDTO.getCertType());
        final boolean isSupport = certificateTypeEnum.getSupportSubjectTypeList().contains(subjectType);
        Assert.isTrue(isSupport, ResultCode.PARAM_VALID_ERROR, "主体类型为" + subjectType + "时，不支持" +
                certificateDTO.getCertType() + "证件类型");
    }

//    /**
//     * 实名认证-基础校验
//     */
//    public void validateAuthBase() {
//        if (SubjectTypeEnum.INSTITUTION.equals(subjectType)) {
//            ValidationUtils.notNull(certificateLetterCopy, "主体类型为事业单位时，单位证明函照片必填");
//        }
//
//        if (Boolean.TRUE.equals(financeInstitution)) {
//            ValidationUtils.notNull(financeInstitutionInfo, "当主体是金融机构时，必填");
//        }
//
//        boolean needCertificateInfo = DocumentedEnum.inRightEnums(subjectType,
//                SubjectTypeEnum.ENTERPRISE,
//                SubjectTypeEnum.INDIVIDUAL,
//                SubjectTypeEnum.GOVERNMENT,
//                SubjectTypeEnum.INSTITUTION,
//                SubjectTypeEnum.OTHERS);
//        if (needCertificateInfo) {
//            ValidationUtils.notNull(certificateInfo, "主体类型为企业/个体户/政府机关/事业单位/社会组织时，必填");
//        }
//    }
}
