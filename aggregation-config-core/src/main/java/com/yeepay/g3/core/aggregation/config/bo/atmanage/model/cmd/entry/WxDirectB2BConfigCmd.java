package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.WxDirectB2BConf;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 18:51
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
public class WxDirectB2BConfigCmd {

    private final PaySceneEnum payScene;

    private final PayChannelEnum payChannel;


    private final SuperAdminInfo superAdminInfo;
    /**
     * 结算账户信息
     */
    private final SettleAccountInfo settleAccountInfo;
    /**
     * 主营商品类型。
     * 通道侧枚举【01：食品 02：饮料 03：乳制品 04：酒水 05：生鲜果蔬 06：调味品 07：日化个护 08：文具 09：鞋服/配饰 10：家居
     * 11：母婴/玩具   12：数码3C 99：其他】
     */
    private final List<String> goodsTypes;
    /**
     * 主要线下销售渠道。
     * 可选项："杂货店", "便利店", "超市", "餐饮店", "母婴店", "烟酒店", "其他"
     * 通道侧枚举：
     * 【A：杂货店
     * B：便利店
     * C：超市
     * D：餐饮店
     * E：母婴店
     * F：烟酒店
     * Z：其他】
     */
    private final List<String> goodsSales;

    /**
     * 商户规模 微信小程序B2B必填
     * LARGE: 大型企业 2000 人以上
     * MIDDLE: 中型企业 150 至 2000 人
     * SMALL: 小型企业 15 至 150 人
     * TINY: 微型企业 15 人以下
     */
    private final String merchantScale;

    /**
     * 门店覆盖数。
     * 可选项："0-5千", "5千-1万", "1万-10万", "10万-50万", "50万以上"
     * 通道侧枚举【N1：0-5千 N2：5千-1万 N3：1万-10万  N4：10万-50万 N5：50万以上】
     */
    private final String coverNum;

    /**
     * 所需服务类型。
     * 可选项："门店订货", "门店促销", "门店活动执行", "门店直播", "其他"
     * 通道侧枚举【S1：门店订货 S2：门店促销 S3：门店活动执行 S4：门店直播 S5：其他】
     */
    private final List<String> services;

    /**
     * 活动信息
     */
    private final ActivityInfo activityInfo;

    /**
     * 小程序信息
     */
    private final MiniProgramInfo miniProgramInfo;

    private final String storeEntrancePic;


    /**
     * 进件类型
     */
    private final EntryTypeEnum entryType;

    private final String extendInfo;

    public static WxDirectB2BConfigCmd build(final WxDirectB2BConf wxDirectB2BConf) {

        Assert.notNull(wxDirectB2BConf, ResultCode.PARAM_VALID_ERROR, "微信B2B配置不能为空");
        return WxDirectB2BConfigCmd.builder()
                .payScene(PaySceneEnum.STORE_ASST)
                .payChannel(PayChannelEnum.WECHAT)
                .entryType(EntryTypeEnum.ENTRY)
                .settleAccountInfo(SettleAccountInfo.build(wxDirectB2BConf.getSettleAccountInfo()))
                .superAdminInfo(SuperAdminInfo.build(wxDirectB2BConf.getSuperAdminInfo()))
                .goodsTypes(wxDirectB2BConf.getGoodsTypes())
                .goodsSales(wxDirectB2BConf.getGoodsSales())
                .merchantScale(wxDirectB2BConf.getMerchantScale())
                .coverNum(wxDirectB2BConf.getCoverNum())
                .services(wxDirectB2BConf.getServices())
                .storeEntrancePic(wxDirectB2BConf.getStoreEntrancePic())
                .activityInfo(ActivityInfo.buildWechatB2BActivity(wxDirectB2BConf.getActivityInfo()))
                .miniProgramInfo(MiniProgramInfo.buildWechatB2BMiniProgramInfo(wxDirectB2BConf.getMiniProgramInfo()))
                .extendInfo(wxDirectB2BConf.getExtendInfo())
                .build();
    }
}
