package com.yeepay.g3.core.aggregation.config.dao;

import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * description: 挂靠订单数据访问对象接口
 *
 * <AUTHOR>
 * @since 2025/5/26:11:38
 * Company: 易宝支付(YeePay)
 */
@Mapper
public interface AnchoredOrderDao {

    /**
     * 插入挂靠订单记录
     *
     * @param anchoredOrderEntity 挂靠订单实体
     * @return 影响行数
     */
    int createAnchoredOrder(AnchoredOrderEntity anchoredOrderEntity);

    /**
     * 更新挂靠订单完成
     *
     * @param orderNo
     * @return
     */
    int updateAnchoredOrderFinish(@Param("orderNo") String orderNo);

    /**
     * 根据业务场景和业务申请单号查询挂靠订单记录
     *
     * @param bizScene   业务场景
     * @param bizApplyNo 业务申请单号
     * @return 挂靠订单实体列表
     */
    AnchoredOrderEntity selectByBizSceneAndBizApplyNo(@Param("bizScene") String bizScene, @Param("bizApplyNo") String bizApplyNo);

    /**
     * 根据orderNo 查询订单是否存在
     *
     * @param orderNo
     * @return
     */
    AnchoredOrderEntity queryAnchoredOrderByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 补偿查询未闭环的订单
     * 时间和单号需要二选一必填
     *
     * @param orderNoList
     * @param startTime
     * @param endTime
     * @return
     */
    List<AnchoredOrderEntity> selectUnFinishAnchoredOrderList(@Param("orderNoList") List<String> orderNoList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


}