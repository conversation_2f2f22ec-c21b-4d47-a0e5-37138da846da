package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/24 20:35
 */

@Getter
public enum ChannelGatewayCode implements DocumentedEnum<String> {

    WECHAT("WECHAT", "微信"),
    UNIONPAY("UNIONPAY", "银联"),
    NUCC("NUCC", "网联"),
    UNIONPAY_NUCC("UNIONPAY_NUCC", "银联网联"),
    BANK("BANK", "银行直连");

    private final String document;
    private final String desc;

    ChannelGatewayCode(final String document, final String desc) {
        this.document = document;
        this.desc = desc;
    }
}
