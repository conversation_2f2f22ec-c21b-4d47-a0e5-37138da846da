package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * description: 渠道进件回调结果消息体
 *
 * <AUTHOR>
 * @since 2025/6/15:20:31
 * Company: 易宝支付(YeePay)
 */
@Data
public class ChannelEntryResultEvent implements Serializable {

    private static final long serialVersionUID = 222816060113848881L;
    /**
     * 请求流水号
     */
    @NotBlank(message = "请求流水号不能为空")
    private String requestOrderNo;

    /**
     * 回调类型
     */
    private String callbackType;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 渠道编号
     */
    private String channelNo;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 是否开启标志
     */
    private boolean open;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 报备商户号
     */
    private String reportMerchantNo;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 报备类型
     */
    private String reportType;

    /**
     * 错误码
     */
    private String errCode;

    /**
     * 错误描述
     */
    private String errMsg;

    /**
     * 报备费率
     */
    private String reportFee;

    /**
     * 签约地址链接，用于引导用户完成签约操作的 URL
     */
    private String signUrl;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 扩展备注ID，用于存储额外的业务标识信息或上下文ID
     */
    private String externalRemarkId;

    /**
     * 渠道订单号，由银行或渠道方生成的唯一订单编号
     */
    private String bankOrderNo;

}
