package com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.QueryChannelApplyResultReqDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.QueryChannelInfoRequestDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 渠道号申请业务对象
 * <AUTHOR>
 * @since 2025/5/27:16:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class ChannelNoQueryBO implements Serializable {


    private static final long serialVersionUID = -5074948975765516836L;

    /**
     * 商户编码（必填）
     */
    private String merchantNo;

    /**
     * 支付渠道（必填）
     */
    private PayChannelEnum payChannel;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型（必填）
     */
    private ActivityTypeEnum activityType;

    /**
     * 构建 QueryChannelApplyResultBO 对象
     * @param reqDTO 请求 DTO
     * @return QueryChannelApplyResultBO 对象
     */
    public static ChannelNoQueryBO build(QueryChannelApplyResultReqDTO reqDTO) {
        return ChannelNoQueryBO.builder()
                .merchantNo(reqDTO.getMerchantNo())
                .payChannel(DocumentedEnum.fromValue(PayChannelEnum.class, reqDTO.getPayChannel()))
                .payScene(DocumentedEnum.fromValue(PaySceneEnum.class, reqDTO.getPayScene()))
                .activityType(DocumentedEnum.fromValue(ActivityTypeEnum.class, reqDTO.getActivityType()))
                .build();
    }

    /**
     * 构建 QueryApplyChannelResultRequestDTO 对象
     * @return QueryApplyChannelResultRequestDTO 对象
     */
    public static QueryChannelInfoRequestDTO buildChannelDTO(ChannelNoQueryBO requestBO) {
        SceneAndActivityTypeBO sceneAndPromotionTypeBO = SceneAndActivityUtils.getSceneAndPromotionTypeBO(
                requestBO.getPayChannel(), requestBO.getPayScene(), requestBO.getActivityType());

        QueryChannelInfoRequestDTO requestDTO = new QueryChannelInfoRequestDTO();
        requestDTO.setChannelIdentifier(requestBO.getMerchantNo());
        requestDTO.setFeeType(sceneAndPromotionTypeBO.getChannelFeeType());
        requestDTO.setPromotionType(sceneAndPromotionTypeBO.getChannelActivityType());
        return requestDTO;
    }
}
