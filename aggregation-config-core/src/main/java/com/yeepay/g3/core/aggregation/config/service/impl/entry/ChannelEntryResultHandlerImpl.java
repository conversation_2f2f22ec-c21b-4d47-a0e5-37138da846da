package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryNotifyBizTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryResultHandler;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @date 2025/6/15 16:35
 */
@Component("channelEntryResultHandlerImpl")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelEntryResultHandlerImpl<C extends BaseEntryApply<ChannelSpecialParams>> implements ChannelEntryResultHandler<C> {

    private final ThirdPartyRecordService thirdPartyRecordService;
    private final ChannelEntryOrderService channelEntryOrderService;
    private final TransactionTemplate transactionTemplate;
    private final EntryResultProducer entryResultProducer;

    @Override
    public void resultHandle(final EntryApplyContext<C> context, final ChannelEntryResult channelEntryResult) {

        buildThirdPartyRecordAndEntryApplyResult(context, channelEntryResult);
        transactionTemplate.execute(transactionStatus -> {
                thirdPartyRecordService.updateByRequestNo(context.getThirdPartyRecord());
                channelEntryOrderService.updateChannelEntryApplyDetailEntryApplyStatus(context.getEntryApply());
                return null;

        });
        if (context.getEntryApply().getEntryStatus().finalStatus()) {
            entryResultProducer.sendEntryNotifyMessage(context.getEntryApply(), EntryNotifyBizTypeEnum.ENTRY);
        }
    }

    private void buildThirdPartyRecordAndEntryApplyResult(final EntryApplyContext<C> context,
                                                          final ChannelEntryResult channelEntryResult) {
        final ThirdPartyRecord thirdPartyRecord = context.getThirdPartyRecord();

        final boolean isSuccess = channelEntryResult.getChannelEntryDetails().stream()
                .anyMatch(detail -> ThirdPartyRecordStatusEnum.SUCCESS == detail.getStatus());

        final boolean isFail = channelEntryResult.getChannelEntryDetails().stream()
                .allMatch(detail -> ThirdPartyRecordStatusEnum.FAIL == detail.getStatus());

        if (isSuccess) {
            final ChannelEntryResult.SuccessBuilder successBuilder = channelEntryResult.getChannelEntryDetails().stream()
                    .filter(detail -> ThirdPartyRecordStatusEnum.SUCCESS == detail.getStatus())
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(ResultCode.PARAM_BIND_ERROR))
                    .getSuccessBuilder();

            thirdPartyRecord.success(channelEntryResult.getInnerChannelOrderNo(), successBuilder.getFinishTime());
            context.getEntryApply().success(channelEntryResult);
        } else if (isFail) {
            final ChannelEntryResult.FailBuilder fail = channelEntryResult.getChannelEntryDetails().stream()
                    .filter(detail -> ThirdPartyRecordStatusEnum.FAIL == detail.getStatus())
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(ResultCode.PARAM_BIND_ERROR))
                    .getFailBuilder();

            thirdPartyRecord.fail(channelEntryResult.getInnerChannelOrderNo(), fail.getChannelResponseCode(),
                    fail.getChannelResponseMessage(), fail.getFinishTime());
            context.getEntryApply().fail(channelEntryResult);
        } else {
            thirdPartyRecord.processing(channelEntryResult.getInnerChannelOrderNo());
            channelEntryResult.getChannelEntryDetails().stream()
                    .filter(channelEntryDetail -> ThirdPartyRecordStatusEnum.PROCESSING == channelEntryDetail.getStatus())
                    .findFirst()
                    .ifPresent(channelEntryDetail -> context.getEntryApply().processing(channelEntryDetail.getProcessBuilder().getSignUrl(), channelEntryDetail.getEntryAuditStatus()));

        }
    }
}

