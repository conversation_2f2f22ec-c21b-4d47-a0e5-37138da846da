package com.yeepay.g3.core.aggregation.config.dao;

import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.AttachConfigStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.SourceEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-13 15:15
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Mapper
public interface AttachResultDao {


    /**
     * 根据ID查询记录
     *
     * @param id 主键ID
     * @return AttachResultEntity 对象
     */
    AttachResultEntity queryById(Long id);

    /**
     * 根据mainMerchantNo查询记录
     *
     * @param mainMerchantNo 主商户号
     * @return AttachResultEntity 对象
     */
    AttachResultEntity queryByMainMerchantNo(@Param("mainMerchantNo") String mainMerchantNo);

    /**
     * 选择性插入记录
     *
     * @param entity 要插入的实体对象
     * @return 插入的记录数
     */
    int insert(AttachResultEntity entity);

    /**
     * 选择性插入记录
     *
     * @param entity 要插入的实体对象
     * @return 插入的记录数
     */
    @Deprecated
    void insertBatch(AttachResultEntity entity);


    /**
     * 根据ID选择性更新记录
     *
     * @param entity 要更新的实体对象
     * @return 更新的记录数
     */
    int updateSelective(AttachResultEntity entity);

    /**
     *  根据唯一主键查询挂靠实体
     * @param attachResultEntity
     * @return com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity
     * <AUTHOR>
     * @date 2024/12/17 17:31
     */
    AttachResultEntity queryByUniqueKey(AttachResultEntity attachResultEntity);

    /**
     * 根据商户编号和来源查询（只查询启用）
     * @param merchantNo 商户编号
     * @param source 来源
     * @return
     */
    List<AttachResultEntity> queryByMerchantNoAndStatus(
            @Param("merchantNo") String merchantNo, @Param("configStatus") AttachConfigStatusEnum configStatus,
            @Param("source") SourceEnum source, @Param("channelType") ChannelTypeEnum channelType,@Param("dataClearStatus") DataCleanStatusEnum dataClearStatus);

    /**
     * 跟新数据清洗状态
     *
     * @param merchantNo
     * @param dataCleanStatus
     * @param oldClearStatus
     */
    int updateClearStatus(@Param("merchantNo") String merchantNo, @Param("dataCleanStatus") DataCleanStatusEnum dataCleanStatus,@Param("oldClearStatus") DataCleanStatusEnum oldClearStatus );


}
