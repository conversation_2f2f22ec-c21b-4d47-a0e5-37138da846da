package com.yeepay.g3.core.aggregation.config.flow.node.decorator;


import com.yeepay.g3.core.aggregation.config.flow.FlowContext;
import com.yeepay.g3.core.aggregation.config.flow.node.Node;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StopWatch;

/**
 * 事务性结算处理器
 */
@Slf4j
@Getter
public class TransactionalNode implements Node {

    /**
     * 内置 handler
     */
    private final Node delegate;

    /**
     * 事务执行器
     */
    private final TransactionTemplate transactionTemplate;

    public TransactionalNode(Node delegate, TransactionTemplate transactionTemplate) {
        this.delegate = delegate;
        this.transactionTemplate = transactionTemplate;
    }

    @Override
    public void execute(FlowContext context) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        transactionTemplate.execute(transactionStatus -> {
            delegate.execute(context);
            return null;
        });
    }

    @Override
    public TransactionalNode combine(Node after) {
        return new TransactionalNode(delegate.combine(after), transactionTemplate);
    }
}
