package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryAuditStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.EntryApplyCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.WxIndirectConfigCmd;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlag;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlagStatus;
import com.yeepay.g3.core.aggregation.config.utils.GMUtils;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/6 13:32
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@SuperBuilder
public class WxIndirectEntryApply extends BaseEntryApply<WechatIndirectSpecialParams> {

    /**
     * 微信商户经营类目
     */
    private final String mcc;

    /**
     * 手续费规则id 微信费率规则号
     */
    private final String feeRuleId;

    /**
     * 挂靠订单
     */
    private AnchoredOrder anchoredOrder;


    private Boolean isNeedTerminalReport;

    private SettleAccountInfo settleAccountInfo;


    public WxIndirectEntryApply(ChannelEntryOrderEntity order, ChannelEntryApplyDetailEntity detailEntity) {
        super(order, detailEntity);
        WechatIndirectSpecialParams specialParams = JsonUtils.fromJson(GMUtils.decrypt(detailEntity.getChannelParams()), WechatIndirectSpecialParams.class);
        this.channelSpecialParams = specialParams;
        if (null != specialParams) {
            this.mcc = specialParams.getMcc();
            this.feeRuleId = specialParams.getFeeRuleId();
            this.settleAccountInfo = specialParams.getSettleAccountInfo();
        } else {
            this.mcc = null;
            this.feeRuleId = null;
            this.settleAccountInfo = null;
        }
        this.anchoredOrder = null;
        this.isNeedTerminalReport = super.getTerminalReportFlag();
    }


    public static WxIndirectEntryApply createWxIndirectEntryApply(final WxIndirectConfigCmd cmd,
                                                                  final String orderNo,
                                                                  final EntryApplyCmd entryApplyCmd) {
        String applyNo = "WXI" + UniqueNoGenerateUtils.getUniqueNo();
        long flag = EntryFlag.NONE.getBit();
        flag = Boolean.TRUE.equals(cmd.getIsNeedTerminalReport()) ? addTerminalReportFlag(flag) : flag;
        flag = Boolean.TRUE.equals(EntryTypeEnum.needAnchored(cmd.getEntryType())) ? addAnchoredApply(flag) : flag;
        flag = cmd.getBackupCount() != null ? addSpecifyBackupFlag(flag) : flag;
        flag = StringUtils.isNotBlank(cmd.getChannelNo()) || StringUtils.isNotBlank(cmd.getChannelIdentifier())
                ? addSpecifyChannelNo(flag) : flag;
        final WxIndirectEntryApplyBuilder<?, ?> builder = WxIndirectEntryApply.builder()
                .merchantInfo(entryApplyCmd.getMerchantInfo())
                .orderNo(orderNo)
                .bizScene(entryApplyCmd.getBizScene())
                .bizApplyNo(entryApplyCmd.getBizApplyNo())
                .applyNo(applyNo)
                .channelNo(cmd.getChannelNo())
                .channelIdentifier(cmd.getChannelIdentifier())
                .subjectInfo(entryApplyCmd.getSubjectInfo())
                .contactInfo(entryApplyCmd.getContactInfo())
                .entryType(cmd.getEntryType())
                .entryStatus(EntryStatusEnum.INIT)
                .payScene(cmd.getPayScene())
                .payChannel(cmd.getPayChannel())
                .activityType(cmd.getActivityInfo().getActivityType())
                .activityInfo(cmd.getActivityInfo())
                .backupCounts(Optional.ofNullable(cmd.getBackupCount()).orElse(0))
                .channelSpecialParams(WechatIndirectSpecialParams.build(cmd))
                .mcc(cmd.getMcc())
                .feeRuleId(cmd.getFeeRuleId())
                .flag(flag)
                .flagStatus(EntryFlagStatus.NONE.getBit())
                .entryAuditStatus(EntryAuditStatus.INIT)
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .extendInfo(cmd.getExtendInfo());
        if (Boolean.TRUE.equals(EntryTypeEnum.needAnchored(cmd.getEntryType()))) {
            AnchoredOrder anchoredOrder = AnchoredOrder.createAnchoredOrder(cmd.getAnchoredInfo(), applyNo);
            builder.anchoredOrder(anchoredOrder);
        }
        return builder.build();
    }

}
