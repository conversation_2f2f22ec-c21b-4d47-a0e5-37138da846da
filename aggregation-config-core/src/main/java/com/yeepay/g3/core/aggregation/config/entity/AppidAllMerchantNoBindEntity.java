package com.yeepay.g3.core.aggregation.config.entity;

import com.yeepay.g3.core.aggregation.config.enums.BindAllStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.WaringStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @className AppipAllMerchantNoBindEntity
 * <AUTHOR>
 * @date 2024/12/17 14:40
 * @version 1.0
 */
@Data
public class AppidAllMerchantNoBindEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自动生成的唯一标识符
     */
    private Long id;

    /**
     * APPID
     */
    private String appid;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     *  一级商户编号
     */
    private String firstMerchantNo;

    /**
     * 二级商户编号
     */
    private String secondMerchantNo;

    /**
     * 三级商户编号
     */
    private String threeMerchantNo;

    /**
     * 挂靠商编
     */
    private String attachMerchantNo;

    /**
     * 行业线业绩属性
     */
    private String merchantTag;

    /**
     * 通知类型（新增/修改）
     */
    private String notifyType;

    /**
     * 绑定状态
     */
    private BindAllStatusEnum bindStatus;

    /**
     *  邮箱等级
     */
    private  int emailLevel;

    /**
     *  url 等级
     */
    private  int urlLevel;

    /**
     * 告警状态
     */
    private WaringStatusEnum waringStatus;

    /**
     * 邮件通知状态
     */
    private String emailNotifyStatus;

    /**
     * HTTP通知状态
     */
    private String urlNotifyStatus;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date updateTime;

}
