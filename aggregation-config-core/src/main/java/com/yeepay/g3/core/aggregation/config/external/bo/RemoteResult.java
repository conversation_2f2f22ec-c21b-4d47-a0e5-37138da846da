package com.yeepay.g3.core.aggregation.config.external.bo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import lombok.Data;

import java.io.Serializable;

@Data
public class RemoteResult<T> implements Serializable {
    private T data;
    /**
     * 本系统的错误信息
     */
    private String message;
    /**
     * 本系统的码
     */
    private String code;

    /**
     * 远程调用返回的原码
     */
    private String channelCode;

    /**
     * 远程调用返回的原错误信息
     */
    private String channelMessage;

    private static final String SUCCESS_CODE = "0000";

    public RemoteResult() {
    }

    public boolean isSuccess() {
        return getCode().equals(SUCCESS_CODE);
    }

    public static <T> RemoteResult<T> success(String channelCode, String channelMessage) {
        return success(null, channelCode, channelMessage);
    }

    public static <T> RemoteResult<T> success(T data, String channelCode, String channelMessage) {
        RemoteResult<T> result = new RemoteResult<>();
        result.setData(data);
        result.setCode(SUCCESS_CODE);
        result.setMessage("success");
        result.setChannelCode(channelCode);
        result.setChannelMessage(channelMessage);
        return result;
    }

    /**
     * 想要一下失败的请求单号的场景
     *
     * @param data
     * @param channelCode
     * @param channelMessage
     * @param <T>
     * @return
     */
    public static <T> RemoteResult<T> fail(T data, String channelCode, String channelMessage) {
        RemoteResult<T> result = new RemoteResult<>();
        result.setData(data);
        result.setCode(channelCode);
        result.setMessage(channelMessage);
        result.setChannelCode(channelCode);
        result.setChannelMessage(channelMessage);
        return result;
    }

    public static <T> RemoteResult<T> fail(String channelCode, String channelMessage) {
        RemoteResult<T> result = new RemoteResult<>();
        result.setCode(channelCode);
        result.setMessage(channelMessage);
        result.setChannelCode(channelCode);
        result.setChannelMessage(channelMessage);
        return result;
    }

    public static <T> RemoteResult<T> fail(ResultCode errorCodeEnum) {
        RemoteResult<T> result = new RemoteResult<>();
        result.setCode(errorCodeEnum.getCode());
        result.setMessage(errorCodeEnum.getMessage());
        return result;
    }

    public static <T> RemoteResult<T> fail(ResultCode errorCodeEnum, String channelCode, String channelMessage) {
        RemoteResult<T> result = new RemoteResult<>();
        result.setCode(errorCodeEnum.getCode());
        result.setMessage(errorCodeEnum.getMessage());
        result.setChannelCode(channelCode);
        result.setChannelMessage(channelMessage);
        return result;
    }

    public static <T> RemoteResult<T> fail(ResultCode errorCodeEnum, String message) {
        RemoteResult<T> result = new RemoteResult<>();
        result.setCode(errorCodeEnum.getCode());
        result.setMessage(message);
        return result;
    }

    public static <T> RemoteResult<T> fail(ResultCode errorCodeEnum, String message, String channelCode, String channelMessage) {
        RemoteResult<T> result = new RemoteResult<>();
        result.setCode(errorCodeEnum.getCode());
        result.setMessage(message);
        result.setChannelCode(channelCode);
        result.setChannelMessage(channelMessage);
        return result;
    }
}
