package com.yeepay.g3.core.aggregation.config.factory.anchored.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.external.ChannelAnchoredExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.AggSameSubjectAnchoredReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.AggSameReportRespBO;
import com.yeepay.g3.core.aggregation.config.utils.ConfigUtil;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同主体跨SAAS体系
 */
@Component
public class DesignateSAASSameSubjectAnchoredApplyHandler extends BaseAnchoredApplyHandler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ChannelAnchoredExternal channelAnchoredExternal;

    @Override
    public boolean isSupport(AnchoredType anchoredType, Boolean anchoredDimension) {
        /*同主体 跨SAAS*/
        return AnchoredType.SAME_SUBJECT == anchoredType && anchoredDimension;
    }

    @Override
    public void validateAnchoredParams(AnchoredApply anchoredApply) {
        /*获取跨SAAS商编*/
        Map<String, String> map = ConfigUtil.getSameSubjectDesignateSaasScopeConfig();
        String sameSubjectDesignateSaasScope = null != map.get(anchoredApply.getParentMerchantNo()) ? map.get(anchoredApply.getParentMerchantNo()) : map.get(anchoredApply.getTopMerchantNo());
        Assert.isTrue(StringUtils.isNotBlank(sameSubjectDesignateSaasScope), ResultCode.PARAM_VALID_ERROR, "指定商编跨SAAS体系挂靠，未配置");
        anchoredApply.buildSameSubjectDesignateSaasRelation(sameSubjectDesignateSaasScope);
        SceneAndActivityUtils.getSceneAndPromotionTypeBO(anchoredApply.getChannelType(), anchoredApply.getPayScene(), anchoredApply.getActivityType());
    }

    @Override
    public RemoteResult<List<AggAnchoredMerchantInfo>> aggregationAnchoredApply(AnchoredApply anchoredApply) {
        logger.info("[同主体挂靠][跨SAAS] 处理开始 anchoredApplyBO={}", JsonUtils.toJSONString(anchoredApply));
        /*请求指定商编挂靠*/
        AggSameSubjectAnchoredReqBO reqBO = assembleAggSameSubjectAnchoredReqBO(anchoredApply);
        RemoteResult<List<AggSameReportRespBO>> result = channelAnchoredExternal.aggSameSubjectAnchored(reqBO);
        if (result.isSuccess()) {
            List<AggAnchoredMerchantInfo> anchoredMerchantInfoList = result.getData().stream()
                    .map(AggAnchoredMerchantInfo::covert).collect(Collectors.toList());
            return RemoteResult.success(anchoredMerchantInfoList, result.getChannelCode(), result.getChannelMessage());
        } else {
            return RemoteResult.fail(result.getCode(), result.getMessage());
        }
    }

    private AggSameSubjectAnchoredReqBO assembleAggSameSubjectAnchoredReqBO(AnchoredApply anchoredApply) {
        AggSameSubjectAnchoredReqBO reqBO = new AggSameSubjectAnchoredReqBO();
        reqBO.setMerchantNo(anchoredApply.getMerchantNo());
        reqBO.setPayScene(anchoredApply.getPayScene().getDocument());
        reqBO.setChannelType(anchoredApply.getChannelType().getDocument());
        reqBO.setActivityType(null == anchoredApply.getActivityType() ? null : anchoredApply.getActivityType().getDocument());
        reqBO.setSameSubjectSameScopeMerchantNo(anchoredApply.getRelationMerchantNo());
        return reqBO;
    }
}
