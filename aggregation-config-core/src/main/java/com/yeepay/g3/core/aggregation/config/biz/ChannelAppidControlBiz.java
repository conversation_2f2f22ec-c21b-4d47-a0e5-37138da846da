package com.yeepay.g3.core.aggregation.config.biz;

import com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppidControlEvent;

import java.text.ParseException;

/**
 * @Description:
 * @ClassName: ChannelAppIdControlBiz
 * @Author: cong.huo
 * @Date: 2024/12/18 11:36   // 时间
 * @Version: 1.0
 */
public interface ChannelAppidControlBiz {
    /**
     * appid管控信息同步
     * @param message
     * @throws ParseException 如果解析日期时发生错误
     * @return void
     * <AUTHOR>
     * @date 2024/12/18 16:23
     */
    AppidControlEntity handle(ChannelAppidControlEvent message) throws ParseException;

    /**
     * 异步通知
     * @param appidControlEntity
     * @return void
     * <AUTHOR>
     * @date 2024/12/18 17:14
     */
    void notify(AppidControlEntity appidControlEntity);

    /**
     * 根据AppID 补偿
     *
     * @param appId        appId
     */
    void compensateNotify(String appId);
}
