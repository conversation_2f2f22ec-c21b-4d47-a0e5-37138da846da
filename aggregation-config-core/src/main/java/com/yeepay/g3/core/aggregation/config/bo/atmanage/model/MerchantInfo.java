package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.MerchantRoleEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.enumerate.SceneType;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.MerchantInfoDTO;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/5/23 14:33
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class MerchantInfo {

    /**
     * 商户编号
     */
    private final String merchantNo;

    /**
     * 父商编
     */
    private final String parentMerchantNo;

    /**
     * 顶级商户编号
     */
    private final String topLevelMerchantNo;

    /**
     * 商户名称
     * 必须与企业证照上的名称一致；
     * 个体工商户的营业执照如没有名称，名称为“*”或空，则商户名称应填 “个体户XXX”（XXX为营业执照上经营者姓名），如“个体户张三”，汉字以2个字符计算 ；
     */
    private final String merchantName;
    /**
     * 商户简称
     * 企业商户必填，需与商户注册名称全称相关；用于微信、支付宝入驻时上传；
     * 注意：微信只支持20个字节，10个汉字；
     */
    private final String shortName;

    /**
     * 是否直营商
     */
    private final Boolean isDirectMerchant;

    /**
     * 行业线
     */
    private final String industryLine;

    /**
     * 商户角色
     */
    private final MerchantRoleEnum role;

    /**
     * 是否退出区域
     */
    private final Boolean isExitArea;

    private final SceneType sceneType;


    public static MerchantInfo build(final MerchantInfoDTO merchantInfo) {
        if (Boolean.FALSE.equals(merchantInfo.getIsDirectMerchant())) {
            Assert.notNull(merchantInfo.getParentMerchantNo(), ResultCode.PARAM_VALID_ERROR, "父商编不能为空");
            Assert.notNull(merchantInfo.getTopLevelMerchantNo(), ResultCode.PARAM_VALID_ERROR, "顶级商编不能为空");
        }
        return MerchantInfo.builder()
                .merchantNo(merchantInfo.getMerchantNo())
                .parentMerchantNo(merchantInfo.getParentMerchantNo())
                .topLevelMerchantNo(merchantInfo.getTopLevelMerchantNo())
                .merchantName(merchantInfo.getMerchantName())
                .shortName(merchantInfo.getShortName())
                .isDirectMerchant(merchantInfo.getIsDirectMerchant())
                .industryLine(merchantInfo.getIndustryLine())
                .role(DocumentedEnum.fromValue(MerchantRoleEnum.class, merchantInfo.getRole()))
                .isExitArea(merchantInfo.getIsExitArea())
                .sceneType(DocumentedEnum.fromValueOfNullable(SceneType.class, merchantInfo.getSceneType()))
                .build();
    }

    public static MerchantInfoDTO convert(final MerchantInfo merchantInfo) {
        MerchantInfoDTO merchantInfoDTO = new MerchantInfoDTO();
        merchantInfoDTO.setMerchantNo(merchantInfo.getMerchantNo());
        merchantInfoDTO.setParentMerchantNo(merchantInfo.getParentMerchantNo());
        merchantInfoDTO.setTopLevelMerchantNo(merchantInfo.getTopLevelMerchantNo());
        merchantInfoDTO.setMerchantName(merchantInfo.getMerchantName());
        merchantInfoDTO.setShortName(merchantInfo.getShortName());
        merchantInfoDTO.setIsDirectMerchant(merchantInfo.getIsDirectMerchant());
        merchantInfoDTO.setIndustryLine(merchantInfo.getIndustryLine());
        merchantInfoDTO.setRole(merchantInfo.getRole().getDocument());
        return merchantInfoDTO;
    }
}
