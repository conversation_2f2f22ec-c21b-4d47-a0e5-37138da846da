package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.g3.core.aggregation.config.dao.MerchantAuthApplyDao;
import com.yeepay.g3.core.aggregation.config.dao.ThirdPartyRecordDao;
import com.yeepay.g3.core.aggregation.config.service.MerchantAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: 商户认证服务ServiceImpl
 * <AUTHOR>
 * @since 2025/5/26:11:29
 * Company: 易宝支付(YeePay)
 */
@Service("merchantAuthService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantAuthServiceImpl implements MerchantAuthService {

    private final ThirdPartyRecordDao thirdPartyRecordDao;

    private final MerchantAuthApplyDao authApplyDao;
//
//
//    @Override
//    public MerchantAuthApplyEntity queryAuthRecord(AuthQueryBO authQueryBO) {
//        if (CheckUtils.isEmpty(authQueryBO.getChannel()) || CheckUtils.isEmpty(authQueryBO.getPayScene())
//                || (CheckUtils.isEmpty(authQueryBO.getChannelIdentifier()) && CheckUtils.isEmpty(authQueryBO.getChannelNo()))
//                || (CheckUtils.isEmpty(authQueryBO.getBizApplyNo()) && CheckUtils.isEmpty(authQueryBO.getChannelApplyNo()))) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "查询认证记录参数不完整");
//        }
//        Weekend<MerchantAuthApplyEntity> weekend = Weekend.of(MerchantAuthApplyEntity.class);
//        WeekendCriteria<MerchantAuthApplyEntity, Object> criteria = weekend.weekendCriteria();
//        criteria.andEqualTo(MerchantAuthApplyEntity::getInstitutionCode, authQueryBO.getChannel().getDocument())
//                .andEqualTo(MerchantAuthApplyEntity::getPayScene, authQueryBO.getPayScene().getDocument())
//                .andEqualTo(MerchantAuthApplyEntity::getChannelIdentifier, authQueryBO.getChannelIdentifier())
//                .andEqualTo(MerchantAuthApplyEntity::getChannelNo, authQueryBO.getChannelNo())
//                .andEqualTo(MerchantAuthApplyEntity::getBizApplyNo, authQueryBO.getBizApplyNo())
//                .andEqualTo(MerchantAuthApplyEntity::getChannelApplyNo, authQueryBO.getChannelApplyNo());
//        return authApplyDao.selectOneByExample(weekend);
//    }
//
//    @Override
//    public String saveAuthRecord(AuthApplyBO authApplyBO) {
//        MerchantAuthApplyEntity authApplyEntity = MerchantAuthApplyEntity.build(authApplyBO);
//        // 保存商户认证申请记录
//        authApplyDao.insertSelective(authApplyEntity);
//        return authApplyEntity.getApplyNo();
//    }
//
//    /**
//     * 更新商户实名认证申请结果
//     * @param requestNo 请求号
//     * @param result 认证申请结果
//     */
//    @Override
//    public void updateAuthApplyResult(String requestNo, AuthApplyResultBO result) {
//        AuthStatusEnum authStatusEnum;
//        if (CheckUtils.isEmpty(result.getChannelApplyNo())) {
//            // 如果渠道返回的申请号为空，则认为是审核取消（更新为：作废）
//            authStatusEnum = AuthStatusEnum.AUDIT_CANCELED;
//        } else {
//            authStatusEnum = AuthStatusEnum.PROCESSING;
//        }
//
//        ThirdPartyRecordEntity thirdPartyRecordEntity = ThirdPartyRecordConvert.convertUpdate(requestNo,
//                result.getReturnCode(), result.getReturnMessage(),
//                authStatusEnum.getThirdRecordStatus());
//
//        MerchantAuthApplyEntity authApplyEntity = MerchantAuthApplyEntity.buildUpdateChannelApplyNo(requestNo,
//                result.getChannelApplyNo(),
//                authStatusEnum);
//
//        // 更新第三方记录
//        this.updateApplyByRequestNo(thirdPartyRecordEntity, authApplyEntity, requestNo);
//    }
//
//    /**
//     * 取消商户认证申请
//     * @param requestNo 请求号
//     */
//    @Override
//    public void cancelAuthApply(String requestNo) {
//        // 已作废
//        AuthResultBO authResultBO = AuthResultBO.builder()
//                .authStatusEnum(AuthStatusEnum.AUDIT_CANCELED)
//                .qrcodeData(null)
//                .rejectReasonList(null)
//                .build();
//        this.updateAuthResult(requestNo, authResultBO);
//    }
//
//    /**
//     * 更新商户认证结果
//     * @param requestNo 请求号
//     * @param authResultBO 认证结果
//     */
//    @Override
//    public void updateAuthResult(String requestNo, AuthResultBO authResultBO) {
//        // 更新第三方记录状态
//        ThirdPartyRecordEntity thirdPartyRecordEntity = ThirdPartyRecordConvert.convertUpdate(requestNo,
//                null, null,
//                authResultBO.getAuthStatusEnum().getThirdRecordStatus());
//
//        // 如果认证状态，二维码数据、拒绝原因
//        MerchantAuthApplyEntity authApplyEntity = MerchantAuthApplyEntity.buildUpdate(requestNo, authResultBO);
//
//        // 更新第三方记录
//        this.updateApplyByRequestNo(thirdPartyRecordEntity, authApplyEntity, requestNo);
//    }
//
//    @Override
//    public List<MerchantAuthApplyEntity> queryUnfinishedAuthApply(Date startTime, Date endTime) {
//        if (CheckUtils.isEmpty(startTime) || CheckUtils.isEmpty(endTime)) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "查询未完成认证申请记录参数不完整");
//        }
//        List<String> statusList = Arrays.asList(
//                AuthStatusEnum.PROCESSING.getDocument(),
//                AuthStatusEnum.WAIT_CONTACT_CONFIRM.getDocument(),
//                AuthStatusEnum.WAIT_LEGAL_CONFIRM.getDocument(),
//                AuthStatusEnum.WAIT_ACCOUNT_VERIFY.getDocument(),
//                AuthStatusEnum.AUDIT_REJECT.getDocument()
//        );
//        Weekend<MerchantAuthApplyEntity> weekend = Weekend.of(MerchantAuthApplyEntity.class);
//        WeekendCriteria<MerchantAuthApplyEntity, Object> criteria = weekend.weekendCriteria();
//        criteria.andIn(MerchantAuthApplyEntity::getStatus, statusList)
//                .andLessThanOrEqualTo(MerchantAuthApplyEntity::getUpdateDt, startTime)
//                .andLessThan(MerchantAuthApplyEntity::getUpdateDt, endTime);
//        weekend.orderBy(Const.UPDATE_DT).asc();
//        return authApplyDao.selectByExample(criteria);
//    }
//
//    @Override
//    public MerchantAuthApplyEntity queryAuthApplyByApplyNo(String applyNo) {
//        if (CheckUtils.isEmpty(applyNo)) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "查询认证申请记录参数不完整");
//        }
//        Weekend<MerchantAuthApplyEntity> weekend = Weekend.of(MerchantAuthApplyEntity.class);
//        WeekendCriteria<MerchantAuthApplyEntity, Object> criteria = weekend.weekendCriteria();
//        criteria.andEqualTo(MerchantAuthApplyEntity::getApplyNo, applyNo);
//        return authApplyDao.selectOneByExample(weekend);
//    }
//
//    /**
//     * 更新第三方记录和商户认证申请记录
//     * @param thirdPartyRecordEntity 第三方记录实体
//     * @param authApplyEntity 商户认证申请实体
//     * @param requestNo 请求号
//     */
//    private void updateApplyByRequestNo(ThirdPartyRecordEntity thirdPartyRecordEntity,
//                                        MerchantAuthApplyEntity authApplyEntity,
//                                        String requestNo) {
//        Weekend<ThirdPartyRecordEntity> thirdPartyRecordEntityWeekend = Weekend.of(ThirdPartyRecordEntity.class);
//        WeekendCriteria<ThirdPartyRecordEntity, Object> thirdPartyCriteria = thirdPartyRecordEntityWeekend.weekendCriteria();
//        thirdPartyCriteria.andEqualTo(ThirdPartyRecordEntity::getRequestNo, requestNo);
//        thirdPartyRecordDao.updateByExampleSelective(thirdPartyRecordEntity, thirdPartyRecordEntityWeekend);
//
//        Weekend<MerchantAuthApplyEntity> merchantAuthApplyEntityWeekend = Weekend.of(MerchantAuthApplyEntity.class);
//        WeekendCriteria<MerchantAuthApplyEntity, Object> authApplyCriteria = merchantAuthApplyEntityWeekend.weekendCriteria();
//        authApplyCriteria.andEqualTo(MerchantAuthApplyEntity::getRequestNo, requestNo);
//        authApplyDao.updateByExampleSelective(authApplyEntity, merchantAuthApplyEntityWeekend);
//    }

}
