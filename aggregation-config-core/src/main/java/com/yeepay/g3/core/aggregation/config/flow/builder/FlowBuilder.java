package com.yeepay.g3.core.aggregation.config.flow.builder;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.core.aggregation.config.flow.ContextMatcher;
import com.yeepay.g3.core.aggregation.config.flow.Flow;
import com.yeepay.g3.core.aggregation.config.flow.node.decorator.AsyncNode;
import com.yeepay.g3.core.aggregation.config.flow.node.decorator.ConditionalNode;
import com.yeepay.g3.core.aggregation.config.flow.node.impl.MarkedNode;
import com.yeepay.g3.core.aggregation.config.flow.node.Node;
import com.yeepay.g3.core.aggregation.config.flow.enums.Mark;
import com.yeepay.g3.core.aggregation.config.flow.node.decorator.TransactionalNode;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2025/5/26 21:50
 */
public class FlowBuilder {

    private final Deque<Node> stack = new ArrayDeque<>();

    public FlowBuilder addHandler(Node handler) {
        Assert.notNull(handler,ResultCode.PARAM_VALID_ERROR,"handler can not be null!");
        stack.push(handler);
        return this;
    }

    public FlowBuilder startTransaction(TransactionTemplate transactionTemplate) {
        Assert.notNull(transactionTemplate,ResultCode.PARAM_VALID_ERROR, "transactionTemplate can not be null!");
        stack.push(MarkedNode.startTransaction(transactionTemplate));
        return this;
    }

    public FlowBuilder endTransaction() {
        TransactionalNode handler = aggregateHandlersUntilMark(
                Mark.TRANSACTION_START,
                h -> {
                    Assert.notNull(stack.peek(), ResultCode.PARAM_VALID_ERROR, "transactionTemplate can not be null!");
                    return new TransactionalNode(h, ((MarkedNode) stack.peek()).getTransactionTemplate());
                }
        );
        stack.push(handler);
        return this;
    }

    public FlowBuilder startAsync(Executor executor) {
        Assert.notNull(executor, ResultCode.PARAM_VALID_ERROR,"executor can not be null!");
        stack.push(MarkedNode.startAsync(executor));
        return this;
    }

    public FlowBuilder endAsync() {
        AsyncNode handler = aggregateHandlersUntilMark(
                Mark.ASYNC_START,
                h -> new AsyncNode(h, ((MarkedNode) stack.peek()).getExecutor())
        );
        stack.push(handler);
        return this;
    }

    public FlowBuilder startCondition(ContextMatcher matcher) {
        Assert.notNull(matcher, ResultCode.PARAM_VALID_ERROR,"matcher can not be null!");
        stack.push(MarkedNode.startCondition(matcher));
        return this;
    }

    public FlowBuilder endCondition() {
        ConditionalNode handler = aggregateHandlersUntilMark(
                Mark.CONDITION_START,
                h -> new ConditionalNode(h, ((MarkedNode) stack.peek()).getMatcher())
        );
        stack.push(handler);
        return this;
    }

    private <T extends Node> T aggregateHandlersUntilMark(
            Mark targetMark,
            Function<Node, T> handlerFactory) {
        LinkedList<Node> reverse = new LinkedList<>();

        while (!stack.isEmpty()) {
            Node handler = stack.peek();
            if (handler instanceof MarkedNode) {
                MarkedNode markedHandler = (MarkedNode) handler;

                if (markedHandler.getMark() == targetMark) {
                    stack.pop(); // 弹出标记
                    return handlerFactory.apply(
                            reverse.stream().reduce(Node.EMPTY, Node::combine)
                    );
                }
            }
            reverse.addFirst(stack.pop());
        }

        throw new IllegalArgumentException("Missing start mark for " + targetMark);
    }

    public Flow build() {
        LinkedList<Node> result = new LinkedList<>();
        while (!stack.isEmpty()) {
            result.addFirst(stack.pop());
        }
        return new Flow(result);
    }
}


