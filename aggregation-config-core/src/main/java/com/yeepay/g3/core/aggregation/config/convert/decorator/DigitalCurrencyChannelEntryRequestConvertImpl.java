package com.yeepay.g3.core.aggregation.config.convert.decorator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.DigitalCurrencyEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryRequestConvert;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/18 19:06
 */
@Service
@Slf4j
public class DigitalCurrencyChannelEntryRequestConvertImpl implements ChannelEntryRequestConvert<DigitalCurrencyEntryApply> {
    private final ChannelEntryRequestConvert<DigitalCurrencyEntryApply> channelEntryRequestConvert;

    public DigitalCurrencyChannelEntryRequestConvertImpl(@Qualifier("baseChannelEntryRequestConvert") final ChannelEntryRequestConvert<DigitalCurrencyEntryApply> channelEntryRequestConvert) {
        this.channelEntryRequestConvert = channelEntryRequestConvert;
    }

    @Override
    public OpenPayAsyncReportRequestDTO buildChannelRequest(final EntryApplyContext<DigitalCurrencyEntryApply> context) {
        final OpenPayAsyncReportRequestDTO request = channelEntryRequestConvert.buildChannelRequest(context);
        final SettleAccountInfo settleAccountInfo = context.getEntryApply().getSettleAccountInfo();
        // 结算账户
        request.setMerAccNo(settleAccountInfo.getCardNo());
        request.setMerAccName(settleAccountInfo.getCardName());
        request.setMerAccBank(settleAccountInfo.getBankName());
        request.setMerAccBankName(settleAccountInfo.getBankBranchName());
        request.setMerAccBankCode(settleAccountInfo.getBankCode());
        request.setMerAccBankNo(settleAccountInfo.getBranchCode());
        request.setReportBankCode(context.getEntryApply().getBankCode());

        request.setExternalInfo(JsonUtils.fromJson(context.getEntryApply().getExtendInfo(), new TypeReference<Map<String, String>>() {
        }));
        return request;
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {

        return PayChannelEnum.BANK == payChannel
                && PaySceneEnum.DIGITAL_CURRENCY == payScene;
    }
}
