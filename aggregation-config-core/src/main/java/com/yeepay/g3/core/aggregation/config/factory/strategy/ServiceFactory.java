package com.yeepay.g3.core.aggregation.config.factory.strategy;


import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;

/**
 *
 */

public class ServiceFactory {

    private static final Logger logger = LoggerFactory.getLogger(ServiceFactory.class);

    public static <T> T getService(String type, Class<T> classType) {
        if (StringUtils.isBlank(type) || classType == null) {
            logger.error("获取service失败,参数校验未通过type={},classType={}", type, classType);
            return null;
        }
        Iterator it;
        try {
            Map<String, T> services = BeanHoldFactory.getApplicationContext().getBeansOfType(classType);
            if (services != null) {
                it = services.values().iterator();

                while (it.hasNext()) {
                    T s = (T) it.next();
                    if (s instanceof ExtendService) {
                        ExtendService extendService = (ExtendService) s;
                        if (type.equals(extendService.getType())) {
                            return s;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("通过getBeansOfType获取Service异常,type=" + type + ",classType=" + classType, e);
            throw new AggConfigException(ErrorCodeEnum.SYSTEM_ERROR, "通过getBeansOfType获取Service异常");
        }
        logger.error("通过getBeansOfType获取Service失败,type={},classType={}", type, classType);
        return null;
    }
}
