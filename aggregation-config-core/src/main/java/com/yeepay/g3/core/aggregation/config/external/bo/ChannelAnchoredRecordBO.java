package com.yeepay.g3.core.aggregation.config.external.bo;

import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayReportRelationRecordDTO;
import lombok.Data;

import java.util.Date;

/**
 * 通道  挂靠记录
 *
 * @author: Mr.yin
 * @date: 2025/6/4  20:19
 */
@Data
public class ChannelAnchoredRecordBO {

    /**
     * 商编
     */
    private String merchantNo;

    /**
     * 挂靠商编
     */
    private String anchoredMerchantNo;

    /**
     * 挂靠机构商编
     */
    private String anchoredInstitutionMerchantNo;
    /**
     * 挂靠 商编 渠道号
     */
    private String anchoredMerchantChannelNo;

    /**
     * 挂靠费率场景
     */
    private String feeType;

    /**
     * 挂靠时间
     */
    private Date successTime;

    public static ChannelAnchoredRecordBO covert(OpenPayReportRelationRecordDTO channelAnchoredRecord) {
        ChannelAnchoredRecordBO channelAnchoredRecordBO = new ChannelAnchoredRecordBO();
        channelAnchoredRecordBO.setMerchantNo(channelAnchoredRecord.getRequireMerchantNo());
        channelAnchoredRecordBO.setAnchoredMerchantNo(channelAnchoredRecord.getMaskMerchantNo());
        channelAnchoredRecordBO.setAnchoredInstitutionMerchantNo(channelAnchoredRecord.getMaskReportMerchantNo());
        channelAnchoredRecordBO.setAnchoredMerchantChannelNo(channelAnchoredRecord.getMaskChannelNo());
        channelAnchoredRecordBO.setFeeType(channelAnchoredRecord.getMaskReportFee());
        channelAnchoredRecordBO.setSuccessTime(channelAnchoredRecord.getSuccessTime());
        return channelAnchoredRecordBO;
    }

}
