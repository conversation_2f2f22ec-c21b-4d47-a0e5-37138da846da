package com.yeepay.g3.core.aggregation.config.exception;

import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.text.MessageFormat;

/**
 * @Title : com.yeepay.g3.core.aggregation.pay.exception
 * @Description :
 * @Company : 易宝支付(Yeepay)
 * <AUTHOR> jiafu.wu
 * @Since: 2020/5/12
 * @Version : 1.0.0
 */
@Setter
@Getter
public class AggConfigException extends RuntimeException implements Serializable {
    private static final long serialVersionUID = 1L;
    private static final int MESSAGE_LENGTH = 64;
    private String code;
    private String message;
    private ErrorCodeEnum errorCodeEnum;

    /**
     * 最简单的构造器，只支持错误码
     *
     * @param errorCodeEnum
     */
    public AggConfigException(ErrorCodeEnum errorCodeEnum) {
        this(errorCodeEnum, (String) null);
    }

    /**
     * 支持追加message
     * @param errorCodeEnum
     * @param message
     */
    public AggConfigException(ErrorCodeEnum errorCodeEnum, String message) {
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getDesc();
        this.errorCodeEnum = errorCodeEnum;

        if(StringUtils.isNotBlank(message)){
            this.message  +=  message.length() > MESSAGE_LENGTH ? ":" + message.substring(0, MESSAGE_LENGTH) : ":" + message;
        }
    }

    /**
     * 拼接下层错误码和错误信息
     * @param errorCodeEnum
     * @param bizCode
     * @param bizMessage
     */
    public AggConfigException(ErrorCodeEnum errorCodeEnum, String bizCode, String bizMessage) {
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getDesc() + ":" + bizCode + ":" + bizMessage;
        this.errorCodeEnum = errorCodeEnum;
    }

    /**
     * 拼接下层错误码和错误信息
     * @param bizCode
     * @param bizMessage
     */
    public AggConfigException(String bizCode, String bizMessage) {
        this.code = bizCode;
        this.message = bizMessage;
    }


    /**
     * 追加数组类入参
     * @param errorCodeEnum
     * @param msgParams
     */
    public AggConfigException(ErrorCodeEnum errorCodeEnum, String[] msgParams) {
        this(errorCodeEnum, "", msgParams);
    }

    /**
     * 追加message + 数组类入参
     * @param errorCodeEnum
     * @param message
     * @param msgParams
     */
    public AggConfigException(ErrorCodeEnum errorCodeEnum, String message, String[] msgParams) {
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getDesc();
        try {
            // 从错误码系统取得msg
            if (StringUtils.isNotBlank(message)) {
                if (msgParams != null && msgParams.length > 0) {
                    MessageFormat msf = new MessageFormat(message);
                    this.message += ":" + msf.format(msgParams);
                } else{
                    this.message += ":" + message;
                }
            }
        } catch (Exception ex) {
            // 错误码系统已经做了异常处理了，如果仍然有异常，上层业务是无法处理的，此处将msg默认设置为code
            this.message = code;
        }
    }
}
