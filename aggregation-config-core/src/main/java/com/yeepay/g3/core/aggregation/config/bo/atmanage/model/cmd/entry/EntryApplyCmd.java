package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ContactInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.MerchantInfo;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.*;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/23 14:22
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
public class EntryApplyCmd {

    /**
     * 业务场景
     */
    private final BizSceneEnum bizScene;

    /**
     * 业务申请单号
     */
    private final String bizApplyNo;

    /**
     * 商户信息
     */
    private final MerchantInfo merchantInfo;

    /**
     * 主体信息
     */
    private final SubjectInfo subjectInfo;


    /**
     * 联系人信息
     */
    private final ContactInfo contactInfo;

    /**
     * 扩展信息
     */
    private final String extendInfo;

    /**
     * 微信直连配置
     */
    private final WxDirectConfigCmd wxDirectConfig;

    /**
     * 微信直连B2B配置
     */
    private final WxDirectB2BConfigCmd wxDirectB2BConfig;

    /**
     * 微信间连配置
     */
    private final List<WxIndirectConfigCmd> wxIndirectConfigs;

    /**
     * 支付宝间连配置
     */
    private final List<AlipayIndirectConfigCmd> alipayIndirectConfigs;

    /**
     * 数币配置
     */
    private final List<DigitalCurrencyConfigCmd> digitalCurrencyConfigs;


    public static EntryApplyCmd create(EntryApplyRequest entryApplyRequest) {

        final boolean wxIndirectConf = CollectionUtils.isNotEmpty(entryApplyRequest.getWxIndirectConfigs());
        final boolean aliIndirectConf = CollectionUtils.isNotEmpty(entryApplyRequest.getAliIndirectConfigs());
        final boolean wxDirectCof = entryApplyRequest.getWxDirectConfig() != null;
        final boolean digitalCurrencyConf = CollectionUtils.isNotEmpty(entryApplyRequest.getDigitalCurrencyConfigs());
        final boolean wxDirectB2BCof = entryApplyRequest.getWxDirectB2BConfig() != null;


        Assert.isTrue(wxIndirectConf || aliIndirectConf || wxDirectCof || digitalCurrencyConf || wxDirectB2BCof,
                ResultCode.PARAM_VALID_ERROR, "请至少填写一种支付配置");
        final BizSceneEnum bizScene = DocumentedEnum.fromValue(BizSceneEnum.class, entryApplyRequest.getBizScene());
        final MerchantInfo merchantInfo = MerchantInfo.build(entryApplyRequest.getMerchantInfo());

        return EntryApplyCmd.builder()
                .bizScene(bizScene)
                .bizApplyNo(entryApplyRequest.getBizApplyNo())
                .merchantInfo(merchantInfo)
                .subjectInfo(SubjectInfo.build(entryApplyRequest.getSubjectInfo()))
                .contactInfo(ContactInfo.build(entryApplyRequest.getContactInfo()))
                .extendInfo(entryApplyRequest.getExtendInfo())
                .wxIndirectConfigs(buildWxIndirectConfigs(entryApplyRequest.getWxIndirectConfigs(),
                        entryApplyRequest.getMerchantInfo()))
                .alipayIndirectConfigs(buildAliIndirectConfigs(entryApplyRequest.getAliIndirectConfigs(),
                        entryApplyRequest.getMerchantInfo()))
                .digitalCurrencyConfigs(buildDigitalCurrencyConfigs(entryApplyRequest.getDigitalCurrencyConfigs()))
                .wxDirectConfig(wxDirectCof ? WxDirectConfigCmd.build(entryApplyRequest.getWxDirectConfig()) : null)
                .wxDirectB2BConfig(wxDirectB2BCof ? WxDirectB2BConfigCmd.build(entryApplyRequest.getWxDirectB2BConfig()) : null)
                .build();
    }

    private static List<WxIndirectConfigCmd> buildWxIndirectConfigs(final List<WxIndirectConf> wxIndirectConfigs,
                                                                    final MerchantInfoDTO merchantInfo) {
        if (CollectionUtils.isEmpty(wxIndirectConfigs)) {
            return Collections.emptyList();
        }
        return wxIndirectConfigs.stream()
                .map(wxIndirectConf -> WxIndirectConfigCmd.build(wxIndirectConf, merchantInfo))
                .collect(Collectors.toList());
    }

    private static List<AlipayIndirectConfigCmd> buildAliIndirectConfigs(List<AliIndirectConf> aliIndirectConfigs,
                                                                         final MerchantInfoDTO merchantInfo) {
        if (CollectionUtils.isEmpty(aliIndirectConfigs)) {
            return Collections.emptyList();
        }
        return aliIndirectConfigs.stream()
                .map(aliIndirectConf -> AlipayIndirectConfigCmd.build(aliIndirectConf, merchantInfo))
                .collect(Collectors.toList());
    }

    private static List<DigitalCurrencyConfigCmd> buildDigitalCurrencyConfigs(List<DigitalCurrencyConf> digitalCurrencyConfig) {
        if (CollectionUtils.isEmpty(digitalCurrencyConfig)) {
            return Collections.emptyList();
        }
        return digitalCurrencyConfig.stream()
                .map(DigitalCurrencyConfigCmd::build)
                .collect(Collectors.toList());
    }
}


