package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.annotation.EntryApplyProcess;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.service.EntryOrderHandler;
import com.yeepay.g3.core.aggregation.config.service.impl.ChannelEntryApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:47
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
@EntryApplyProcess(EntryTypeEnum.ENTRY)
public class OnlyEntryOrderHandler implements EntryOrderHandler {

    private final ChannelEntryApplyService channelEntryApplyService;

    @Override
    public void handle(final EntryApplyContext<? extends EntryApply<? extends ChannelSpecialParams>> context) {
        log.info("进件类型：ENTRY 开始处理: 请求参数:{}", JsonUtils.toJSONString(context.getEntryApply()));
        channelEntryApplyService.entryApply(context);
    }

}
