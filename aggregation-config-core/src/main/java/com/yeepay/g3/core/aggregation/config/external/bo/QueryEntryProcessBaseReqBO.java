package com.yeepay.g3.core.aggregation.config.external.bo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * description: 查询聚合挂靠请求参数
 * <AUTHOR>
 * @since 2025/6/10:23:08
 * Company: 易宝支付(YeePay)
 */
@Builder
@Getter
public class QueryEntryProcessBaseReqBO implements Serializable {
    private static final long serialVersionUID = -2982647014969158790L;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 支付渠道
     */
    private PayChannelEnum payChannel;

    /**
     * 场景
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;

    /**
     * 数币银行编码
     */
    private String digitalCurrencyBankCode;
}
