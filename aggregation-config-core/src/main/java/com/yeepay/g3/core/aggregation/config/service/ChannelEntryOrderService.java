package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.QueryEntryApplyResultReqBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryOrder;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9 22:35
 */
public interface ChannelEntryOrderService {
    void saveChannelEntryOrder(EntryOrder entryOrder);

    EntryOrder getChannelEntryOrder(final BizSceneEnum bizScene, final String bizApplyNo);

    List<BaseEntryApply<? extends ChannelSpecialParams>> queryChannelEntryOrder(QueryEntryApplyResultReqBO reqBO);

    int updateChannelEntryApplyDetailEntryApplyStatus(BaseEntryApply<? extends ChannelSpecialParams> entryApply);

    /**
     * 底层更新了 flagStatus 和 entryStatus 字段
     * 防争抢需要加锁
     *
     * @param entryApply
     * @return
     */
    int updateChannelEntryApplyDetailFlagStatus(BaseEntryApply<? extends ChannelSpecialParams> entryApply);

    BaseEntryApply<? extends ChannelSpecialParams> getChannelEntryApply(String applyNo);

    /**
     * 加锁
     *
     * @param applyNo
     * @return
     */
    ChannelEntryApplyDetailEntity selectChannelEntryApplyDetailEntityForUpdate(String applyNo);

    /**
     * 补偿查询未完成的进件申请
     * 终端报备/分账方创建/进件申请
     * 转换出进件的 BO
     *
     * @return
     */
    List<BaseEntryApply<? extends ChannelSpecialParams>> queryNotFinishChannelEntryOrderBO(Date startTime, Date endTime, List<String> applyNoList);


}
