package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.DigitalCurrencyConf;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/2 17:14
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
public class DigitalCurrencyConfigCmd {

    private final SettleAccountInfo settleAccountInfo;

    private final PaySceneEnum payScene;

    private final PayChannelEnum payChannel;

    /**
     * 进件类型
     */
    private final EntryTypeEnum entryType;

    /**
     * 银行编码
     */
    private final String bankCode;

    private final String extendInfo;


    public static DigitalCurrencyConfigCmd build(DigitalCurrencyConf digitalCurrencyConf) {

        Assert.notNull(digitalCurrencyConf, ResultCode.PARAM_VALID_ERROR, "数字货币进件配置不能为空");
        Assert.notBlank(digitalCurrencyConf.getBankCode(), ResultCode.PARAM_VALID_ERROR, "数字货币进件银行编码不能为空");
        return DigitalCurrencyConfigCmd.builder()
                .payChannel(PayChannelEnum.BANK)
                .payScene(PaySceneEnum.DIGITAL_CURRENCY)
                .entryType(EntryTypeEnum.ENTRY)
                .bankCode(digitalCurrencyConf.getBankCode())
                .settleAccountInfo(SettleAccountInfo.build(digitalCurrencyConf.getSettleAccountInfo()))
                .extendInfo(digitalCurrencyConf.getExtendInfo())
                .build();
    }
}
