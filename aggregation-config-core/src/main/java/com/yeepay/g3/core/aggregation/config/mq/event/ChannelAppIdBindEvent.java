package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @description: 通道appId绑定事件同步
 * @author: xuchen.liu
 * @date: 2024-12-15 01:13
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Data
public class ChannelAppIdBindEvent implements Serializable {

    private static final long serialVersionUID = -3039037985207620364L;

    /**
     * appID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号不能为空")
    private String yeepayMerchantNo;

    /**
     * 报备商户号
     */
    private String reportMerchantNo;

    /**
     * 渠道绑定状态
     */
    private String status;

    /**
     * 操作时间不能为空
     */
    @NotBlank(message = "操作时间不能为空")
    private String  configTime;
}
