package com.yeepay.g3.core.aggregation.config.utils;

/**
 * Created by liushanping on 2017/1/10.
 */


import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 校验工具类
 *
 * <AUTHOR>
 */
public class ValidationUtils {

    private static final Validator validator = Validation.byProvider(HibernateValidator.class)
            .configure().addProperty("hibernate.validator.fail_fast", "true")
            .buildValidatorFactory().getValidator();

    /**
     * 验证实体是否符合验证标准，fail_fast为快速失败返回，验证第一次失败即会返回
     *
     * @param obj   方法入参
     * @param <T>
     */
    public static <T> void validateEntity(T obj) {
        Set<ConstraintViolation<T>> set = validator.validate(obj, Default.class);
        if (CollectionUtils.isNotEmpty(set)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (ConstraintViolation<T> cv : set) {
            	// 验证框架返回的message，就是错误码系统的code
                String errorMessage = cv.getMessage();
                stringBuilder.append(errorMessage).append(";");
            }
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, stringBuilder.toString());
        }
    }

    public static <T> void validateEntity(T obj, Class<?>[] groups) {
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        Set<Class<?>> groupSet = new HashSet<>();
        groupSet.add(Default.class); // 始终包含默认分组
        groupSet.addAll(Arrays.asList(groups));
        Set<ConstraintViolation<T>> violations = validator.validate(obj, groupSet.toArray(new Class[0]));
        if (!violations.isEmpty()) {
            // 路由对应报错信息
            StringBuilder stringBuilder = new StringBuilder();
            for (ConstraintViolation<T> violation : violations) {
                String errorMessage = violation.getMessage();
                stringBuilder.append(errorMessage).append(";");
            }
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, stringBuilder.toString());
        }
    }

    public static void notNull(Object object, String message) {
        if (CheckUtils.isEmpty(object)) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, message);
        }
    }


    public static void isTrue(Boolean object, String message) {
        if (!object) {
            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, message);
        }
    }

}
