package com.yeepay.g3.core.aggregation.config.mq.listener;

import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.EntryApplyCompensateBiz;
import com.yeepay.g3.core.aggregation.config.mq.event.AnchoredApplyResultMessage;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 进件模块用于闭环 进件上的 挂靠订单成功状态使用
 */
@Component
public class EntryAnchoredCallBackListener implements MessageListenerConcurrently {
    private static final Logger LOGGER = LoggerFactory.getLogger(EntryAnchoredCallBackListener.class);
    private final int maxConsumeCount = 10;

    @Resource
    private EntryApplyCompensateBiz entryApplyCompensateBiz;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        LogGuidUtil.clearLogContext();
        MessageExt msg = list.get(0);
        int reconsumeTimes = msg.getReconsumeTimes();
        String message = new String(msg.getBody());
        try {
            LOGGER.info("EntryAnchoredCallBackListener 进件的挂靠回调 接收到了消息[{}]，msgId={}", message, msg.getMsgId());
            AnchoredApplyResultMessage callBackBO = JsonUtils.fromJson(message, AnchoredApplyResultMessage.class);
            entryApplyCompensateBiz.compensateEntryApplyAnchoredSuccess(callBackBO);
        } catch (Exception e) {
            if (reconsumeTimes == maxConsumeCount) {
                LOGGER.error("进件的挂靠回调处理失败,已达最大重试次数,mq不会再进行重试,message=" + message, e);
            } else {
                LOGGER.error("进件的挂靠回调处理失败,mq稍后会进行重试,message=" + message, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
