package com.yeepay.g3.core.aggregation.config.convert.decorator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.enumerate.BankAccountTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WxIndirectEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryRequestConvert;
import com.yeepay.g3.core.aggregation.config.external.constant.ChannelConst;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/18 18:58
 */
@Slf4j
@Service
public class WechatIndirectChannelEntryRequestConvertImpl implements ChannelEntryRequestConvert<WxIndirectEntryApply> {
    private final ChannelEntryRequestConvert<WxIndirectEntryApply> channelEntryRequestConvert;

    public WechatIndirectChannelEntryRequestConvertImpl(@Qualifier("baseChannelEntryRequestConvert") final ChannelEntryRequestConvert<WxIndirectEntryApply> channelEntryRequestConvert) {
        this.channelEntryRequestConvert = channelEntryRequestConvert;
    }

    @Override
    public OpenPayAsyncReportRequestDTO buildChannelRequest(final EntryApplyContext<WxIndirectEntryApply> context) {
        final OpenPayAsyncReportRequestDTO request = channelEntryRequestConvert.buildChannelRequest(context);
        final WxIndirectEntryApply entryApply = context.getEntryApply();

        // 结算卡账户
        final SettleAccountInfo settleAccountInfo = entryApply.getSettleAccountInfo();
        final BankAccountTypeEnum bankAccountType = settleAccountInfo.getBankAccountType();
        request.setMerAccBank(settleAccountInfo.getBankName());
        request.setMerAccBankName(settleAccountInfo.getBankBranchName());
        request.setMerAccBankCode(settleAccountInfo.getBankCode());
        request.setMerAccBankNo(settleAccountInfo.getBranchCode());
        request.setMerAccBankType(bankAccountType == null ? null : bankAccountType.getChannelBankAccountType());
        request.setMerAccNo(settleAccountInfo.getCardNo());
        request.setMerAccName(settleAccountInfo.getCardName());
        request.setMerAccBankDistinctCode(settleAccountInfo.getDistinctCode());
        final Map<String, String> externalInfo = convertToExternalInfo(context.getEntryApply().getExtendInfo(),
                context.getEntryApply().getBackupSwitch());
        request.setExternalInfo(externalInfo);
        return request;
    }

    private static Map<String, String> convertToExternalInfo(final String extendInfo, final boolean backupSwitch) {
        Map<String, String> externalInfo = JsonUtils.fromJson(extendInfo, new TypeReference<Map<String, String>>() {
        });
        if (backupSwitch) {
            if (externalInfo != null) {
                externalInfo.put(ChannelConst.BACKUP_SWITCH, ChannelConst.ON);
            } else {
                externalInfo = new HashMap<>();
                externalInfo.put(ChannelConst.BACKUP_SWITCH, ChannelConst.ON);
            }
        }
        return externalInfo;
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        boolean flag = PaySceneEnum.DIRECT.equals(payScene) || PaySceneEnum.DIRECT_STANDARD.equals(payScene)
                || PaySceneEnum.STORE_ASST.equals(payScene);
        return PayChannelEnum.WECHAT.equals(payChannel) && !flag;
    }

}
