package com.yeepay.g3.core.aggregation.config.biz.disposal;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 17:27
 */
@Setter
@Getter

public class DisposalQueryEntity implements Serializable {
    private static final long serialVersionUID = -3798935615184572053L;

    private Integer channelType;

    /**
     * 易宝商编
     */
    private String yeepayMerchantNo;

    /**
     * 报备商户号
     */
    private String reportMerchantNo;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 页数
     */
    private Integer pageNum;

    /**
     * 代理商编
     */
    private String agentMerchantNo;
    /**
     * 顶级商编
     */
    private String topMerchantNo;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 处罚类型
     */
    private String punishType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 处罚方案/原因
     */
    private String punishPlan;
    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 查询范围
     */
    private String queryType;
    private Set<String> roleType;
    private Set<String> roles;

    /**
     * 是否查询自己
     * true:查询自己+下级
     * false:只查下级
     */
    private Boolean withSelf;
}
