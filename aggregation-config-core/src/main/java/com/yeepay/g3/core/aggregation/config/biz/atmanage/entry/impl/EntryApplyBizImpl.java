package com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.EntryApplyBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryOrder;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.EntryApplyCmd;
import com.yeepay.g3.core.aggregation.config.factory.EntryTypeHandlerRouter;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/2 22:20
 */
@Slf4j
@Service
public class EntryApplyBizImpl implements EntryApplyBiz {
    private final EntryTypeHandlerRouter entryTypeHandlerRouter;
    private final ChannelEntryOrderService channelEntryOrderService;
    private final AtlasThreadPoolExecutor entryHandleExecutor;


    public EntryApplyBizImpl(final EntryTypeHandlerRouter entryTypeHandlerRouter,
                             final ChannelEntryOrderService channelEntryOrderService,
                             @Qualifier("entryHandleExecutor") final AtlasThreadPoolExecutor entryHandleExecutor) {
        this.entryTypeHandlerRouter = entryTypeHandlerRouter;
        this.channelEntryOrderService = channelEntryOrderService;
        this.entryHandleExecutor = entryHandleExecutor;
    }


    @Override
    public EntryOrder handle(final EntryApplyCmd entryApplyCmd) {

        final EntryOrder channelEntryOrder = channelEntryOrderService.getChannelEntryOrder(entryApplyCmd.getBizScene(), entryApplyCmd.getBizApplyNo());
        if (channelEntryOrder != null) {
            throw new BusinessException(ResultCode.ENTRY_APPLY_IDEMPOTENT);
        }
        final EntryOrder entryOrder = EntryOrder.createEntryOrder(entryApplyCmd);

        channelEntryOrderService.saveChannelEntryOrder(entryOrder);

        entryOrder.getEntryApplies().forEach(entryApply ->
                entryHandleExecutor.submit(() -> {
                    try {
                        LogGuidUtil.clearLogContext();
                        entryTypeHandlerRouter.getHandler(entryApply.getEntryType())
                                .handle(new EntryApplyContext<>(entryApply));
                    } catch (Exception e) {
                        log.error("entryApplyHandler error", e);
                    }
                }));
        return entryOrder;
    }


}
