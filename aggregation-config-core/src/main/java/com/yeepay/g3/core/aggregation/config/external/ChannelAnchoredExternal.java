package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.external.bo.AggDesignateMerchantAnchoredReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.AggSameSubjectAnchoredReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.ChannelAnchoredRecordBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.AggSameReportRespBO;

import java.util.List;

/**
 * 通道 挂靠 远程服务
 *
 * @author: Mr.yin
 * @date: 2025/6/4  20:17
 */
public interface ChannelAnchoredExternal {

    /**
     * 请求聚合进行挂靠
     *
     * @param reqBO
     * @return
     */
    RemoteResult<String> aggregationDesignateMerchantAnchored(AggDesignateMerchantAnchoredReqBO reqBO);

    /**
     * 请求聚合进行 同主体挂靠
     *
     * @param reqBO
     * @return
     */
    RemoteResult<List<AggSameReportRespBO>> aggSameSubjectAnchored(AggSameSubjectAnchoredReqBO reqBO);

    /**
     * 请求通道进行商户挂靠
     *
     * @param requestDTO
     * @return
     */
    RemoteResult<List<ChannelAnchoredRecordBO>> channelAnchored(AnchoredApply anchoredApply, AggAnchoredMerchantInfo aggAnchoredMerchantInfo);
}
