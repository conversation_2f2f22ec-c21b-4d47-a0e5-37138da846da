package com.yeepay.g3.core.aggregation.config.assembler;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.SubjectTypeEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.SubjectInfoDTO;

/**
 * <AUTHOR>
 * @date 2025/5/23 14:43
 */
public interface EntryInfoBuild {
    SubjectTypeEnum getSubjectType();

    PayChannelEnum getChannelType();
    PaySceneEnum getPayScene();

    SubjectInfo buildSubjectInfo(SubjectInfoDTO subject);
    boolean isSupport();
}
