package com.yeepay.g3.core.aggregation.config.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * @Description:
 * @ClassName: FileUtils
 * @Author: cong.huo
 * @Date: 2024/12/16 18:16   // 时间
 * @Version: 1.0
 */
@Slf4j
public class FileUtils {

    public static byte[] getFileBytes(File file){
        if (file == null) {
            return null;
        }
        FileInputStream fileInputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fileInputStream.read(b)) != -1) {
                byteArrayOutputStream.write(b, 0 , n);
            }
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
                if (byteArrayOutputStream != null) {
                    byteArrayOutputStream.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage(),e);
            }
        }
        return null;

    }

    public static void deleteFile(File file) {
        if (file != null && file.exists()) {
            boolean delete = file.delete();
            if (!delete) {
                log.error("delete file error ,"+file.getAbsoluteFile());
            }
        }
    }
}
