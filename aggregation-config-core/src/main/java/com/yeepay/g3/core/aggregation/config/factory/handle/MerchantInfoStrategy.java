package com.yeepay.g3.core.aggregation.config.factory.handle;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.auth.AuthApplyBO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;

/**
 * description: 商户信息处理策略接口
 * <AUTHOR>
 * @since 2025/5/22:11:03
 * Company: 易宝支付(YeePay)
 */
public interface MerchantInfoStrategy {

    AuthApplyBO buildAndValidateAuth(AuthApplyReqDTO authApplyReqDTO);

}
