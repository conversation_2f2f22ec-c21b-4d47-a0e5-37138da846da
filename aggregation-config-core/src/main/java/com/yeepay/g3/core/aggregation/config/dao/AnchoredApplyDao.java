package com.yeepay.g3.core.aggregation.config.dao;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 挂靠申请记录
 */
@Mapper
public interface AnchoredApplyDao {

    /**
     * 插入挂靠申请记录
     * @param anchoredApplyEntity 挂靠申请实体
     * @return 影响行数
     */
    int saveAnchoredApply(AnchoredApplyEntity anchoredApplyEntity);

    /**
     * 批量插入挂靠申请记录
     *
     * @param anchoredApplyEntities 挂靠申请实体列表
     * @return 影响行数
     */
    int batchSaveAnchoredApply(@Param("list") List<AnchoredApplyEntity> anchoredApplyEntities);

    /**
     * 更新聚合挂靠失败
     *
     * @param
     * @return
     */
    int updateAnchoredProcess(@Param("applyNo") String applyNo);

    /**
     * 更新聚合挂靠成功
     *
     * @param applyNo
     * @param aggAnchoredMerchantInfoJson
     * @return
     */
    int updateAggAnchoredResultSuccess(@Param("applyNo") String applyNo, @Param("aggAnchoredMerchantInfoJson") String aggAnchoredMerchantInfoJson);

    /**
     * 更新聚合挂靠失败
     *
     * @param
     * @return
     */
    int updateAggAnchoredResultFail(@Param("applyNo") String applyNo, @Param("failReason") String failReason);

    /**
     * 更新聚合挂靠成功
     * @param applyNo
     * @param aggAnchoredMerchantInfoJson
     * @return
     */
    int updateChannelAnchoredInfo(@Param("applyNo") String applyNo, @Param("aggAnchoredMerchantInfoJson") String aggAnchoredMerchantInfoJson);

    /**
     * 更新渠道调用最终结果
     * @param applyNo
     * @param anchoredStatus
     * @param failReason
     */
    int updateChannelAnchoredResult(@Param("applyNo") String applyNo, @Param("anchoredStatus") AnchoredStatus anchoredStatus, @Param("failReason") String failReason );

    /**
     * 查询同一批次下的所有挂靠申请
     * @param orderNo
     * @return
     */
    List<AnchoredApplyEntity> queryAllAnchoredApplyByOrderNo(@Param("orderNo") String orderNo);
    /**
     * 根据申请编号查询
     * @param applyNo
     * @return
     */
    AnchoredApplyEntity queryAnchoredApplyByApplyNo(@Param("applyNo") String applyNo);

    /**
     * 根据申请编号查询 加锁
     * @param applyNo
     * @return
     */
    AnchoredApplyEntity queryAnchoredApplyByApplyNoForUpdate(@Param("applyNo") String applyNo);

}