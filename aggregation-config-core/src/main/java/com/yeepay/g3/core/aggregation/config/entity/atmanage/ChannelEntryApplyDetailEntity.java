package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/2 16:07
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Table(name = "t_channel_entry_apply_detail")
public class ChannelEntryApplyDetailEntity extends BaseEntity {

    /**
     * 订单号
     */
    @Column(name = "order_no", length = 32)
    private String orderNo;


    /**
     * 申请单号
     */
    @Column(name = "apply_no", length = 32)
    private String applyNo;

    /**
     * 商户编号
     */
    @Column(name = "merchant_no", length = 16)
    private String merchantNo;

    /**
     * 商户名称
     */
    @Column(name = "merchant_name", length = 128)
    private String merchantName;

    /**
     * 入驻类型
     */
    @Column(name = "entry_type", length = 16)
    private String entryType;

    /**
     * 渠道编号
     */
    @Column(name = "channel_no", length = 32)
    private String channelNo;

    /**
     * 渠道标识
     */
    @Column(name = "channel_identifier", length = 16)
    private String channelIdentifier;

    /**
     * 支付场景
     */
    @Column(name = "pay_scene", length = 32)
    private String payScene;

    /**
     * 支付渠道
     */
    @Column(name = "pay_channel", length = 16)
    private String payChannel;

    /**
     * 银行代码
     */
    @Column(name = "bank_code", length = 16)
    private String bankCode;


    /**
     * 通道网关代码
     */
    @Column(name = "channel_gateway_code", length = 16)
    private String channelGatewayCode;

    /**
     * 活动类型
     */
    @Column(name = "activity_type", length = 32)
    private String activityType;

    /**
     * 渠道参数（JSON 格式）
     */
    @Column(name = "channel_params", columnDefinition = "text")
    private String channelParams;

    /**
     * 备份数量
     */
    @Column(name = "backup_count")
    private Integer backupCount;

    /**
     * 状态
     */
    @Column(name = "status", length = 16)
    private String status;

    /**
     * 审核状态
     */
    @Column(name = "audit_status", length = 16)
    private String auditStatus;

    @Column(name = "sign_url", length = 1024)
    private String signUrl;
    /**
     * 二进制
     * 终端报备标志/微信直连是否需要创建分账方/是否存在挂靠申请
     */
    @Column(name = "flag")
    private Long flag;

    /**
     * 二进制
     * 辅助终端报备是否完成/微信直连创建分账方是否完成/挂靠结果
     */
    @Column(name = "flag_status")
    private Long flagStatus;

    /**
     * 渠道商户号（多个商户号用逗号分隔）
     */
    @Column(name = "channel_mch_nos")
    private String channelMchNos;

    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 挂靠订单号
     */
    @Column(name = "anchored_order_no", length = 32)
    private String anchoredOrderNo;

    @Column(name = "extend_info")
    private String extendInfo;
}
