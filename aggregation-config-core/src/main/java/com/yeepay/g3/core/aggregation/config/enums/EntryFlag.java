package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/9 23:35
 */
@Getter
@AllArgsConstructor
public enum EntryFlag implements DocumentedEnum<Long>, BitEnum {

    NONE(0, "无标识"),
    NEED_TERMINAL_REPORT(1, "是否需要终端报备"),
    NEED_WX_DIRECT_CREATE_SPLITTER(1 << 1, "是否需要微信直连创建分账方"),
    EXIST_ANCHORED_APPLY(1 << 2, "是否存在挂靠申请"),
    SPECIFY_BACKUP(1 << 3, "指定备份标识"),
    SPECIFY_CHANNEL_NO(1 << 4, "指定渠道号/渠道标识"),
    ;
    private final long bit;
    private final String desc;

    @Override
    public Long getDocument() {
        return bit;
    }
}
