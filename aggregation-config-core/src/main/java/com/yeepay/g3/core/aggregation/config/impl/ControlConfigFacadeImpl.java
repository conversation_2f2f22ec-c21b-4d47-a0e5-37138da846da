package com.yeepay.g3.core.aggregation.config.impl;

import com.yeepay.g3.core.aggregation.config.biz.ControlConfigBiz;
import com.yeepay.g3.core.aggregation.config.builder.ControlConfigBuilder;
import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.facade.aggregation.config.dto.ControlConfigRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.ControlConfigResponseDTO;
import com.yeepay.g3.facade.aggregation.config.enums.OperateTypeEnum;
import com.yeepay.g3.facade.aggregation.config.facade.ControlConfigFacade;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: facade实现
 * @author: xuchen.liu
 * @date: 2024-12-13 11:27
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Service
public class ControlConfigFacadeImpl  implements ControlConfigFacade {

    @Autowired
    private ControlConfigBuilder controlConfigBuilder;

    @Autowired
    private ControlConfigBiz controlConfigBiz;

    @Autowired
    private MerchantCenterExternal merchantCenterExternal;

    /**
     * 管理控制配置的业务逻辑方法。根据请求的配置数据，执行相应的操作（如创建、更新等），
     * 并返回操作结果的响应对象。此方法还会验证商户编号是否为顶级商编，如果不是顶级商编则抛出异常。
     *
     * <p>此方法执行以下操作：
     * <ul>
     *     <li>查询商户组信息，获取顶级商户编号。</li>
     *     <li>如果传入的商户编号不是顶级商户编号，则抛出异常。</li>
     *     <li>将请求数据转换为控制配置实体。</li>
     *     <li>调用业务层进行配置管理操作。</li>
     *     <li>返回操作结果的响应对象，包含配置 ID。</li>
     * </ul>
     * </p>
     */
    @Override
    public ControlConfigResponseDTO configManage(ControlConfigRequestDTO controlConfigIRequestDTO) {
        //调用业务层
        ControlConfigEntity entity = controlConfigBuilder.toEntity(controlConfigIRequestDTO);
        Pair<OperateTypeEnum, ControlConfigEntity> entityPair = Pair.of(controlConfigIRequestDTO.getOperateType(), entity);
        Long id = controlConfigBiz.configManage(entityPair);
        ControlConfigResponseDTO controlConfigResponseDTO = new ControlConfigResponseDTO();
        controlConfigResponseDTO.setId(id);
        return controlConfigResponseDTO;
    }
}
