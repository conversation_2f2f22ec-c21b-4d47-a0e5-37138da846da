package com.yeepay.g3.core.aggregation.config.external.impl;

import com.google.common.collect.Sets;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryChannelTerminalInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryEntryInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.core.aggregation.config.convert.ChannelResultConvert;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.ChannelEntryExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.QueryEntryProcessBaseReqBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.trade.bankcooper.OpenPayAsyncReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportInfoQueryRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportInfoQueryResponseDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportQueryRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportQueryResponseDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayTerminalReportQueryRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayTerminalReportQueryResponseDTO;
import com.yeepay.g3.facade.trade.bankcooper.enums.OpenReportDataRangeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @author: Mr.yin
 * @date: 2025/6/4  20:20
 */
@Service
public class ChannelEntryExternalImpl implements ChannelEntryExternal {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 渠道进件服务
     */
    private OpenPayAsyncReportFacade openPayAsyncReportFacade = RemoteServiceFactory.getService(OpenPayAsyncReportFacade.class);
    private static final String CHANNEL_SUCCESS = "0000";

    /**
     * 查询进件请求订单不存在
     */
    private static final Set<String> ENTRY_ORDER_NOT_EXIST = Sets.newHashSet("851055", "830003");

    @Override
    public List<QueryEntryInfoBO> queryMerchantEntryResult(QueryEntryProcessBaseReqBO reqBO) {
        logger.info("[进件查询] 请求查询通道的进件结果 reqBO={}", JsonUtils.toJSONString(reqBO));

        OpenPayAsyncReportInfoQueryRequestDTO requestDTO = new OpenPayAsyncReportInfoQueryRequestDTO();
        requestDTO.setMerchantNo(reqBO.getMerchantNo());
        requestDTO.setBankCode(reqBO.getDigitalCurrencyBankCode());
        requestDTO.setDataRange(OpenReportDataRangeEnum.MAJOR.name());
        SceneAndActivityTypeBO sceneAndPromotionTypeBO = SceneAndActivityUtils
                .getSceneAndPromotionTypeBO(reqBO.getPayChannel(), reqBO.getPayScene(), reqBO.getActivityType());
        requestDTO.setReportFee(sceneAndPromotionTypeBO.getChannelFeeType());
        requestDTO.setPromotionType(sceneAndPromotionTypeBO.getChannelActivityType());

        OpenPayAsyncReportInfoQueryResponseDTO responseDTO = null;
        try {
            logger.info("[进件查询] 请求查询通道  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = openPayAsyncReportFacade.queryOpenPayReportRecordInfo(requestDTO);
            logger.info("[进件查询] 请求查询通道的结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[进件查询] 请求查询通道的进件结果异常 reqBO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (null == responseDTO) {
            logger.error("[进件查询] 请求查询通道的进件结果返回为null reqBO={%s})", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (ChannelResultCode.CHANNEL_ENTRY_QUERY_ERROR.getCode().equals(responseDTO.getBizCode())) {
            return Collections.emptyList();
        } else if (!CHANNEL_SUCCESS.equals(responseDTO.getBizCode())) {
            logger.warn("[进件查询] 查询通道失败 reqBO={}", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, StringUtils.isBlank(responseDTO.getBizMsg()) ? "查询进件结果异常" : responseDTO.getBizMsg());
        }

        return QueryEntryInfoBO.buildList(responseDTO.getOpenPayReportRecordInfoDTOList());
    }

    @Override
    public QueryChannelTerminalInfoBO queryTerminalEntryResult(String merchantNo, PayChannelEnum payChannel) {
        OpenPayTerminalReportQueryRequestDTO reqDTO = new OpenPayTerminalReportQueryRequestDTO();
        reqDTO.setMerchantNo(merchantNo);
        reqDTO.setBankCode(payChannel.getDocument());
        try {
            logger.info("查询通道终端报备结果，请求参数：{}", JsonUtils.toJSONString(reqDTO));
            OpenPayTerminalReportQueryResponseDTO responseDTO = openPayAsyncReportFacade.queryTerminalReport(reqDTO);
            logger.info("查询通道终端报备结果，响应结果：{}", JsonUtils.toJSONString(responseDTO));
            if (responseDTO == null) {
                throw new BusinessException(ResultCode.SYSTEM_ERROR);
            } else if (ChannelResultCode.CHANNEL_ENTRY_QUERY_ERROR.getCode().equals(responseDTO.getBizCode())) {
                return null;
            } else if (!ChannelResultCode.DIRECT_SUCCESS.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.ENTRY_QUERY_TERMINAL_ERROR, responseDTO.getBizMsg());
            }

            return QueryChannelTerminalInfoBO.build(responseDTO);
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            logger.error("查询通道终端报备结果异常", e);
            throw new BusinessException(ResultCode.ENTRY_QUERY_TERMINAL_ERROR);
        }
    }


    @Override
    public RemoteResult<ChannelEntryResult> queryEntryResultByRequestOrderNo(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo) {
        OpenPayAsyncReportQueryRequestDTO requestDTO = assembleEntryQueryRequest(entryApply, requestNo);

        OpenPayAsyncReportQueryResponseDTO responseDTO = null;
        try {
            logger.info("[remote][查询通道单笔请求进件结果]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = openPayAsyncReportFacade.queryOpenPayReportRecord(requestDTO);
            logger.info("[remote][查询通道单笔请求进件结果]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[remote][查询通道单笔请求进件结果] 调用异常 requestDTO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (null == responseDTO) {
            logger.error("[remote][查询通道单笔请求进件结果] 调用异常返回为null requestDTO={%s})", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        /*0000：表示查询成功，其它为失败 851055 记录不存在*/
        if (!CHANNEL_SUCCESS.equals(responseDTO.getBizCode()) && !ENTRY_ORDER_NOT_EXIST.contains(responseDTO.getBizCode())) {
            return RemoteResult.fail(responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        if (ENTRY_ORDER_NOT_EXIST.contains(responseDTO.getBizCode()) || CollectionUtils.isEmpty(responseDTO.getOpenPayReportRecordDTOs())) {
            return RemoteResult.success(responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        ChannelEntryResult channelEntryResult = ChannelResultConvert.convert(responseDTO, requestNo);
        logger.info("[remote][查询通道单笔请求进件结果]  结果返回 channelEntryResult={}", JsonUtils.toJSONString(channelEntryResult));
        return RemoteResult.success(channelEntryResult, responseDTO.getBizCode(), responseDTO.getBizMsg());
    }

    private OpenPayAsyncReportQueryRequestDTO assembleEntryQueryRequest(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo) {
        OpenPayAsyncReportQueryRequestDTO requestDTO = new OpenPayAsyncReportQueryRequestDTO();
        requestDTO.setQueryBackup(true);
        requestDTO.setRequestOrderNo(requestNo);
        requestDTO.setSourceName(Const.DEFAULT_SYSTEM_NAME);
//        requestDTO.setMerchantNoList(Lists.newArrayList());
        requestDTO.setMerchantNo(entryApply.getMerchantInfo().getMerchantNo());
        requestDTO.setChannelNo(entryApply.getChannelNo());
        SceneAndActivityTypeBO sceneAndPromotionTypeBO = SceneAndActivityUtils.getSceneAndPromotionTypeBO(entryApply.getPayChannel(),
                entryApply.getPayScene(), entryApply.getActivityType());
        requestDTO.setPromotionType(sceneAndPromotionTypeBO.getChannelActivityType());
        requestDTO.setReportFee(sceneAndPromotionTypeBO.getChannelFeeType());
//        requestDTO.setBankCode(DocumentedEnum.parseName(entryApply.getPayChannel()));
//        requestDTO.setReportMerchantNo("");
//        requestDTO.setChannelIdentifier("");
        return requestDTO;
    }

}
