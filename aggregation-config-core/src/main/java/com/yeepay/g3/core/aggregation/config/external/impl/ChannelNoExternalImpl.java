package com.yeepay.g3.core.aggregation.config.external.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.channel.direct.conn.dto.ResultInfo;
import com.yeepay.channel.direct.conn.enums.MerchantModeEnum;
import com.yeepay.channel.direct.conn.facade.manage.ChannelBusinessFacade;
import com.yeepay.channel.direct.conn.facade.manage.dto.channelBusinessProvider.BusinessServiceProviderDTO;
import com.yeepay.channel.direct.conn.facade.manage.dto.channelBusinessProvider.QueryBusinessServiceProviderChannelNoReqDTO;
import com.yeepay.channel.direct.conn.facade.manage.dto.channelBusinessProvider.QueryBusinessServiceProviderReqDTO;
import com.yeepay.channel.direct.conn.facade.manage.dto.channelBusinessProvider.QueryBusinessServiceProviderRespDTO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.*;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.core.aggregation.config.external.ChannelNoExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelNoSignSubjectBO;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.trade.bankcooper.OpenPayAsyncReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.*;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * description: 渠道号管理外部接口实现类
 *
 * <AUTHOR>
 * @since 2025/5/27:18:03
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class ChannelNoExternalImpl implements ChannelNoExternal {

    private final OpenPayAsyncReportFacade reportFacade = RemoteServiceFactory.getService(OpenPayAsyncReportFacade.class);
    private final ChannelBusinessFacade channelBusinessFacade = RemoteServiceFactory.getService(ChannelBusinessFacade.class);


    @Override
    public void applyChannelInfo(String requestNo, ChannelNoApplyBO channelInfo) {
        SubmitApplyChannelInfoRequestDTO reqDTO = ChannelNoApplyBO.buildChannelDTO(requestNo, channelInfo);
        try {
            log.info("提交渠道号申请信息，参数：{}", reqDTO);
            SubmitApplyChannelInfoResponseDTO responseDTO = reportFacade.submitApplyChannelInfo(reqDTO);
            log.info("提交渠道号申请信息，返回结果：{}", responseDTO);
            if (responseDTO == null) {
                throw new BusinessException(ResultCode.SYSTEM_ERROR);
            } else if (ChannelResultCode.CHANNEL_NO_REPEAT.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.CHANNEL_NO_REPEAT);
            } else if (ChannelResultCode.CHANNEL_APPLY_RECORD_ERROR.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.CHANNEL_APPLY_RECORD_REPEAT, "已存在申请的渠道记录");
            } else if (!ChannelResultCode.DIRECT_SUCCESS.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.CHANNEL_APPLY_SUBMIT_ERROR, responseDTO.getBizMsg());
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            // 系统异常-不做异常处理 待补单
            log.error("渠道号申请失败，异常信息：", e);
        }
    }

    @Override
    public ChannelNoApplyResultBO queryApplyChannelResult(QueryChannelNoApplyResultBO resultBO) {
        QueryApplyChannelResultRequestDTO reqDTO = QueryChannelNoApplyResultBO.buildChannelDTO(resultBO);
        try {
            log.info("查询渠道号申请结果，参数：{}", reqDTO);
            QueryApplyChannelResultResponseDTO responseDTO = reportFacade.queryApplyChannelResult(reqDTO);
            log.info("查询渠道号申请结果，返回结果：{}", responseDTO);
            if (responseDTO == null) {
                throw new BusinessException(ResultCode.SYSTEM_ERROR);
            } else if (ChannelResultCode.CHANNEL_APPLY_RECORD_ERROR.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.CHANNEL_NO_APPLY_NOT_EXIST, responseDTO.getBizMsg());
            }
            if (!ChannelResultCode.DIRECT_SUCCESS.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.CHANNEL_APPLY_QUERY_ERROR, responseDTO.getBizMsg());
            }
            return ChannelNoApplyResultBO.build(responseDTO);
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询渠道号申请结果失败，异常信息：", e);
            throw new BusinessException(ResultCode.CHANNEL_APPLY_QUERY_ERROR);
        }
    }

    @Override
    public List<ChannelNoQueryResultBO> queryChannelInfo(ChannelNoQueryBO queryBO) {
        QueryChannelInfoRequestDTO reqDTO = ChannelNoQueryBO.buildChannelDTO(queryBO);
        try {
            log.info("查询渠道信息，参数：{}", reqDTO);
            List<OpenPayChannelInfoDTO> infoList = reportFacade.queryChannelInfo(reqDTO);
            log.info("查询渠道信息，返回结果：{}，参数：{}", infoList, reqDTO);
            if (infoList == null || infoList.isEmpty()) {
                return new ArrayList<>();
            }
            return ChannelNoQueryResultBO.build(infoList);
        } catch (Exception e) {
            log.error("查询渠道号结果失败，异常信息：", e);
            throw new BusinessException(ResultCode.CHANNEL_NO_QUERY_ERROR);
        }
    }

    @Override
    public String queryChannelNoByBusinessLine(String industryLine, PaySceneEnum paySceneEnum) {
        QueryBusinessServiceProviderReqDTO reqDTO = getQueryBusinessServiceProviderReqDTO(industryLine, paySceneEnum);
        ResultInfo<QueryBusinessServiceProviderRespDTO> responseDto;
        try {
            log.info("根据行业线查询渠道号，参数：{}", JsonUtils.toJSONString(reqDTO));
            responseDto = channelBusinessFacade.queryChannelServiceProviderByBusinessLine(reqDTO);
            log.info("根据行业线查询渠道号，返回结果：{}", JsonUtils.toJSONString(responseDto));
        } catch (Exception e) {
            log.error("根据行业线查询渠道号系统异常", e);
            throw new BusinessException(ResultCode.CHANNEL_NO_BUSINESS_LINE_QUERY_ERROR);
        }
        // todo 李兆祥 不要放在通道枚举 DIRECT_SUCCESS
        if (!ChannelResultCode.DIRECT_SUCCESS.getCode().equals(responseDto.getRetCode())) {
            throw new BusinessException(ResultCode.CHANNEL_NO_BUSINESS_LINE_QUERY_ERROR);
        }
        QueryBusinessServiceProviderRespDTO data = responseDto.getData();

        if (CheckUtils.isEmpty(data) || CheckUtils.isEmpty(data.getChannelNo())) {
            log.error("根据行业线:{},支付场景:{}查询渠道号不存在，请联系运营！", industryLine, paySceneEnum.getDocument());
            throw new BusinessException(ResultCode.CHANNEL_NO_BUSINESS_LINE_QUERY_ERROR, "根据行业线查询渠道号不存在");
        }
        return data.getChannelNo();
    }

    /**
     * 获取查询行业线对应渠道号请求参数
     *
     * @param industryLine 行业线
     * @param paySceneEnum 支付场景
     */
    private static QueryBusinessServiceProviderReqDTO getQueryBusinessServiceProviderReqDTO(String industryLine, PaySceneEnum paySceneEnum) {
        String merchantMode;
        if (PaySceneEnum.DIRECT.equals(paySceneEnum)) {
            merchantMode = MerchantModeEnum.WXZL.name();
        } else if (PaySceneEnum.DIRECT_STANDARD.equals(paySceneEnum)) {
            merchantMode = MerchantModeEnum.WFT_ZL.name();
        } else if (PaySceneEnum.STORE_ASST.equals(paySceneEnum)) {
            merchantMode = MerchantModeEnum.WX_B2B_ZL.name();
        } else {
            throw new BusinessException(ResultCode.PARAM_VALID_ERROR, "该支付场景不支持查询行业线对应渠道号");
        }
        QueryBusinessServiceProviderReqDTO reqDTO = new QueryBusinessServiceProviderReqDTO();
        reqDTO.setBusinessLineCode(industryLine);
        reqDTO.setMerchantMode(merchantMode);
        return reqDTO;
    }

    @Override
    public RemoteResult<ChannelNoSignSubjectBO> queryChannelSubjectByChannelNo(String channelNo) {
        QueryBusinessServiceProviderChannelNoReqDTO requestDTO = new QueryBusinessServiceProviderChannelNoReqDTO();
        requestDTO.setChannelNo(channelNo);
        ResultInfo<BusinessServiceProviderDTO> responseDTO = null;
        try {
            log.info("[remote][查询应用层渠道号注册信息]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = channelBusinessFacade.queryChannelServiceProviderByChannelNo(requestDTO);
            log.info("[remote][查询应用层渠道号注册信息]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            log.error(String.format("[remote][查询渠道号注册信息] 调用异常 requestDTO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            return RemoteResult.fail(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR.getCode(), "查询渠道号信息异常");
        }
        if (null == responseDTO) {
            log.error("[remote][查询应用层渠道号注册信息] 调用异常返回为null requestDTO={%s})", JsonUtils.toJSONString(requestDTO));
            return RemoteResult.fail(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR.getCode(), "查询渠道号信息异常");
        }
        if (!"0000".equals(responseDTO.getRetCode())) {
            return RemoteResult.fail(responseDTO.getRetCode(), responseDTO.getRetMsg());
        }
        BusinessServiceProviderDTO data = responseDTO.getData();
        if (CheckUtils.isEmpty(data)) {
            log.warn("查询应用层微信直连渠道号不存在，请联系运营！channelNo={}", channelNo);
            return RemoteResult.success(responseDTO.getRetCode(), responseDTO.getRetMsg());
        }
        ChannelNoSignSubjectBO signSubjectBO = covertChannelNoSignSubjectBO(data);
        log.info("[remote][查询应用层渠道号注册信息] 返回结果 reportResultBO={}", JsonUtils.toJSONString(signSubjectBO));
        return RemoteResult.success(signSubjectBO, responseDTO.getRetCode(), responseDTO.getRetMsg());
    }

    @Override
    public ChannelInfoResponsesDTO queryChannelNoInfoByChannelNo(List<String> channelNoList) {
        try {
            log.info("依据渠道号查询渠道信息，参数：{}", channelNoList);
            ChannelInfoResponsesDTO responseDTO = reportFacade.queryOpenChanenlInfos(channelNoList);
            log.info("依据渠道号查询渠道信息，返回结果：{}，参数：{}", responseDTO, channelNoList);
            if (responseDTO == null) {
                throw new BusinessException(ResultCode.SYSTEM_ERROR);
            } else if (!ChannelResultCode.DIRECT_SUCCESS.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.CHANNEL_APPLY_QUERY_ERROR, responseDTO.getBizMsg());
            }
            return responseDTO;
        } catch (Exception e) {
            log.error("查询渠道号结果失败，异常信息：", e);
            throw new BusinessException(ResultCode.CHANNEL_NO_QUERY_ERROR);
        }
    }

    private ChannelNoSignSubjectBO covertChannelNoSignSubjectBO(BusinessServiceProviderDTO data) {
        ChannelNoSignSubjectBO signSubjectBO = new ChannelNoSignSubjectBO();
        signSubjectBO.setLicenceNo(data.getLicenseNo());
        signSubjectBO.setChannelNo(data.getChannelNo());
        signSubjectBO.setChannelMerchantNo(data.getChannelMerchantNo());
        signSubjectBO.setSignName(data.getSignName());
        signSubjectBO.setLegalName(data.getLegalName());
        signSubjectBO.setLegalPhone(data.getLegalPhone());
        signSubjectBO.setAdminIdentNo(data.getAdminIdentNo());
        signSubjectBO.setAdminName(data.getAdminName());
        signSubjectBO.setContactPhone(data.getContactPhone());
        signSubjectBO.setIdentType(data.getIdentType());
        signSubjectBO.setRegisterAdd(data.getRegisterAdd());
        signSubjectBO.setPostalCode(data.getPostalCode());
        signSubjectBO.setPhone(data.getPhone());
        signSubjectBO.setBankAccountName(data.getBankAccountName());
        signSubjectBO.setBankName(data.getBankName());
        signSubjectBO.setBankCardNo(data.getBankCardNo());
        signSubjectBO.setCompanyWebsite(data.getCompanyWebsite());
        signSubjectBO.setSignKeyWord(data.getSignName());
        return signSubjectBO;
    }
}
