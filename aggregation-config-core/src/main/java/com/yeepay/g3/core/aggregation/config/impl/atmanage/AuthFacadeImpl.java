package com.yeepay.g3.core.aggregation.config.impl.atmanage;

import com.yeepay.g3.core.aggregation.config.biz.atmanage.AuthBiz;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.AuthFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: 实名认证/商户意愿确认facade实现类
 * <AUTHOR>
 * @since 2025/5/21:11:26
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthFacadeImpl implements AuthFacade {

    private final AuthBiz authBiz;
//    @Override
//    public Result<AuthApplyRespDTO> authApply(AuthApplyReqDTO authApplyReqDTO) {
//        log.info("实名认证申请开始,请求参数:{}", JsonUtils.toJSONString(authApplyReqDTO));
//        AuthApplyBO authApplyBO = AuthApplyBO.buildAndValidateAuthApplyBO(authApplyReqDTO);
//        String channelApplyNo = authBiz.authApply(authApplyBO);
//        log.info("实名认证申请结束,响应参数:{}", channelApplyNo);
//        return Result.success(AuthApplyRespDTO.builder().channelApplyNo(channelApplyNo).build());
//    }
//
//    @Override
//    public Result<?> cancelAuthApply(CancelAuthApplyReqDTO reqDTO) {
//        log.info("实名认证取消申请开始,请求参数:{}", JsonUtils.toJSONString(reqDTO));
//        PaySceneEnum paySceneEnum = DocumentedEnum.fromValue(PaySceneEnum.class, reqDTO.getPayScene());
//        AuthCancelBO authCancelBO = AuthCancelBO.builder()
//                .payScene(paySceneEnum)
//                .channelApplyNo(reqDTO.getChannelApplyNo())
//                .bizApplyNo(CheckUtils.isEmpty(reqDTO.getChannelApplyNo()) ? reqDTO.getBizApplyNo() : null)
//                .channelNo(reqDTO.getChannelNo())
//                .channelIdentifier(reqDTO.getChannelIdentifier()).build();
//        authBiz.authCancel(authCancelBO);
//        return Result.success();
//    }
//
//    @Override
//    public Result<QueryAuthResultRespDTO> queryAuthAuditResult(QueryAuthResultReqDTO reqDTO) {
//        log.info("实名认证结果查询开始,请求参数:{}", JsonUtils.toJSONString(reqDTO));
//        PaySceneEnum paySceneEnum = DocumentedEnum.fromValue(PaySceneEnum.class, reqDTO.getPayScene());
//        AuthQueryBO authQueryBO = AuthQueryBO.builder()
//                .payScene(paySceneEnum)
//                .channelApplyNo(reqDTO.getChannelApplyNo())
//                .bizApplyNo(CheckUtils.isEmpty(reqDTO.getChannelApplyNo()) ? reqDTO.getBizApplyNo() : null)
//                .channelNo(reqDTO.getChannelNo())
//                .channelIdentifier(reqDTO.getChannelIdentifier()).build();
//        AuthResultBO authResultBO = authBiz.queryAuthResult(authQueryBO);
//        if (authResultBO == null) {
//            log.info("实名认证结果查询结束,无响应结果");
//             return Result.success();
//        }
//
//        List<AuthRejectReasonBO> rejectReasonList = authResultBO.getRejectReasonList();
//        List<AuthRejectReasonDTO> rejectReasonDTOList = new ArrayList<>();
//        if (!CheckUtils.isEmpty(rejectReasonList)) {
//            rejectReasonDTOList = rejectReasonList.stream().map(
//                    rejectReasonBO -> AuthRejectReasonDTO.builder()
//                            .rejectParam(rejectReasonBO.getRejectParam())
//                            .rejectReason(rejectReasonBO.getRejectReason())
//                            .build()
//            ).collect(Collectors.toList());
//        }
//        return Result.success(QueryAuthResultRespDTO.builder()
//                .applymentState(authResultBO.getAuthStatusEnum().getDocument())
//                .qrcodeData(authResultBO.getQrcodeData())
//                .rejectReasonList(rejectReasonDTOList).build());
//    }
//
//    @Override
//    public Result<QueryMerchantAuthResultRespDTO> queryMerchantAuthResult(QueryMerchantAuthResultReqDTO reqDTO) {
//        return Result.success();
//    }
}
