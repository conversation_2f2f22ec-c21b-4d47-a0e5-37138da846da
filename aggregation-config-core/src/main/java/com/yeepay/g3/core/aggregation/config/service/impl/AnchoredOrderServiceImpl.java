package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredOrder;
import com.yeepay.g3.core.aggregation.config.dao.AnchoredOrderDao;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity;
import com.yeepay.g3.core.aggregation.config.service.AnchoredApplyService;
import com.yeepay.g3.core.aggregation.config.service.AnchoredOrderService;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Mr.yin
 * @date: 2025/6/8  16:33
 */
@Service
public class AnchoredOrderServiceImpl implements AnchoredOrderService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private AnchoredOrderDao anchoredOrderDao;

    @Resource
    private AnchoredApplyService anchoredApplyService;

    @Override
    public AnchoredOrderEntity queryAnchoredOrderByRequestNo(String bizScene, String bizApplyNo) {
        return anchoredOrderDao.selectByBizSceneAndBizApplyNo(bizScene, bizApplyNo);
    }

    @Override
    public AnchoredOrderEntity queryAnchoredOrderByOrderNo(String orderNo) {
        return anchoredOrderDao.queryAnchoredOrderByOrderNo(orderNo);
    }

    @Override
    public void createAnchoredOrder(AnchoredOrder anchoredOrder) {
        AnchoredOrderEntity anchoredOrderEntity = AnchoredOrderEntity.build(anchoredOrder);
        List<AnchoredApplyEntity> anchoredApplyList = anchoredOrder.getAnchoredApplyList().stream().map(anchoredApply -> AnchoredApplyEntity.build(anchoredApply, anchoredOrder)).collect(Collectors.toList());
        logger.info("[挂靠请求] 落库订单 anchoredOrderEntity={}，anchoredApplyList={}", JsonUtils.toJSONString(anchoredOrderEntity), JsonUtils.toJSONString(anchoredApplyList));
        try {
            transactionTemplate.execute(status -> {
                anchoredOrderDao.createAnchoredOrder(anchoredOrderEntity);
                anchoredApplyService.batchCreateAnchoredApply(anchoredApplyList);
                return null;
            });
        } catch (DuplicateKeyException e) {
            logger.info(String.format("[存储挂靠请求] 并发操作 anchoredOrder={%s} cased by", JsonUtils.toJSONString(anchoredOrder)), e);
            throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
        }
    }

    @Override
    public Boolean updateAnchoredOrderFinish(String orderNo) {
        return 1 == anchoredOrderDao.updateAnchoredOrderFinish(orderNo);
    }

    @Override
    public List<AnchoredOrderEntity> selectUnFinishAnchoredOrderList(List<String> orderNoList, Date startTime, Date endTime) {
        return anchoredOrderDao.selectUnFinishAnchoredOrderList(orderNoList, startTime, endTime);
    }
}
