package com.yeepay.g3.core.aggregation.config.biz.atmanage.entry;

import com.yeepay.g3.core.aggregation.config.mq.event.AnchoredApplyResultMessage;

import java.util.Date;
import java.util.List;

/**
 * 补偿未完成的进件订单
 *
 * <AUTHOR>
 * @date 2025/6/2 22:20
 */
public interface EntryApplyCompensateBiz {

    /**
     * 补偿未完成的进件订单
     */
    void compensateEntryApply(Date startTime, Date endTime, List<String> applyNoList);

    /**
     * 补偿挂靠订单成功
     */
    void compensateEntryApplyAnchoredSuccess(AnchoredApplyResultMessage callBackBO);

}
