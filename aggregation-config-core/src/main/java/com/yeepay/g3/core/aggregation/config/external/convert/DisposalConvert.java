package com.yeepay.g3.core.aggregation.config.external.convert;

import com.google.common.collect.Lists;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryResultEntity;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalRecordEntity;
import com.yeepay.g3.facade.trade.bankcooper.param.WechatPlatformDisposalNotifyQueryParamDTO;
import com.yeepay.g3.facade.trade.bankcooper.result.PlatformDisposalDTO;
import com.yeepay.g3.facade.trade.bankcooper.result.WechatPlatformDisposalNotifyQueryResultDTO;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 17:43
 */
public class DisposalConvert {

    public static DisposalQueryResultEntity buildWechatDisposalResultDTO(WechatPlatformDisposalNotifyQueryResultDTO result) {

        if (result == null) {
            return null;
        }
        DisposalQueryResultEntity resultEntity = new DisposalQueryResultEntity();
        resultEntity.setTotalCount(result.getTotalCount());
        List<DisposalRecordEntity> recordEntityList = buildPunishRecordEntityList(result.getPlatformDisposalList());
        resultEntity.setDisposalRecordEntityList(recordEntityList);
        return resultEntity;
    }

    private static DisposalRecordEntity buildPunishRecordEntity(PlatformDisposalDTO platformDisposalDTO) {
        DisposalRecordEntity disposalRecordEntity = new DisposalRecordEntity();
        disposalRecordEntity.setYeepayMerchantNo(platformDisposalDTO.getYeepayMerchantNo());
        disposalRecordEntity.setYeepayMerchantName(platformDisposalDTO.getYeepayMerchantName());
        disposalRecordEntity.setCompanyName(platformDisposalDTO.getCompanyName());
        disposalRecordEntity.setChannelNo(platformDisposalDTO.getChannelNo());
        disposalRecordEntity.setReportMerchantNo(platformDisposalDTO.getReportMerchantNo());
        disposalRecordEntity.setPunishPlan(platformDisposalDTO.getPunishPlan());
        disposalRecordEntity.setLimitRecoverFunction(platformDisposalDTO.getRiskDescription());
        disposalRecordEntity.setPunishType(platformDisposalDTO.getEventType());
        disposalRecordEntity.setPunishTime(platformDisposalDTO.getPunishTime());
        return disposalRecordEntity;
    }


    private static List<DisposalRecordEntity> buildPunishRecordEntityList(List<PlatformDisposalDTO> platformDisposalDTOList) {

        if (CollectionUtils.isEmpty(platformDisposalDTOList)) {
            return Collections.emptyList();
        }

        List<DisposalRecordEntity> disposalRecordEntityList = Lists.newArrayList();
        for (PlatformDisposalDTO platformDisposalDTO : platformDisposalDTOList) {
            if (platformDisposalDTO == null) {
                continue;
            }
            DisposalRecordEntity entity = buildPunishRecordEntity(platformDisposalDTO);
            disposalRecordEntityList.add(entity);
        }

        return disposalRecordEntityList;

    }

    public static WechatPlatformDisposalNotifyQueryParamDTO buildWechatDisposalParamDTO(DisposalQueryEntity requestEntity) {
        WechatPlatformDisposalNotifyQueryParamDTO paramDTO = new WechatPlatformDisposalNotifyQueryParamDTO();
        paramDTO.setReportMerchantNo(requestEntity.getReportMerchantNo());
        paramDTO.setYeepayMerchantNo(requestEntity.getYeepayMerchantNo());
        paramDTO.setChannelNo(requestEntity.getChannelNo());
        paramDTO.setTopMerchantNo(requestEntity.getTopMerchantNo());
        paramDTO.setAgentMerchantNo(requestEntity.getAgentMerchantNo());

        paramDTO.setStartPunishTime(requestEntity.getStartTime());
        paramDTO.setEndPunishTime(requestEntity.getEndTime());
        paramDTO.setPageNum(requestEntity.getPageNum());
        paramDTO.setPageSize(requestEntity.getPageSize());

        paramDTO.setPunishPlan(requestEntity.getPunishPlan());
        paramDTO.setEventType(requestEntity.getPunishType());
        paramDTO.setCompanyName(requestEntity.getCompanyName());

        paramDTO.setQueryType(requestEntity.getQueryType());
        paramDTO.setWithSelf(requestEntity.getWithSelf());
        return paramDTO;
    }


}
