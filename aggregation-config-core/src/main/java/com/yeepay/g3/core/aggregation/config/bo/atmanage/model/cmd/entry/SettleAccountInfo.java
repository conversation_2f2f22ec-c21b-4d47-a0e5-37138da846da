package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.BankAccountTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.SettleAccountInfoDTO;
import lombok.*;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/5/27 18:58
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class SettleAccountInfo {
    /**
     * 银行账户类型
     */
    private final BankAccountTypeEnum bankAccountType;
    /**
     * 开户名称
     */
    private final String cardName;
    /**
     * 银行卡号
     */
    private final String cardNo;
    /**
     * 银行编码
     */
    private final String bankCode;

    /**
     * 开户行所在区
     * 微信直连&微信小程序B2B必填
     */
    private final String distinctCode;

    /**
     * 联行号
     */
    private final String branchCode;
    /**
     * 开户银行全称
     */
    private final String bankName;
    /**
     * 支行名称
     */
    private final String bankBranchName;

    public static SettleAccountInfo build(final SettleAccountInfoDTO settleAccountInfo) {
        BankAccountTypeEnum bankAccountType = StringUtils.isBlank(settleAccountInfo.getBankAccountType()) ? null :
                DocumentedEnum.fromValue(BankAccountTypeEnum.class, settleAccountInfo.getBankAccountType());

        return SettleAccountInfo.builder()
                .bankAccountType(bankAccountType)
                .cardName(settleAccountInfo.getCardName())
                .cardNo(settleAccountInfo.getCardNo())
                .bankCode(settleAccountInfo.getBankCode())
                .branchCode(settleAccountInfo.getBranchCode())
                .bankName(settleAccountInfo.getBankName())
                .bankBranchName(settleAccountInfo.getBankBranchName())
                .distinctCode(settleAccountInfo.getDistinctCode())
                .build();
    }
}
