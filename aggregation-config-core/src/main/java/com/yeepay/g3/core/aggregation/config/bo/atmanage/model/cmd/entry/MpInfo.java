package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.MpInfoDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 21:10
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class MpInfo implements Serializable {
    /**
     * 【服务商服务号或公众号AppID】 选填 string(256)
     * 1、服务商服务号或公众号AppID、商家服务号或公众号AppID，二选一必填；
     * 2、可填写当前服务商商户号已绑定的服务号或公众号AppID。
     */
    private String mpAppId;

    /**
     * 【商家服务号或公众号AppID】 选填 string(256)
     * 1、服务商服务号或公众号AppID、商家服务号或公众号AppID，二选一必填；
     * 2、可填写与商家主体一致且已认证的服务号或公众号AppID，需是已认证的服务号、政府或媒体类型的公众号；
     * 3、审核通过后，系统将发起特约商家商户号与该AppID的绑定（即配置为sub_appid），
     *    服务商随后可在发起支付时选择传入该appid，以完成支付，并获取sub_openid用于数据统计，营销等业务场景。
     */
    private String mpSubAppId;

    private String mpAppName;

    /**
     * 【服务号或公众号页面截图】 必填 array[string(1024)]
     * 1、请提供展示商品/服务的页面截图/设计稿（最多5张），若服务号或公众号未建设完善或未上线请务必提供；
     * 2、请填写通过图片上传API预先上传图片生成好的MediaID。
     */
    private List<String> mpPics;

    public static MpInfo build(MpInfoDTO mpInfo) {
        return MpInfo.builder()
                .mpAppId(mpInfo.getMpAppId())
                .mpSubAppId(mpInfo.getMpSubAppId())
                .mpAppName(mpInfo.getMpAppName())
                .mpPics(mpInfo.getMpPics())
                .build();
    }
}
