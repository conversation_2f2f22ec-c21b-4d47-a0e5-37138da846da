package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.WxIndirectConfigCmd;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/6/9 23:16
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class WechatIndirectSpecialParams implements ChannelSpecialParams {
    /**
     * 微信商户经营类目
     */
    private final String mcc;

    /**
     * 手续费规则id 微信费率规则号
     */
    private final String feeRuleId;

    private SettleAccountInfo settleAccountInfo;

    private final String extendInfo;

    public static WechatIndirectSpecialParams build(final WxIndirectConfigCmd cmd) {
        return WechatIndirectSpecialParams.builder()
                .mcc(cmd.getMcc())
                .feeRuleId(cmd.getFeeRuleId())
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .extendInfo(cmd.getExtendInfo())
                .build();
    }
}
