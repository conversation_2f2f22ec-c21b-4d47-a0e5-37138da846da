package com.yeepay.g3.core.aggregation.config.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * appid管控记录
 * @className AddidControlEntity
 * <AUTHOR>
 * @date 2024/12/18 11:02
 * @version 1.0
 */
@Data
public class AppidControlEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键，自动生成的唯一标识符
     */
    private Long id;

    /**
     * APPID
     */
    private String appid;

    /**
     * 管控开始时间
     */
    private Date controlStartTime;

    /**
     * 管控结束时间
     */
    private Date controlEndTime;

    /**
     * 通知类型（新增/修改）
     */
    private String notifyType;

    /**
     * 通知状态  被弃用了
     */
    private String notifyStatus;

    /**
     * 版本
     */
    private Long version;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date updateTime;
}
