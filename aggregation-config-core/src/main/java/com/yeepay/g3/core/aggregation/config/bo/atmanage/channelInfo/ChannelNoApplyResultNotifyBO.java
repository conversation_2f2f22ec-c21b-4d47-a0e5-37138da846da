package com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo;

import com.yeepay.g3.core.aggregation.config.mq.event.ChannelNoApplyResultEvent;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 渠道号申请结果通知业务对象
 * <AUTHOR>
 * @since 2025/5/27:16:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class ChannelNoApplyResultNotifyBO implements Serializable {

    private static final long serialVersionUID = -4335411319959525936L;

    /**
     * 商户编码（必填）
     */
    private String merchantNo;

    /**
     * 支付渠道（必填）
     */
    private String requestNo;

    /**
     * 机构类型
     */
    private String instituteType;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道号申请结果
     */
    private String applyResult;

    /**
     * 渠道号申请结果描述
     */
    private String applyResultDesc;

    public static ChannelNoApplyResultNotifyBO build(ChannelNoApplyResultEvent reqDTO) {

        return ChannelNoApplyResultNotifyBO.builder()
                .merchantNo(reqDTO.getChannelIdentifier())
                .requestNo(reqDTO.getRequestOrderNo())
//                .instituteType(reqDTO.getIn())
                .channelNo(reqDTO.getChannelNo())
                .applyResult(reqDTO.getApplyResult())
                .applyResultDesc(reqDTO.getApplyResultDes())
                .build();
    }

}
