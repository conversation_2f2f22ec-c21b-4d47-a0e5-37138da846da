package com.yeepay.g3.core.aggregation.config.external.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 客户中心配置结果
 *
 * @author: Mr.yin
 * @date: 2025/6/24  11:15
 */
@Data
public class CustomerConfigResultBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置开关
     * ON/OFF
     */
    private String configStatus;

    /**
     * 配置值
     */
    private String configValue;

}
