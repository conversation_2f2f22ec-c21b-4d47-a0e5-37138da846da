package com.yeepay.g3.core.aggregation.config.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Title : com.yeepay.g3.core.refund.processor.external.proxy
 * @Description :
 * @Company : 易宝支付(Yeepay)
 * <AUTHOR> jiafu.wu
 * @Since: 2020/6/2
 * @Version : 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ExternalSystemEnum {

    MERCHANTCENTER("客户中心"),
    YOP_NOTIFY("YOP通知"),
    BANKCHANNEL("银行子系统"),
    ;
    /**
     * 系统名
     */
    private final String sysName;
}
