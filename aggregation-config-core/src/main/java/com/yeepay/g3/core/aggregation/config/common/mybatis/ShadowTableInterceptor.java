package com.yeepay.g3.core.aggregation.config.common.mybatis;

import com.yeepay.g3.core.aggregation.config.utils.ConfigUtil;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
})
public class ShadowTableInterceptor implements org.apache.ibatis.plugin.Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Map<String, String> shadowTableConfig = ConfigUtil.getShadowTableConfig();
        if (!shadowTableConfig.isEmpty()) {
            Set<String> targetTableSet = shadowTableConfig.keySet();
            for (String target : targetTableSet) {
                Object[] args = invocation.getArgs();
                MappedStatement ms = (MappedStatement) args[0];
                String originalSql = ms.getBoundSql(args[1]).getSql();

                String suffix = shadowTableConfig.get(target);
                boolean sign = !originalSql.contains(target + suffix);
                // 如果 SQL 中包含目标表名，则修改表名，添加尾缀
                if ( sign &&  originalSql.contains(target)) {
                    // 替换表名，添加尾缀
                    String newSql = originalSql.replace(target, target + suffix);

                    // 创建新的 BoundSql 对象
                    BoundSql newBoundSql = new BoundSql(ms.getConfiguration(), newSql, ms.getBoundSql(args[1]).getParameterMappings(), args[1]);

                    // 使用反射修改 MappedStatement 的 SqlSource
                    updateMappedStatementSql(ms, newBoundSql);

                    // 更新 MappedStatement
                    args[0] = ms;
                }
            }
        }

        return invocation.proceed();  // 执行原始或修改后的 SQL
    }

    /**
     * 使用反射更新 MappedStatement 的 SQL
     */
    private void updateMappedStatementSql(MappedStatement ms, BoundSql newBoundSql) throws NoSuchFieldException, IllegalAccessException {
        // 通过反射获取 SqlSource 字段
        Field sqlSourceField = MappedStatement.class.getDeclaredField("sqlSource");
        sqlSourceField.setAccessible(true);

        // 创建新的 SqlSource 实例，并设置到 MappedStatement 中
        SqlSource newSqlSource = new CustomSqlSource(newBoundSql);
        sqlSourceField.set(ms, newSqlSource);
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);  // 包装目标对象
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以在此读取一些配置属性
    }

    /**
     * 自定义 SqlSource 实现，用于替换 MappedStatement 中的 SqlSource
     */
    public static class CustomSqlSource implements SqlSource {
        private final BoundSql boundSql;

        public CustomSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }
}
