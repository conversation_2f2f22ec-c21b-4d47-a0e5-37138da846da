package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryAuditStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.AlipayIndirectConfigCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.EntryApplyCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SiteInfo;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlag;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlagStatus;
import com.yeepay.g3.core.aggregation.config.utils.GMUtils;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/6 13:00
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@SuperBuilder
public class AliIndirectEntryApply extends BaseEntryApply<AliIndirectEntrySpecialParams> {

    /**
     * 支付宝商户经营类目
     */
    private final String mcc;

    /**
     * 商户等级
     * INDIRECT_LEVEL_M1
     * INDIRECT_LEVEL_M2
     * INDIRECT_LEVEL_M3
     * INDIRECT_LEVEL_M4
     * 支付宝必填；拟申请的间连商户等级，银联根据 申请等级决定转发报文要素，具体 申请结果以支付宝结果为准
     */
    private final String merchantLevel;

    /**
     * 站点信息
     * 支付宝线上报备费率时必填
     */
    private final List<SiteInfo> siteInfos;

    /**
     * 挂靠订单
     */
    private final AnchoredOrder anchoredOrder;


    private final Boolean isNeedTerminalReport;

    private final SettleAccountInfo settleAccountInfo;


    public AliIndirectEntryApply(ChannelEntryOrderEntity order, ChannelEntryApplyDetailEntity detailEntity) {
        super(order, detailEntity);
        AliIndirectEntrySpecialParams specialParams = JsonUtils.fromJson(GMUtils.decrypt(detailEntity.getChannelParams()), AliIndirectEntrySpecialParams.class);
        this.channelSpecialParams = specialParams;
        this.mcc = specialParams.getMcc();
        this.merchantLevel = specialParams.getMerchantLevel();
        this.siteInfos = specialParams.getSiteInfos();
        this.settleAccountInfo = specialParams.getSettleAccountInfo();
        this.isNeedTerminalReport = super.getTerminalReportFlag();
        //挂靠因为缺信息，现在不补了
        this.anchoredOrder = null;
    }

    public static AliIndirectEntryApply createAliIndirectEntryApply(final AlipayIndirectConfigCmd cmd,
                                                                    final String orderNo,
                                                                    final EntryApplyCmd entryApplyCmd) {
        String applyNo = "ALI" + UniqueNoGenerateUtils.getUniqueNo();
        long flag = EntryFlag.NONE.getBit();
        flag = Boolean.TRUE.equals(cmd.getIsNeedTerminalReport()) ? addTerminalReportFlag(flag) : flag;
        flag = Objects.nonNull(cmd.getAnchoredCmd()) ? addAnchoredApply(flag) : flag;
        flag = cmd.getBackupCount() != null ? addSpecifyBackupFlag(flag) : flag;
        flag = StringUtils.isNotBlank(cmd.getChannelNo()) || StringUtils.isNotBlank(cmd.getChannelIdentifier())
                ? addSpecifyChannelNo(flag) : flag;
        final AliIndirectEntryApplyBuilder<?, ?> builder = AliIndirectEntryApply.builder()
                .merchantInfo(entryApplyCmd.getMerchantInfo())
                .bizApplyNo(entryApplyCmd.getBizApplyNo())
                .bizScene(entryApplyCmd.getBizScene())
                .orderNo(orderNo)
                .applyNo(applyNo)
                .channelNo(cmd.getChannelNo())
                .channelIdentifier(cmd.getChannelIdentifier())
                .subjectInfo(entryApplyCmd.getSubjectInfo())
                .contactInfo(entryApplyCmd.getContactInfo())
                .entryType(cmd.getEntryType())
                .payScene(cmd.getPayScene())
                .payChannel(cmd.getPayChannel())
                .activityType(cmd.getActivityInfo().getActivityType())
                .activityInfo(cmd.getActivityInfo())
                .backupCounts(Optional.ofNullable(cmd.getBackupCount()).orElse(0))
                .mcc(cmd.getMcc())
                .merchantLevel(cmd.getMerchantLevel())
                .siteInfos(cmd.getSiteInfos())
                .isNeedTerminalReport(cmd.getIsNeedTerminalReport())
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .channelSpecialParams(AliIndirectEntrySpecialParams.build(cmd))
                .entryStatus(EntryStatusEnum.INIT)
                .entryAuditStatus(EntryAuditStatus.INIT)
                .flag(flag)
                .flagStatus(EntryFlagStatus.NONE.getBit())
                .extendInfo(cmd.getExtendInfo());

        if (Boolean.TRUE.equals(EntryTypeEnum.needAnchored(cmd.getEntryType()))) {
            AnchoredOrder anchoredOrder = AnchoredOrder.createAnchoredOrder(cmd.getAnchoredCmd(), applyNo);
            builder.anchoredOrder(anchoredOrder);
        }
        return builder.build();
    }
}
