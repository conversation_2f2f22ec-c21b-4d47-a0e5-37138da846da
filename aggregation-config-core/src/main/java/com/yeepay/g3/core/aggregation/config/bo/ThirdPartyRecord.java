package com.yeepay.g3.core.aggregation.config.bo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;


@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
public class ThirdPartyRecord {

    private Long id;


    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 请求编号
     */
    private String requestNo;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 状态
     * INIT - 初始化
     * SUCCESS - 成功
     * FAIL - 失败
     * PROCESSING - 处理中
     */
    private ThirdPartyRecordStatusEnum status;

    /**
     * 状态
     * INIT - 初始化
     * SUCCESS - 成功
     * FAIL - 失败
     * PROCESSING - 处理中
     */
    private ThirdPartyRecordStatusEnum oldStatus;

    /**
     * 内部渠道申请编号
     */
    private String innerChannelOrderNo;

    /**
     * 渠道申请编号
     */
    private String channelOrderNo;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;


    private Integer delFlag;
    /**
     * 渠道响应码
     */
    private String channelResponseCode;

    /**
     * 渠道响应消息
     */
    private String channelResponseMessage;

    /**
     * true代表需要创建记录
     */
    private Boolean newRecord;

    public void processing(final String innerChannelOrderNo) {
        Assert.isFalse(this.status.isFinish(), ResultCode.ENTRY_APPLY_FINISHED);
        this.oldStatus = this.status;
        this.status = ThirdPartyRecordStatusEnum.PROCESSING;
        this.innerChannelOrderNo = innerChannelOrderNo;
    }

    public void success(final String innerChannelOrderNo, final LocalDateTime finishTime) {
        // 现在场景通道可能失败变成功
        Assert.isFalse(ThirdPartyRecordStatusEnum.SUCCESS == this.status, ResultCode.ENTRY_APPLY_FINISHED);
        this.oldStatus = this.status;
        this.status = ThirdPartyRecordStatusEnum.SUCCESS;
        this.innerChannelOrderNo = innerChannelOrderNo;
        this.finishTime = finishTime;
    }

    public void fail(final String innerChannelOrderNo, final String channelResponseCode,
                     final String channelResponseMessage, final LocalDateTime finishTime) {
        Assert.isFalse(this.status.isFinish(), ResultCode.ENTRY_APPLY_FINISHED);
        this.oldStatus = this.status;
        this.status = ThirdPartyRecordStatusEnum.FAIL;
        this.innerChannelOrderNo = innerChannelOrderNo;
        this.channelResponseCode = channelResponseCode;
        this.channelResponseMessage = StringUtils.isNotBlank(channelResponseMessage) && channelResponseMessage.length() > 85 ? channelResponseMessage.substring(0, 85) : channelResponseMessage;
        this.finishTime = finishTime;
    }


}

