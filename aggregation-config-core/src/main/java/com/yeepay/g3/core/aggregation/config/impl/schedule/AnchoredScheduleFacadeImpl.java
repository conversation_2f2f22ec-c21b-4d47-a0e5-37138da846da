package com.yeepay.g3.core.aggregation.config.impl.schedule;

import com.google.common.collect.Lists;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.AnchoredBiz;
import com.yeepay.g3.facade.aggregation.config.facade.schedule.AnchoredScheduleFacade;
import com.yeepay.g3.utils.common.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: Mr.yin
 * @date: 2025/6/11  17:57
 */
@Slf4j
@Component
public class AnchoredScheduleFacadeImpl implements AnchoredScheduleFacade {

    @Resource
    private AnchoredBiz anchoredBiz;

    @Override
    public void compensateAnchoredOrder(Integer startTime, Integer endTime, String startDateStr, String endDateStr, String orderNoListStr) {
        log.info("[补偿][处理未闭环的挂靠订单][handleAnchoredOrder]  startTime={}, endTime={}，startDateStr={},endDateStr={},orderNo={}", startTime, endTime, startDateStr, endDateStr, orderNoListStr);
        Date startTimeDate = null;
        Date endTimeDate = null;
        try {
            if (StringUtils.hasLength(startDateStr)) {
                startTimeDate = DateUtils.parseDate(startDateStr, DateUtils.DATE_FORMAT_DATETIME);
                endTimeDate = DateUtils.parseDate(endDateStr, DateUtils.DATE_FORMAT_DATETIME);
            } else if (null != startTime) {
                Date now = new Date();
                startTimeDate = DateUtils.addDay(now, -startTime);
                endTimeDate = DateUtils.addDay(now, -endTime);
            }
        } catch (Exception e) {
            log.warn("[补偿][处理未闭环的挂靠订单][handleAnchoredOrder] 时间转换异常 startTime={}, endTime={}，startDateStr={},endDateStr={},orderNo={}  异常", startTime, endTime, startDateStr, endDateStr, orderNoListStr, e);
            throw new IllegalArgumentException("时间转换异常");
        }
        List<String> orderNoList = null;
        if (StringUtils.hasLength(orderNoListStr)) {
            orderNoList = Lists.newArrayList(orderNoListStr.split(","));
        }
        if (CollectionUtils.isEmpty(orderNoList) && null == startTimeDate) {
            log.warn("[补偿][处理未闭环的挂靠订单][handleAnchoredOrder] 时间或者单号二选一必填");
            throw new IllegalArgumentException("时间或者单号二选一必填");
        }
        anchoredBiz.compensateAnchoredOrder(startTimeDate, endTimeDate, orderNoList);
    }
}
