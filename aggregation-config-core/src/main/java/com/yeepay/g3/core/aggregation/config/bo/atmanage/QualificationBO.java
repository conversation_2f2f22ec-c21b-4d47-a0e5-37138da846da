package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * description: 资质信息DTO
 *
 * <AUTHOR>
 * @since 2025/5/20:18:56
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@Builder
public class QualificationBO implements Serializable {

    private static final long serialVersionUID = 305245830986347021L;

    /**
     * 行业类目id
     */
    private String categoryId;

    /**
     * 行业经营许可证资质照片列表
     */
    private List<String> operationCopyList;

//    public static List<QualificationBO> buildAuthList(List<QualificationDTO> qualificationDTOS) {
//        if (qualificationDTOS == null || qualificationDTOS.isEmpty()) {
//            return new ArrayList<>();
//        }
//        return qualificationDTOS.stream().map(QualificationBO::buildAuth).collect(Collectors.toList());
//    }
//    public static QualificationBO buildAuth(QualificationDTO qualificationDTO) {
//        if (qualificationDTO == null) {
//            return null;
//        }
//        return QualificationBO.builder()
//                .categoryId(qualificationDTO.getCategoryId())
//                .operationCopyList(qualificationDTO.getOperationCopyList())
//                .build();
//    }
}
