package com.yeepay.g3.core.aggregation.config.impl.schedule;

import com.yeepay.g3.core.aggregation.config.biz.atmanage.AuthBiz;
import com.yeepay.g3.facade.aggregation.config.facade.schedule.AuthScheduleFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: 聚合配置的认证定时任务实现类
 * <AUTHOR>
 * @since 2025/5/28:19:41
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthScheduleFacadeImpl implements AuthScheduleFacade {

    private final AuthBiz authBiz;

    @Override
    public void handleAuthResultSchedule(String requestNo, Integer startTime, Integer endTime) {
        log.info("handleAuthResultSchedule: 开始处理认证结果定时任务, requestNo={}, startTime={}, endTime={}", requestNo, startTime, endTime);
//        Date now = new Date();
//        Date startTimeDate = DateUtils.addDay(now, -startTime);
//        Date endTimeDate = DateUtils.addDay(now, -endTime);
//        authBiz.handleAuthResult(requestNo, startTimeDate, endTimeDate);
    }
}
