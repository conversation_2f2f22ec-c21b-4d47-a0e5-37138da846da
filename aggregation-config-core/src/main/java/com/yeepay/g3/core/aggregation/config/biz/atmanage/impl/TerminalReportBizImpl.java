package com.yeepay.g3.core.aggregation.config.biz.atmanage.impl;

import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.TerminalReportBiz;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.convert.ThirdPartyRecordConvert;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.enums.TerminalReportStatus;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.external.ChannelTerminalReportExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.TerminalReportSubmitResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelTerminalReportResultBO;
import com.yeepay.g3.core.aggregation.config.mq.event.TerminalReportMessage;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 终端报备处理
 *
 * @author: Mr.yin
 * @date: 2025/6/16  18:26
 */
@Component
public class TerminalReportBizImpl implements TerminalReportBiz {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EntryResultProducer entryResultProducer;

    @Resource
    private ChannelEntryOrderService channelEntryOrderService;

    @Resource
    private ThirdPartyRecordService thirdPartyRecordService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ChannelTerminalReportExternal channelTerminalReportExternal;

    @Override
    public TerminalReportStatus applyChannelTerminalReport(BaseEntryApply<? extends ChannelSpecialParams> entryApply) {
        logger.info("[终端报备],开始处理,entryApply={}", JsonUtils.toJSONString(entryApply));
        List<ThirdPartyRecord> recordList = thirdPartyRecordService.queryNotFinishOrSuccessThirdPartyRecordByApplyNo(entryApply.getApplyNo(), ThirdPartyBusinessTypeEnum.CHANNEL_TERMINAL_REPORT.getDocument());
        ThirdPartyRecord thirdPartyRecord = null;
        if (CollectionUtils.isEmpty(recordList)) {
            thirdPartyRecord = ThirdPartyRecordConvert.createTerminalReportThirdPartyRecord(entryApply, ThirdPartyBusinessTypeEnum.CHANNEL_TERMINAL_REPORT);
            saveTerminalSubmitRecord(entryApply, thirdPartyRecord);
            RemoteResult<TerminalReportSubmitResultBO> result = channelTerminalReportExternal.applyChannelTerminalReport(entryApply, thirdPartyRecord.getRequestNo());
            return handleTerminalSubmitResult(entryApply, thirdPartyRecord, result);

        }
        Optional<ThirdPartyRecord> optionalRecord = recordList.stream()
                .filter(record -> record.getStatus() == ThirdPartyRecordStatusEnum.SUCCESS)
                .findAny();
        if (optionalRecord.isPresent()) {
            logger.warn("[终端报备] [并发操作] 已存在完成记录: {}", optionalRecord.get().getRequestNo());
            throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
        }
        /*订单存在则补查*/
        thirdPartyRecord = recordList.get(0);
        RemoteResult<ChannelTerminalReportResultBO> result = channelTerminalReportExternal.queryChannelTerminalReport(entryApply, thirdPartyRecord.getRequestNo());
        return handleTerminalQueryResult(entryApply, thirdPartyRecord, result);

    }

    /**
     * 保存终端报备提交记录
     */
    private void saveTerminalSubmitRecord(BaseEntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord) {
        logger.info("[终端报备],保存终端报备提交记录,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
        transactionTemplate.execute(transactionStatus -> {
            /*加锁*/
            ChannelEntryApplyDetailEntity applyDetailEntity = channelEntryOrderService.selectChannelEntryApplyDetailEntityForUpdate(entryApply.getApplyNo());
            List<ThirdPartyRecord> currentRecords = thirdPartyRecordService.queryNotFinishOrSuccessThirdPartyRecordByApplyNo(entryApply.getApplyNo(), ThirdPartyBusinessTypeEnum.CHANNEL_TERMINAL_REPORT.getDocument());
            if (!CollectionUtils.isEmpty(currentRecords)) {
                logger.info("[终端报备],落库终端申请，并发操作,currentRecords={}", JsonUtils.toJSONString(currentRecords));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            thirdPartyRecordService.saveThirdPartyRecord(thirdPartyRecord);
            return null;
        });
    }

    /**
     * 处理终端报备的查询结果
     *
     * @return
     */
    private TerminalReportStatus handleTerminalQueryResult(BaseEntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord, RemoteResult<ChannelTerminalReportResultBO> result) {
        if (!result.isSuccess()) {
            logger.info("[终端报备],查询报备记录失败 applyNo={},requestNo={} ", entryApply.getApplyNo(), thirdPartyRecord.getRequestNo());
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询终端报备记录失败");
        } else {
            ChannelTerminalReportResultBO reportResultBO = result.getData();
            if (null == reportResultBO) {
                logger.info("[终端报备],查询报备记录不存在 走再次提交流程 applyNo={},requestNo={} ", entryApply.getApplyNo(), thirdPartyRecord.getRequestNo());
                RemoteResult<TerminalReportSubmitResultBO> submitResult = channelTerminalReportExternal.applyChannelTerminalReport(entryApply, thirdPartyRecord.getRequestNo());
                return handleTerminalSubmitResult(entryApply, thirdPartyRecord, submitResult);
            }
            if (TerminalReportStatus.SUCCESS == reportResultBO.getStatus() || TerminalReportStatus.PARTY_SUCCESS == reportResultBO.getStatus()) {
                handleSuccessTerminalReport(reportResultBO.getBankOrderNo(), entryApply, thirdPartyRecord);
            } else if (TerminalReportStatus.FAIL == reportResultBO.getStatus()) {
                thirdPartyRecord.fail(reportResultBO.getBankOrderNo(), reportResultBO.getFailCode(), reportResultBO.getFailMessage(), LocalDateTime.now());
                if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                    logger.warn("[终端报备],查询到终端报备结果为失败，更新失败结果,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                    throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
                }
            }
            return reportResultBO.getStatus();
        }
    }

    /**
     * 处理 终端提交的结果
     *
     * @return
     */
    private TerminalReportStatus handleTerminalSubmitResult(BaseEntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord, RemoteResult<TerminalReportSubmitResultBO> result) {
        if (result.isSuccess()) {
            TerminalReportSubmitResultBO submitResultBO = result.getData();
            if (TerminalReportStatus.SUCCESS == submitResultBO.getStatus()) {
                logger.info("[终端报备],报备同步成功这种没有回调呢,code ={} getRequestNo={}", result.getChannelCode(), thirdPartyRecord.getRequestNo());
                handleSuccessTerminalReport(submitResultBO.getChannelOrderNo(), entryApply, thirdPartyRecord);
            }
            logger.info("[终端报备],提交发起完成,terminalStatus={}", submitResultBO.getStatus());
            return submitResultBO.getStatus();
        } else {
            logger.info("[终端报备],提交报备失败,code ={} getRequestNo={}", result.getChannelCode(), thirdPartyRecord.getRequestNo());
            thirdPartyRecord.fail(null, result.getChannelCode(), result.getChannelMessage(), LocalDateTime.now());
            if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                logger.warn("[终端报备],查询到终端报备结果为失败，更新失败结果,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            logger.info("[终端报备],提交发起完成,terminalStatus={}", TerminalReportStatus.SUBMIT_FAIL);
            return TerminalReportStatus.SUBMIT_FAIL;
        }
    }

    @Override
    public void callBackTerminalReport(TerminalReportMessage callBackBO) {
        ThirdPartyRecordStatusEnum thirdStatus = null;
        if ("SUCCESS".equals(callBackBO.getTerminalStatus())) {
            thirdStatus = ThirdPartyRecordStatusEnum.SUCCESS;
        } else if ("FAIL".equals(callBackBO.getTerminalStatus())) {
            thirdStatus = ThirdPartyRecordStatusEnum.FAIL;
        } else {
            logger.warn("[终端报备],接收到处理不了的状态 callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        if (!"WECHAT".equals(callBackBO.getBankCode()) && !"ALIPAY".equals(callBackBO.getBankCode())) {
            logger.warn("[终端报备],接收到处理不了的银行 callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        ThirdPartyRecord thirdPartyRecord = thirdPartyRecordService.queryThirdRecordByRequestNo(callBackBO.getRequestOrderNo(), ThirdPartyBusinessTypeEnum.CHANNEL_TERMINAL_REPORT.name());
        if (null == thirdPartyRecord) {
            logger.warn("[终端报备],未查询到对应的第三方报备记录,callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        if (ThirdPartyRecordStatusEnum.SUCCESS == thirdPartyRecord.getStatus()) {
            logger.info("[终端报备],第三方报备记录已处理成功,callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        if (thirdStatus == thirdPartyRecord.getStatus()) {/*失败的那种*/
            logger.info("[终端报备],第三方报备记录已处理,callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        BaseEntryApply<? extends ChannelSpecialParams> entryApply = channelEntryOrderService.getChannelEntryApply(thirdPartyRecord.getApplyNo());
        logger.info("[终端报备],查询到当前的申请记录,channelEntryOrder={}", JsonUtils.toJSONString(entryApply));
        /*变更结果*/
        if (StringUtils.isNotBlank(entryApply.getChannelMchNos()) && !Lists.newArrayList(entryApply.getChannelMchNos().split(",")).contains(callBackBO.getReportMerchantNo())) {
            logger.info("[终端报备],报备商户号不一致的消息本期丢掉 applyNo={},ChannelMchNos={},callBackBO={}", entryApply.getApplyNo(), entryApply.getChannelMchNos(), JsonUtils.toJSONString(callBackBO));
            return;
        }

        if (ThirdPartyRecordStatusEnum.FAIL == thirdStatus) {
            logger.info("[终端报备],失败,准备更新结果 requestNo={},applyNo={}", thirdPartyRecord.getRequestNo(), entryApply.getApplyNo());
            thirdPartyRecord.fail(callBackBO.getBankOrderNo(), callBackBO.getBizCode(), callBackBO.getBizMsg(), LocalDateTime.now());
            if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                logger.warn("[终端报备],失败，第三方报备记录更新失败,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
        } else if (ThirdPartyRecordStatusEnum.SUCCESS == thirdStatus) {
            handleSuccessTerminalReport(callBackBO.getBankOrderNo(), entryApply, thirdPartyRecord);
        }


    }

    /**
     * 处理终端报备成功
     */
    private void handleSuccessTerminalReport(String bankOrderNo, BaseEntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord) {
        logger.info("[终端报备],成功,准备更新结果 ,requestNo={},applyNo={}", thirdPartyRecord.getRequestNo(), entryApply.getApplyNo());
        thirdPartyRecord.success(bankOrderNo, LocalDateTime.now());
        transactionTemplate.execute(transactionStatus -> {
            if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                logger.warn("[终端报备],成功，第三方报备记录更新失败,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            /*需要加查询锁更新一下*/
            ChannelEntryApplyDetailEntity currentEntity = channelEntryOrderService.selectChannelEntryApplyDetailEntityForUpdate(entryApply.getApplyNo());
            entryApply.flashCurrentFlagStatusAndEntryStatus(currentEntity.getFlagStatus(), DocumentedEnum.fromValueOfNullable(EntryStatusEnum.class, currentEntity.getStatus()));
            entryApply.terminalReportSuccess();
            if (0 == channelEntryOrderService.updateChannelEntryApplyDetailFlagStatus(entryApply)) {
                logger.warn("[终端报备],成功，进件申请更新失败,baseEntryApply={}", JsonUtils.toJSONString(entryApply));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            return null;
        });

        entryResultProducer.sendEntryNotifyMessage(entryApply, EntryNotifyBizTypeEnum.TERMINAL_REPORT);
    }
}
