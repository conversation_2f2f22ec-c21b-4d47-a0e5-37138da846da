package com.yeepay.g3.core.aggregation.config.mq.event;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 聚合返回的挂靠商编信息
 */
@Data
public class AnchoredMerchantInfoMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商编
     */
    private String merchantNo;

    /**
     * 挂靠商户的渠道号
     */
    private String anchoredChannelNo;

    /**
     * 挂靠商户的商户号
     */
    private String anchoredInstitutionMerchantNo;

    /**
     * 挂靠商户编号
     */
    private String anchoredMerchantNo;

    /**
     * 同主体转换出挂靠商编
     *
     * @param aggSameReportRespBO
     * @return
     */
    public static AnchoredMerchantInfoMessage covert(AggAnchoredMerchantInfo aggSameReportRespBO,String merchantNo) {
        AnchoredMerchantInfoMessage payAnchoredMerchantInfo = new AnchoredMerchantInfoMessage();
        payAnchoredMerchantInfo.setMerchantNo(merchantNo);
        payAnchoredMerchantInfo.setAnchoredChannelNo(aggSameReportRespBO.getAnchoredChannelNo());
        payAnchoredMerchantInfo.setAnchoredInstitutionMerchantNo(aggSameReportRespBO.getInstitutionMchNo());
        payAnchoredMerchantInfo.setAnchoredMerchantNo(aggSameReportRespBO.getAnchoredMerchantNo());
        return payAnchoredMerchantInfo;
    }

}
