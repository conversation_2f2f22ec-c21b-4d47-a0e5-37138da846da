package com.yeepay.g3.core.aggregation.config.biz.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultNotifyBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoQueryBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoQueryResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.QueryChannelNoApplyResultBO;

import java.util.Date;
import java.util.List;

/**
 * description: 渠道号管理业务接口
 *
 * <AUTHOR>
 * @since 2025/5/27:16:57
 * Company: 易宝支付(YeePay)
 */
public interface ChannelNoBiz {

    /**
     * 申请渠道号
     *
     * @param channelInfo 渠道号申请信息
     */
    void applyChannelNo(ChannelNoApplyBO channelInfo);

    /**
     * 渠道号申请通知
     */
    void applyChannelResultNotify(ChannelNoApplyResultNotifyBO notifyBO);

    /**
     * 查询渠道号申请结果
     *
     * @param queryBO 查询条件
     * @return 渠道号申请结果
     */
    ChannelNoApplyResultBO queryChannelApplyResult(QueryChannelNoApplyResultBO queryBO);

    /**
     * 定时任务处理渠道号申请结果
     *
     * @param requestNo 申请单号
     */
    void handleChannelNoApplyResult(String requestNo, Date startTime, Date endTime);

    //------------------------------------以下为渠道号查询相关接口--------------------------------------------------------

    /**
     * 查询渠道号列表
     *
     * @param queryBO 查询条件
     * @return 渠道号列表
     */
    List<ChannelNoQueryResultBO> queryChannelNoList(ChannelNoQueryBO queryBO);

    /**
     * 查询直连配置渠道号
     */
    String queryChannelNoByDirect(PayChannelEnum payChannel, String industryLine, PaySceneEnum paySceneEnum);

    /**
     * 查询微信B2B配置渠道号
     *
     * @return 微信B2B配置渠道号
     */
    String queryChannelNoByWechatB2B();

    /**
     * 查询数币配置渠道号
     *
     * @return 数币配置渠道号
     */
    String queryChannelNoByDigitalCurrency();

    /**
     * 取默认配置渠道号
     *
     * @param payChannel   渠道
     * @param payScene     业务场景
     * @param activityType 活动类型
     */
    String queryChannelNoBySceneAndActivity(PayChannelEnum payChannel, PaySceneEnum payScene,
                                            ActivityTypeEnum activityType, String industryLine);

    /**
     * 查询渠道号Info
     *
     * @return 渠道号信息
     */
    List<ChannelNoInfoBO> getChannelNoInfo(String channelNo);

    /**
     * 查询渠道号Info
     *
     * @return 渠道号信息
     */
    ChannelNoInfoBO getChannelNoInfo(String channelNo, PayChannelEnum payChannel, PaySceneEnum payScene,
                                     ActivityTypeEnum activityType);

    /**
     * 校验渠道号是否存在
     *
     * @return 是否存在
     */
    Boolean validateChannelNo(String channelNo);

}
