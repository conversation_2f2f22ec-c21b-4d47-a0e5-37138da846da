package com.yeepay.g3.core.aggregation.config.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-26 20:34
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Data
public class MerchantGroupEntity implements Serializable {

    private static final long serialVersionUID = -6715886644180877267L;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     *    <p>1号索引=firstMerchantNo</p>
     *    <p>2号索引=secondMerchantNo</p>
     *     <p>3号索引=threeMerchantNo</p>
     * 商户组
     */
    private List<String> group = new LinkedList<>();
}
