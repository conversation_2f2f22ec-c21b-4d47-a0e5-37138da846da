package com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayChannelInfoDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * description: 渠道号信息业务对象
 *
 * <AUTHOR>
 * @since 2025/6/26:16:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@Builder
@Slf4j
public class ChannelNoInfoBO implements Serializable {

    private static final long serialVersionUID = 8913627393707920121L;
    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道号归属商编
     */
    private String merchantNo;

    /**
     * 支付渠道（必填）
     */
    private PayChannelEnum payChannel;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型（必填）
     */
    private ActivityTypeEnum activityType;

    /**
     * 通道费率
     */
    private String channelFeeType;

    /**
     * 通道活动类型
     */
    private String channelActivityType;

    /**
     * 将查询结果转换为业务对象
     *
     * @param infoList 查询结果
     * @return 渠道号信息业务对象
     */
    public static List<ChannelNoInfoBO> buildList(List<OpenPayChannelInfoDTO> infoList) {
        List<ChannelNoInfoBO> resultList = new ArrayList<>();
        if (CheckUtils.isEmpty(infoList)) {
            return resultList;
        }

        for (OpenPayChannelInfoDTO dto : infoList) {
            SceneAndActivityTypeBO sceneAndPromotion = SceneAndActivityUtils.getSceneAndPromotionWithoutNull(
                    dto.getFeeType(), dto.getPromotionType());
            if (sceneAndPromotion == null) {
                log.warn("渠道号信息转换失败，渠道信息：{}", JsonUtils.toJSONString(dto));
                continue;
            }
            ChannelNoInfoBO channelNoInfoBO = ChannelNoInfoBO.builder()
                    .channelNo(dto.getChannelNo())
                    .merchantNo(dto.getChannelIdentifier())
                    .payChannel(sceneAndPromotion.getChannel())
                    .payScene(sceneAndPromotion.getPayScene())
                    .activityType(sceneAndPromotion.getActivityType())
                    .channelFeeType(dto.getFeeType())
                    .channelActivityType(dto.getPromotionType())
                    .build();
            resultList.add(channelNoInfoBO);
        }
        return resultList;
    }

}
