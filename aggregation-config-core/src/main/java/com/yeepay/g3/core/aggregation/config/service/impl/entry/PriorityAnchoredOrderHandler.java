package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.annotation.EntryApplyProcess;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.AnchoredBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyRespBO;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.EntryOrderHandler;
import com.yeepay.g3.core.aggregation.config.service.impl.ChannelEntryApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:50
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
@EntryApplyProcess(EntryTypeEnum.PRIORITY_ANCHORED)
public class PriorityAnchoredOrderHandler implements EntryOrderHandler {
    private final ChannelEntryApplyService channelEntryApplyService;
    private final AnchoredBiz anchoredBiz;
    private final ChannelEntryOrderService channelEntryOrderService;
    private final EntryResultProducer entryResultProducer;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void handle(final EntryApplyContext<? extends EntryApply<? extends ChannelSpecialParams>> context) {
        log.info("进件类型：PRIORITY_ANCHORED 开始处理: 请求参数:{}", JsonUtils.toJSONString(context.getEntryApply()));
        Assert.notNull(context.getEntryApply().getAnchoredOrder(), "优先挂靠类型,挂靠信息不能为空");
        //挂靠
        log.info("进件类型：PRIORITY_ANCHORED 进件申请单:{} 挂靠开始处理", context.getEntryApply().getApplyNo());
        AnchoredApplyRespBO anchoredApplyRespBO;
        try {
            anchoredApplyRespBO = anchoredBiz.anchoredApply(context.getEntryApply().getAnchoredOrder());
            if (AnchoredStatus.SUCCESS.getDocument().equals(anchoredApplyRespBO.getApplyStatus())) {
                final BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply = (BaseEntryApply<? extends ChannelSpecialParams>) context.getEntryApply();
                transactionTemplate.execute(transactionStatus -> {
                    /*需要加查询锁更新一下*/
                    ChannelEntryApplyDetailEntity currentEntity = channelEntryOrderService.selectChannelEntryApplyDetailEntityForUpdate(baseEntryApply.getApplyNo());
                    baseEntryApply.flashCurrentFlagStatusAndEntryStatus(currentEntity.getFlagStatus(), DocumentedEnum.fromValueOfNullable(EntryStatusEnum.class, currentEntity.getStatus()));
                    baseEntryApply.anchoredSuccess(anchoredApplyRespBO.getOrderNo());
                    if (0 == channelEntryOrderService.updateChannelEntryApplyDetailFlagStatus(baseEntryApply)) {
                        log.warn("[挂靠],成功，进件申请更新失败, 并发操作 baseEntryApply={}", JsonUtils.toJSONString(baseEntryApply));
                        throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
                    }
                    return null;
                });
                if (context.getEntryApply().getEntryStatus().finalStatus()) {
                    entryResultProducer.sendEntryNotifyMessage((BaseEntryApply<? extends ChannelSpecialParams>) context.getEntryApply(), EntryNotifyBizTypeEnum.ENTRY);
                }
                return;
            }

        } catch (Exception e) {
            log.error("进件类型：PRIORITY_ANCHORED 挂靠处理失败，进件申请单:{} cased by", context.getEntryApply().getApplyNo(), e);
        }


        log.info("进件类型：PRIORITY_ANCHORED 挂靠失败，进件申请单:{}开始处理", context.getEntryApply().getApplyNo());
        // 挂靠失败 进件
        channelEntryApplyService.entryApply(context);
    }
}
