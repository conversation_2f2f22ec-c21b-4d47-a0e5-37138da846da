package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.external.bo.DivideCreateSubmitResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelDivideInfo;

/**
 * 通道分帐方创建相关接口
 */

public interface ChannelDivideExternal {

    /**
     * 创建分帐方接口
     * 840005等不到回调，直接成功
     */
    RemoteResult<DivideCreateSubmitResultBO> createDivideReceiver(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo);


    /**
     * 查询 渠道的 分帐方信息
     * 请注意，当查询结果不存在时 data为空
     *
     * @return
     */
    RemoteResult<ChannelDivideInfo> queryDivideReceiver(String merchantNo, String channelNo, String requestNo);
}
