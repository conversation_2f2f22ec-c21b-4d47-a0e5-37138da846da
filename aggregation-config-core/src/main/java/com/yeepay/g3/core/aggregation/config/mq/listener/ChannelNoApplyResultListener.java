package com.yeepay.g3.core.aggregation.config.mq.listener;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoApplyResultNotifyBO;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelNoApplyResultEvent;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description: 渠道号申请结果监听
 * <AUTHOR>
 * @since 2025/6/15:20:30
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ChannelNoApplyResultListener implements MessageListenerConcurrently {


    private final ChannelNoBiz channelNoBiz;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        LogGuidUtil.clearLogContext();
        MessageExt msg = list.get(0);
        String message = new String(msg.getBody());
        log.info("ChannelNoApplyResultListener接收到了消息[{}]，msgId={}", message, msg.getMsgId());
        ChannelNoApplyResultEvent callBackBO = JsonUtils.fromJson(message, ChannelNoApplyResultEvent.class);
        Assert.notNull(callBackBO, ResultCode.PARAM_VALID_ERROR, "回调参数解析异常");
        ChannelNoApplyResultNotifyBO notifyBO = ChannelNoApplyResultNotifyBO.build(callBackBO);
        channelNoBiz.applyChannelResultNotify(notifyBO);
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
