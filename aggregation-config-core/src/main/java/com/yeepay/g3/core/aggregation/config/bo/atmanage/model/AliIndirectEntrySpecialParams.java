package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.AlipayIndirectConfigCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SiteInfo;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6 13:00
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class AliIndirectEntrySpecialParams implements ChannelSpecialParams {
    /**
     * 支付宝商户经营类目
     */
    private final String mcc;

    /**
     * 商户等级
     * INDIRECT_LEVEL_M1
     * INDIRECT_LEVEL_M2
     * INDIRECT_LEVEL_M3
     * INDIRECT_LEVEL_M4
     * 支付宝必填；拟申请的间连商户等级，银联根据 申请等级决定转发报文要素，具体 申请结果以支付宝结果为准
     */
    private final String merchantLevel;

    /**
     * 站点信息
     * 支付宝线上报备费率时必填
     */
    private final List<SiteInfo> siteInfos;

    private final SettleAccountInfo settleAccountInfo;

    private final String extendInfo;

    public static AliIndirectEntrySpecialParams build(final AlipayIndirectConfigCmd cmd) {
        return AliIndirectEntrySpecialParams.builder()
                .mcc(cmd.getMcc())
                .merchantLevel(cmd.getMerchantLevel())
                .siteInfos(cmd.getSiteInfos())
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .extendInfo(cmd.getExtendInfo())
                .build();
    }
}
