package com.yeepay.g3.core.aggregation.config.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.config.ConfigParam;
import com.yeepay.g3.utils.config.ConfigurationUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description : 通用工具类
 * @Company : 易宝支付(Yeepay)
 * <AUTHOR> ziyu.qiu
 * @Since: 2020/9/1
 * @Version : 1.0.0
 */

@Slf4j
public class ConfigUtil {

    private ConfigUtil() {
    }

    /**
     * 邮件通知 统一配置键
     */
    private static final String MAIL_NOTIFY_CONFIG_KEY = "AGGREGATION_PAY_MAIL_NOTIFY";

    /**
     * 启用影子表配置
     */
    private static final String SHADOW_TABLE_CONFIG = "SHADOW_TABLE_CONFIG";

    /**
     * 所有渠道和场景和活动类型的管理和映射
     */
    private static final String AGGREGATION_CONFIG_CHANNEL_SCENE_ACTIVITY_TYPE = "AGGREGATION_CONFIG_CHANNEL_SCENE_ACTIVITY_TYPE";

    /**
     * 微信B2B渠道号
     */
    private static final String WECHAT_B2B_CHANNEL_NO = "AGGREGATION_CONFIG_WECHAT_B2B_CHANNEL_NO";

    /**
     * 数币渠道号
     */
    private static final String DIGITAL_CURRENCY_CHANNEL_NO = "AGGREGATION_CONFIG_DIGITAL_CURRENCY_CHANNEL_NO";

    /**
     * 默认渠道号
     */
    private static final String DEFAULT_CHANNEL_NO = "AGGREGATION_CONFIG_DEFAULT_CHANNEL_NO_NORMAL";

    /**
     * 航旅默认渠道号
     */
    private static final String DEFAULT_CHANNEL_NO_HL = "AGGREGATION_CONFIG_DEFAULT_CHANNEL_NO_HL";

    /**
     * 邮箱用户名
     */
    public static final String USER_NAME = "userName";
    /**
     * 邮箱秘钥
     */
    public static final String SECRET_KEY = "secretKey";
    /**
     * 邮箱规则名称
     */
    public static final String NOTIFY_RULE_NAME = "notifyRuleName";

    /**
     * 获取同主体跨SAAS范围配置
     */
    private static final String SAME_SUBJECT_DESIGNATE_SCOPE_CONFIG_KEY = "AGGREGATION_CONFIG_SAME_SUBJECT_DESIGNATE_SCOPE_CONFIG";

    /**
     * 按照商编配置的备份数量
     */
    private static final String MERCHANT_BACK_UP_COUNT_CONFIG_KEY = "AGGREGATION_CONFIG_MERCHANT_BACK_UP_COUNT";

    /**
     * 按照行业线名称配置的备份数量
     */
    private static final String PRODUCT_LINE_BACK_UP_COUNT_CONFIG_KEY = "AGGREGATION_CONFIG_PRODUCT_LINE_BACK_UP_COUNT";

    private static final Map<String, String> MAIL_NOTIFY_CONFIG_MAP_DEFAULT = new HashMap<>();

    static {
        MAIL_NOTIFY_CONFIG_MAP_DEFAULT.put(USER_NAME, "aggregation-pay");
        MAIL_NOTIFY_CONFIG_MAP_DEFAULT.put(SECRET_KEY, "aggregation-pay-hessian");
        MAIL_NOTIFY_CONFIG_MAP_DEFAULT.put(NOTIFY_RULE_NAME, "aggregation_appid_management");
    }

    private static final Map<String, String> SAME_SUBJECT_DESIGNATE_SCOPE_MAP = new HashMap<>();

    static {
        SAME_SUBJECT_DESIGNATE_SCOPE_MAP.put("10088538110", "10086270301");
        SAME_SUBJECT_DESIGNATE_SCOPE_MAP.put("10088530477", "10086270301");
        SAME_SUBJECT_DESIGNATE_SCOPE_MAP.put("10088240690", "10086270301");
        SAME_SUBJECT_DESIGNATE_SCOPE_MAP.put("10088240689", "10086270301");
        SAME_SUBJECT_DESIGNATE_SCOPE_MAP.put("10088240687", "10086270301");
        SAME_SUBJECT_DESIGNATE_SCOPE_MAP.put("10088240685", "10086270301");
        SAME_SUBJECT_DESIGNATE_SCOPE_MAP.put("10087743344", "10085801636");
    }

    /**
     * 获得三代统一配置数据
     */
    @SuppressWarnings("unchecked")
    public static <T> T getSysConfigFrom3G(String key) {
        T config = null;
        try {
            ConfigParam<T> p = ConfigurationUtils.getSysConfigParam(key);
            if (p == null || p.getValue() == null) {
                return null;
            } else {
                config = p.getValue();
            }
        } catch (Exception e) {
            log.error("获取统一配置有误", e);
            return null;
        }
        return config;
    }


    public enum Type {

        BOOLEAN(new String[]{"boolean", "java.lang.boolean"}),
        STRING(new String[]{"string", "java.lang.string"}),
        INT(new String[]{"int", "integer", "java.lang.integer"}),
        LONG(new String[]{"long", "java.lang.long"}),
        MAP(new String[]{"map", "java.util.map", "java.util.hashmap", "java.util.treemap"}),
        LIST(new String[]{"list", "java.util.arraylist", "java.util.list", "java.util.linkedlist"}),
        DECIMAL(new String[]{"decimal", "java.math.bigdecimal"});


        private String[] names;

        private Type(String[] names) {
            this.names = names;
        }

        public static Type parseType(String name) {
            if (name != null) {
                name = name.toLowerCase();
            }
            for (Type type : Type.values()) {
                for (String tname : type.names) {
                    if (tname.equals(name)) {
                        return type;
                    }
                }
            }
            return null;
        }

        public static Type parseType(@SuppressWarnings("rawtypes") Class clz) {
            if (clz == null) {
                return null;
            }
            return parseType(clz.getName());
        }
    }

    /**
     * 获取默认值
     *
     * @param configKey
     * @param defaultValue
     * @return
     */
    @SuppressWarnings("unchecked")
    private static <T> T getDefaultValue(String configKey, T defaultValue) {
        try {
            ConfigParam<?> configParam = ConfigurationUtils.getSysConfigParam(configKey);
            if (null == configParam || configParam.getValue() == null) {
                return defaultValue;
            }
            Type type = Type.parseType(defaultValue.getClass());
            switch (type) {
                case STRING:
                    return (T) configParam.getValue().toString();
                case INT:
                    return (T) new Integer(configParam.getValue().toString());
                case LONG:
                    return (T) new Long(configParam.getValue().toString());
                case BOOLEAN:
                    return (T) new Boolean(configParam.getValue().toString());
                case DECIMAL:
                    return (T) new BigDecimal(configParam.getValue().toString());
                case MAP:
                case LIST:
                    return (T) configParam.getValue();
                default:
                    throw new RuntimeException("unsupport type : " + type);
            }
        } catch (Exception e) {
            log.error("configKey=" + configKey + ", 获取异常，走默认配置=" + JsonUtils.toJSONString(defaultValue) + ",cased by ", e);
            return defaultValue;
        }
    }

    /**
     * 获得三代统一配置数据（取到null时返回默认值）
     */
    public static <T> T getSysConfigFrom3G(String key, T defaultValue) {
        T configValue = getSysConfigFrom3G(key);
        return configValue != null ? configValue : defaultValue;
    }

    public static Map<String, String> getMailNotifyConfig() {
        return getSysConfigFrom3G(MAIL_NOTIFY_CONFIG_KEY, MAIL_NOTIFY_CONFIG_MAP_DEFAULT);
    }

    public static Map<String, String> getShadowTableConfig() {
        return getSysConfigFrom3G(SHADOW_TABLE_CONFIG, new HashMap<>());
    }

    /**
     * 聚合场景和报备费率活动类型查询
     *
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String, String> queryAllSceneAndPromotionType() {
        try {
            return getSysConfigFrom3G(AGGREGATION_CONFIG_CHANNEL_SCENE_ACTIVITY_TYPE, new HashMap<>());
        } catch (Exception t) {
            log.error("从统一配置中查询聚合场景和报备费率活动类型异常", t);
        }
        return new HashMap<>();
    }

    /**
     * 微信B2B渠道号
     *
     * @return String 微信B2B渠道号
     */
    public static String getChannelNoWeChatB2B() {
        return getSysConfigFrom3G(WECHAT_B2B_CHANNEL_NO);
    }

    /**
     * 数字货币渠道号
     *
     * @return String 数字货币渠道号
     */
    public static String getChannelNoDigitalCurrency() {
        return getSysConfigFrom3G(DIGITAL_CURRENCY_CHANNEL_NO);
    }

    /**
     * 航旅默认渠道号
     *
     * @return String 默认渠道号
     */
    public static String getChannelNoDefaultHl(PayChannelEnum payChannel, PaySceneEnum payScene, ActivityTypeEnum activityType) {
        return getChannelNoDefault(payChannel, payScene, activityType, DEFAULT_CHANNEL_NO_HL);
    }

    private static String getChannelNoDefault(PayChannelEnum payChannel, PaySceneEnum payScene, ActivityTypeEnum activityType, String defaultChannelNoHl) {
        HashMap<String, String> map = getSysConfigFrom3G(defaultChannelNoHl, new HashMap<>());
        String payChannelDocument = payChannel.getDocument();
        String paySceneDocument = payScene.getDocument();
        StringBuilder key = new StringBuilder();
        key.append(payChannelDocument)
                .append(Const.SPLIT_CHAR)
                .append(paySceneDocument);
        if (!CheckUtils.isEmpty(activityType)) {
            key.append(Const.SPLIT_CHAR).append(activityType);
        }
        return map.get(key.toString());
    }

    /**
     * 默认渠道号
     *
     * @return String 默认渠道号
     */
    public static String getChannelNoDefaultNormal(PayChannelEnum payChannel, PaySceneEnum payScene, ActivityTypeEnum activityType) {
        return getChannelNoDefault(payChannel, payScene, activityType, DEFAULT_CHANNEL_NO);
    }

    /**
     * 获取支持同主体跨SAAS体系下的商编配置
     *
     * @return
     */
    public static Map<String, String> getSameSubjectDesignateSaasScopeConfig() {

        return getDefaultValue(SAME_SUBJECT_DESIGNATE_SCOPE_CONFIG_KEY, SAME_SUBJECT_DESIGNATE_SCOPE_MAP);
    }

    public static Integer getBackUpCountByMerchantNo(String merchantNo, String payChannel) {
        String key = payChannel + "_" + merchantNo;
        String configValue = getDefaultValue(MERCHANT_BACK_UP_COUNT_CONFIG_KEY, "{}");
        Map<String, Integer> configMap = JsonUtils.fromJson(configValue, new TypeReference<Map<String, Integer>>() {
        });
        return configMap.get(key);
    }

    public static Integer getBackUpCountByBusinessLine(String businessLineName, PayChannelEnum payChannel) {
        log.info("获取备份数量配置,businessLineName={},payChannel={}", businessLineName, payChannel);
        String key = payChannel.getDocument() + "_" + businessLineName;
        Map<String, String> configMap = getDefaultValue(PRODUCT_LINE_BACK_UP_COUNT_CONFIG_KEY, new HashMap<>());
        if (configMap == null || !configMap.containsKey(key)) {
            return null;
        }
        return Integer.parseInt(configMap.get(key));
    }

}
