package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ContactInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.*;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/4 21:52
 */
@Getter
@Builder
@Setter(AccessLevel.PRIVATE)
public class EntryOrder {
    /**
     * 进件单号
     */
    private final String orderNo;

    /**
     * 业务场景
     */
    private final BizSceneEnum bizScene;
    /**
     * 业务申请单号
     */
    private final String bizApplyNo;

    /**
     * 商户信息
     */
    private final MerchantInfo merchantInfo;

    /**
     * 主体信息
     */
    private final SubjectInfo subjectInfo;


    /**
     * 联系人信息
     */
    private final ContactInfo contactInfo;

    /**
     * 扩展信息
     */
    private final String extendInfo;

    /**
     * 进件信息
     */
    private List<? extends EntryApply<? extends ChannelSpecialParams>> entryApplies;

    public static EntryOrder createEntryOrder(final EntryApplyCmd entryApplyCmd) {

        final String orderNo = UniqueNoGenerateUtils.getUniqueNo();
        List<EntryApply<? extends ChannelSpecialParams>> entryApplies = Lists.newArrayList();

        entryApplies.addAll(createAliIndirectEntryApply(entryApplyCmd.getAlipayIndirectConfigs(), orderNo, entryApplyCmd));
        entryApplies.addAll(createWxIndirectEntryApply(entryApplyCmd.getWxIndirectConfigs(), orderNo, entryApplyCmd));
        entryApplies.addAll(createDigitalCurrencyEntryApply(entryApplyCmd.getDigitalCurrencyConfigs(), orderNo, entryApplyCmd));
        if (Objects.nonNull(entryApplyCmd.getWxDirectConfig())) {
            entryApplies.add(createWxDirectEntryApply(entryApplyCmd.getWxDirectConfig(), orderNo, entryApplyCmd));
        }
        if (Objects.nonNull(entryApplyCmd.getWxDirectB2BConfig())) {
            entryApplies.add(createWxB2BEntryApply(entryApplyCmd.getWxDirectB2BConfig(), orderNo, entryApplyCmd));
        }

        return EntryOrder.builder()
                .orderNo(orderNo)
                .bizScene(entryApplyCmd.getBizScene())
                .bizApplyNo(entryApplyCmd.getBizApplyNo())
                .merchantInfo(entryApplyCmd.getMerchantInfo())
                .subjectInfo(entryApplyCmd.getSubjectInfo())
                .contactInfo(entryApplyCmd.getContactInfo())
                .extendInfo(entryApplyCmd.getExtendInfo())
                .entryApplies(entryApplies)
                .build();
    }

    /**
     * 创建支付宝间连进件申请
     *
     * @param cmd           支付宝间连进件cmd
     * @param orderNo       进件单号
     * @param entryApplyCmd 进件申请
     * @return 支付间连进件申请
     */
    public static List<AliIndirectEntryApply> createAliIndirectEntryApply(final List<AlipayIndirectConfigCmd> cmd,
                                                                          final String orderNo, final EntryApplyCmd entryApplyCmd) {
        if (CollectionUtils.isEmpty(cmd)) {
            return Collections.emptyList();
        }
        return cmd.stream()
                .map(aliIndirectCmd ->
                        AliIndirectEntryApply.createAliIndirectEntryApply(aliIndirectCmd, orderNo, entryApplyCmd))
                .collect(Collectors.toList());
    }

    /**
     * 创建微信间连进件申请
     *
     * @param cmd           微信间连进件申请命令
     * @param orderNo       进件单号
     * @param entryApplyCmd 进件申请
     * @return 微信间连申请
     */
    public static List<WxIndirectEntryApply> createWxIndirectEntryApply(final List<WxIndirectConfigCmd> cmd,
                                                                        final String orderNo, final EntryApplyCmd entryApplyCmd) {
        if (CollectionUtils.isEmpty(cmd)) {
            return Collections.emptyList();
        }
        return cmd.stream()
                .map(wxIndirectConfigCmd ->
                        WxIndirectEntryApply.createWxIndirectEntryApply(wxIndirectConfigCmd, orderNo, entryApplyCmd))
                .collect(Collectors.toList());
    }

    public static WechatDirectEntryApply createWxDirectEntryApply(final WxDirectConfigCmd cmd,
                                                                  final String orderNo,
                                                                  final EntryApplyCmd entryApplyCmd) {
        if (Objects.isNull(cmd)) {
            return null;
        }
        return WechatDirectEntryApply.createEntryApply(cmd, orderNo, entryApplyCmd);
    }

    private static WechatB2BEntryApply createWxB2BEntryApply(final WxDirectB2BConfigCmd cmd, final String orderNo,
                                                             final EntryApplyCmd entryApplyCmd) {
        if (Objects.isNull(cmd)) {
            return null;
        }
        return WechatB2BEntryApply.createWechatB2BEntryApply(cmd, orderNo, entryApplyCmd);
    }

    private static List<DigitalCurrencyEntryApply> createDigitalCurrencyEntryApply(final List<DigitalCurrencyConfigCmd> cmd,
                                                                                   final String orderNo,
                                                                                   final EntryApplyCmd entryApplyCmd) {
        if (CollectionUtils.isEmpty(cmd)) {
            return Collections.emptyList();
        }
        return cmd.stream()
                .map(c -> DigitalCurrencyEntryApply.createDigitalCurrencyEntryApply(c, orderNo, entryApplyCmd))
                .collect(Collectors.toList());
    }

}
