package com.yeepay.g3.core.aggregation.config.biz.atmanage.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.DivideReceiverBiz;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.MerchantInfo;
import com.yeepay.g3.core.aggregation.config.convert.ThirdPartyRecordConvert;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.enums.DivideReceiverStatus;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.external.ChannelDivideExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.DivideCreateSubmitResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelDivideInfo;
import com.yeepay.g3.core.aggregation.config.mq.event.DivideReceiverMessage;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 分账方处理
 *
 * @author: Mr.yin
 * @date: 2025/6/16  18:30
 */
@Component
public class DivideReceiverBizImpl implements DivideReceiverBiz {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EntryResultProducer entryResultProducer;

    @Resource
    private ChannelEntryOrderService channelEntryOrderService;

    @Resource
    private ThirdPartyRecordService thirdPartyRecordService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ChannelDivideExternal channelDivideExternal;

    @Override
    public DivideReceiverStatus createDivideReceiver(BaseEntryApply<? extends ChannelSpecialParams> entryApply) {
        logger.info("[创建聚合分帐方],开始处理,entryApply={}", JsonUtils.toJSONString(entryApply));
        List<ThirdPartyRecord> recordList = thirdPartyRecordService.queryNotFinishOrSuccessThirdPartyRecordByApplyNo(entryApply.getApplyNo(), ThirdPartyBusinessTypeEnum.CHANNEL_DIVIDE_CREATE.getDocument());
        ThirdPartyRecord thirdPartyRecord = null;
        if (CollectionUtils.isEmpty(recordList)) {
            thirdPartyRecord = ThirdPartyRecordConvert.createTerminalReportThirdPartyRecord(entryApply, ThirdPartyBusinessTypeEnum.CHANNEL_DIVIDE_CREATE);
            saveDivideReceiverThirdRecord(entryApply, thirdPartyRecord);

            RemoteResult<DivideCreateSubmitResultBO> result = channelDivideExternal.createDivideReceiver(entryApply, thirdPartyRecord.getRequestNo());
            return handleDivideReceiverSubmitResult(entryApply, thirdPartyRecord, result);

        }
        Optional<ThirdPartyRecord> optionalRecord = recordList.stream()
                .filter(record -> record.getStatus() == ThirdPartyRecordStatusEnum.SUCCESS)
                .findAny();
        if (optionalRecord.isPresent()) {
            logger.warn("[创建聚合分帐方] [并发操作] 已存在完成记录: {}", optionalRecord.get().getRequestNo());
            throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
        }
        /*订单存在则补查*/
        thirdPartyRecord = recordList.get(0);
        MerchantInfo merchantInfo = entryApply.getMerchantInfo();
        RemoteResult<ChannelDivideInfo> result = channelDivideExternal.queryDivideReceiver(merchantInfo.getMerchantNo(), entryApply.getChannelNo(), thirdPartyRecord.getRequestNo());
        return handleDivideReceiverQueryResult(entryApply, thirdPartyRecord, result);

    }

    /**
     * 处理分账方的查询结果
     */
    private DivideReceiverStatus handleDivideReceiverQueryResult(BaseEntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord, RemoteResult<ChannelDivideInfo> result) {
        if (!result.isSuccess()) {
            logger.info("[创建聚合分帐方],查询 记录失败 applyNo={},requestNo={} ", entryApply.getApplyNo(), thirdPartyRecord.getRequestNo());
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询聚合分帐方创建记录失败");
        } else {
            ChannelDivideInfo reportResultBO = result.getData();
            if (null == reportResultBO) {
                logger.info("[创建聚合分帐方],查询记录不存在 走再次提交流程 applyNo={},requestNo={} ", entryApply.getApplyNo(), thirdPartyRecord.getRequestNo());
                RemoteResult<DivideCreateSubmitResultBO> submitResult = channelDivideExternal.createDivideReceiver(entryApply, thirdPartyRecord.getRequestNo());
                return handleDivideReceiverSubmitResult(entryApply, thirdPartyRecord, submitResult);
            }
            if (DivideReceiverStatus.SUCCESS == reportResultBO.getStatus()) {
                handleDivideCreateSuccess(reportResultBO.getBankOrderNo(), entryApply, thirdPartyRecord);
            } else if (DivideReceiverStatus.FAIL == reportResultBO.getStatus()) {
                thirdPartyRecord.fail(reportResultBO.getBankOrderNo(), reportResultBO.getFailCode(), reportResultBO.getFailReason(), LocalDateTime.now());
                if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                    logger.warn("[创建分帐方],失败，第三方报备记录更新失败,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                    throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
                }
            }
            return reportResultBO.getStatus();
        }
    }

    /**
     * 处理 终端提交的结果
     *
     * @param entryApply
     * @param thirdPartyRecord
     * @param result
     * @return
     */
    private DivideReceiverStatus handleDivideReceiverSubmitResult(BaseEntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord, RemoteResult<DivideCreateSubmitResultBO> result) {
        if (!result.isSuccess()) {
            thirdPartyRecord.fail(null, result.getChannelCode(), result.getChannelMessage(), LocalDateTime.now());
            if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                logger.warn("[创建聚合分帐方],提交失败，第三方报备记录更新失败,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            return DivideReceiverStatus.SUBMIT_FAIL;
        }
        if ("840005".equals(result.getChannelCode())) {/*通道理论上这个码这一期就干掉了*/
            /*通道说这种情况不确定成功还是失败 需要走一次查询*/
            logger.info("[创建聚合分帐方], 同步成功这种没有回调呢,code ={} getRequestNo={}", result.getChannelCode(), thirdPartyRecord.getRequestNo());
            MerchantInfo merchantInfo = entryApply.getMerchantInfo();
            RemoteResult<ChannelDivideInfo> queryResult = channelDivideExternal.queryDivideReceiver(merchantInfo.getMerchantNo(), entryApply.getChannelNo(), thirdPartyRecord.getRequestNo());
            if (queryResult.isSuccess() && queryResult.getData() == null) {
                logger.error("[告警][创建聚合分帐方], 同步告诉我走查询，查询又没有,code ={} getRequestNo={}", result.getChannelCode(), thirdPartyRecord.getRequestNo());
                thirdPartyRecord.fail(null, result.getChannelCode(), result.getChannelMessage(), LocalDateTime.now());
                if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                    logger.warn("[创建聚合分帐方],查询失败，第三方报备记录更新失败,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                    throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
                }
                return DivideReceiverStatus.SUBMIT_FAIL;
            }
            return handleDivideReceiverQueryResult(entryApply, thirdPartyRecord, queryResult);
        }
        if (DivideReceiverStatus.SUCCESS == result.getData().getStatus()) {/*同步成功了*/
            handleDivideCreateSuccess(result.getData().getChannelOrderNo(), entryApply, thirdPartyRecord);
        }
        logger.info("[创建聚合分帐方],提交发起完成,divideReceiverStatus={}", result.getData().getStatus());
        return result.getData().getStatus();
    }


    /**
     * 保存分账方创建调用记录
     *
     * @param entryApply
     * @param thirdPartyRecord
     */
    private void saveDivideReceiverThirdRecord(EntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord) {
        logger.info("[创建聚合分帐方],保存 提交记录,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
        transactionTemplate.execute(transactionStatus -> {
            /*加锁*/
            ChannelEntryApplyDetailEntity applyDetailEntity = channelEntryOrderService.selectChannelEntryApplyDetailEntityForUpdate(entryApply.getApplyNo());
            List<ThirdPartyRecord> currentRecords = thirdPartyRecordService.queryNotFinishOrSuccessThirdPartyRecordByApplyNo(entryApply.getApplyNo(), ThirdPartyBusinessTypeEnum.CHANNEL_DIVIDE_CREATE.getDocument());
            if (!CollectionUtils.isEmpty(currentRecords)) {
                logger.info("[创建聚合分帐方],落库记录 ，并发操作,entryApply={}", JsonUtils.toJSONString(entryApply));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            thirdPartyRecordService.saveThirdPartyRecord(thirdPartyRecord);
            return null;
        });
    }

    @Override
    public void callBackDivideReceiver(DivideReceiverMessage callBackBO) {
        ThirdPartyRecordStatusEnum thirdStatus = null;
        if ("SUCCESS".equals(callBackBO.getStatus())) {
            thirdStatus = ThirdPartyRecordStatusEnum.SUCCESS;
        } else if ("FAILED".equals(callBackBO.getStatus()) || "FAIL".equals(callBackBO.getStatus())) {
            thirdStatus = ThirdPartyRecordStatusEnum.FAIL;
        } else {
            logger.warn("[创建聚合分帐方],接收到处理不了的状态 callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        ThirdPartyRecord thirdPartyRecord = thirdPartyRecordService.queryThirdRecordByRequestNo(callBackBO.getRequestOrderNo(), ThirdPartyBusinessTypeEnum.CHANNEL_DIVIDE_CREATE.name());
        if (null == thirdPartyRecord) {
            logger.warn("[创建聚合分帐方],未查询到对应的第三方记录,callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        if (ThirdPartyRecordStatusEnum.SUCCESS == thirdPartyRecord.getStatus()) {
            logger.info("[创建聚合分帐方],第三方记录已处理成功,callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        if (thirdStatus == thirdPartyRecord.getStatus()) {/*失败的那种*/
            logger.info("[创建聚合分帐方],第三方报备记录已处理,callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        BaseEntryApply<? extends ChannelSpecialParams> entryApply = channelEntryOrderService.getChannelEntryApply(thirdPartyRecord.getApplyNo());
        logger.info("[创建聚合分帐方],查询到当前的申请记录,channelEntryOrder={}", JsonUtils.toJSONString(entryApply));
        /*变更结果*/
        logger.info("[创建聚合分帐方],接受到回调结果 requestNno={}, thirdStatus={}，", callBackBO.getRequestOrderNo(), thirdStatus);
        if (ThirdPartyRecordStatusEnum.FAIL == thirdStatus) {
            thirdPartyRecord.fail(callBackBO.getId(), callBackBO.getErrCode(), callBackBO.getErrMsg(), LocalDateTime.now());
            if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                logger.warn("[创建聚合分帐方],订单失败，第三方报备记录更新失败,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
        } else if (ThirdPartyRecordStatusEnum.SUCCESS == thirdStatus) {
            handleDivideCreateSuccess(callBackBO.getId(), entryApply, thirdPartyRecord);
        }
    }

    private void handleDivideCreateSuccess(String bankOrderNo, BaseEntryApply<? extends ChannelSpecialParams> entryApply, ThirdPartyRecord thirdPartyRecord) {
        thirdPartyRecord.success(bankOrderNo, LocalDateTime.now());
        transactionTemplate.execute(transactionStatus -> {
            if (Boolean.FALSE.equals(thirdPartyRecordService.updateByRequestNo(thirdPartyRecord))) {
                logger.warn("[创建聚合分帐方],成功，第三方报备记录更新失败,thirdPartyRecord={}", JsonUtils.toJSONString(thirdPartyRecord));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            /*需要加查询锁更新一下*/
            ChannelEntryApplyDetailEntity currentEntity = channelEntryOrderService.selectChannelEntryApplyDetailEntityForUpdate(entryApply.getApplyNo());
            entryApply.flashCurrentFlagStatusAndEntryStatus(currentEntity.getFlagStatus(), DocumentedEnum.fromValueOfNullable(EntryStatusEnum.class, currentEntity.getStatus()));
            entryApply.wxDirectCreateSplitterSuccess();
            if (0 == channelEntryOrderService.updateChannelEntryApplyDetailFlagStatus(entryApply)) {
                logger.warn("[创建聚合分帐方],成功，进件申请更新失败,baseEntryApply={}", JsonUtils.toJSONString(entryApply));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            return null;
        });
        entryResultProducer.sendEntryNotifyMessage(entryApply, EntryNotifyBizTypeEnum.DIVIDE_CREATE);
    }
}
