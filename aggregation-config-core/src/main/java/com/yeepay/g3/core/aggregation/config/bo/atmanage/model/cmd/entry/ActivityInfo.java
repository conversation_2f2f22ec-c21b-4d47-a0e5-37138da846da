package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.ActivityInfoDTO;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/6/2 20:39
 */
@Setter(AccessLevel.PRIVATE)
@Getter
@Builder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class ActivityInfo {


    /**
     * 优惠费率活动ID
     */
    private String activitiesId;
    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;
    /**
     * 借记活动费率
     * 微信直连时：
     * 1、若填写了优惠费率活动ID，则该字段必填
     * 2、仅能填入2位以内小数，且在优惠费率活动ID指定费率范围内
     * 微信小程序B2B：该字段必填且需要和信用卡活动费率值保持一致
     */
    private String debitActivitiesRate;
    /**
     * 贷记活动费率
     * 微信直连时：
     * 1、若填写了优惠费率活动ID，则该字段必填
     * 2、仅能填入2位以内小数，且在优惠费率活动ID指定费率范围内
     * 微信小程序B2B：该字段必填且需要和非信用卡活动费率值保持一致
     */
    private String creditActivitiesRate;

    public static ActivityInfo build(ActivityInfoDTO activityInfoDTO) {
        Assert.notNull(activityInfoDTO, ResultCode.PARAM_VALID_ERROR, "activityInfoDTO can not be null!");
        final ActivityTypeEnum activityType = DocumentedEnum.fromValue(ActivityTypeEnum.class, activityInfoDTO.getActivityType());
        return ActivityInfo.builder()
                .activitiesId(activityInfoDTO.getActivitiesId())
                .activityType(activityType)
                .debitActivitiesRate(activityInfoDTO.getDebitActivitiesRate())
                .creditActivitiesRate(activityInfoDTO.getCreditActivitiesRate())
                .build();
    }

    public static ActivityInfo buildWechatB2BActivity(ActivityInfoDTO activityInfoDTO) {
        Assert.notNull(activityInfoDTO, ResultCode.PARAM_VALID_ERROR, "activityInfoDTO can not be null!");
        final ActivityTypeEnum activityType = DocumentedEnum.fromValue(ActivityTypeEnum.class, activityInfoDTO.getActivityType());
        Assert.notNull(activityInfoDTO.getDebitActivitiesRate(), ResultCode.PARAM_VALID_ERROR, "debitActivitiesRate can not be null!");
        Assert.notNull(activityInfoDTO.getCreditActivitiesRate(), ResultCode.PARAM_VALID_ERROR, "creditActivitiesRate can not be null!");
        Assert.isTrue(activityInfoDTO.getDebitActivitiesRate().equals(activityInfoDTO.getCreditActivitiesRate()), ResultCode.PARAM_VALID_ERROR, "微信B2B借记活动费率和贷记活动费率必须相等");
        return ActivityInfo.builder()
                .activitiesId(activityInfoDTO.getActivitiesId())
                .activityType(activityType)
                .debitActivitiesRate(activityInfoDTO.getDebitActivitiesRate())
                .creditActivitiesRate(activityInfoDTO.getCreditActivitiesRate())
                .build();
    }
}
