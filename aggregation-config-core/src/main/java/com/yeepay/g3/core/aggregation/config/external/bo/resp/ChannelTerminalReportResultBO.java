package com.yeepay.g3.core.aggregation.config.external.bo.resp;

import com.yeepay.g3.core.aggregation.config.enums.TerminalReportStatus;
import lombok.Data;

import java.util.List;

/**
 * 终端报备结果BO
 *
 * @author: Mr.yin
 * @date: 2025/6/13  17:25
 */
@Data
public class ChannelTerminalReportResultBO {

    private String bankOrderNo;

    /**
     * 终端报备状态
     */
    private TerminalReportStatus status;

    /**
     * 成功信息
     */
    private List<TerminalReportDetailInfo> successDetailInfo;

    /**
     * 失败信息
     */
    private List<TerminalReportDetailInfo> failDetailInfo;

    private String failCode;

    private String failMessage;


}
