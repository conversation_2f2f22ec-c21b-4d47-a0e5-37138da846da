package com.yeepay.g3.core.aggregation.config.bo.atmanage.auth;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 取消认证 BO
 * <AUTHOR>
 * @since 2025/5/26:18:47
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@Builder
public class AuthCancelBO implements Serializable {

    private static final long serialVersionUID = -6279566177152109218L;

    /**
     * 渠道类型
     */
    private PayChannelEnum channel;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 渠道标识（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelIdentifier;

    /**
     * 渠道号（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelNo;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 申请单据结果单号
     */
    private String channelApplyNo;
}
