package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * description: 商户信息业务对象
 * <AUTHOR>
 * @since 2025/5/21:17:58
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@AllArgsConstructor
public class AtMerchantInfoBO implements Serializable {

    private static final long serialVersionUID = -1038982687069173817L;
    /**
     * 业务申请单号（必填）
     */
    private String bizApplyNo;

    /**
     * 联系人信息（对象字段）
     */
    private ContactInfo contactInfo;

    /**
     * 主体信息（对象字段）
     */
    private SubjectInfo subjectInfo;

    /**
     * 最终受益人信息列表（选填）
     */
    private List<UboInfoBO> uboInfoList;

}
