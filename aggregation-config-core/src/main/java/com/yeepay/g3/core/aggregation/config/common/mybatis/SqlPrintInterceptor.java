package com.yeepay.g3.core.aggregation.config.common.mybatis;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.type.TypeHandlerRegistry;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.regex.Matcher;

@Intercepts({
    @Signature(
        type = StatementHandler.class,
        method = "prepare",
        args = {Connection.class, Integer.class}
    )
})
public class SqlPrintInterceptor implements Interceptor {

    private static final Logger logger = LoggerFactory.getLogger(SqlPrintInterceptor.class);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            if (logger.isDebugEnabled()) {
                // 获取 StatementHandler
                StatementHandler statementHandler = (StatementHandler) invocation.getTarget();

                // 通过 MetaObject 获取 MappedStatement
                MetaObject metaObject = org.apache.ibatis.reflection.SystemMetaObject.forObject(statementHandler);
                while (metaObject.hasGetter("h")) {
                    Object object = metaObject.getValue("h");
                    metaObject = org.apache.ibatis.reflection.SystemMetaObject.forObject(object);
                }
                while (metaObject.hasGetter("target")) {
                    Object object = metaObject.getValue("target");
                    metaObject = org.apache.ibatis.reflection.SystemMetaObject.forObject(object);
                }

                // 获取 MappedStatement
                MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

                // 获取 BoundSql
                BoundSql boundSql = statementHandler.getBoundSql();

                // 获取配置对象
                org.apache.ibatis.session.Configuration configuration = mappedStatement.getConfiguration();

                // 获取 SQL 语句
                String sql = getCompleteSql(configuration, boundSql);

                // 获取 Mapper 的全限定名和方法名
                String mapperId = mappedStatement.getId();
                String className = mapperId.substring(0, mapperId.lastIndexOf("."));
                String methodName = mapperId.substring(mapperId.lastIndexOf(".") + 1);

                // 输出日志
                logger.debug("执行SQL :: [{}.{}] :: {}", className, methodName, sql);
            }
        }catch (Throwable e){
            logger.error("SqlPrintInterceptor error",e);
        }

        // 继续执行
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        // 包装目标对象
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 设置属性（如果有）
    }

    // 组装完整的SQL语句，替换参数占位符
    private String getCompleteSql(org.apache.ibatis.session.Configuration configuration, BoundSql boundSql) {
        // 原始SQL语句
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");

        // 参数对象
        Object parameterObject = boundSql.getParameterObject();

        if (parameterObject == null) {
            return sql;
        }

        // 解析参数
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
        MetaObject metaObject = configuration.newMetaObject(parameterObject);

        for (ParameterMapping parameterMapping : parameterMappings) {
            String propertyName = parameterMapping.getProperty();
            Object value;

            if (metaObject.hasGetter(propertyName)) {
                value = metaObject.getValue(propertyName);
            } else if (boundSql.hasAdditionalParameter(propertyName)) {
                value = boundSql.getAdditionalParameter(propertyName);
            } else {
                value = null;
            }

            // 格式化参数值
            String parameterValue = formatParameterValue(value);

            // 替换占位符
            sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(parameterValue));
        }

        return sql;
    }

    // 格式化参数值
    private String formatParameterValue(Object obj) {
        if (obj instanceof String) {
            return "'" + obj + "'";
        } else if (obj instanceof Date) {
            return "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(obj) + "'";
        } else if (obj != null) {
            return obj.toString();
        } else {
            return "null";
        }
    }
}
