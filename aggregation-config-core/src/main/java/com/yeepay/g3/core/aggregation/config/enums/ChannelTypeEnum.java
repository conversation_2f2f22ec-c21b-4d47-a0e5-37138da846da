package com.yeepay.g3.core.aggregation.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: 渠道类型
 * @author: xuchen.liu
 * @date: 2024-12-16 19:08
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Getter
@ToString
@AllArgsConstructor
public enum ChannelTypeEnum {

    /**
     * 微信
     */
    WECHAT("微信"),

    /**
     * 支付宝
     */
    ALIPAY("支付宝");

    private final String desc;
}
