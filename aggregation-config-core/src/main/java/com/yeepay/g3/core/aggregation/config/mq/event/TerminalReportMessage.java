package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;

import java.io.Serializable;

/**
 * 分帐接收方消息
 *
 * @author: Mr.yin
 * @date: 2025/6/16  10:55
 * {@link com.yeepay.g3.facade.trade.bankcooper.dto.async.message.CallbackOpenPayTerminalReportResultMessageFroRocket}
 */
@Data
public class TerminalReportMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求号
     */
    private String requestOrderNo;

    /**
     * 通道的号
     */
    private String bankOrderNo;

    private String merchantNo;

    private String terminalStatus;

    /**
     * WECHAT
     * ALIPAY
     */
    private String bankCode;

    private String reportMerchantNo;

    private String bizCode;
    private String bizMsg;

    private String opSourceName;

    private String terminalId;
    private String terminalType;
    private String serialNum;
    private String terminalProvince;
    private String terminalCity;
    private String terminalDistrict;
    private String terminalAddress;
    private String terminalAppId;
    private String terminalModelId;
    private String terminalGPSFlag;
    private String reportChannel;

    private String operator;

}
