package com.yeepay.g3.core.aggregation.config.common;

import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.g3.core.aggregation.config.utils.ValidationUtils;
import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.aggregation.config.validator.ConditionIgnor;
import com.yeepay.g3.utils.common.ArrayUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;

/**
 * @description: facade层拦截器
 * @author: xuchen.liu
 * @date: 2024-12-13 13:48
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Aspect
@Order(1)
@Component
public class FacadeInspectAop {

    @Around("execution(* com.yeepay.g3.facade.aggregation.config.facade..*.*(..)) " +
            "&& !execution(* com.yeepay.g3.facade.aggregation.config.facade.AggConfigScheduleFacade.*(..))" +
            "&& !execution(* com.yeepay.g3.facade.aggregation.config.facade.schedule.*.*(..))"
    )
    public Object processSaas(ProceedingJoinPoint invocation) {
        Logger logger = LoggerFactory.getLogger(invocation.getTarget().getClass());
        StopWatch stopWatch = new StopWatch();
        Object result = null;
        String  simpleName = "";
        String name = "";
        try {
            stopWatch.start();
            MethodSignature methodSignature = (MethodSignature) invocation.getSignature();
            Method method = methodSignature.getMethod();
            Object[] args = invocation.getArgs();
            simpleName = method.getDeclaringClass().getSimpleName();
            name = method.getName();
            logger.info("[{}入参] - [{}] - {}", simpleName, name, JsonUtils.convert(args));

            if (BaseResponseDTO.class.isAssignableFrom(method.getReturnType())) {
                // 初始化继承ResponseDTO的返回值
                result = method.getReturnType().newInstance();
            }

            if (ArrayUtils.isNotEmpty(args)) {
                for (Object object : args) {
                    ConditionIgnor conditionIgnor = object.getClass().getAnnotation(ConditionIgnor.class);
                    // 复杂情况的参数校验，不在切面校验注解
                    if (conditionIgnor == null) {
                        ValidationUtils.validateEntity(object, method.getDeclaringClass().getInterfaces());
                    }
                }
            }

            // 方法执行
            result = invocation.proceed();

        } catch (AggConfigException e) {
            logger.error("[" + simpleName+ "[业务异常] - [" + name + "]，e=", e);
            if (result != null) {
                BaseResponseDTO baseResponseDTO = (BaseResponseDTO) result;
                baseResponseDTO.setCode(e.getCode());
                baseResponseDTO.setMessage(e.getCode()+e.getMessage());
                return baseResponseDTO;
            }
        } catch (BaseException e) {
            logger.error("[" + simpleName+ "[业务异常] - [" + name + "]，e=", e);
            if (result != null) {
                BaseResponseDTO baseResponseDTO = (BaseResponseDTO) result;
                baseResponseDTO.setCode(e.getResponseEnum().getCode());
                baseResponseDTO.setMessage(e.getResponseEnum().getCode() + e.getMessage());
                return baseResponseDTO;
            }
        }catch (Throwable e) {
            logger.error("[" + simpleName+ "[系统异常] - [" + name + "]，e=", e);
            if (result != null) {
                BaseResponseDTO baseResponseDTO = (BaseResponseDTO) result;
                baseResponseDTO.setCode(ErrorCodeEnum.SYSTEM_ERROR.getCode());
                baseResponseDTO.setMessage(ErrorCodeEnum.SYSTEM_ERROR.getDesc());
                return baseResponseDTO;
            }
        }finally {
            stopWatch.stop();
            logger.info("[{}]-[执行时间]-[{}]-耗时[{}]毫秒-执行结果[{}]",
                    simpleName,name,stopWatch.getTotalTimeMillis(),JsonUtils.convert(result));
        }
        return result;
    }
}
