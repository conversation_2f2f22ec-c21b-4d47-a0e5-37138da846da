package com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoApplyRecordEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelNoEntity;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * description: 渠道关系创建信息
 * <AUTHOR>
 * @since 2025/6/3:15:51
 * Company: 易宝支付(YeePay)
 */
@Getter
@Builder
public class ChannelNoCreateBO implements Serializable {
    private static final long serialVersionUID = 2440159424459875222L;

    /**
     * 商户编码
     */
    private String merchantNo;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 渠道类型
     */
    private PayChannelEnum channelType;

    /**
     * 支付场景
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型
     */
    private ActivityTypeEnum promotionType;

    /**
     * 机构类型
     */
    private String instituteType;

    public static ChannelNoCreateBO build(ChannelNoApplyResultNotifyBO notifyBO, ChannelNoApplyRecordEntity channelNoApplyRecord) {

        return ChannelNoCreateBO.builder()
                .merchantNo(notifyBO.getMerchantNo())
                .channelNo(notifyBO.getChannelNo())
                .applyNo(channelNoApplyRecord.getApplyNo())
                .channelType(PayChannelEnum.valueOf(channelNoApplyRecord.getPayChannel()))
                .payScene(PaySceneEnum.valueOf(channelNoApplyRecord.getPayScene()))
                .promotionType(ActivityTypeEnum.valueOf(channelNoApplyRecord.getActivityType()))
                .instituteType(notifyBO.getInstituteType())
                .build();
    }

}
