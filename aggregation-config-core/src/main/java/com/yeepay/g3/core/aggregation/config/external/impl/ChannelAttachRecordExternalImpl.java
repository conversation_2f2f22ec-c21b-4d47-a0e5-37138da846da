package com.yeepay.g3.core.aggregation.config.external.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryAnchoredInfoBO;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.core.aggregation.config.external.ChannelAttachRecordExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.QueryEntryProcessBaseReqBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.trade.bankcooper.OpenPayAsyncReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRelationQueryRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRelationQueryResponseDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * description: 通道挂靠记录
 * <AUTHOR>
 * @since 2025/6/10:00:11
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class ChannelAttachRecordExternalImpl implements ChannelAttachRecordExternal {

    private final OpenPayAsyncReportFacade reportFacade = RemoteServiceFactory.getService(OpenPayAsyncReportFacade.class);

    @Override
    public List<QueryAnchoredInfoBO> queryAttachRecord(QueryEntryProcessBaseReqBO reqBO) {
        if (PayChannelEnum.BANK.equals(reqBO.getPayChannel())) {
            // 数币通道不支持挂靠查询
            return Collections.emptyList();
        }
        OpenPayAsyncReportRelationQueryRequestDTO reqDTO = new OpenPayAsyncReportRelationQueryRequestDTO();
        reqDTO.setRequireMerchantNo(reqBO.getMerchantNo());
        if (!CheckUtils.isEmpty(reqBO.getPayChannel()) && !CheckUtils.isEmpty(reqBO.getPayScene())) {
            SceneAndActivityTypeBO sceneAndPromotionTypeBO = SceneAndActivityUtils
                    .getSceneAndPromotionTypeBO(reqBO.getPayChannel(), reqBO.getPayScene(), reqBO.getActivityType());
            reqDTO.setMaskReportFee(sceneAndPromotionTypeBO.getChannelFeeType());
            reqDTO.setPromotionType(sceneAndPromotionTypeBO.getChannelActivityType());
        }
        try {
            log.info("查询通道挂靠结果，参数：{}", reqDTO);
            OpenPayAsyncReportRelationQueryResponseDTO responseDTO = reportFacade.queryOpenPayReportRelation(reqDTO);
            log.info("查询通道挂靠结果，返回结果：{}", responseDTO);
            if (responseDTO == null) {
                throw new BusinessException(ResultCode.SYSTEM_ERROR);
            } else if (ChannelResultCode.CHANNEL_ATTACH_QUERY_ERROR.getCode().equals(responseDTO.getBizCode())) {
                return Collections.emptyList();
            } else if (!ChannelResultCode.DIRECT_SUCCESS.getCode().equals(responseDTO.getBizCode())) {
                throw new BusinessException(ResultCode.ATTACH_QUERY_ERROR, responseDTO.getBizMsg());
            }
            return QueryAnchoredInfoBO.buildChannelList(responseDTO.getRelationRecordDTOs());
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询通道挂靠结果，异常信息：", e);
            throw new BusinessException(ResultCode.ATTACH_QUERY_ERROR, e.getMessage());
        }

    }
}
