package com.yeepay.g3.core.aggregation.config.biz;

import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.facade.aggregation.config.enums.OperateTypeEnum;
import org.apache.commons.lang3.tuple.Pair;

/**
 * @description: 管控配置上层业务层
 * @author: xuchen.liu
 * @date: 2024-12-13 15:11
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface ControlConfigBiz {

    /**
     *  管控配置管理
     * @param entityPair entityPair
     * @return id
     */
    Long configManage(Pair<OperateTypeEnum, ControlConfigEntity> entityPair);
}
