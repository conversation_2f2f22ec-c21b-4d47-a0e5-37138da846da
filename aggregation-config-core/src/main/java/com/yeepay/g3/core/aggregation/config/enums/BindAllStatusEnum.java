package com.yeepay.g3.core.aggregation.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 总表绑定状态枚举
 * @author: xuchen.liu
 * @date: 2024-12-18 16:16
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum BindAllStatusEnum {

    BIND("绑定"),
    RELIEVE("解除");
    /**
     * 描述
     */
    private final String desc ;
}
