package com.yeepay.g3.core.aggregation.config.external.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SalesInfo;
import com.yeepay.g3.core.aggregation.config.enums.TerminalReportStatus;
import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.external.ChannelTerminalReportExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.bo.TerminalReportSubmitResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.ChannelTerminalReportResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.resp.TerminalReportDetailInfo;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.trade.bankcooper.OpenPayAsyncReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.*;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 聚合终端报备
 *
 * @author: Mr.yin
 * @date: 2025/6/13  15:07
 */
@Service
public class ChannelTerminalReportExternalImpl implements ChannelTerminalReportExternal {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private OpenPayAsyncReportFacade openPayAsyncReportFacade = RemoteServiceFactory.getService(OpenPayAsyncReportFacade.class);

    private static final String BANK_CHANNEL_SUCCESS = "0000";

    /**
     * 终端报备可以处理中，等回调，或者等补偿的码
     * /*851006:真正报备成功(回调报备成功后)再次相同的参数请求，返回次返回码
     * 851009 终端报备记录还未终态，等待终态回调或者主动查询终态
     * 850003 请求号重复，这种算提交成功，等回调就行
     * 999999 通道系统异常
     */
    private final static Set<String> SUBMIT_ORDER_PROCESS = Sets.newHashSet("851006", "851009", "850003", "999999");

    @Override
    public RemoteResult<TerminalReportSubmitResultBO> applyChannelTerminalReport(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo) {

        OpenPayAsyncTerminalReportRequestDTO requestDTO = assembleTerminalReportParam(entryApply, requestNo);
        OpenPayAsyncTerminalReportResponseDTO responseDTO = null;
        try {
            logger.info("[remote][请求通道终端报备]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = openPayAsyncReportFacade.asyncTerminalReport(requestDTO);
            logger.info("[remote][请求通道终端报备]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[remote][请求通道终端报备] 调用异常 requestDTO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (null == responseDTO) {
            logger.error("[remote][请求通道终端报备] 调用异常返回为null requestDTO={%s})", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }

        if (!BANK_CHANNEL_SUCCESS.equals(responseDTO.getBizCode())
                && !SUBMIT_ORDER_PROCESS.contains(responseDTO.getBizCode())) {
            logger.warn("[remote][请求通道终端报备] 通道处理失败 requestDTO={}", JsonUtils.toJSONString(requestDTO));
            return RemoteResult.fail(responseDTO.getBizCode(), responseDTO.getBizMsg());
        }
        TerminalReportSubmitResultBO resultBO = new TerminalReportSubmitResultBO();
        resultBO.setChannelOrderNo(responseDTO.getTraceId());
        resultBO.setStatus(TerminalReportStatus.SUBMIT_PROCESS);
        if ("851006".equals(responseDTO.getBizCode())) {
            resultBO.setStatus(TerminalReportStatus.SUCCESS);
        }
        return RemoteResult.success(resultBO, responseDTO.getBizCode(), responseDTO.getBizMsg());
    }

    private OpenPayAsyncTerminalReportRequestDTO assembleTerminalReportParam(EntryApply entryApply, String requestNo) {
        SalesInfo.SalesAddress salesAddress = entryApply.getSubjectInfo().getSalesInfo().getSalesAddress();
        OpenPayAsyncTerminalReportRequestDTO requestDTO = new OpenPayAsyncTerminalReportRequestDTO();
        requestDTO.setMerchantNo(entryApply.getMerchantInfo().getMerchantNo());
//        Assert.isTrue(DocumentedEnum.inRightEnums(entryApply.getPayChannel(), PayChannelEnum.WECHAT, PayChannelEnum.ALIPAY), ResultCode.PARAM_VALID_ERROR, "终端报备暂时仅支持微信和支付宝");
        requestDTO.setBankCode(entryApply.getPayChannel().getDocument());
        //固定传参 11:条码支付辅助受理终端；
        requestDTO.setTerminalType("11");
        requestDTO.setTerminalProvince(salesAddress.getProvince());
        requestDTO.setTerminalCity(salesAddress.getCity());
        requestDTO.setTerminalDistrict(salesAddress.getDistrict());
        requestDTO.setTerminalAddress(salesAddress.getAddress());
        requestDTO.setSourceName(Const.DEFAULT_SYSTEM_NAME);
        /*--------  上面是原入网传参    ----------*/
        requestDTO.setRequestOrderNo(requestNo);
//        requestDTO.setOpSource(Const.DEFAULT_SYSTEM_NAME);

        /*下面的先不传了*/
//        requestDTO.setTerminalId("");
//        requestDTO.setSerialNum("");
//        requestDTO.setTerminalAppId("");
//        requestDTO.setTerminalModelId("");
//        requestDTO.setTerminalGPSFlag("");
        return requestDTO;
    }

    @Override
    public RemoteResult<ChannelTerminalReportResultBO> queryChannelTerminalReport(EntryApply<? extends ChannelSpecialParams> entryApply, String requestNo) {
        String merchantNo = entryApply.getMerchantInfo().getMerchantNo();
        PayChannelEnum payChannel = entryApply.getPayChannel();

        OpenPayTerminalReportQueryRequestDTO requestDTO = new OpenPayTerminalReportQueryRequestDTO();
        requestDTO.setRequestOrderNo(requestNo);
        requestDTO.setOpSource(Const.DEFAULT_SYSTEM_NAME);
//        Assert.isTrue(DocumentedEnum.inRightEnums(payChannel, PayChannelEnum.WECHAT, PayChannelEnum.ALIPAY), ResultCode.PARAM_VALID_ERROR, "终端报备暂时仅支持微信和支付宝");
        requestDTO.setBankCode(payChannel.getDocument());
        requestDTO.setMerchantNo(merchantNo);
        requestDTO.setTerminalType("11");
        OpenPayTerminalReportQueryResponseDTO responseDTO = null;
        try {
            logger.info("[remote][查询通道终端报备结果]  requestDTO={}", JsonUtils.toJSONString(requestDTO));
            responseDTO = openPayAsyncReportFacade.queryTerminalReport(requestDTO);
            logger.info("[remote][查询通道终端报备结果]  结果返回 responseDTO={}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            logger.error(String.format("[remote][查询通道终端报备结果] 调用异常 requestDTO={%s})", JsonUtils.toJSONString(requestDTO)), e);
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        if (null == responseDTO) {
            logger.error("[remote][查询通道终端报备结果] 调用异常返回为null requestDTO={%s})", JsonUtils.toJSONString(requestDTO));
            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "系统异常");
        }
        /*0000：表示查询成功，其它为失败
            830003：表示未查询到终点报备记录*/
        if ("830003".equals(responseDTO.getBizCode()) || "851055".equals(responseDTO.getBizCode())) {
            return RemoteResult.success(responseDTO.getBizCode(), responseDTO.getBizMsg());
        } else if (BANK_CHANNEL_SUCCESS.equals(responseDTO.getBizCode())) {
            List<String> channelMchNos = null;
            if (StringUtils.isNotBlank(entryApply.getChannelMchNos())) {
                channelMchNos = Lists.newArrayList(entryApply.getChannelMchNos().split(","));
            }
            if (null == responseDTO.getOriOpenPayTerminalReportDTOList()) {
                return RemoteResult.fail(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR.getCode(), "系统异常");
            }
            ChannelTerminalReportResultBO reportResultBO = covertChannelTerminalReport(responseDTO, channelMchNos);
            logger.info("[remote][查询通道终端报备结果] 返回结果 reportResultBO={}", JsonUtils.toJSONString(reportResultBO));
            return RemoteResult.success(reportResultBO, responseDTO.getBizCode(), responseDTO.getBizMsg());
        }

        return RemoteResult.fail(responseDTO.getBizCode(), responseDTO.getBizMsg());
    }

    private ChannelTerminalReportResultBO covertChannelTerminalReport(OpenPayTerminalReportQueryResponseDTO responseDTO, List<String> channelMchNos) {
        ChannelTerminalReportResultBO reportResultBO = new ChannelTerminalReportResultBO();
        reportResultBO.setBankOrderNo(responseDTO.getTraceId());
        if ("INIT".equals(responseDTO.getTotalTerminalStatus())) {
            reportResultBO.setStatus(TerminalReportStatus.SUBMIT_PROCESS);
            return reportResultBO;
        }

        /*转换结果*/
        List<TerminalReportDetailInfo> successDetailInfo = Lists.newArrayList();
        List<TerminalReportDetailInfo> failDetailInfo = Lists.newArrayList();
        /*此处获取一下最新的列表，原来的情况是一条成功一条失败，返回的是失败，而且成功的列表也没了...*/
        for (OpenPayTerminalReportDTO reportDTO : responseDTO.getOriOpenPayTerminalReportDTOList()) {
            if (!"SUCCESS".equals(reportDTO.getTerminalStatus()) && !"FAIL".equals(reportDTO.getTerminalStatus())) {
                continue;
            }
            if (null != channelMchNos && !channelMchNos.contains(reportDTO.getReportMerchantNo())) {
                logger.warn("[终端报备],不是进件关联的的商户号，丢弃,ReportMerchantNo={},channelMchNos={}", reportDTO.getReportMerchantNo(), JsonUtils.toJSONString(channelMchNos));
                continue;
            }
            TerminalReportDetailInfo detailInfo = new TerminalReportDetailInfo();
            detailInfo.setMerchantNo(reportDTO.getMerchantNo());
            detailInfo.setChannelMchNo(reportDTO.getReportMerchantNo());
            detailInfo.setTerminalStatus(reportDTO.getTerminalStatus());
            detailInfo.setTerminalType(reportDTO.getTerminalType());
            detailInfo.setTerminalId(reportDTO.getTerminalId());
            detailInfo.setCode(reportDTO.getBizCode());
            detailInfo.setMessage(reportDTO.getBizMsg());
            if ("SUCCESS".equals(reportDTO.getTerminalStatus())) {
                successDetailInfo.add(detailInfo);
            } else {
                failDetailInfo.add(detailInfo);
            }
        }
        reportResultBO.setFailDetailInfo(failDetailInfo);
        reportResultBO.setSuccessDetailInfo(successDetailInfo);
        /*精准状态*/
        if ("SUCCESS".equals(responseDTO.getTotalTerminalStatus())) {
            reportResultBO.setStatus(TerminalReportStatus.SUCCESS);
            if (CollectionUtils.isEmpty(successDetailInfo)) {/*如果没找到想要的，那就失败*/
                reportResultBO.setStatus(TerminalReportStatus.FAIL);
            }
        } else if ("FAIL".equals(responseDTO.getTotalTerminalStatus())) {
            if (!CollectionUtils.isEmpty(successDetailInfo)) {
                reportResultBO.setStatus(TerminalReportStatus.PARTY_SUCCESS);
            } else {
                reportResultBO.setStatus(TerminalReportStatus.FAIL);
                reportResultBO.setFailCode(!CollectionUtils.isEmpty(failDetailInfo) ? failDetailInfo.get(0).getCode() : "");
                reportResultBO.setFailMessage(responseDTO.getTotalTerminalFailMsg());
            }
        }
        return reportResultBO;
    }
}
