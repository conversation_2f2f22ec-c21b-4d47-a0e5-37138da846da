package com.yeepay.g3.core.aggregation.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 渠道商户绑定状态
 * @author: xuchen.liu
 * @date: 2024-12-17 15:13
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum ChannelAppIdBindStatusEnum {

    SUCCESS("绑定"),
    FAILED("解绑"),
    ;
    /**
     * 描述
     */
    private final String desc;
}
