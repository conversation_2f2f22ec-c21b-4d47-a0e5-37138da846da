package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.DigitalCurrencyEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:11
 */
@Slf4j
@Service
public class DigitalCurrencyEntryApplyHandler implements EntryApplyHandler<DigitalCurrencyEntryApply> {

    @Qualifier("baseEntryApplyHandler")
    private final EntryApplyHandler<DigitalCurrencyEntryApply> baseEntryApplyHandler;

    private final ChannelNoBiz channelNoBiz;

    @Autowired
    public DigitalCurrencyEntryApplyHandler(@Qualifier("baseEntryApplyHandler") EntryApplyHandler<DigitalCurrencyEntryApply>
                                                        baseEntryApplyHandler,
                                            final ChannelNoBiz channelNoBiz) {
        this.baseEntryApplyHandler = baseEntryApplyHandler;
        this.channelNoBiz = channelNoBiz;
    }
    @Override
    public void buildChannelNo(final DigitalCurrencyEntryApply entryApply) {
        // 1、获取数币配置渠道号
        entryApply.buildChannelNo(channelNoBiz.queryChannelNoByDigitalCurrency());
    }

    @Override
    public void buildBackupInfo(final DigitalCurrencyEntryApply entryApply) {
        if (log.isDebugEnabled()) {
            log.debug("数币无备份");
        }
    }

    @Override
    public ChannelEntryResult entryApply(final EntryApplyContext<DigitalCurrencyEntryApply> entryApplyContext) {
        return baseEntryApplyHandler.entryApply(entryApplyContext);
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {

        return PayChannelEnum.BANK == payChannel
                && PaySceneEnum.DIGITAL_CURRENCY == payScene;
    }

    @Override
    public DigitalCurrencyEntryApply buildEntryApply(ChannelEntryOrderEntity orderEntity, ChannelEntryApplyDetailEntity detailEntity) {
        return new DigitalCurrencyEntryApply(orderEntity, detailEntity);
    }

    @Override
    public void resultHandle(final EntryApplyContext<DigitalCurrencyEntryApply> context,
                             final ChannelEntryResult channelEntryResult) {
        baseEntryApplyHandler.resultHandle(context, channelEntryResult);
    }
}
