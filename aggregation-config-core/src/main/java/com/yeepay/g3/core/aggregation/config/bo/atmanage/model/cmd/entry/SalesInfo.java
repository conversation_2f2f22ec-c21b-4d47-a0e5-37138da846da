package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.SalesInfoDTO;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/5/27 20:38
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class SalesInfo {


    /**
     * 客服电话
     */
    private final String servicePhone;

    /**
     * 经营许可证信息
     */

    private final Qualification qualificationInfo;

    /**
     * 经营地址信息
     * 终端报备必须要啊？？？
     */

    private final SalesAddress salesAddress;


    @Builder
    @Getter
    @Setter(AccessLevel.PRIVATE)
    @NoArgsConstructor(force = true)
    @AllArgsConstructor
    public static class SalesAddress {

        /**
         * 省编码
         */
        private final String province;

        /**
         * 市编码
         */
        private final String city;
        /**
         * 区编码
         */
        private final String district;
        /**
         * 地址
         */
        private final String address;

    }

    public static SalesInfo build(SalesInfoDTO salesInfoDTO) {
        return SalesInfo.builder()
                .servicePhone(salesInfoDTO.getServicePhone())
                .qualificationInfo(Qualification.build(salesInfoDTO.getQualificationInfo()))
                .salesAddress(SalesInfo.SalesAddress.builder()
                        .province(salesInfoDTO.getSalesAddressInfo().getProvinceCode())
                        .city(salesInfoDTO.getSalesAddressInfo().getCityCode())
                        .district(salesInfoDTO.getSalesAddressInfo().getDistrictCode())
                        .address(salesInfoDTO.getSalesAddressInfo().getAddress())
                        .build())
                .build();
    }
}
