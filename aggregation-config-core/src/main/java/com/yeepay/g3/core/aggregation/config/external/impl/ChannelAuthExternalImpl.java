package com.yeepay.g3.core.aggregation.config.external.impl;

import com.yeepay.g3.core.aggregation.config.external.ChannelAuthExternal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * description: 通道授权外部域
 * <AUTHOR>
 * @since 2025/5/23:16:14
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class ChannelAuthExternalImpl implements ChannelAuthExternal {

//    /**
//    * 实名认证/商户意愿确认远程服务
//     */
//    private OpenIdentityAuthFacade authFacade = RemoteServiceFactory.getService(OpenIdentityAuthFacade.class);
//    @Override
//    public AuthApplyResultBO submitIdentityAuth(String requestNo, AuthApplyBO authApplyBO) {
////        SubmitIdentityAuthParamDTO paramDTO = MerchantInfoFactory.getMerchantInfoDecorator(
////                authApplyBO.getChannelType(), authApplyBO.getSubjectInfo().getSubjectType())
////                .buildExternalAuth(authApplyBO);
//
////        authFacade.submitIdentityAuth(paramDTO);
//        return null;
//    }
//
//    @Override
//    public void cancelIdentityAuth(String requestNo, AuthCancelBO authCancelBO) {
//        IdentityAuthCancelParamDTO paramDTO = new IdentityAuthCancelParamDTO();
//        paramDTO.setReportFee(SceneAndActivityUtils.getFeeTypeByScene(authCancelBO.getChannel(), authCancelBO.getPayScene()));
//        paramDTO.setChannelIdentifier(authCancelBO.getChannelIdentifier());
//        paramDTO.setChannelId(authCancelBO.getChannelNo());
//        paramDTO.setApplymentId(authCancelBO.getChannelApplyNo());
//        paramDTO.setBusinessCode(requestNo);
//        try {
//            IdentityAuthCancelResultDTO resultDTO = authFacade.cancelIdentityAuth(paramDTO);
//            if (CheckUtils.isEmpty(resultDTO)) {
//                log.error("实名认证取消申请结果为空,请求号:{}", requestNo);
//                throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "实名认证取消申请结果为空");
//            }
//            if (!"200".equals(resultDTO.getReturnCode())
//                    && !"204".equals(resultDTO.getReturnCode())
//                    && !"10000".equals(resultDTO.getReturnCode())) {
//                log.error("实名认证取消申请失败,请求号:{},错误信息:{}", requestNo, resultDTO.getReturnMsg());
//                throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, resultDTO.getReturnMsg());
//            }
//        } catch (AggConfigException e) {
//            throw e;
//        } catch (Exception e) {
//            log.error("实名认证取消申请异常,请求号:{}", requestNo, e);
//            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "实名认证取消申请异常");
//        }
//
//    }
//
//    @Override
//    public AuthResultBO queryIdentityAuth(String requestNo, AuthQueryBO authQueryBO) {
//        IdentityAuthQueryParamDTO paramDTO = new IdentityAuthQueryParamDTO();
//        paramDTO.setReportFee(SceneAndActivityUtils.getFeeTypeByScene(authQueryBO.getChannel(), authQueryBO.getPayScene()));
//        paramDTO.setChannelIdentifier(authQueryBO.getChannelIdentifier());
//        paramDTO.setChannelId(authQueryBO.getChannelNo());
//        paramDTO.setApplymentId(authQueryBO.getChannelApplyNo());
//        paramDTO.setBusinessCode(requestNo);
//        try {
//            IdentityAuthQueryResultDTO resultDTO = authFacade.queryIdentityAuth(paramDTO);
//            if (CheckUtils.isEmpty(resultDTO)) {
//                log.error("实名认证查询结果为空,请求号:{}", requestNo);
//                throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "实名认证查询结果为空");
//            }
//            if (!"10000".equals(resultDTO.getReturnCode())) {
//                log.error("实名认证查询失败,请求号:{},错误信息:{}", requestNo, resultDTO.getReturnMsg());
//                throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, resultDTO.getReturnMsg());
//            }
//            List<AuthRejectReasonBO> rejectReasonList = new ArrayList<>();
//            if (!CheckUtils.isEmpty(resultDTO.getRejectMap())) {
//                resultDTO.getRejectMap().forEach((key, value) ->
//                        rejectReasonList.add(AuthRejectReasonBO.builder().rejectParam(key).rejectReason(value).build())
//                );
//            }
//            return AuthResultBO.builder()
//                    .authStatusEnum(AuthStatusEnum.getEnumByChannelStatus(resultDTO.getApplymentState()))
//                    .qrcodeData(resultDTO.getQrcodeData())
//                    .rejectReasonList(rejectReasonList).build();
//        } catch (AggConfigException e) {
//            throw e;
//        } catch (Exception e) {
//            log.error("实名认证取消申请异常,请求号:{}", requestNo, e);
//            throw new AggConfigException(ErrorCodeEnum.BANKCHANNEL_CONFIG_ERROR, "实名认证取消申请异常");
//        }
//    }


}
