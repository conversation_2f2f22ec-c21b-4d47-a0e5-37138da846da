package com.yeepay.g3.core.aggregation.config.mq.producer.impl;

import com.yeepay.aggregation.config.share.kernel.constant.RocketMqConst;
import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.convert.AnchoredApplyResultConvert;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity;
import com.yeepay.g3.core.aggregation.config.mq.event.AnchoredApplyResultMessage;
import com.yeepay.g3.core.aggregation.config.mq.producer.AnchoredResultProducer;
import com.yeepay.g3.core.aggregation.config.service.AnchoredOrderService;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 挂靠结果发送者
 *
 * @author: Mr.yin
 * @date: 2025/6/10  17:30
 */
@Component
public class AnchoredResultProducerImpl implements AnchoredResultProducer {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "aggConfigAnchoredProducer")
    private DefaultMQProducer aggConfigAnchoredProducer;

    @Resource
    private AnchoredOrderService anchoredOrderService;


    /**
     * 发送挂靠完成消息
     *
     * @param anchoredApply
     */
    @Override
    public void sendAnchoredApplyFinishMessage(AnchoredApply anchoredApply) {
        if (AnchoredApply.needSpecialHandleStatus(anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension())) {
            /*原来的两种模式，是FAIL或聚合挂靠成功就通知消息*/
            if (!DocumentedEnum.inRightEnums(anchoredApply.getAnchoredStatus(), AnchoredStatus.FAIL, AnchoredStatus.AGG_SUCCESS)) {
                logger.info("[发送挂靠完成消息] 当前模式下不允许发送消息 ApplyNo={},AnchoredStatus={}，AnchoredType={},AnchoredDimension={}", anchoredApply.getApplyNo(), anchoredApply.getAnchoredStatus(), anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension());
                return;
            }
        } else {
            if (!DocumentedEnum.inRightEnums(anchoredApply.getAnchoredStatus(), AnchoredStatus.FAIL, AnchoredStatus.CHANNEL_FAIL, AnchoredStatus.SUCCESS)) {
                logger.info("[发送挂靠完成消息] 当前模式下不允许发送消息 ApplyNo={},AnchoredStatus={}，AnchoredType={},AnchoredDimension={}", anchoredApply.getApplyNo(), anchoredApply.getAnchoredStatus(), anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension());
                return;
            }
        }
        AnchoredOrderEntity anchoredOrderEntity = anchoredOrderService.queryAnchoredOrderByOrderNo(anchoredApply.getOrderNo());
        AnchoredApplyResultMessage message = AnchoredApplyResultConvert.covertAnchoredApplyResultMessage(anchoredApply, anchoredOrderEntity);

        logger.info("[发送挂靠完成消息] message={}", JSONUtils.toJsonString(message));
        Message msg = new Message(RocketMqConst.ANCHORED_RESULT_TOPIC, JsonUtils.toJSONString(message).getBytes());
        msg.setKeys(anchoredApply.getApplyNo());
        msg.setTags(anchoredOrderEntity.getBizScene());
        int i = 0;
        while (i < 3) {
            try {
                SendResult sendResult = aggConfigAnchoredProducer.send(msg);
                switch (sendResult.getSendStatus()) {
                    case SEND_OK:
                        logger.info("[发送挂靠完成消息]发送成功 msgId={} message={}", sendResult.getMsgId(), JSONUtils.toJsonString(message));
                        return;
                    default:
                        logger.warn("[发送挂靠完成消息]  [rocketMQ状态异常] ,message={}", JSONUtils.toJsonString(message));
                        throw new BusinessException(ResultCode.SERVER_ERROR, "系统异常");
                }
            } catch (Exception e) {
                logger.warn("[发送挂靠完成消息] [推送风控] MQ发送重试 ,message=" + JSONUtils.toJsonString(message) + " , cause by ", e);
            }
            i++;
            if (i == 3) {
                logger.error("[发送挂靠完成消息] [推送风控] MQ发送失败 message={}", JSONUtils.toJsonString(message));
            }
            try {
                Thread.sleep(100L);
            } catch (InterruptedException e) {
                //do nothing
            }
        }
    }

}
