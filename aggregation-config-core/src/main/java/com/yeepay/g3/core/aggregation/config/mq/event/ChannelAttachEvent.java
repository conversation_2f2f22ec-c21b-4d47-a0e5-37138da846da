package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-16 16:13
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Data
public class ChannelAttachEvent implements Serializable {

    private static final long serialVersionUID = -4553481409864055674L;

    /**
     * 挂靠主商户号
     */
    @NotBlank(message = "挂靠主商户号不能为空")
    private String maskMainMerchantNo;

    /**
     * 挂靠的子商编
     */
    @NotBlank(message = "挂靠的子商编不能为空")
    private String yeepayMerchantNo;

    /**
     * 渠道类型 : /微信/支付宝
     */
    @NotBlank(message = "渠道类型不能为空")
    private String channelType;

    /**
     * 挂靠状态
     * （1:绑定 0:解绑 -1:删除）
     */
    @NotBlank(message = "挂靠状态不能为空")
    private String status;

    /**
     * 成功时间
     */
    private String successTime;

    /**
     * 数据来源
     */
    private String source;
}
