package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.CertificateTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.RegistrationCertificateDTO;
import lombok.*;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * description: 登记证书信息DTO
 *
 * <AUTHOR>
 * @since 2025/5/20:18:44
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@Builder
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class RegistrationCertificateBO implements Serializable {

    private static final long serialVersionUID = -1360791916115858311L;

    /**
     * 商户名称
     */
    private final String certMerchantName;

    /**
     * 法人姓名
     */
    private final String certLegalPerson;

    /**
     * 注册地址
     */
    private final String certCompanyAddress;

    /**
     * 有效期限开始日期
     */
    private final String effectTime;

    /**
     * 有效期限结束日期
     */
    private final String expireTime;

    /**
     * 证书类型
     */
    private final CertificateTypeEnum certType;

    /**
     * 证书编号
     */
    private final String certNumber;

    /**
     * 证书照片
     */
    private final String certCopy;

    public static RegistrationCertificateBO build(RegistrationCertificateDTO certificateDTO) {
        Assert.notNull(certificateDTO, ResultCode.PARAM_VALID_ERROR, "非小微场景登记证书信息不能为空");
        final CertificateTypeEnum certificateType = DocumentedEnum.fromValueOfNullable(CertificateTypeEnum.class, certificateDTO.getCertType());

        return RegistrationCertificateBO.builder()
                .certMerchantName(certificateDTO.getCertMerchantName())
                .certLegalPerson(certificateDTO.getCertLegalPerson())
                .certCompanyAddress(certificateDTO.getCertCompanyAddress())
                .effectTime(buildEffectTime(certificateDTO.getEffectTime()))
                .expireTime(buildExpireTime(certificateDTO.getExpireTime()))
                .certType(certificateType)
                .certNumber(certificateDTO.getCertNumber())
                .certCopy(certificateDTO.getCertCopy())
                .build();
    }

    public static String buildEffectTime(String effectTime) {
        if (StringUtils.isNotBlank(effectTime)) {
            boolean effectTimeIsValid = Const.DATE_PATTERN.matcher(effectTime).matches();
            Assert.isTrue(effectTimeIsValid, ResultCode.PARAM_VALID_ERROR, "证书有效期限开始日期格式错误");
            return effectTime;
        }
        return null;
    }

    public static String buildExpireTime(String expireTime) {
        if (StringUtils.isNotBlank(expireTime)) {
            boolean expireTimeIsValid = Const.DATE_PATTERN.matcher(expireTime).matches()
                    || Const.PERMANENT.equals(expireTime);
            Assert.isTrue(expireTimeIsValid, ResultCode.PARAM_VALID_ERROR, "证书结束日期格式错误");
            return expireTime;
        }
        return null;
    }

    public static RegistrationCertificateBO buildByChannelNoApply(RegistrationCertificateDTO certificateDTO) {
        if (null == certificateDTO) return null;
        return RegistrationCertificateBO.builder()
                .certMerchantName(certificateDTO.getCertMerchantName())
                .certCompanyAddress(certificateDTO.getCertCompanyAddress())
                .certNumber(certificateDTO.getCertNumber())
                .certCopy(certificateDTO.getCertCopy())
                .build();
    }

    /**
     * 是否统一社会信用代码证书
     *
     * @return
     */
    @JsonIgnore
    public boolean isUnifiedCreditCertificate() {
        return CertificateTypeEnum.UNIFIED_CREDIT_CERTIFICATE.equals(certType);
    }
}

