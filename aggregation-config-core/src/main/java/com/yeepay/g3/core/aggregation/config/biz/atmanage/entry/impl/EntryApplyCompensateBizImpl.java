package com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.DivideReceiverBiz;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.TerminalReportBiz;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.entry.EntryApplyCompensateBiz;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.external.ChannelEntryExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.factory.EntryApplyFactory;
import com.yeepay.g3.core.aggregation.config.mq.event.AnchoredApplyResultMessage;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryOrderService;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.core.aggregation.config.service.impl.ChannelEntryApplyService;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/2 22:20
 */
@Slf4j
@Service
public class EntryApplyCompensateBizImpl implements EntryApplyCompensateBiz {
    private final ChannelEntryOrderService channelEntryOrderService;
    private final TerminalReportBiz terminalReportBiz;
    private final DivideReceiverBiz divideReceiverBiz;
    private final ThirdPartyRecordService thirdPartyRecordService;
    private final EntryApplyFactory entryApplyFactory;
    private final ChannelEntryApplyService channelEntryApplyService;
    private final ChannelEntryExternal channelEntryExternal;
    private final TransactionTemplate transactionTemplate;
    private final EntryResultProducer entryResultProducer;


    public EntryApplyCompensateBizImpl(final ChannelEntryOrderService channelEntryOrderService,
                                       final TerminalReportBiz terminalReportBiz,
                                       final DivideReceiverBiz divideReceiverBiz,
                                       final ThirdPartyRecordService thirdPartyRecordService,
                                       final EntryApplyFactory entryApplyFactory,
                                       final ChannelEntryApplyService channelEntryApplyService,
                                       final ChannelEntryExternal channelEntryExternal,
                                       final TransactionTemplate transactionTemplate,
                                       final EntryResultProducer entryResultProducer) {
        this.channelEntryOrderService = channelEntryOrderService;
        this.terminalReportBiz = terminalReportBiz;
        this.divideReceiverBiz = divideReceiverBiz;
        this.thirdPartyRecordService = thirdPartyRecordService;
        this.entryApplyFactory = entryApplyFactory;
        this.channelEntryApplyService = channelEntryApplyService;
        this.channelEntryExternal = channelEntryExternal;
        this.transactionTemplate = transactionTemplate;
        this.entryResultProducer = entryResultProducer;
    }

    @Override
    public void compensateEntryApply(Date startTime, Date endTime, List<String> applyNoList) {
        Assert.isFalse(CollectionUtils.isEmpty(applyNoList) && null == startTime, ResultCode.PARAM_VALID_ERROR, "时间或者单号二选一必填");
        if (!CollectionUtils.isEmpty(applyNoList)) {
            startTime = null;
            endTime = null;
        }
        List<BaseEntryApply<? extends ChannelSpecialParams>> needHandleList = channelEntryOrderService.queryNotFinishChannelEntryOrderBO(startTime, endTime, applyNoList);

        if (CheckUtils.isEmpty(needHandleList)) {
            log.info("[补偿][进件订单] 未查询到需要处理的记录");
            return;
        }
        for (BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply : needHandleList) {
            LogGuidUtil.clearLogContext();
            try {
                // 1.处理进件未完成的
                if (EntryStatusEnum.INIT == baseEntryApply.getEntryStatus()
                        || EntryStatusEnum.PROCESSING == baseEntryApply.getEntryStatus()) {
                    log.info("[补偿][进件订单] 进件处理中 applyNo={},EntryStatus={}", baseEntryApply.getApplyNo(), baseEntryApply.getEntryStatus());
                    compensateEntryResult(baseEntryApply);
                    continue;
                }

                //2.处理终端未完成的
                if (baseEntryApply.canTerminalReport() && !baseEntryApply.getTerminalReportFlagStatus()) {
                    log.info("[补偿][进件订单] 终端未完成  applyNo={}", baseEntryApply.getApplyNo());
                    terminalReportBiz.applyChannelTerminalReport(baseEntryApply);
                    continue;
                }

                //3.处理分账方未完成的
                if (EntryStatusEnum.SUCCESS == baseEntryApply.getEntryStatus()
                        && baseEntryApply.getWxDirectCreateSplitterFlag()
                        && !baseEntryApply.getWxDirectCreateSplitterFlagStatus()) {
                    log.info("[补偿][进件订单] 分账方未完成  applyNo={}", baseEntryApply.getApplyNo());
                    divideReceiverBiz.createDivideReceiver(baseEntryApply);
                }

            } catch (Exception e) {
                log.error("[补偿][进件订单] 批量处理异常 anchoredOrder={} ,cased by", JsonUtils.toJSONString(baseEntryApply), e);
            }
        }
    }

    /**
     * 补查或者重新调用进件
     *
     * @param baseEntryApply
     */
    private void compensateEntryResult(BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply) {
        List<ThirdPartyRecord> records = thirdPartyRecordService.queryNotFinishOrSuccessThirdPartyRecordByApplyNo(baseEntryApply.getApplyNo(), ThirdPartyBusinessTypeEnum.CHANNEL_ENTRY.getDocument());
        Map<ThirdPartyRecordStatusEnum, List<ThirdPartyRecord>> recordMap = records.stream()
                .collect(Collectors.groupingBy(e -> ThirdPartyRecordStatusEnum.SUCCESS == e.getStatus() ? ThirdPartyRecordStatusEnum.SUCCESS : ThirdPartyRecordStatusEnum.PROCESSING));
        if (!CollectionUtils.isEmpty(recordMap.get(ThirdPartyRecordStatusEnum.SUCCESS))) {
            log.warn("[补偿][进件订单] [并发操作] 已存在完成记录: {}", JsonUtils.toJSONString(recordMap.get(ThirdPartyRecordStatusEnum.SUCCESS)));
            throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
        }
        /*1.1 如果没有三方记录*/
        if (CollectionUtils.isEmpty(recordMap.get(ThirdPartyRecordStatusEnum.PROCESSING))) {
            log.info("[补偿][进件订单] 未查询到处理中的三方记录，直接发起 ApplyNo={}", baseEntryApply.getApplyNo());
            channelEntryApplyService.entryApply(new EntryApplyContext<>(baseEntryApply));
            return;
        }
        ThirdPartyRecord notFinishRecord = recordMap.get(ThirdPartyRecordStatusEnum.PROCESSING).get(0);

        log.info("[补偿][进件订单] 获取未完成的 三方记录 notFinishRecord={}", JsonUtils.toJSONString(notFinishRecord));
        /*1.2 有三方记录，INIT 证明发起过调用过，但是没拿到结果，所以要去查询*/
        /*PROCESSING 调用过，也处理了同步响应结果，这种是一直没回调 也要查询*/
        //查询，处理查询结果
        RemoteResult<ChannelEntryResult> queryResult = channelEntryExternal.queryEntryResultByRequestOrderNo(baseEntryApply, notFinishRecord.getRequestNo());
        if (!queryResult.isSuccess()) {
            log.error("[补偿][进件订单] 查询结果失败 ApplyNo={}", baseEntryApply.getApplyNo());
            return;
        }
        ChannelEntryResult channelEntryResult = queryResult.getData();
        EntryApplyContext entryApplyEntryApplyContext = new EntryApplyContext<>(baseEntryApply);
        entryApplyEntryApplyContext.buildThirdPartyRecord(notFinishRecord);
        /*1.3 查询不到*/
        if (null == channelEntryResult) {
            log.info("[补偿][进件订单] 获取结果为空 ApplyNo={}", baseEntryApply.getApplyNo());
            /*因为查不到订单 直接 进件吧 底下控制不落单*/
            channelEntryApplyService.entryApply(entryApplyEntryApplyContext);
            return;
        }
        /*1.4 处理查询*/
        EntryApplyHandler handler = entryApplyFactory.getHandler(baseEntryApply.getPayChannel(), baseEntryApply.getPayScene());
        handler.resultHandle(entryApplyEntryApplyContext, channelEntryResult);
        log.info("[补偿][进件订单] 查询结果处理完成 ApplyNo={}", baseEntryApply.getApplyNo());
    }

    @Override
    public void compensateEntryApplyAnchoredSuccess(AnchoredApplyResultMessage callBackBO) {
        String applyNo = callBackBO.getBizApplyNo();
        String anchoredOrderNo = callBackBO.getOrderNo();
        if (!"SUCCESS".equals(callBackBO.getAnchoredStatus())) {
            log.warn("[回调][进件订单][挂靠结果],如果不是成功，不关心 callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        log.info("[回调][进件订单][挂靠结果]挂靠补偿成功  callBackBO={}", JsonUtils.toJSONString(callBackBO));
        BaseEntryApply<? extends ChannelSpecialParams> entryApply = channelEntryOrderService.getChannelEntryApply(applyNo);
        if (null == entryApply) {
            log.warn("[回调][进件订单][挂靠结果] 未查询到进件申请 callBackBO={}", JsonUtils.toJSONString(callBackBO));
            return;
        }
        if (!entryApply.getAnchoredApplyFlag() || entryApply.getAnchoredApplyFlagStatus()) {
            log.warn("[回调][进件订单][挂靠结果] 进件订单的挂靠状态已成功,或者不需要挂靠 AnchoredFlag={},AnchoredFlagStatus={} callBackBO={}", entryApply.getAnchoredApplyFlag(), entryApply.getAnchoredApplyFlagStatus(), JsonUtils.toJSONString(callBackBO));
            return;
        }
        transactionTemplate.execute(transactionStatus -> {
            /*需要加查询锁更新一下*/
            ChannelEntryApplyDetailEntity currentEntity = channelEntryOrderService.selectChannelEntryApplyDetailEntityForUpdate(applyNo);
            entryApply.flashCurrentFlagStatusAndEntryStatus(currentEntity.getFlagStatus(), DocumentedEnum.fromValueOfNullable(EntryStatusEnum.class, currentEntity.getStatus()));
            if (entryApply.getAnchoredApplyFlagStatus()) {
                log.warn("[回调][进件订单][挂靠结果],挂靠状态已经成功,baseEntryApply={}", JsonUtils.toJSONString(entryApply));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            entryApply.compensateAnchoredSuccess(anchoredOrderNo);
            if (0 == channelEntryOrderService.updateChannelEntryApplyDetailFlagStatus(entryApply)) {
                log.warn("[回调][进件订单][挂靠结果],成功，进件申请更新失败,baseEntryApply={}", JsonUtils.toJSONString(entryApply));
                throw new BusinessException(ResultCode.CONCURRENT_OPERATE_ERROR, "并发操作");
            }
            return null;
        });

        entryResultProducer.sendEntryNotifyMessage(entryApply, EntryNotifyBizTypeEnum.ENTRY_ANCHORED);

    }
}
