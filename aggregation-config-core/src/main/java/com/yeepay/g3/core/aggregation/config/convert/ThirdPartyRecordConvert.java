package com.yeepay.g3.core.aggregation.config.convert;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/10 15:15
 */
public class ThirdPartyRecordConvert {

    public static ThirdPartyRecord createThirdPartyRecord(final EntryApply entryApply) {

        String requestNo = UniqueNoGenerateUtils.getUniqueNo();
        return ThirdPartyRecord.builder()
                .requestNo(requestNo)
                .applyNo(entryApply.getApplyNo())
                .businessType(ThirdPartyBusinessTypeEnum.CHANNEL_ENTRY.getDocument())
                .status(ThirdPartyRecordStatusEnum.INIT)
                .oldStatus(ThirdPartyRecordStatusEnum.INIT)
                .newRecord(Boolean.TRUE)
                .build();
    }

    public static ThirdPartyRecord createTerminalReportThirdPartyRecord(final EntryApply entryApply, ThirdPartyBusinessTypeEnum businessType) {

        String requestNo = UniqueNoGenerateUtils.getUniqueNo();
        return ThirdPartyRecord.builder()
                .requestNo(requestNo)
                .applyNo(entryApply.getApplyNo())
                .businessType(businessType.getDocument())
                .status(ThirdPartyRecordStatusEnum.INIT)
                .oldStatus(ThirdPartyRecordStatusEnum.INIT)
                .newRecord(Boolean.TRUE)
                .build();
    }

    public static ThirdPartyRecordEntity convert(final ThirdPartyRecord thirdPartyRecord) {
        return ThirdPartyRecordEntity.builder()
                .applyNo(thirdPartyRecord.getApplyNo())
                .businessType(thirdPartyRecord.getBusinessType())
                .requestNo(thirdPartyRecord.getRequestNo())
                .status(thirdPartyRecord.getStatus().getDocument())
                .build();
    }

    public static ThirdPartyRecordEntity convertUpdateEntity(final ThirdPartyRecord thirdPartyRecord) {
        String message = thirdPartyRecord.getChannelResponseMessage();
        if (StringUtils.isNotBlank(message) && message.length() > 85) {
            message = message.substring(0, 85);
        }
        return ThirdPartyRecordEntity.builder()
                .innerChannelApplyNo(thirdPartyRecord.getInnerChannelOrderNo())
                .channelResponseCode(thirdPartyRecord.getChannelResponseCode())
                .channelResponseMessage(message)
                .status(thirdPartyRecord.getStatus().getDocument())
                .finishTime(ThirdPartyRecordStatusEnum.SUCCESS.equals(thirdPartyRecord.getStatus()) ? LocalDateTime.now() : null)
                .build();
    }

    public static ThirdPartyRecordEntity convert(String applyNo,
                                                 ThirdPartyBusinessTypeEnum businessType,
                                                 String operator) {
        String requestNo = UniqueNoGenerateUtils.getUniqueNo();
        return ThirdPartyRecordEntity.builder()
                .requestNo(requestNo)
                .applyNo(applyNo)
                .businessType(businessType.getDocument())
                .status(ThirdPartyRecordStatusEnum.INIT.getDocument())
                .createdBy(operator)
                .createDt(LocalDateTime.now())
                .updatedBy(operator)
                .build();
    }

    public static ThirdPartyRecordEntity convertUpdate(String requestNo,
                                                       String channelResponseCode,
                                                       String channelResponseMessage,
                                                       ThirdPartyRecordStatusEnum statusEnum) {
        if (StringUtils.isNotBlank(channelResponseMessage) && channelResponseMessage.length() > 85) {
            channelResponseMessage = channelResponseMessage.substring(0, 85);
        }
        return ThirdPartyRecordEntity.builder()
                .requestNo(requestNo)
                .channelResponseCode(channelResponseCode)
                .channelResponseMessage(channelResponseMessage)
                .status(statusEnum.getDocument())
                .finishTime(ThirdPartyRecordStatusEnum.SUCCESS.equals(statusEnum) ? LocalDateTime.now() : null)
                .build();
    }

    public static ThirdPartyRecordEntity convertUpdate(
            String channelResponseCode,
            String channelResponseMessage,
            ThirdPartyRecordStatusEnum statusEnum) {
        if (StringUtils.isNotBlank(channelResponseMessage) && channelResponseMessage.length() > 85) {
            channelResponseMessage = channelResponseMessage.substring(0, 85);
        }
        return ThirdPartyRecordEntity.builder()
                .channelResponseCode(channelResponseCode)
                .channelResponseMessage(channelResponseMessage)
                .status(statusEnum.getDocument())
                .finishTime(ThirdPartyRecordStatusEnum.SUCCESS.equals(statusEnum) ? LocalDateTime.now() : null)
                .build();
    }

    public static ThirdPartyRecord covertThirdPartyRecord(ThirdPartyRecordEntity entity) {
        if (CheckUtils.isEmpty(entity)) {
            return null;
        }
        return ThirdPartyRecord.builder()
                .id(entity.getId())
                .applyNo(entity.getApplyNo())
                .requestNo(entity.getRequestNo())
                .businessType(entity.getBusinessType())
                .status(DocumentedEnum.fromValue(ThirdPartyRecordStatusEnum.class, entity.getStatus()))
                .oldStatus(DocumentedEnum.fromValue(ThirdPartyRecordStatusEnum.class, entity.getStatus()))
                .innerChannelOrderNo(entity.getInnerChannelApplyNo())
                .channelOrderNo(entity.getChannelApplyNo())
                .finishTime(entity.getFinishTime())
                .delFlag(entity.getDelFlag())
                .channelResponseCode(entity.getChannelResponseCode())
                .channelResponseMessage(entity.getChannelResponseMessage())
                .newRecord(Boolean.FALSE)
                .build();

    }
}
