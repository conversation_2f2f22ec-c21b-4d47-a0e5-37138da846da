package com.yeepay.g3.core.aggregation.config.service.impl;

import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.ThirdPartyRecordStatusEnum;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.convert.ThirdPartyRecordConvert;
import com.yeepay.g3.core.aggregation.config.dao.ThirdPartyRecordDao;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10 20:56
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class ThirdPartyRecordServiceImpl implements ThirdPartyRecordService {

    private final ThirdPartyRecordDao thirdPartyRecordDao;

    @Override
    public int saveThirdPartyRecord(final ThirdPartyRecord thirdPartyRecord) {
        return thirdPartyRecordDao.insertSelective(ThirdPartyRecordConvert.convert(thirdPartyRecord));
    }

    @Override
    public Boolean updateByRequestNo(final ThirdPartyRecord thirdPartyRecord) {
        final Weekend<ThirdPartyRecordEntity> weekend = Weekend.of(ThirdPartyRecordEntity.class, true, true);
        weekend.weekendCriteria()
                .andEqualTo(ThirdPartyRecordEntity::getRequestNo, thirdPartyRecord.getRequestNo())
                .andEqualTo(ThirdPartyRecordEntity::getStatus, thirdPartyRecord.getOldStatus())
                .andNotEqualTo(ThirdPartyRecordEntity::getStatus, ThirdPartyRecordStatusEnum.SUCCESS.getDocument());
        return 1 == thirdPartyRecordDao.updateByExampleSelective(ThirdPartyRecordConvert.convertUpdateEntity(thirdPartyRecord), weekend);
    }

    @Override
    public List<ThirdPartyRecordEntity> queryThirdPartyRecordListByApplyNo(List<String> applyNoList, String businessType) {
        final Weekend<ThirdPartyRecordEntity> weekend = Weekend.of(ThirdPartyRecordEntity.class);
        weekend.weekendCriteria()
                .andEqualTo(ThirdPartyRecordEntity::getBusinessType, businessType)
                .andIn(ThirdPartyRecordEntity::getApplyNo, applyNoList);
        return thirdPartyRecordDao.selectByExample(weekend);

    }

    @Override
    public void saveThirdPartyRecord(ThirdPartyRecordEntity thirdPartyRecordEntity) {
        thirdPartyRecordDao.insertSelective(thirdPartyRecordEntity);
    }

//    @Override
//    public Boolean batchInsertThirdPartyRecord(List<ThirdPartyRecordEntity> records) {
//        return records.size() == thirdPartyRecordDao.batchInsertThirdPartyRecord(records);
//    }

    @Override
    public List<ThirdPartyRecord> queryThirdPartyRecordByApplyNo(String applyNo, String businessType) {
        List<ThirdPartyRecordEntity> records = thirdPartyRecordDao.queryThirdPartyRecordByApplyNo(applyNo, businessType);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream()
                .map(ThirdPartyRecordConvert::covertThirdPartyRecord)
                .collect(Collectors.toList());

    }

    @Override
    public List<ThirdPartyRecord> queryNotFinishOrSuccessThirdPartyRecordByApplyNo(String applyNo, String businessType) {
        final Weekend<ThirdPartyRecordEntity> weekend = Weekend.of(ThirdPartyRecordEntity.class);
        weekend.weekendCriteria()
                .andEqualTo(ThirdPartyRecordEntity::getBusinessType, businessType)
                .andEqualTo(ThirdPartyRecordEntity::getApplyNo, applyNo)
                .andNotIn(ThirdPartyRecordEntity::getStatus, Lists.newArrayList(ThirdPartyRecordStatusEnum.FAIL.getDocument(), ThirdPartyRecordStatusEnum.CLOSED.getDocument()));/*成功的也返回一下*/
        List<ThirdPartyRecordEntity> records = thirdPartyRecordDao.selectByExample(weekend);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream()
                .map(ThirdPartyRecordConvert::covertThirdPartyRecord)
                .collect(Collectors.toList());
    }

    @Override
    public Boolean updateThirdRecordChannelResult(String requestNo,
                                                  String channelCode,
                                                  String channelMsg,
                                                  ThirdPartyRecordStatusEnum statusEnum) {
        // 更新第三方记录基础：渠道返回码、渠道返回信息
        ThirdPartyRecordEntity thirdPartyRecordEntity = ThirdPartyRecordConvert.convertUpdate(requestNo,
                channelCode, channelMsg,
                statusEnum);
        Weekend<ThirdPartyRecordEntity> thirdPartyRecordEntityWeekend = Weekend.of(ThirdPartyRecordEntity.class);
        WeekendCriteria<ThirdPartyRecordEntity, Object> weekendCriteria = thirdPartyRecordEntityWeekend.weekendCriteria();
        weekendCriteria.andEqualTo(ThirdPartyRecordEntity::getRequestNo, requestNo);
        weekendCriteria.andNotEqualTo(ThirdPartyRecordEntity::getStatus, ThirdPartyRecordStatusEnum.SUCCESS.getDocument());
        return 1 == thirdPartyRecordDao.updateByExampleSelective(thirdPartyRecordEntity, thirdPartyRecordEntityWeekend);
    }

    @Override
    public ThirdPartyRecord queryThirdRecordByRequestNo(String requestNo, String businessType) {
        ThirdPartyRecordEntity thirdPartyRecordEntity = thirdPartyRecordDao.queryThirdRecordByRequestNo(requestNo, businessType);
        return ThirdPartyRecordConvert.covertThirdPartyRecord(thirdPartyRecordEntity);
    }
}
