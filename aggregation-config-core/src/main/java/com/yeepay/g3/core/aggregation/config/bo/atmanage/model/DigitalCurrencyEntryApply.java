package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryAuditStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.DigitalCurrencyConfigCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.EntryApplyCmd;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlag;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlagStatus;
import com.yeepay.g3.core.aggregation.config.utils.GMUtils;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025/6/8 14:10
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@SuperBuilder
public class DigitalCurrencyEntryApply extends BaseEntryApply<DigitalCurrencyEntrySpecialParams> {

    /**
     * 银行编码
     */
    private final String bankCode;


    public DigitalCurrencyEntryApply(final ChannelEntryOrderEntity orderEntity,
                                     final ChannelEntryApplyDetailEntity detailEntity) {
        super(orderEntity, detailEntity);
        DigitalCurrencyEntrySpecialParams specialParams = JsonUtils.fromJson(GMUtils.decrypt(detailEntity.getChannelParams()), DigitalCurrencyEntrySpecialParams.class);
        this.channelSpecialParams = specialParams;
        this.bankCode = specialParams == null ? null : specialParams.getBankCode();
    }

    public static DigitalCurrencyEntryApply createDigitalCurrencyEntryApply(final DigitalCurrencyConfigCmd cmd,
                                                                            final String orderNo,
                                                                            final EntryApplyCmd entryApplyCmd) {
        final String applyNo = "D" + UniqueNoGenerateUtils.getUniqueNo();
        return DigitalCurrencyEntryApply.builder()
                .orderNo(orderNo)
                .applyNo(applyNo)
                .bizApplyNo(entryApplyCmd.getBizApplyNo())
                .bizScene(entryApplyCmd.getBizScene())
                .merchantInfo(entryApplyCmd.getMerchantInfo())
                .subjectInfo(entryApplyCmd.getSubjectInfo())
                .contactInfo(entryApplyCmd.getContactInfo())
                .bankCode(cmd.getBankCode())
                .settleAccountInfo(cmd.getSettleAccountInfo())
                .entryType(cmd.getEntryType())
                .payScene(cmd.getPayScene())
                .payChannel(cmd.getPayChannel())
                .channelSpecialParams(DigitalCurrencyEntrySpecialParams.build(cmd))
                .entryAuditStatus(EntryAuditStatus.INIT)
                .entryStatus(EntryStatusEnum.INIT)
                .flag(EntryFlag.NONE.getBit())
                .flagStatus(EntryFlagStatus.NONE.getBit())
                .activityType(ActivityTypeEnum.NORMAL)
                .extendInfo(cmd.getExtendInfo())
                .build();
    }
}
