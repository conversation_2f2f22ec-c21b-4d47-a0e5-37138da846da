package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.ChannelNoBiz;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoQueryBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo.ChannelNoQueryResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.MerchantInfo;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryRequestConvert;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryRequestConvertFactory;
import com.yeepay.g3.core.aggregation.config.convert.ChannelResultConvert;
import com.yeepay.g3.core.aggregation.config.convert.ThirdPartyRecordConvert;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.external.CustomerConfigExternal;
import com.yeepay.g3.core.aggregation.config.external.EntryExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.CustomerConfigResultBO;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.core.aggregation.config.external.constant.CustomerCenterConst;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryResultHandler;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import com.yeepay.g3.core.aggregation.config.service.ThirdPartyRecordService;
import com.yeepay.g3.core.aggregation.config.utils.ConfigUtil;
import com.yeepay.g3.facade.business.manage.enumtype.MerchantBizConfigTypeEnum;
import com.yeepay.g3.facade.trade.bankcooper.OpenPayAsyncReportFacade;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportResponseDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:03
 */
@Slf4j
@Component("baseEntryApplyHandler")
public class BaseEntryApplyHandler<C extends BaseEntryApply<ChannelSpecialParams>> implements EntryApplyHandler<C>, EntryExternal<C> {

    private final ChannelNoBiz channelNoBiz;
    private final ThirdPartyRecordService thirdPartyRecordService;
    private final OpenPayAsyncReportFacade reportFacade;
    private final ChannelEntryResultHandler<C> channelEntryResultHandler;
    private final CustomerConfigExternal customerConfigExternal;
    private final ChannelEntryRequestConvertFactory channelEntryRequestConvertFactory;


    @Autowired
    public BaseEntryApplyHandler(final ChannelNoBiz channelNoBiz, final ThirdPartyRecordService thirdPartyRecordService,
                                 @Qualifier("channelEntryResultHandlerImpl") final ChannelEntryResultHandler<C> channelEntryResultHandler,
                                 final CustomerConfigExternal customerConfigExternal,
                                 final ChannelEntryRequestConvertFactory channelEntryRequestConvertFactory) {
        this.channelNoBiz = channelNoBiz;
        this.thirdPartyRecordService = thirdPartyRecordService;
        this.customerConfigExternal = customerConfigExternal;
        this.reportFacade = RemoteServiceFactory.getService(OpenPayAsyncReportFacade.class);
        this.channelEntryResultHandler = channelEntryResultHandler;
        this.channelEntryRequestConvertFactory = channelEntryRequestConvertFactory;
    }

    @Override
    public void buildChannelNo(final C entryApply) {
        MerchantInfo merchantInfo = entryApply.getMerchantInfo();
        // 直营商户
        if (Boolean.TRUE.equals(merchantInfo.getIsDirectMerchant())) {
            this.buildDirectMerchantChannelNo(entryApply);
        } else {
            // 非直营商户 渠道扩展
            this.buildNoDirectMerchantChannelNo(entryApply);
        }

    }

    @Override
    public void buildBackupInfo(final C entryApply) {

        MerchantInfo merchantInfo = entryApply.getMerchantInfo();
        // 小微或者线下不备份 用户指定按指定
        final boolean micro = entryApply.getSubjectInfo().getSubjectType().isMicro();
        final SceneType sceneType = entryApply.getMerchantInfo().getSceneType();
        boolean isOffline = sceneType != null && sceneType.isContainsOffline();
        boolean backup = entryApply.getSpecifyBackupFlag();

        // 直营商户 或 小微/线下且未指定备份数量时，不启用备份
        if (Boolean.TRUE.equals(merchantInfo.getIsDirectMerchant())
                || ((micro || isOffline) && !backup)) {
            entryApply.buildBackupInfo(0, false);
            return;
        }
        // 用户指定了备份数量
        if (backup) {
            boolean backupSwitch = getBackupSwitch(entryApply.getMerchantInfo());
            entryApply.buildBackupInfo(entryApply.getBackupCounts(), backupSwitch);
            return;
        }

        // 是否存在按商编配置（优先级：父级-> 顶级）
        Integer counts = queryBackupCountFromConfig(entryApply.getPayChannel(), merchantInfo);
        if (counts != null) {
            boolean backupSwitch = getBackupSwitch(merchantInfo);
            entryApply.buildBackupInfo(counts, backupSwitch);
            return;
        }

        // 按行业线+渠道+支付场景取备份数量
        counts = ConfigUtil.getBackUpCountByBusinessLine(entryApply.getMerchantInfo().getIndustryLine(),
                entryApply.getPayChannel());
        if (null != counts) {
            boolean backupSwitch = getBackupSwitch(merchantInfo);
            entryApply.buildBackupInfo(counts, backupSwitch);
        }

    }

    /**
     * 查询客户配置中的备份数量
     * 是否存在按商编配置（优先级：父级-> 顶级）
     */
    private Integer queryBackupCountFromConfig(final PayChannelEnum payChannel, final MerchantInfo merchantInfo) {
        String configType = getConfigTypeByPayChannel(payChannel);
        if (configType == null) {
            return null;
        }

        // 先查父级
        if (StringUtils.isNotBlank(merchantInfo.getParentMerchantNo())) {
            RemoteResult<CustomerConfigResultBO> result = customerConfigExternal.queryCustomerConfig(merchantInfo.getParentMerchantNo(), configType);
            if (result.isSuccess() && "ON".equalsIgnoreCase(result.getData().getConfigStatus()) && StringUtils.isNotBlank(result.getData().getConfigValue())) {
                return Integer.parseInt(result.getData().getConfigValue());
            }
        }

        // 再查顶级
        if (StringUtils.isNotBlank(merchantInfo.getTopLevelMerchantNo())) {
            RemoteResult<CustomerConfigResultBO> result = customerConfigExternal.queryCustomerConfig(merchantInfo.getTopLevelMerchantNo(), configType);
            if (result.isSuccess() && "ON".equalsIgnoreCase(result.getData().getConfigStatus()) && StringUtils.isNotBlank(result.getData().getConfigValue())) {
                return Integer.parseInt(result.getData().getConfigValue());
            }
        }
        return null;
    }

    /**
     * 根据支付渠道获取对应的配置类型
     */
    private String getConfigTypeByPayChannel(final PayChannelEnum payChannel) {
        if (PayChannelEnum.WECHAT == payChannel) {
            return MerchantBizConfigTypeEnum.REPORT_BACKUP_COUNT_WECHAT.name();
        } else if (PayChannelEnum.ALIPAY == payChannel) {
            return MerchantBizConfigTypeEnum.REPORT_BACKUP_COUNT_ALIPAY.name();
        }
        return null;
    }

    private boolean getBackupSwitch(final MerchantInfo merchantInfo) {
        if (StringUtils.isBlank(merchantInfo.getTopLevelMerchantNo()) && StringUtils.isBlank(merchantInfo.getParentMerchantNo())) {
            return false;
        }
        String merchantNo = StringUtils.isNotBlank(merchantInfo.getTopLevelMerchantNo()) ? merchantInfo.getTopLevelMerchantNo() : merchantInfo.getParentMerchantNo();
        RemoteResult<CustomerConfigResultBO> result = customerConfigExternal.queryCustomerConfig(merchantNo, CustomerCenterConst.BACKUP_CHANNEL_MERCHANT_SWITCH);
        return result.isSuccess() && result.getData() != null && CustomerCenterConst.BACKUP_CHANNEL_MERCHANT_SWITCH_OPEN.equals(result.getData().getConfigStatus());
    }

    @Override
    public ChannelEntryResult entryApply(final EntryApplyContext<C> entryApplyContext) {
        if (null == entryApplyContext.getThirdPartyRecord()) {
            final ThirdPartyRecord thirdPartyRecord = ThirdPartyRecordConvert.createThirdPartyRecord(entryApplyContext.getEntryApply());
            entryApplyContext.buildThirdPartyRecord(thirdPartyRecord);
            thirdPartyRecordService.saveThirdPartyRecord(thirdPartyRecord);
        }

        return asyncReport(entryApplyContext);
    }

    @Override
    public BaseEntryApply<ChannelSpecialParams> buildEntryApply(final ChannelEntryOrderEntity orderEntity, ChannelEntryApplyDetailEntity detailEntity) {
        return new BaseEntryApply(orderEntity, detailEntity);
    }


    @Override
    public ChannelEntryResult asyncReport(final EntryApplyContext<C> context) {

        OpenPayAsyncReportResponseDTO responseDTO;

        try {
            @SuppressWarnings("unchecked") final ChannelEntryRequestConvert<C> handler = (ChannelEntryRequestConvert<C>) channelEntryRequestConvertFactory.getHandler(context.getEntryApply().getPayChannel(),
                    context.getEntryApply().getPayScene());

            final OpenPayAsyncReportRequestDTO request = handler.buildChannelRequest(context);
            log.info("报备申请单请求通道，参数：{}", JsonUtils.toJSONString(request));
            responseDTO = reportFacade.asyncReport(request);
            log.info("报备申请单请求通道，返回结果：{}", JsonUtils.toJSONString(responseDTO));
        } catch (Exception e) {
            log.error("报备申请单{}请求通道失败，异常信息：", context.getEntryApply().getApplyNo(), e);
            throw new BusinessException(ResultCode.ENTRY_SUBMIT_ERROR);
        }

        if (!isSuccess(responseDTO)) {
            return convertToFail(responseDTO, context.getThirdPartyRecord().getRequestNo());
        }
        return convert(responseDTO, context.getThirdPartyRecord().getRequestNo());
    }

    @Override
    public ChannelEntryResult convert(final OpenPayAsyncReportResponseDTO responseDTO, final String requestNo) {
        return ChannelResultConvert.convert(responseDTO, requestNo);
    }

    @Override
    public void resultHandle(final EntryApplyContext<C> entryApplyContext,
                             final ChannelEntryResult channelEntryResult) {
        channelEntryResultHandler.resultHandle(entryApplyContext, channelEntryResult);
    }

    /**
     * 构建直营商户的渠道号
     *
     * @param entryApply 进件申请单信息
     */
    private void buildDirectMerchantChannelNo(final C entryApply) {
        // 直营:平台商、标准商户
        boolean canBuild = DocumentedEnum.inRightEnums(entryApply.getMerchantInfo().getRole(),
                MerchantRoleEnum.ORDINARY_MERCHANT, MerchantRoleEnum.PLATFORM_MERCHANT);
        Assert.isTrue(canBuild, ResultCode.ENTRY_ROUTE_CHANNEL_NO_ERROR, "直营商户仅支持标准/平台商");

        // 标准商户：依据渠道、场景、活动，获取默认渠道号
        if (MerchantRoleEnum.ORDINARY_MERCHANT.equals(entryApply.getMerchantInfo().getRole())) {
            this.buildDirectMerchantChannelNoOrdinary(entryApply);
        } else {
            // 平台商：未指定渠道号：取自己作为渠道标识(直营平台商只能上送自己)
            this.buildDirectMerchantChannelNoPlatform(entryApply);
        }
    }

    /**
     * 构建直营标准的渠道号
     *
     * @param entryApply 进件申请单
     */
    private void buildDirectMerchantChannelNoOrdinary(final C entryApply) {
        // 标准商户：依据渠道、场景、活动，获取默认渠道号
        String channelNo = channelNoBiz.queryChannelNoBySceneAndActivity(entryApply.getPayChannel(),
                entryApply.getPayScene(), entryApply.getActivityType(), entryApply.getMerchantInfo().getIndustryLine());
        Assert.notBlank(channelNo, ResultCode.ENTRY_ROUTE_UN_FIND_CHANNEL_NO_ERROR);
        entryApply.buildChannelIdentifier(null);
        entryApply.buildChannelNo(channelNo);
    }

    /**
     * 构建直营平台商的渠道号
     *
     * @param entryApply 进件申请单
     */
    private void buildDirectMerchantChannelNoPlatform(final C entryApply) {
        if (!MerchantRoleEnum.PLATFORM_MERCHANT.equals(entryApply.getMerchantInfo().getRole())) {
            return;
        }
        ChannelNoQueryBO platformChannelNoQueryBO = ChannelNoQueryBO.builder().merchantNo(entryApply.getMerchantInfo().getMerchantNo())
                .payChannel(entryApply.getPayChannel()).payScene(entryApply.getPayScene()).activityType(entryApply.getActivityType()).build();
        List<ChannelNoQueryResultBO> platformResults = channelNoBiz.queryChannelNoList(platformChannelNoQueryBO);

        Assert.notEmpty(platformResults, ResultCode.ENTRY_ROUTE_UN_FIND_CHANNEL_NO_ERROR);
        entryApply.buildChannelNo(null);
        entryApply.buildChannelIdentifier(entryApply.getMerchantInfo().getMerchantNo());
    }

    /**
     * 构建非直营商户的渠道号
     *
     * @param entryApply 进件申请单
     */
    private void buildNoDirectMerchantChannelNo(final C entryApply) {
        // 指定渠道信息
        if (entryApply.getSpecifyChannelNoFlag()) {
            this.buildSpecifyChannelInfo(entryApply);
        } else {
            // 非指定渠道信息
            this.buildNoSpecifyChannelInfo(entryApply);
        }
    }

    /**
     * 构建指定渠道信息
     *
     * @param entryApply 进件申请单
     */
    private void buildSpecifyChannelInfo(final C entryApply) {
        if (!StringUtils.isBlank(entryApply.getChannelNo())) {
            // 校验指定的渠道号
            Assert.isTrue(channelNoBiz.validateChannelNo(entryApply.getChannelNo()), ResultCode.ENTRY_CHANNEL_NO_VALID_ERROR, "渠道号不存在");
        }

        if (!StringUtils.isBlank(entryApply.getChannelIdentifier())) {
            // 校验指定的渠道标识
            ChannelNoQueryBO topChannelNoQueryBO = ChannelNoQueryBO.builder().merchantNo(entryApply.getChannelIdentifier())
                    .payChannel(entryApply.getPayChannel()).payScene(entryApply.getPayScene()).activityType(entryApply.getActivityType()).build();
            Assert.notEmpty(channelNoBiz.queryChannelNoList(topChannelNoQueryBO), ResultCode.ENTRY_CHANNEL_NO_VALID_ERROR, "渠道标识不可用");
        }

        // 本期不做渠道号-渠道标识关系校验

    }

    /**
     * 构建非指定渠道信息
     *
     * @param entryApply 进件申请单
     */
    private void buildNoSpecifyChannelInfo(final C entryApply) {

        // 查询上级渠道号
        Assert.notBlank(entryApply.getMerchantInfo().getParentMerchantNo(), ResultCode.ENTRY_ROUTE_CHANNEL_NO_ERROR, "渠道拓展商户需要传上级商户编码");
        ChannelNoQueryBO channelNoQueryBO = ChannelNoQueryBO.builder().merchantNo(entryApply.getMerchantInfo().getParentMerchantNo())
                .payChannel(entryApply.getPayChannel()).payScene(entryApply.getPayScene()).activityType(entryApply.getActivityType()).build();
        List<ChannelNoQueryResultBO> parentResultBOList = channelNoBiz.queryChannelNoList(channelNoQueryBO);
        if (!CheckUtils.isEmpty(parentResultBOList)) {
            entryApply.buildChannelIdentifier(entryApply.getMerchantInfo().getParentMerchantNo());
            return;
        }

        // 未找到上级渠道号：查询顶级渠道号
        Assert.notBlank(entryApply.getMerchantInfo().getTopLevelMerchantNo(), ResultCode.ENTRY_ROUTE_UN_FIND_CHANNEL_NO_ERROR);
        ChannelNoQueryBO topChannelNoQueryBO = ChannelNoQueryBO.builder().merchantNo(entryApply.getMerchantInfo().getTopLevelMerchantNo())
                .payChannel(entryApply.getPayChannel()).payScene(entryApply.getPayScene()).activityType(entryApply.getActivityType()).build();
        List<ChannelNoQueryResultBO> topResults = channelNoBiz.queryChannelNoList(topChannelNoQueryBO);
        Assert.notEmpty(topResults, ResultCode.ENTRY_ROUTE_UN_FIND_CHANNEL_NO_ERROR);
        entryApply.buildChannelIdentifier(entryApply.getMerchantInfo().getTopLevelMerchantNo());

    }
}
