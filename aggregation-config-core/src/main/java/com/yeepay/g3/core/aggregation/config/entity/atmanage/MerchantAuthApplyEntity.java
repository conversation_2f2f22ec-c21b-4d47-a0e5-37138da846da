package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * description: 商户实名认证申请实体类
 * <AUTHOR>
 * @since 2025/5/21:15:09
 * Company: 易宝支付(YeePay)
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Table(name = "t_merchant_channel_apply")
public class MerchantAuthApplyEntity extends BaseEntity {

    /**
     * 业务申请单号
     */
    @Column(name = "biz_apply_no", length = 32)
    private String bizApplyNo;

    /**
     * 申请编号
     */
    @Column(name = "apply_no", length = 32)
    private String applyNo;

    /**
     * 三方记录表请求号
     */
    @Column(name = "request_no", length = 32)
    private String requestNo;

    /**
     * 第三方返回单号
     */
    @Column(name = "channel_apply_no", length = 32)
    private String channelApplyNo;

    /**
     * 机构代码
     */
    @Column(name = "institution_code", length = 16)
    private String institutionCode;

    /**
     * 渠道号
     */
    @Column(name = "channel_no", length = 32)
    private String channelNo;

    /**
     * 渠道标识
     */
    @Column(name = "channel_identifier", length = 32)
    private String channelIdentifier;

    /**
     * 支付场景
     */
    @Column(name = "pay_scene", length = 8)
    private String payScene;

    /**
     * 状态
     */
    @Column(name = "status", length = 16)
    private String status;

    /**
     * 商户编码
     */
    @Column(name = "merchant_no", length = 32)
    private String merchantNo;

    /**
     * 父级商户编码
     */
    @Column(name = "parent_merchant_no", length = 32)
    private String parentMerchantNo;

    /**
     * 顶级商户编码
     */
    @Column(name = "top_merchant_no", length = 32)
    private String topMerchantNo;

    /**
     * 商户名称
     */
    @Column(name = "merchant_name", length = 128)
    private String merchantName;

    /**
     * 短名
     */
    @Column(name = "short_name", length = 64)
    private String shortName;

    /**
     * 主体信息（JSON格式）
     */
    @Column(name = "subject_info", columnDefinition = "text")
    private String subjectInfo;

    /**
     * 联系人信息（JSON格式）
     */
    @Column(name = "contact_info", columnDefinition = "text")
    private String contactInfo;

    /**
     * 最终受益人信息（JSON格式）
     */
    @Column(name = "ubo_info", columnDefinition = "text")
    private String uboInfo;

    /**
     * 拒绝原因
     */
    @Column(name = "reject_reason", columnDefinition = "text")
    private String rejectReason;

    /**
     * 二维码数据
     */
    @Column(name = "qrcode_data", columnDefinition = "text")
    private String qrcodeData;

//    public static MerchantAuthApplyEntity build(AuthApplyBO authApplyBO) {
//        String applyNo = Const.MERCHANT_AUTH_APPLY_ORDER_PREFIX + UniqueNoGenerateUtils.getUniqueNo();
//        MerchantAuthApplyEntity entity = MerchantAuthApplyEntity.builder().build();
//        entity.setBizApplyNo(authApplyBO.getBizApplyNo());
//        entity.setApplyNo(applyNo);
//        // todo-LZX 更新
//        entity.setRequestNo(applyNo);
//        entity.setInstitutionCode(authApplyBO.getChannelType().getDocument());
//        entity.setChannelNo(authApplyBO.getChannelNo());
//        entity.setChannelIdentifier(authApplyBO.getChannelIdentifier());
//        entity.setPayScene(authApplyBO.getPayScene().getDocument());
//        entity.setStatus(AuthStatusEnum.WAIT_SUBMIT.getDocument());
//        entity.setMerchantNo(authApplyBO.getMerchantNo());
////        entity.setParentMerchantNo(authApplyBO.get);
////        entity.setTopMerchantNo();
//        // todo-LZX-小微取门店名称/取证件名称
////        entity.setMerchantName(authApplyBO.getSubjectInfo().get);
////        entity.setShortName(authApplyBO.);
//        entity.setSubjectInfo(JSONObject.toJSONString(authApplyBO.getSubjectInfo()));
//        entity.setContactInfo(JSONObject.toJSONString(authApplyBO.getContactInfo()));
//        entity.setUboInfo(JSONObject.toJSONString(authApplyBO.getUboInfoList()));
//        entity.setCreatedBy(Const.SYSTEM);
//        entity.setCreateDt(LocalDateTime.now());
//        entity.setUpdatedBy(Const.SYSTEM);
//        entity.setUpdateDt(LocalDateTime.now());
//        return entity;
//    }
//
//    public static MerchantAuthApplyEntity buildUpdate(String requestNo, AuthResultBO authResultBO) {
//        MerchantAuthApplyEntity entity = MerchantAuthApplyEntity.builder().build();
//        entity.setRequestNo(requestNo);
//        entity.setStatus(authResultBO.getAuthStatusEnum().getDocument());
//        entity.setQrcodeData(authResultBO.getQrcodeData());
//        entity.setUpdatedBy(Const.SYSTEM);
//        entity.setUpdateDt(LocalDateTime.now());
//        entity.setRejectReason(CheckUtils.isEmpty(authResultBO.getRejectReasonList())
//                ? null
//                : JsonUtils.toJSONString(authResultBO.getRejectReasonList()));
//        return entity;
//    }
//
//    public static MerchantAuthApplyEntity buildUpdateChannelApplyNo(String requestNo, String channelApplyNo,
//                                                                    AuthStatusEnum authStatusEnum) {
//        MerchantAuthApplyEntity entity = MerchantAuthApplyEntity.builder().build();
//        entity.setRequestNo(requestNo);
//        entity.setStatus(authStatusEnum.getDocument());
//        entity.setChannelApplyNo(channelApplyNo);
//        entity.setUpdatedBy(Const.SYSTEM);
//        entity.setUpdateDt(LocalDateTime.now());
//        return entity;
//    }
}
