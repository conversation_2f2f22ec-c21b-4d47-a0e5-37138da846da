package com.yeepay.g3.core.aggregation.config.entity;

import com.yeepay.g3.core.aggregation.config.enums.ChannelAppIdBindStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Appid和商户绑定关系实体类
 * <p>
 * 该类对应数据库表 TBL_APPID_MERCHANT_BIND，表示appid与商户的绑定关系。
 * </p>
 */
@Data
public class AppIdMerchantBindEntity implements Serializable {

    private static final long serialVersionUID = 6919639813007608955L;
    /**
     * 主键，自动生成的唯一标识符
     */
    private Long id;

    /**
     * appid
     */
    private String appId;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     *  一级商户编号
     */
    private String firstMerchantNo;

    /**
     * 二级商户编号
     */
    private String secondMerchantNo;

    /**
     * 三级商户编号
     */
    private String threeMerchantNo;

    /**
     * 报备商户号
     */
    private String reportMerchantNo;

    /**
     * 绑定状态
     */
    private ChannelAppIdBindStatusEnum bindStatus;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 异常信息编码
     */
    private String errorCode;

    /**
     * 数据清洗状态，默认值为'INIT'
     */
    private DataCleanStatusEnum cleanStatus;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date updateTime;

    /**
     * 操作时间
     */
    private Date operateTime;
}
