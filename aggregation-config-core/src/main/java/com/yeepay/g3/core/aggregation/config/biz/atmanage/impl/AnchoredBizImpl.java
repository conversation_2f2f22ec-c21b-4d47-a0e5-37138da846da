package com.yeepay.g3.core.aggregation.config.biz.atmanage.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.AnchoredBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredOrder;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyDetailResultBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredApplyRespBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredResultBO;
import com.yeepay.g3.core.aggregation.config.convert.AnchoredApplyResultConvert;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity;
import com.yeepay.g3.core.aggregation.config.factory.anchored.AnchoredTypeHandlerRouter;
import com.yeepay.g3.core.aggregation.config.service.AnchoredApplyService;
import com.yeepay.g3.core.aggregation.config.service.AnchoredOrderService;
import com.yeepay.g3.core.aggregation.config.utils.LogGuidUtil;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author: Mr.yin
 * @date: 2025/6/5  18:40
 */
@Component
public class AnchoredBizImpl implements AnchoredBiz {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private AnchoredOrderService anchoredOrderService;

    @Resource
    private AnchoredApplyService anchoredApplyService;

    @Resource
    private AnchoredTypeHandlerRouter anchoredTypeHandlerRouter;

    @Resource
    @Qualifier("anchoredHandleExecutor")
    private AtlasThreadPoolExecutor anchoredHandleExecutor;


    @Override
    public AnchoredApplyRespBO anchoredApply(AnchoredCmd anchoredInfoCmd) {
        logger.info("挂靠请求开始 anchoredInfoCmd={}", JsonUtils.toJSONString(anchoredInfoCmd));
        AnchoredOrder anchoredOrder = AnchoredOrder.createAnchoredOrder(anchoredInfoCmd);
        //挂靠请求的参数校验
        for (AnchoredApply anchoredApply : anchoredOrder.getAnchoredApplyList()) {
            anchoredApply.verifyAnchoredApplyParam();
        }
        return asyncAnchoredApply(anchoredOrder);
    }


    public AnchoredApplyRespBO asyncAnchoredApply(AnchoredOrder anchoredOrder) {
        AnchoredApplyRespBO respBO = new AnchoredApplyRespBO();
        respBO.setBizScene(anchoredOrder.getBizScene().getDocument());
        respBO.setBizApplyNo(anchoredOrder.getBizApplyNo());

        /*1.落库*/
        AnchoredOrderEntity anchoredOrderEntity = anchoredOrderService.queryAnchoredOrderByRequestNo(anchoredOrder.getBizScene().getDocument(), anchoredOrder.getBizApplyNo());
        if (anchoredOrderEntity != null) {
            logger.info("[挂靠请求] 重复请求  anchoredOrderEntity={}", JsonUtils.toJSONString(anchoredOrderEntity));
            respBO.setOrderNo(anchoredOrderEntity.getOrderNo());
            return respBO;
        } else {
            anchoredOrderService.createAnchoredOrder(anchoredOrder);
        }
        respBO.setApplyStatus(AnchoredStatus.PROCESSING.getDocument());
        respBO.setOrderNo(anchoredOrder.getOrderNo());
        /*转异步 挂靠业务批量处理 处理*/
        asyncHandleAnchoredApply(anchoredOrder);
        logger.info("[请求挂靠] 受理完成 respBO={}", JsonUtils.toJSONString(respBO));
        return respBO;
    }

    @Override
    public AnchoredApplyRespBO anchoredApply(AnchoredOrder anchoredOrder) {
        logger.info("[请求挂靠][单笔同步场景] anchoredOrder={}", JsonUtils.toJSONString(anchoredOrder));
        if (Boolean.FALSE.equals(anchoredOrder.getSingSync()) || anchoredOrder.getAnchoredApplyList().size() > 1) {
            logger.info("[请求挂靠][单笔同步场景] 只能是单笔请求anchoredOrder={}", JsonUtils.toJSONString(anchoredOrder));
            /*有问题报错*/
            throw new BusinessException(ResultCode.PARAM_VALID_ERROR, "单笔同步请求不能处理多个挂靠业务");
        }
        anchoredOrder.getAnchoredApplyList().get(0).verifyAnchoredApplyParam();
        AnchoredApplyRespBO respBO = new AnchoredApplyRespBO();
        respBO.setBizScene(anchoredOrder.getBizScene().getDocument());
        respBO.setBizApplyNo(anchoredOrder.getBizApplyNo());
        /*1.落库*/
        AnchoredOrderEntity anchoredOrderEntity = anchoredOrderService.queryAnchoredOrderByRequestNo(anchoredOrder.getBizScene().getDocument(), anchoredOrder.getBizApplyNo());
        if (anchoredOrderEntity != null) {
            logger.info("[挂靠请求] [单笔同步场景]已受理 anchoredOrderEntity={}", JsonUtils.toJSONString(anchoredOrderEntity));
            respBO.setOrderNo(anchoredOrderEntity.getOrderNo());
            List<AnchoredApplyEntity> anchoredApplyList = anchoredApplyService.queryAllAnchoredApplyByOrderNo(anchoredOrderEntity.getOrderNo());
            AnchoredApply apply = AnchoredApply.covertAnchoredApply(anchoredApplyList.get(0));
            respBO.setApplyStatus(AnchoredApply.covertSyncStatus(apply.getAnchoredStatus(), apply.getAnchoredType(), apply.getAnchoredDimension()));
            return respBO;
        } else {
            anchoredOrderService.createAnchoredOrder(anchoredOrder);
        }
        respBO.setApplyStatus(AnchoredStatus.PROCESSING.getDocument());
        respBO.setOrderNo(anchoredOrder.getOrderNo());
        AnchoredApply apply = anchoredOrder.getAnchoredApplyList().get(0);
        apply.updateSingSync(Boolean.TRUE);
        try {
            anchoredTypeHandlerRouter.getHandler(apply.getAnchoredType(), apply.getAnchoredDimension()).anchoredApplyHandle(apply);
            respBO.setApplyStatus(AnchoredApply.covertSyncStatus(apply.getAnchoredStatus(), apply.getAnchoredType(), apply.getAnchoredDimension()));
        } catch (Exception e) {
            logger.info(String.format("[请求挂靠][单笔同步场景] 处理异常 BizApplyNo={%s} cased by", anchoredOrder.getBizApplyNo()), e);
        }
        logger.info("[请求挂靠] [单笔同步场景] 处理完成 respBO={}", JsonUtils.toJSONString(respBO));
        return respBO;
    }

    /**
     * 批量异步处理挂靠请求
     *
     * @param anchoredOrder
     */
    private void asyncHandleAnchoredApply(AnchoredOrder anchoredOrder) {
        try {
            anchoredOrder.getAnchoredApplyList().forEach(anchoredApply -> CompletableFuture.runAsync(() -> {
                        try {
                            LogGuidUtil.clearLogContext();
                            logger.info("[请求异步处理挂靠]  异步处理开始 anchoredApply={} ", JsonUtils.toJSONString(anchoredApply));
                            anchoredTypeHandlerRouter.getHandler(anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension())
                                    .anchoredApplyHandle(anchoredApply);
                            logger.info("[请求异步处理挂靠]  异步处理完成 anchoredApply={} ", JsonUtils.toJSONString(anchoredApply));
                        } catch (Exception e) {
                            logger.error(String.format("[请求挂靠] 挂靠异常 anchoredApply={%s} ,cased by", JsonUtils.toJSONString(anchoredApply)), e);
                        }
                    }, anchoredHandleExecutor)
            );
        } catch (Exception e) {
            logger.error(String.format("[请求挂靠] 批量处理异常 anchoredOrder={%s} ,cased by", JsonUtils.toJSONString(anchoredOrder)), e);
        }
    }

    @Override
    public void compensateAnchoredOrder(Date startTime, Date endTime, List<String> orderNoList) {
        Assert.isFalse(CollectionUtils.isEmpty(orderNoList) && null == startTime, ResultCode.PARAM_VALID_ERROR, "时间或者单号二选一必填");
        if (!CollectionUtils.isEmpty(orderNoList)) {
            startTime = null;
            endTime = null;
        }
        List<AnchoredOrderEntity> needHandleList = anchoredOrderService.selectUnFinishAnchoredOrderList(orderNoList, startTime, endTime);

        if (CheckUtils.isEmpty(needHandleList)) {
            logger.info("[补偿][挂靠订单][compensateAnchoredOrder] 未查询到需要处理的认证申请记录");
            return;
        }
        needHandleList.forEach(anchoredOrderEntity -> {
            try {
                List<AnchoredApplyEntity> anchoredApplyEntityList = anchoredApplyService.queryAllAnchoredApplyByOrderNo(anchoredOrderEntity.getOrderNo());
                List<AnchoredApply> anchoredApplyList = anchoredApplyEntityList.stream()
                        .filter(anchoredApplyEntity -> !DocumentedEnum.stringInRightEnums(anchoredApplyEntity.getAnchoredStatus(), AnchoredStatus.FAIL, AnchoredStatus.SUCCESS, AnchoredStatus.CHANNEL_FAIL))
                        .map(AnchoredApply::covertAnchoredApply).collect(Collectors.toList());
                anchoredApplyList.forEach(anchoredApply -> {
                    LogGuidUtil.clearLogContext();
                    try {
                        anchoredTypeHandlerRouter.getHandler(anchoredApply.getAnchoredType(), anchoredApply.getAnchoredDimension())
                                .anchoredApplyHandle(anchoredApply);
                    } catch (Exception e) {
                        logger.error(String.format("[补偿][请求挂靠] 挂靠异常 anchoredApply={%s} ,cased by", JsonUtils.toJSONString(anchoredApply)), e);
                    }
                });
            } catch (Exception e) {
                logger.error(String.format("[补偿][请求挂靠] 批量处理异常 anchoredOrder={%s} ,cased by", JsonUtils.toJSONString(anchoredOrderEntity)), e);
            }
        });


    }

    @Override
    public AnchoredResultBO queryAnchoredResult(String bizApplyNo, String bizScene) {
        AnchoredOrderEntity anchoredOrderEntity = anchoredOrderService.queryAnchoredOrderByRequestNo(bizScene, bizApplyNo);
        if (null == anchoredOrderEntity) {
            logger.info("[请求查询挂靠结果] 未查询到该单 bizApplyNo={},bizScene={}", bizApplyNo, bizScene);
            return null;
        }
        AnchoredResultBO anchoredResultBO = new AnchoredResultBO();
        anchoredResultBO.setBizScene(anchoredOrderEntity.getBizScene());
        anchoredResultBO.setBizApplyNo(anchoredOrderEntity.getBizApplyNo());
        anchoredResultBO.setOrderNo(anchoredOrderEntity.getOrderNo());

        List<AnchoredApplyEntity> anchoredApplyEntityList = anchoredApplyService.queryAllAnchoredApplyByOrderNo(anchoredOrderEntity.getOrderNo());
        List<AnchoredApplyDetailResultBO> anchoredApplyList = anchoredApplyEntityList.stream().map(AnchoredApplyResultConvert::covertAnchoredApplyQueryResult
        ).collect(Collectors.toList());
        anchoredResultBO.setAnchoredApplyList(anchoredApplyList);

        anchoredResultBO.setOrderStatus(anchoredOrderEntity.getOrderStatus());
        if (!"FINISH".equals(anchoredResultBO.getOrderStatus())) {
            boolean finishTag = anchoredApplyEntityList.stream().allMatch(e -> DocumentedEnum.stringInRightEnums(e.getAnchoredStatus(), AnchoredStatus.SUCCESS, AnchoredStatus.FAIL));
            if (finishTag) {
                anchoredResultBO.setOrderStatus("FINISH");
            }
        }

        logger.info("[查询挂靠结果] 查询结果 anchoredResultBO={}", JsonUtils.toJSONString(anchoredResultBO));
        return anchoredResultBO;
    }
}
