package com.yeepay.g3.core.aggregation.config.impl;

import com.yeepay.g3.core.aggregation.config.biz.ChannelAppIdBindBiz;
import com.yeepay.g3.core.aggregation.config.biz.ChannelAppidControlBiz;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.service.AttachResultService;
import com.yeepay.g3.facade.aggregation.config.facade.AggConfigScheduleFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-30 17:58
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class AggConfigScheduleFacadeImpl implements AggConfigScheduleFacade {

    @Autowired
    private ChannelAppidControlBiz appidControlBiz;

    @Autowired
    private ChannelAppIdBindBiz channelAppIdBindBiz;

    @Autowired
    private AttachResultService attachResultService;

    @Override
    public void compensateNotify(String appId) {
        log.info("补偿 appid 通知请求参数appid={}",appId);
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("APPId不能为空");
        }
        appidControlBiz.compensateNotify(appId);
    }

    @Override
    public void compensateAppIdBind(String appId, String merchantNo) {
        log.info("补偿appId绑定关系appId={}",appId);
        if (StringUtils.isBlank(appId) && StringUtils.isBlank(merchantNo)) {
            throw new IllegalArgumentException("APPId不能为空");
        }
        channelAppIdBindBiz.compensateAppIdBind(appId, merchantNo);
    }

    @Override
    public void compensateAttachBind(String mainMerchantNo, String subMerchantNo,String channel) {
        log.info("挂靠数据合并mainMerchantNo={},subMerchantNo={},channel={}",mainMerchantNo,subMerchantNo,channel);
        if (StringUtils.isBlank(mainMerchantNo) || StringUtils.isBlank(subMerchantNo) || StringUtils.isBlank(channel)) {
            throw new IllegalArgumentException("主商编+子商编号+渠道不能为空");
        }
        AttachResultEntity query = new AttachResultEntity();
        query.setMainMerchantNo(mainMerchantNo);
        query.setSubMerchantNo(subMerchantNo);
        query.setChannel(channel);
        AttachResultEntity attachResultEntity = attachResultService.queryByUniqueKey(query);
        if (attachResultEntity != null && DataCleanStatusEnum.INIT.name().equalsIgnoreCase(attachResultEntity.getCleanStatus())) {
            channelAppIdBindBiz.attachBind(attachResultEntity);
        }
    }
}
