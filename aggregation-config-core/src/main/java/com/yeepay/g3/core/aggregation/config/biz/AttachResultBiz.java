package com.yeepay.g3.core.aggregation.config.biz;

import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAttachEvent;
import org.apache.commons.lang3.tuple.Pair;

import java.text.ParseException;

/**
 * @Description: 挂靠关系同步业务
 * @ClassName: AttachResultBiz
 * @Author: cong.huo
 * @Date: 2024/12/17 17:08   // 时间
 * @Version: 1.0
 */
public interface AttachResultBiz {
    /**
     * 处理同步挂靠关系
     *
     * @param message
     * @param merchantGroupEntity
     * @return void
     * <AUTHOR>
     * @date 2024/12/17 17:09
     */
    Pair<Boolean,AttachResultEntity> handle(ChannelAttachEvent message, MerchantGroupEntity merchantGroupEntity) throws ParseException;
}
