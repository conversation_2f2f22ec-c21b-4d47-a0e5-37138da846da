package com.yeepay.g3.core.aggregation.config.external.impl;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BaseException;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryAnchoredInfoBO;
import com.yeepay.g3.core.aggregation.config.external.AggAttachRecordExternal;
import com.yeepay.g3.core.aggregation.config.external.bo.QueryEntryProcessBaseReqBO;
import com.yeepay.g3.facade.aggregation.pay.dto.AttachRecordQueryBySubReqDTO;
import com.yeepay.g3.facade.aggregation.pay.dto.AttachRecordQueryBySubRespDTO;
import com.yeepay.g3.facade.aggregation.pay.enums.ErrorCodeEnum;
import com.yeepay.g3.facade.aggregation.pay.facade.AttachReportFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * description: 描述
 *
 * <AUTHOR>
 * @since 2025/6/10:21:53
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@Service
public class AggAttachRecordExternalImpl implements AggAttachRecordExternal {

    private final AttachReportFacade reportFacade = RemoteServiceFactory.getService(AttachReportFacade.class);

    @Override
    public List<QueryAnchoredInfoBO> queryAttachRecord(QueryEntryProcessBaseReqBO reqBO) {
        AttachRecordQueryBySubReqDTO reqDTO = new AttachRecordQueryBySubReqDTO();
        reqDTO.setSubMerchantNo(reqBO.getMerchantNo());
        reqDTO.setChannel(reqBO.getPayChannel().getDocument());
        try {
            log.info("查询聚合挂靠记录，参数：{}", reqDTO);
            AttachRecordQueryBySubRespDTO responseDTO = reportFacade.queryAttachRecordBySubMerchant(reqDTO);
            log.info("查询聚合挂靠记录，返回结果：{}", responseDTO);
            if (responseDTO == null) {
                throw new BusinessException(ResultCode.SYSTEM_ERROR);
            } else if (!ErrorCodeEnum.SUCCESS.getCode().equals(responseDTO.getCode())) {
                throw new BusinessException(ResultCode.ATTACH_QUERY_ERROR, responseDTO.getMessage());
            }
            return QueryAnchoredInfoBO.buildList(responseDTO.getInfoList());
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询聚合挂靠记录，异常信息：", e);
            throw new BusinessException(ResultCode.ATTACH_QUERY_ERROR, e.getMessage());
        }

    }
}
