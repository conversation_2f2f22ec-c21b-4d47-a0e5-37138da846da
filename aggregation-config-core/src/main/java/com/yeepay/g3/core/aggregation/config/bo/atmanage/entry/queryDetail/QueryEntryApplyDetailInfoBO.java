package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryAuditStatus;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity;
import com.yeepay.g3.core.aggregation.config.enums.EntryApplyMainStatus;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryApplyInfoDetailDTO;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 聚合进件查询结果DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:57
 */
@Builder
@Getter
public class QueryEntryApplyDetailInfoBO implements Serializable {

    private static final long serialVersionUID = 3031449910682876113L;

    /**
     * 支付渠道（必填）
     */
    private PayChannelEnum payChannel;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型（必填）
     */
    private ActivityTypeEnum activityType;

    /**
     * 通道费率（必填）
     */
    private String channelFeeType;

    /**
     * 通道活动类型（必填）
     */
    private String channelActivityType;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道标识
     */
    private String channelIdentifier;

    /**
     * 业务场景
     */
    private BizSceneEnum bizScene;

    /**
     * 进件类型
     */
    private EntryTypeEnum entryType;

    /**
     * 状态
     */
    private EntryApplyMainStatus status;

    /**
     * 进件状态
     */
    private EntryStatusEnum entryStatus;

    /**
     * 审核状态
     */
    private EntryAuditStatus auditStatus;

    /**
     * 进件失败Code
     */
    private String entryFailCode;

    /**
     * 进件失败原因
     */
    private String entryFailReason;

    /**
     * 是否需要终端报备
     */
    private Boolean anchoredFlag;

    /**
     * 终端报备状态
     */
    private Boolean anchoredFlagStatus;

    /**
     * 是否需要终端报备
     */
    private Boolean terminalReportFlag;

    /**
     * 终端报备状态
     */
    private Boolean terminalReportFlagStatus;

    /**
     * 是否需要创建分账方
     */
    private Boolean createSplitterFlag;

    /**
     * 创建分账方状态
     */
    private Boolean createSplitterFlagStatus;

    public static List<QueryEntryApplyDetailInfoBO> buildList(List<BaseEntryApply<? extends ChannelSpecialParams>> baseEntryApplys,
                                                              List<ThirdPartyRecordEntity> thirdPartyRecords) {
        Map<String, String> codeMap = thirdPartyRecords.stream()
                .sorted(Comparator.comparing(ThirdPartyRecordEntity::getCreateDt).reversed()) // 时间倒序排列
                .collect(Collectors.toMap(
                        ThirdPartyRecordEntity::getApplyNo, // 按 applyNo 作为 key
                        ThirdPartyRecordEntity::getChannelResponseCode,               // value 是当前实体
                        (existing, replacement) -> existing // 如果重复保留已存在的（因为已排序）
                ));
        return baseEntryApplys.stream()
                .map(baseEntryApply -> build(baseEntryApply, codeMap.get(baseEntryApply.getApplyNo())))
                .collect(Collectors.toList());
    }

    public static QueryEntryApplyDetailInfoBO build(BaseEntryApply<? extends ChannelSpecialParams> baseEntryApply, String channelCode) {
        SceneAndActivityTypeBO sceneAndActivityTypeBO = SceneAndActivityUtils.getSceneAndPromotionTypeBO(baseEntryApply.getPayChannel(),
                baseEntryApply.getPayScene(), baseEntryApply.getActivityType());
        return QueryEntryApplyDetailInfoBO.builder()
                .payChannel(baseEntryApply.getPayChannel())
                .payScene(baseEntryApply.getPayScene())
                .activityType(baseEntryApply.getActivityType())
                .applyNo(baseEntryApply.getApplyNo())
                .bizApplyNo(baseEntryApply.getBizApplyNo())
                .channelNo(baseEntryApply.getChannelNo())
                .channelIdentifier(baseEntryApply.getChannelIdentifier())
                .channelFeeType(sceneAndActivityTypeBO.getChannelFeeType())
                .channelActivityType(sceneAndActivityTypeBO.getChannelActivityType())
                .bizScene(baseEntryApply.getBizScene())
                .entryType(baseEntryApply.getEntryType())
                .status(buildStatus(baseEntryApply))
                .entryStatus(baseEntryApply.getEntryStatus())
                .entryFailCode(EntryStatusEnum.FAIL.equals(baseEntryApply.getEntryStatus()) ? ChannelResultCode.convertFailCode(channelCode) : null)
                .entryFailReason(baseEntryApply.getFailReason())
                .auditStatus(baseEntryApply.getEntryAuditStatus())
                .anchoredFlag(baseEntryApply.getAnchoredApplyFlag())
                .anchoredFlagStatus(baseEntryApply.getAnchoredApplyFlagStatus())
                .terminalReportFlag(baseEntryApply.getTerminalReportFlag())
                .terminalReportFlagStatus(baseEntryApply.getTerminalReportFlagStatus())
                .createSplitterFlag(baseEntryApply.getWxDirectCreateSplitterFlag())
                .createSplitterFlagStatus(baseEntryApply.getWxDirectCreateSplitterFlagStatus())
                .build();
    }

    private static EntryApplyMainStatus buildStatus(BaseEntryApply baseEntryApply) {
        boolean terminalReportFlag = baseEntryApply.getTerminalReportFlag();
        boolean terminalReportFlagStatus = baseEntryApply.getTerminalReportFlagStatus();
        boolean wxDirectCreateSplitterFlag = baseEntryApply.getWxDirectCreateSplitterFlag();
        boolean wxDirectCreateSplitterFlagStatus = baseEntryApply.getWxDirectCreateSplitterFlagStatus();
        // 终端报备和创建分账方成功
        boolean terminalSuccess = Objects.equals(terminalReportFlag, terminalReportFlagStatus);
        boolean wxDirectCreateSplitterSuccess = Objects.equals(wxDirectCreateSplitterFlag, wxDirectCreateSplitterFlagStatus);

        boolean subSuccess = terminalSuccess && wxDirectCreateSplitterSuccess;
        return getStatusByEntryStatus(baseEntryApply.getEntryStatus(), subSuccess);
    }

    private static EntryApplyMainStatus getStatusByEntryStatus(EntryStatusEnum entryStatus, boolean subSuccess) {
        switch (entryStatus) {
            case INIT:
                return EntryApplyMainStatus.INIT;
            case PROCESSING:
                return EntryApplyMainStatus.PROCESSING;
            case SUCCESS:
                return subSuccess ? EntryApplyMainStatus.SUCCESS : EntryApplyMainStatus.PROCESSING;
            default:
                return EntryApplyMainStatus.FAIL;
        }
    }

    public static QueryEntryApplyInfoDetailDTO buildDTO(QueryEntryApplyDetailInfoBO applyDetailInfoBO) {
        return QueryEntryApplyInfoDetailDTO.builder()
                .applyNo(applyDetailInfoBO.getApplyNo())
                .channelNo(applyDetailInfoBO.getChannelNo())
                .channelIdentifier(applyDetailInfoBO.getChannelIdentifier())
                .channelFeeType(applyDetailInfoBO.getChannelFeeType())
                .channelActivityType(applyDetailInfoBO.getChannelActivityType())
                .payChannel(applyDetailInfoBO.getPayChannel().getDocument())
                .payScene(applyDetailInfoBO.getPayScene().getDocument())
                .activityType(applyDetailInfoBO.getActivityType().getDocument())
                .entryType(applyDetailInfoBO.getEntryType().getDocument())
                .status(applyDetailInfoBO.getStatus().getDocument())
                .entryStatus(applyDetailInfoBO.getEntryStatus().getDocument())
                .entryFailCode(applyDetailInfoBO.getEntryFailCode())
                .entryFailReason(applyDetailInfoBO.getEntryFailReason())
                .auditStatus(applyDetailInfoBO.getAuditStatus().getDocument())
                .anchoredFlag(applyDetailInfoBO.getAnchoredFlag())
                .anchoredFlagStatus(applyDetailInfoBO.getAnchoredFlagStatus())
                .terminalReportFlag(applyDetailInfoBO.getTerminalReportFlag())
                .terminalReportFlagStatus(applyDetailInfoBO.getTerminalReportFlagStatus())
                .createSplitterFlag(applyDetailInfoBO.getCreateSplitterFlag())
                .createSplitterFlagStatus(applyDetailInfoBO.getCreateSplitterFlagStatus())
                .build();
    }
}