package com.yeepay.g3.core.aggregation.config.convert.decorator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.CompanyRepresentativeBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.UboInfoBO;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WechatDirectEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.*;
import com.yeepay.g3.core.aggregation.config.convert.ChannelEntryRequestConvert;
import com.yeepay.g3.core.aggregation.config.external.constant.ChannelConst;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRequestDTO;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.UboDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/18 19:02
 */
@Service
@Slf4j
public class WechatDirectChannelEntryRequestConvertImpl implements ChannelEntryRequestConvert<WechatDirectEntryApply> {
    private final ChannelEntryRequestConvert<WechatDirectEntryApply> channelEntryRequestConvert;

    public WechatDirectChannelEntryRequestConvertImpl(@Qualifier("baseChannelEntryRequestConvert") final ChannelEntryRequestConvert<WechatDirectEntryApply> channelEntryRequestConvert) {
        this.channelEntryRequestConvert = channelEntryRequestConvert;
    }

    @Override
    public OpenPayAsyncReportRequestDTO buildChannelRequest(final EntryApplyContext<WechatDirectEntryApply> context) {
        final OpenPayAsyncReportRequestDTO request = channelEntryRequestConvert.buildChannelRequest(context);

        final WechatDirectEntryApply entryApply = context.getEntryApply();
        // 结算卡账户
        final SettleAccountInfo settleAccountInfo = entryApply.getSettleAccountInfo();
        final BankAccountTypeEnum bankAccountType = settleAccountInfo.getBankAccountType();
        request.setMerAccBank(settleAccountInfo.getBankName());
        request.setMerAccBankName(settleAccountInfo.getBankBranchName());
        request.setMerAccBankCode(settleAccountInfo.getBankCode());
        request.setMerAccBankNo(settleAccountInfo.getBranchCode());
        request.setMerAccBankType(bankAccountType == null ? null : bankAccountType.getChannelBankAccountType());
        request.setMerAccNo(settleAccountInfo.getCardNo());
        request.setMerAccName(settleAccountInfo.getCardName());
        request.setMerAccBankDistinctCode(settleAccountInfo.getDistinctCode());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //法人信息
        final SubjectInfo subjectInfo = context.getEntryApply().getSubjectInfo();
        final CompanyRepresentativeBO companyRepresentativeInfo = subjectInfo.getCompanyRepresentativeInfo();
        final IdentificationTypeEnum cardType = companyRepresentativeInfo.getCardType();
        request.setIdentityInfoName(companyRepresentativeInfo.getName());
        request.setIdentityInfoIdName(companyRepresentativeInfo.getName());
        request.setIdentityInfoIdHolderType(companyRepresentativeInfo.getLegalType().getDocument());
        request.setIdentityInfoIdType(cardType == null ? null : cardType.getChannelIdentificationType());
        request.setIdentityInfoAuthorizePic(companyRepresentativeInfo.getBusinessAuthorizationLetter());
        request.setIdentityInfoIdNo(companyRepresentativeInfo.getCardNo());
        request.setIdentityInfoIdFrontPic(companyRepresentativeInfo.getCardFrontImg());
        request.setIdentityInfoIdBackPic(companyRepresentativeInfo.getCardBackImg());
        request.setIdentityInfoIdAddress(companyRepresentativeInfo.getCertAddress());
        if (StringUtils.isNotBlank(companyRepresentativeInfo.getEffectTime())) {
            LocalDate effectTime = LocalDate.parse(companyRepresentativeInfo.getEffectTime(), formatter);
            request.setIdentityInfoIdStartTime(Date.from(effectTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }
        final String expireTime = companyRepresentativeInfo.getExpireTime();
        if (StringUtils.isNotBlank(expireTime)) {
            LocalDate expireLocalDate = Const.PERMANENT.equals(expireTime) ?
                    LocalDate.parse(ChannelConst.EXPIRE_TIME_LONG, formatter) : LocalDate.parse(expireTime, formatter);
            request.setIdentityInfoIdEndTime(Date.from(expireLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }

        // 经营场景
        final SalesScene salesScene = entryApply.getSalesScene();
        final List<String> salesScenesTypes = salesScene.getSalesScenesTypes().stream().map(SalesScenesType::getChannelSalesScenesType)
                .collect(Collectors.toList());
        request.setBusinessSceneTypeList(salesScenesTypes);

        // 小程序
        if (salesScene.getSalesScenesTypes().contains(SalesScenesType.MINI_PROGRAM)) {
            final MiniProgramInfo miniProgramInfo = salesScene.getMiniProgramInfo();
            request.setMiniProgramAppId(miniProgramInfo.getMiniProgramSubAppId());
            request.setMiniProgramAppName(miniProgramInfo.getMiniProgramName());
            request.setMiniProgramPicList(miniProgramInfo.getMiniProgramPics());

        }
        if (salesScene.getSalesScenesTypes().contains(SalesScenesType.MP)) {
            // 公众号
            final MpInfo mpInfo = salesScene.getMpInfo();
            request.setJsApiAppId(mpInfo.getMpSubAppId());
            request.setJsApiAppName(mpInfo.getMpAppName());
            request.setJsApiPicList(mpInfo.getMpPics());
        }
        if (salesScene.getSalesScenesTypes().contains(SalesScenesType.STORE)) {
            final StoreInfo storeInfo = salesScene.getStoreInfo();
            request.setStoreName(storeInfo.getStoreName());
            request.setStoreAddress(storeInfo.getStoreAddress());
            request.setStoreAddressCode(storeInfo.getStoreAddressCode());
            request.setStoreEntrancePicList(storeInfo.getStoreEntrancePics());
            request.setStoreIndoorPicList(storeInfo.getStoreInnerPics());
        }


        //受益人
        request.setIdentityInfoUbo(context.getEntryApply().getIsBenefitPerson());
        final List<UboInfoBO> uboInfos = entryApply.getUboInfos();
        if (CollectionUtils.isNotEmpty(uboInfos)) {

            final List<UboDTO> uboDTOList = uboInfos.stream()
                    .map(uboInfo -> {
                        UboDTO uboDTO = new UboDTO();
                        uboDTO.setUboIdType(uboInfo.getCardType().getChannelIdentificationType());
                        uboDTO.setUboIdFrontPic(uboInfo.getCardFrontImg());
                        uboDTO.setUboIdBackPic(uboInfo.getCardBackImg());
                        uboDTO.setUboIdName(uboInfo.getName());
                        uboDTO.setUboIdNo(uboInfo.getCardNo());
                        uboDTO.setUboIdAddress(uboInfo.getCertAddress());
                        if (StringUtils.isNotBlank(uboInfo.getEffectTime())) {
                            LocalDate uboEffectTime = LocalDate.parse(uboInfo.getEffectTime(), formatter);
                            uboDTO.setUboIdStartTime(Date.from(uboEffectTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                        }

                        final String uboExpireTime = uboInfo.getExpireTime();
                        if (StringUtils.isNotBlank(uboExpireTime)) {
                            LocalDate expireTimeLocalDate = Const.PERMANENT.equals(uboExpireTime) ? LocalDate.parse(ChannelConst.EXPIRE_TIME_LONG, formatter)
                                    : LocalDate.parse(uboInfo.getExpireTime(), formatter);
                            uboDTO.setUboIdEndTime(Date.from(expireTimeLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                        }

                        return uboDTO;
                    }).collect(Collectors.toList());
            request.setUboDTOList(uboDTOList);
        }

        // 超级管理员信息
        final SuperAdminInfo superAdminInfo = entryApply.getSuperAdminInfo();
        final IdentificationTypeEnum certType = superAdminInfo.getCertType();

        request.setContactName(superAdminInfo.getContactName());
        request.setContactPhone(superAdminInfo.getContactMobileNo());
        request.setContactIdCardNo(superAdminInfo.getContactCertNo());
        request.setContactName(superAdminInfo.getContactName());
        request.setContactEmail(superAdminInfo.getContactEmail());
        request.setContactType(superAdminInfo.getContactType().getChannelLegalType());
        request.setContactIdType(Objects.isNull(superAdminInfo.getCertType()) ? null : certType.getChannelIdentificationType());
        request.setContactIdFrontPic(superAdminInfo.getCardFrontImg());
        request.setContactIdBackPic(superAdminInfo.getCardBackImg());
        if (StringUtils.isNotBlank(superAdminInfo.getEffectTime())) {
            LocalDate contactIdEffectTime = LocalDate.parse(superAdminInfo.getEffectTime(), formatter);
            request.setContactIdStartTime(Date.from(contactIdEffectTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }

        final String contactIdExpireTime = superAdminInfo.getExpireTime();
        if (StringUtils.isNotBlank(contactIdExpireTime)) {
            LocalDate expireTimeLocalDate = Const.PERMANENT.equals(contactIdExpireTime) ? LocalDate.parse(ChannelConst.EXPIRE_TIME_LONG, formatter)
                    : LocalDate.parse(contactIdExpireTime, formatter);
            request.setContactIdEndTime(Date.from(expireTimeLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }

        request.setContactAuthorizationPic(superAdminInfo.getBusinessAuthorizationLetter());

        //补充说明
        final WxDirectConfigCmd.AdditionInfo additionInfo = entryApply.getAdditionInfo();
        if (null != additionInfo) {
            request.setAdditionMsg(additionInfo.getBusinessAdditionDesc());
            request.setAdditionPicList(additionInfo.getBusinessAdditionPics());
        }

        // 活动信息
        final ActivityInfo activityInfo = entryApply.getActivityInfo();
        if (null != activityInfo) {
            request.setActivitiesId(activityInfo.getActivitiesId());
            request.setDebitActivitiesRate(activityInfo.getDebitActivitiesRate());
            request.setCreditActivitiesRate(activityInfo.getCreditActivitiesRate());
        }
        request.setExternalInfo(JsonUtils.fromJson(entryApply.getExtendInfo(), new TypeReference<Map<String, String>>() {
        }));
        return request;
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {

        return PayChannelEnum.WECHAT == payChannel
                && PaySceneEnum.DIRECT == payScene;
    }
}
