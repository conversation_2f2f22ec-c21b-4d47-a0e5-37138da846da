package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import lombok.Data;

import java.io.Serializable;

/**
 * 挂靠信息
 * @author: Mr.yin
 * @date: 2025/6/3  20:13
 */
@Data
public class EntryAnchoredInfoNotifyCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 挂靠状态
     */
    private String anchoredStatus;

    /**
     * 失败原因
     */
    private String failMessage;

    /**
     * 挂靠商编
     */
    private String anchoredMerchantNo;

    /**
     * 挂靠渠道号
     */
    private String anchoredChannelNo;

    /**
     * 挂靠渠道标识
     */
    private String anchoredChannelIdentifier;


}