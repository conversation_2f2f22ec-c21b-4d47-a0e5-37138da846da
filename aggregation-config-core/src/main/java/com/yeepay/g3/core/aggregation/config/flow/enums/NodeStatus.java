package com.yeepay.g3.core.aggregation.config.flow.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/26 21:30
 */
@Getter
@AllArgsConstructor
public enum NodeStatus implements DocumentedEnum<String> {
    WAIT("WAIT", "待执行"),
    EXECUTING("EXECUTING",  "执行中"),
    SUCCESS("SUCCESS","执行成功"),
    FAIL("FAIL","执行失败"),;


    private final String document;
    private final String desc;

}
