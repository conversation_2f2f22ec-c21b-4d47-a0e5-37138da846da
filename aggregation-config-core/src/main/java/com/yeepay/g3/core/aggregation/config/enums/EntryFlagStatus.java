package com.yeepay.g3.core.aggregation.config.enums;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/9 23:35
 */
@Getter
@AllArgsConstructor
public enum EntryFlagStatus implements DocumentedEnum<Long>, BitEnum {

    NONE(0, "无标识"),
    TERMINAL_REPORT_SUCCESS(1, "是否终端报备完成"),
    WX_DIRECT_CREATE_SPLITTER_SUCCESS(1 << 1, "是否微信直连创建分账方完成"),
    ANCHORED_APPLY_SUCCESS(1 << 2, "挂靠申请完成"),
    ;
    private final long bit;
    private final String desc;

    @Override
    public Long getDocument() {
        return bit;
    }
}
