package com.yeepay.g3.core.aggregation.config.mq.event;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单挂靠场景+活动+渠道申请请求的通知
 *
 * @author: Mr.yin
 * @date: 2025/6/10  16:44
 */
@Data
public class AnchoredApplyResultMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务场景
     */
    private String bizScene;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 同批次订单号
     */
    private String orderNo;

    /**
     * 处理单号
     */
    private String applyNo;

    /**
     * 商户编号
     */
    private String merchantNo;

    private String payScene;

    private String activityType;

    private String channelType;

    /**
     * 挂靠类型
     */
    private String anchoredType;

    /**
     * 挂靠维度 是否需要跨sass体系
     */
    private String anchoredDimension;

    /**
     * 挂靠状态
     */
    private String anchoredStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 挂靠商户信息
     */
    private List<AnchoredMerchantInfoMessage> anchoredMerchantInfoList;


}
