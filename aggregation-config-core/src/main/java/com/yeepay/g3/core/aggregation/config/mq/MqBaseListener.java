package com.yeepay.g3.core.aggregation.config.mq;

import com.yeepay.g3.core.aggregation.config.exception.AggConfigException;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.g3.core.aggregation.config.utils.ValidationUtils;
import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;
import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.threadcontext.ThreadContext;
import com.yeepay.g3.utils.common.threadcontext.ThreadContextType;
import com.yeepay.g3.utils.rmi.soa.SoaSupportUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;

/**
 * @description: 消息队列基类
 * @author: xuchen.liu
 * @date: 2024-12-25 11:25
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
public abstract class MqBaseListener<T> implements MessageListenerConcurrently {

    private final Class<T> tClass;

    @Setter
    private int maxRetryTimes = 16;

    public MqBaseListener(Class<T> tClass) {
        this.tClass = tClass;
    }

    public abstract void onMessage(T message) throws Exception;

    public abstract String topicName();

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> messageList, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        try {
            for (MessageExt messageExt : messageList) {
                String body = null;
                try {
                    this.guidReplace();
                    String msgId = messageExt.getMsgId();
                    body = new String(messageExt.getBody(), StandardCharsets.UTF_8);
                    String topic = messageExt.getTopic() + "("+topicName()+")";
                    log.info("process topic={}-msgId={}-body={}-reconsumeTimes={}" ,topic,msgId,body,messageExt.getReconsumeTimes());
                    if (StringUtils.isNotBlank(body)) {
                        //参数转换，并验证
                        T convert = JsonUtils.convert(body, tClass);
                        ValidationUtils.validateEntity(convert);
                        //调用自动一消息处理
                        this.onMessage(convert);
                        log.info("topic : {}  consume success ",topic);
                    }else {
                        log.error("topic body is null");
                    }

                } catch (AggConfigException aggConfigException) {
                    log.error("mq error is AggConfigException, e=" ,aggConfigException);
                    String code = aggConfigException.getCode();
                    if (ErrorCodeEnum.MQ_CONSUMER_ERROR.getCode().equals(code)) {
                        //当为消费异常时，返回消息队列失败，进行消息重试
                        int reconsumeTimes = messageExt.getReconsumeTimes();
                     /*   long delayTime = getRetryDelayTime(reconsumeTimes);
                        log.warn("延迟重试时间间隔={}",delayTime);*/
                        // 接近最大重试次数时打印警告日志
                        if (reconsumeTimes >= maxRetryTimes - 2) {
                            String name = topicName();
                            if (StringUtils.isBlank(name)) {
                                name = "";
                            }
                            String alert = "消息:[" + name + "]" + "即将达到最大重试次数" + "\n" +
                                    "msgId:" + messageExt.getMsgId() + "\n" +
                                    "当前重试次数:" + reconsumeTimes + "\n" +
                                    "最大重试次数:" + maxRetryTimes + "\n" +
                                    "消息体:" + body +"\n" +
                                    "异常信息:" + aggConfigException.getMessage();
                            log.info(alert);
                        }
                        //Thread.sleep(delayTime);
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                } catch (Throwable e) {
                    log.error(String.format("process topic:%s error", messageExt.getTopic()),e);
                }
            }
        }catch (Throwable e) {
            log.error("consumeMessage fail ,"+e.getMessage());
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private long getRetryDelayTime(int reconsumeTimes) {
        // Implement exponential backoff or custom retry logic here
        long baseDelay = 12; // 1 second
        return baseDelay * (long) Math.pow(2, reconsumeTimes); // Exponential backoff
    }

    private void guidReplace() {
        try {
            ThreadContext context = ThreadContextUtils.getContext();
            String uuid = UUID.randomUUID().toString().replace("-","");
            if (context == null) {
                ThreadContextUtils.initContext(SoaSupportUtils.getAppName(), uuid, ThreadContextType.INTERFACE);
                return;
            }
            ThreadContextUtils.clearContext();
            String threadUID = context.getThreadUID();
            ThreadContextUtils.initContext(SoaSupportUtils.getAppName(), uuid, ThreadContextType.INTERFACE);
            log.info("rocketMq guid={} replace={}  ", threadUID, uuid);
        }catch (Throwable e) {
            log.error("guid替换异常,"+e.getMessage(),e);
        }
    }
}
