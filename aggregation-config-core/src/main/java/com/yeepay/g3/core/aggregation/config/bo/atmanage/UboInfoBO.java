package com.yeepay.g3.core.aggregation.config.bo.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.IdentificationTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.UboInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述: UBO（Ultimate Beneficial Owner）信息 DTO
 *
 * <AUTHOR> @since 2025/5/20
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class UboInfoBO implements Serializable {

    private static final long serialVersionUID = 1438643408882872030L;

    /**
     * 证件类型
     */
    private IdentificationTypeEnum cardType;

    /**
     * 证件正面照片
     */
    private String cardFrontImg;

    /**
     * 证件反面照片
     */
    private String cardBackImg;

    /**
     * 证件姓名
     */
    private String name;

    /**
     * 证件号码
     */
    private String cardNo;

    /**
     * 证件居住地址
     */
    private String certAddress;

    /**
     * 证件有效期开始时间
     */
    private String effectTime;

    /**
     * 证件有效期结束时间
     */
    private String expireTime;

    public static List<UboInfoBO> buildUboInfos(List<UboInfoDTO> uboInfoDTOList) {
        if (CollectionUtils.isEmpty(uboInfoDTOList)) {
            return Collections.emptyList();
        }

        return uboInfoDTOList.stream()
                .map(UboInfoBO::buildUboInfoBO)
                .collect(Collectors.toList());
    }

    public static UboInfoBO buildUboInfoBO(UboInfoDTO uboInfoDTO) {
        Assert.notNull(uboInfoDTO, ResultCode.PARAM_VALID_ERROR, "受益人信息不能为空");
        IdentificationTypeEnum cardType = DocumentedEnum.fromValue(IdentificationTypeEnum.class, uboInfoDTO.getCardType());
        return UboInfoBO.builder()
                .cardType(cardType)
                .cardFrontImg(uboInfoDTO.getCardFrontImg())
                .cardBackImg(uboInfoDTO.getCardBackImg())
                .name(uboInfoDTO.getName())
                .cardNo(uboInfoDTO.getCardNo())
                .certAddress(uboInfoDTO.getCertAddress())
                .effectTime(uboInfoDTO.getEffectTime())
                .expireTime(uboInfoDTO.getExpireTime())
                .build();
    }

//    /**
//     * 实名认证-微信校验
//     */
//    public void validateAuthWechat() {
//        // todo-LZX-基础校验
//        if (!IdentificationTypeEnum.getWechatIdentificationType().contains(cardType)) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "证件类型不支持");
//        }
//        this.validateAuthBase();
//    }
//
//    /**
//     * 实名认证-支付宝校验
//     */
//    public void validateAuthAliPay() {
//        // todo-LZX-基础校验
//        if (!IdentificationTypeEnum.getAliPayIdentificationType().contains(cardType)) {
//            throw new AggConfigException(ErrorCodeEnum.PARAM_ERROR, "证件类型不支持");
//        }
//        this.validateAuthBase();
//    }
//
//    /**
//     * 实名认证-基础校验
//     */
//    public void validateAuthBase() {
//        if (!IdentificationTypeEnum.OVERSEA_PASSPORT.equals(cardType)) {
//            ValidationUtils.notNull(cardBackImg, "证件反面照片不能为空");
//        }
//    }
}
