package com.yeepay.g3.core.aggregation.config.biz;

import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;

/**
 * @description: appID与商户的绑定关系
 * @author: xuchen.liu
 * @date: 2024-12-16 18:47
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface ChannelAppIdBindBiz {
    /**
     * 通过挂靠记录绑定AppId和商户的关系
     * @param attachResultEntity 挂靠实体
     */
    void attachBind(AttachResultEntity attachResultEntity);

    /**
     * 接收数据处理绑定关系
     * @param appIdMerchantBindEntity appIdMerchantBindEntity
     */
    void handle(AppIdMerchantBindEntity appIdMerchantBindEntity);

    /**
     * appId 绑定补偿
     *
     * @param appId      appId
     * @param merchantNo 商编
     */
    void compensateAppIdBind(String appId,String merchantNo);
}
