package com.yeepay.g3.core.aggregation.config.enums;

import lombok.Getter;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-07-08 21:30
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Getter
public enum AttachConfigStatusEnum {

    /**
     * 已配置
     */
    SUCCESS("已配置"),


    /**
     * 已解除
     */
    RELIEVE("已解除");



    private final String desc;

    AttachConfigStatusEnum(String desc) {
        this.desc = desc;
    }
}
