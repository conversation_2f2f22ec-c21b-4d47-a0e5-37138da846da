package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail.QueryAnchoredInfoBO;
import com.yeepay.g3.core.aggregation.config.external.bo.QueryEntryProcessBaseReqBO;

import java.util.List;

/**
 * description: 通道挂靠记录
 * <AUTHOR>
 * @since 2025/6/10:00:11
 * Company: 易宝支付(YeePay)
 */
public interface ChannelAttachRecordExternal {

    List<QueryAnchoredInfoBO> queryAttachRecord(QueryEntryProcessBaseReqBO reqBO);
}
