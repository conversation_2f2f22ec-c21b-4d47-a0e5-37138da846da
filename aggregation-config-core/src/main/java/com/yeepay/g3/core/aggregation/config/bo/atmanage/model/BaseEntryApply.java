package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ContactInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.SubjectInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.ActivityInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry.SettleAccountInfo;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.enums.BitEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelGatewayCode;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlag;
import com.yeepay.g3.core.aggregation.config.enums.EntryFlagStatus;
import com.yeepay.g3.core.aggregation.config.utils.GMUtils;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/6/4 22:45
 */
@Getter
@Setter(AccessLevel.PROTECTED)
@NoArgsConstructor(force = true)
@AllArgsConstructor
@SuperBuilder
public class BaseEntryApply<C extends ChannelSpecialParams> implements EntryApply<C> {

    protected final MerchantInfo merchantInfo;

    protected final String orderNo;

    protected final BizSceneEnum bizScene;

    protected final String bizApplyNo;

    protected final String applyNo;

    protected final SubjectInfo subjectInfo;

    /**
     * 联系人信息
     */
    protected final ContactInfo contactInfo;

    protected String channelNo;

    protected String channelIdentifier;

    protected final EntryTypeEnum entryType;

    protected final PaySceneEnum payScene;

    protected final PayChannelEnum payChannel;

    protected final ActivityTypeEnum activityType;

    protected final ActivityInfo activityInfo;


    protected final SettleAccountInfo settleAccountInfo;

    protected EntryAuditStatus entryAuditStatus;

    protected EntryAuditStatus oldEntryAuditStatus;

    protected EntryStatusEnum entryStatus;

    protected EntryStatusEnum oldEntryStatus;

    //EntryFlag
    protected Long flag;

    // EntryFlagStatus
    protected Long flagStatus;

    // EntryFlagStatus
    protected Long oldFlagStatus;

    protected Integer backupCounts = 0;

    protected boolean backupSwitch;

    protected C channelSpecialParams;

    protected String extendInfo;

    protected String signUrl;

    /**
     * 渠道商户号
     */
    protected String channelMchNos;

    protected ChannelGatewayCode channelGatewayCode;

    /**
     * 完成时间
     */
    protected LocalDateTime finishTime;

    /**
     * 失败码
     */
    protected String failCode;

    /**
     * 失败原因
     */
    protected String failReason;

    protected String anchoredApplyNo;

    public BaseEntryApply(ChannelEntryOrderEntity order, ChannelEntryApplyDetailEntity detailEntity) {
        this.orderNo = order.getOrderNo();
        this.applyNo = detailEntity.getApplyNo();
        this.bizApplyNo = order.getBizApplyNo();
        this.bizScene = DocumentedEnum.fromValueOfNullable(BizSceneEnum.class, order.getBizScene());
        this.channelNo = detailEntity.getChannelNo();
        this.channelIdentifier = detailEntity.getChannelIdentifier();
        this.entryType = DocumentedEnum.fromValueOfNullable(EntryTypeEnum.class, detailEntity.getEntryType());
        this.payScene = DocumentedEnum.fromValueOfNullable(PaySceneEnum.class, detailEntity.getPayScene());
        this.payChannel = DocumentedEnum.fromValueOfNullable(PayChannelEnum.class, detailEntity.getPayChannel());
        this.activityType = DocumentedEnum.fromValueOfNullable(ActivityTypeEnum.class, detailEntity.getActivityType());
        this.entryStatus = DocumentedEnum.fromValueOfNullable(EntryStatusEnum.class, detailEntity.getStatus());
        this.oldEntryStatus = DocumentedEnum.fromValueOfNullable(EntryStatusEnum.class, detailEntity.getStatus());
        this.entryAuditStatus = DocumentedEnum.fromValueOfNullable(EntryAuditStatus.class, detailEntity.getAuditStatus());
        this.oldEntryAuditStatus = DocumentedEnum.fromValueOfNullable(EntryAuditStatus.class, detailEntity.getAuditStatus());
        this.flag = detailEntity.getFlag();
        this.flagStatus = detailEntity.getFlagStatus();
        this.oldFlagStatus = detailEntity.getFlagStatus();
        this.backupCounts = detailEntity.getBackupCount();
        this.channelMchNos = detailEntity.getChannelMchNos();
        this.finishTime = detailEntity.getFinishTime();
        this.failReason = detailEntity.getFailReason();
        this.merchantInfo = StringUtils.isBlank(order.getMerchantInfo()) ? null :
                JsonUtils.fromJson(order.getMerchantInfo(), MerchantInfo.class);
        this.subjectInfo = StringUtils.isBlank(order.getSubjectInfo()) ? null :
                JsonUtils.fromJson(GMUtils.decrypt(order.getSubjectInfo()), SubjectInfo.class);
        this.contactInfo = StringUtils.isBlank(order.getContactInfo()) ? null :
                JsonUtils.fromJson(GMUtils.decrypt(order.getContactInfo()), ContactInfo.class);
        this.extendInfo = detailEntity.getExtendInfo();
        this.anchoredApplyNo = detailEntity.getAnchoredOrderNo();
        //子类赋值
        this.settleAccountInfo = null;
        this.activityInfo = null;
    }

    public void buildChannelNo(final String channelNo) {
        this.channelNo = channelNo;
    }

    public void buildChannelIdentifier(final String channelIdentifier) {
        this.channelIdentifier = channelIdentifier;
    }

    public void buildBackupInfo(final Integer backupCounts, final boolean backupSwitch) {
        this.backupCounts = Optional.ofNullable(backupCounts).orElse(0);
        this.backupSwitch = backupSwitch;
    }


    public static long addTerminalReportFlag(long flag) {
        return BitEnum.addFlag(flag, EntryFlag.NEED_TERMINAL_REPORT);
    }

    public static long addTerminalReportStatus(long flag) {
        return BitEnum.addFlag(flag, EntryFlagStatus.TERMINAL_REPORT_SUCCESS);
    }

    public void terminalReportSuccess() {
        this.oldFlagStatus = this.flagStatus;
        this.flagStatus = addTerminalReportStatus(this.flagStatus);
    }

    public static long addAnchoredApply(long flag) {
        return BitEnum.addFlag(flag, EntryFlag.EXIST_ANCHORED_APPLY);
    }

    public static long addAnchoredApplyStatus(long flag) {
        return BitEnum.addFlag(flag, EntryFlagStatus.ANCHORED_APPLY_SUCCESS);
    }

    public static long addWxDirectCreateSplitterFlag() {
        return BitEnum.combine(EntryFlag.NEED_WX_DIRECT_CREATE_SPLITTER);
    }

    public static long addWxDirectCreateSplitterStatus(long flag) {
        return BitEnum.addFlag(flag, EntryFlagStatus.WX_DIRECT_CREATE_SPLITTER_SUCCESS);
    }

    public void wxDirectCreateSplitterSuccess() {
        this.oldFlagStatus = this.flagStatus;
        this.flagStatus = addWxDirectCreateSplitterStatus(this.flagStatus);
    }

    /**
     * 添加指定备份标识
     *
     * @param flag 当前flag标识
     * @return
     */
    public static long addSpecifyBackupFlag(long flag) {
        return BitEnum.addFlag(flag, EntryFlag.SPECIFY_BACKUP);
    }

    /**
     * 添加指定渠道标识/渠道号
     *
     * @param flag 当前flag标识
     * @return
     */
    public static long addSpecifyChannelNo(long flag) {
        return BitEnum.addFlag(flag, EntryFlag.SPECIFY_CHANNEL_NO);
    }

    /**
     * 需要终端报备/分账方关单的场景
     *
     * @return
     */
    public boolean needTerminalOrDivideClosed() {
        final boolean idempotentFail = DocumentedEnum.inRightEnums(this.getEntryStatus(), EntryStatusEnum.FAIL, EntryStatusEnum.CLOSED)
                && StringUtils.isNotBlank(this.getFailReason())
                && !this.getFailReason().contains(ResultCode.CHANNEL_ENTRY_IDEMPOTENT.getCode());
        return idempotentFail
                || (DocumentedEnum.inRightEnums(this.getEntryType(), EntryTypeEnum.PRIORITY_ANCHORED)
                && this.getAnchoredApplyFlagStatus());
    }

    /**
     * 刷新一下当前最新的状态，结合加锁一起用
     *
     * @param currentFlagStatus
     * @param currentEntryStatus
     */
    public void flashCurrentFlagStatusAndEntryStatus(Long currentFlagStatus, EntryStatusEnum currentEntryStatus) {
        this.oldFlagStatus = currentFlagStatus;
        this.flagStatus = currentFlagStatus;
        this.entryStatus = currentEntryStatus;
        this.oldEntryStatus = currentEntryStatus;
    }

    @JsonIgnore
    public boolean getTerminalReportFlag() {
        return BitEnum.hasFlag(this.flag, EntryFlag.NEED_TERMINAL_REPORT);
    }

    @JsonIgnore
    public boolean getTerminalReportFlagStatus() {
        return BitEnum.hasFlag(this.flagStatus, EntryFlagStatus.TERMINAL_REPORT_SUCCESS);
    }

    @JsonIgnore
    public boolean getAnchoredApplyFlag() {
        return BitEnum.hasFlag(this.flag, EntryFlag.EXIST_ANCHORED_APPLY);
    }

    @JsonIgnore
    public boolean getAnchoredApplyFlagStatus() {
        return BitEnum.hasFlag(this.flagStatus, EntryFlagStatus.ANCHORED_APPLY_SUCCESS);
    }

    @JsonIgnore
    public boolean getWxDirectCreateSplitterFlag() {
        return BitEnum.hasFlag(this.flag, EntryFlag.NEED_WX_DIRECT_CREATE_SPLITTER);
    }

    @JsonIgnore
    public boolean getWxDirectCreateSplitterFlagStatus() {
        return BitEnum.hasFlag(this.flagStatus, EntryFlagStatus.WX_DIRECT_CREATE_SPLITTER_SUCCESS);
    }

    @JsonIgnore
    public boolean getSpecifyBackupFlag() {
        return BitEnum.hasFlag(this.flag, EntryFlag.SPECIFY_BACKUP);
    }

    @JsonIgnore
    public boolean getSpecifyChannelNoFlag() {
        return BitEnum.hasFlag(this.flag, EntryFlag.SPECIFY_CHANNEL_NO);
    }

    public void processing(final String signUrl, final EntryAuditStatus entryAuditStatus) {
        Assert.isFalse(this.entryStatus.finalStatus(), ResultCode.ENTRY_APPLY_FINISHED, "进件申请单已终态:" + this.entryStatus);
        this.oldEntryStatus = this.entryStatus;
        this.entryStatus = EntryStatusEnum.PROCESSING;
        this.entryAuditStatus = entryAuditStatus;
        this.signUrl = signUrl;
    }

    public void anchoredSuccess(final String anchoredApplyNo) {
        if (EntryTypeEnum.PRIORITY_ANCHORED == this.getEntryType()) {
            this.oldEntryStatus = this.entryStatus;
            this.entryStatus = EntryStatusEnum.SUCCESS;
            this.oldFlagStatus = this.flagStatus;
            this.flagStatus = addAnchoredApplyStatus(this.flagStatus);
            this.anchoredApplyNo = anchoredApplyNo;
        } else if (EntryTypeEnum.ENTRY_ANCHORED == this.getEntryType()) {
            this.oldEntryStatus = this.entryStatus;
            this.oldFlagStatus = this.flagStatus;
            this.flagStatus = addAnchoredApplyStatus(this.flagStatus);
            this.anchoredApplyNo = anchoredApplyNo;
        }
    }

    public void compensateAnchoredSuccess(final String anchoredApplyNo) {
        this.oldFlagStatus = this.flagStatus;
        this.flagStatus = addAnchoredApplyStatus(this.flagStatus);
        this.anchoredApplyNo = anchoredApplyNo;
    }

    public void success(final ChannelEntryResult channelEntryResult) {
        Assert.isFalse(EntryStatusEnum.SUCCESS == this.entryStatus, ResultCode.ENTRY_APPLY_FINISHED, "进件申请单已终态:" + this.entryStatus);
        final ChannelEntryResult.ChannelEntryDetail entryDetail = channelEntryResult.getChannelEntryDetails().stream()
                .filter(detail -> ThirdPartyRecordStatusEnum.SUCCESS == detail.getStatus())
                .findFirst()
                .orElseThrow(() -> new BusinessException(ResultCode.PARAM_BIND_ERROR));

        final String channelMchNos = channelEntryResult.getChannelEntryDetails().stream()
                .filter(detail -> ThirdPartyRecordStatusEnum.SUCCESS == detail.getStatus())
                .map(detail -> detail.getSuccessBuilder().getChannelMchNo())
                .collect(Collectors.joining(","));


        final ChannelEntryResult.SuccessBuilder successBuilder = entryDetail.getSuccessBuilder();

        this.oldEntryStatus = this.entryStatus;
        this.entryStatus = EntryStatusEnum.SUCCESS;
        this.entryAuditStatus = EntryAuditStatus.FINISHED;
        this.channelNo = successBuilder.getChannelNo();
        this.channelMchNos = Stream.of(this.channelMchNos, channelMchNos)
                .filter(s -> s != null && !s.isEmpty())
                .collect(Collectors.joining(","));
        this.finishTime = successBuilder.getFinishTime();
        this.channelGatewayCode = successBuilder.getChannelGatewayCode();
    }

    public void fail(final ChannelEntryResult channelEntryResult) {
        Assert.isFalse(this.entryStatus.finalStatus(), ResultCode.ENTRY_APPLY_FINISHED, "进件申请单已终态:" + this.entryStatus);
        this.oldEntryStatus = this.entryStatus;
        this.entryStatus = EntryStatusEnum.FAIL;
        channelEntryResult.getChannelEntryDetails().stream()
                .filter(detail -> ThirdPartyRecordStatusEnum.FAIL == detail.getStatus())
                .findFirst()
                .ifPresent(detail -> {
                    final String reason = detail.getFailBuilder().getFailReason();
                    this.entryAuditStatus = detail.getEntryAuditStatus();
                    this.failCode = ChannelResultCode.convertFailCode(detail.getFailBuilder().getChannelResponseCode());
                    this.failReason = StringUtils.isNotBlank(reason)
                            ? this.failCode + ":" + reason : this.failCode;
                    this.finishTime = detail.getFailBuilder().getFinishTime();
                });

    }

    public boolean canTerminalReport() {
        final boolean canStatus = EntryStatusEnum.SUCCESS == this.getEntryStatus()
                || (EntryStatusEnum.FAIL == this.getEntryStatus()
                && ResultCode.CHANNEL_ENTRY_IDEMPOTENT.getCode().equals(this.getFailCode()));
        return canStatus && Boolean.TRUE.equals(this.getTerminalReportFlag());
    }

    @Override
    public boolean getBackupSwitch() {
        return this.backupSwitch;
    }
}
