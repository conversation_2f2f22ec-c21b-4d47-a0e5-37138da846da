package com.yeepay.g3.core.aggregation.config.bo.atmanage.entry.queryDetail;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryStatusEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.common.ChannelResultCode;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryInfoDTO;
import com.yeepay.g3.facade.bankchannel.enums.ReportStatusEnum;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayReportRecordInfoDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.StringUtils;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description: 进件信息结果BO
 * <AUTHOR>
 * @since 2025/6/10:14:15
 * Company: 易宝支付(YeePay)
 */
@Builder
@Getter
public class QueryEntryInfoBO implements Serializable {

    private static final long serialVersionUID = -2339321982357234819L;
    /**
     * 进件状态
     */
    private String entryStatus;

    /**
     * 关联申请单号
     */
    private String bizApplyNo;

    /**
     * 关联失败原因
     */
    private String failMessage;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 支付渠道（必填）
     */
    private PayChannelEnum payChannel;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型（必填）
     */
    private ActivityTypeEnum activityType;

    /**
     * 费率类型
     */
    private String feeType;

    /**
     * 通道返回的活动类型
     */
    private String channelActivityType;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 错误码
     */
    private String errorCode;

    public static List<QueryEntryInfoBO> buildList(List<OpenPayReportRecordInfoDTO> infoDTOList) {
        if (CheckUtils.isEmpty(infoDTOList)) {
            return new ArrayList<>();
        }
        return infoDTOList.stream().map(e -> {
            SceneAndActivityTypeBO sceneAndPromotion = SceneAndActivityTypeBO.builder().build();
            if (!CheckUtils.isEmpty(e.getReportFee())) {
                sceneAndPromotion = SceneAndActivityUtils.getSceneAndPromotion(e.getReportFee(), e.getPromotionType());
            }
            String entryStatusStr = convertStatus(e.getReportStatus(), e.getErrCode());
            return QueryEntryInfoBO.builder()
                    .entryStatus(entryStatusStr)
                    .channelNo(e.getChannelNo())
                    .payChannel(sceneAndPromotion.getChannel())
                    .payScene(sceneAndPromotion.getPayScene())
                    .activityType(sceneAndPromotion.getActivityType())
                    .feeType(sceneAndPromotion.getChannelFeeType())
                    .channelActivityType(sceneAndPromotion.getChannelActivityType())
                    .bankCode(e.getBankCode())
                    .errorCode(EntryStatusEnum.FAIL.getDocument().equals(entryStatusStr) ? ChannelResultCode.convertFailCode(e.getErrCode()) : null)
                    .failMessage(ReportStatusEnum.FAILED.name().equals(e.getReportStatus()) ? e.getErrMsg() : null)
                    .build();
        }).collect(Collectors.toList());
    }

    public String getUniqueKey() {
        return SceneAndActivityUtils.getUniqueKey(payChannel, payScene, activityType);
    }

    public static QueryEntryInfoDTO buildDTO(QueryEntryInfoBO infoDTO) {
        if (CheckUtils.isEmpty(infoDTO)) {
            return null;
        }
        return QueryEntryInfoDTO.builder()
                .entryStatus(infoDTO.getEntryStatus())
                .bizApplyNo(infoDTO.getBizApplyNo())
                .failMessage(infoDTO.getFailMessage())
                .channelNo(infoDTO.getChannelNo())
                .errorCode(infoDTO.getErrorCode())
                .build();
    }

    /**
     * NIT/初始化
     * AUDITING/审核中
     * SUCCESS/报备成功
     * FAILED/报备失败
     *
     * @param channelStatus
     * @return
     */
    private static String convertStatus(String channelStatus, String errorCode) {
        switch (channelStatus) {
            case "NIT":
            case "AUDITING":
                return EntryStatusEnum.PROCESSING.getDocument();
            case "SUCCESS":
                return EntryStatusEnum.SUCCESS.getDocument();
            case "FAILED":
                if (!StringUtils.isBlank(errorCode) && ChannelResultCode.getEntryFailedWaitRetryCode().contains(errorCode)) {
                    return EntryStatusEnum.PROCESSING.getDocument();
                }
                return EntryStatusEnum.FAIL.getDocument();
            default:
                return channelStatus;
        }
    }
}
