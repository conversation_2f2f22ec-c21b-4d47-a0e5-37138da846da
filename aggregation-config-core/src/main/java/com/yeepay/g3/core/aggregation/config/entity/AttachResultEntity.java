package com.yeepay.g3.core.aggregation.config.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 挂靠结果实体
 * @author: xuchen.liu
 * @date: 2024-12-13 15:14
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Data
public class AttachResultEntity implements Serializable {

    private static final long serialVersionUID = 6135526819022997251L;


    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 主商户号
     */
    private String mainMerchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;

    /**
     *  一级商户编号
     */
    private String subFirstMerchantNo;

    /**
     * 二级商户编号
     */
    private String subSecondMerchantNo;

    /**
     * 三级商户编号
     */
    private String subThreeMerchantNo;

    /**
     * 子商户号名称
     */
    private String subMerchantName;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 商户扫码
     */
    private String merchantScan;

    /**
     * 用户扫码
     */
    private String userScan;

    /**
     * 小程序
     */
    private String miniProgram;

    /**
     * 微信公众号
     */
    private String wechatOffiaccount;

    /**
     * 支付宝生活号
     */
    private String alipayLife;

    /**
     * 审批结果
     */
    private String approvalResult;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 配置状态
     */
    private String configStatus;

    /**
     * 配置时间
     */
    private Date configTime;

    /**
     * 创建时间，默认当前时间
     */
    private Date createdTime;

    /**
     * 最后修改时间，默认当前时间
     */
    private Date lastModifiedTime;

    /**
     * 来源 AGG/CHANNEL  聚合 通道
     */
    private String source;
    /**
     * 清洗状态   INIT/SUCCESS  初始化 成功
     */
    private String cleanStatus;

    /**
     * 原清洗状态
     */
    private String oldClearStatus;
}
