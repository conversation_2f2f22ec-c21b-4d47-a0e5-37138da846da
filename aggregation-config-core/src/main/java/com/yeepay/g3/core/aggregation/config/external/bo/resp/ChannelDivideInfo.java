package com.yeepay.g3.core.aggregation.config.external.bo.resp;

import com.yeepay.g3.core.aggregation.config.enums.DivideReceiverStatus;
import lombok.Data;

/**
 * 分账方详细信息
 *
 * @author: Mr.yin
 * @date: 2025/6/13  17:35
 */
@Data
public class ChannelDivideInfo {

    private String bankOrderNo;

    /**
     * 商户编号
     */
    private String topMerchantNo;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * SUCCESS: 成功
     * FAIL: 失败
     * PROCESSING: 处理中(WAIT_AUTH: 待授权 AUTH_FINISH: 授权完成)
     */
    private DivideReceiverStatus status;

    private String failCode;
    private String failReason;


}
