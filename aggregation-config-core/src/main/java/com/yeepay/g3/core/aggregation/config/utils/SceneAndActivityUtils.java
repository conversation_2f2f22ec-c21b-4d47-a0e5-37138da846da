package com.yeepay.g3.core.aggregation.config.utils;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * description: 场景和活动类型工具类
 * <AUTHOR>
 * @since 2025/5/28:15:03
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@UtilityClass
public class SceneAndActivityUtils {

    /**
     * 获取支付场景、活动类型
     *
     * @return String
     */
    public static SceneAndActivityTypeBO getSceneAndPromotionWithoutNull(String channelFeeType, String channelPromotionType) {
        return getConfig().stream().filter(e -> channelFeeType.equals(e.getChannelFeeType())
                        && channelPromotionType.equals(e.getChannelActivityType()))
                .findFirst().orElse(null);
    }


    /**
     * 获取支付场景、活动类型
     * @return String
     */
    public static SceneAndActivityTypeBO getSceneAndPromotion(String channelFeeType, String channelPromotionType) {
        return getConfig().stream().filter(e-> channelFeeType.equals(e.getChannelFeeType())
                        && channelPromotionType.equals(e.getChannelActivityType()))
                .findFirst().orElseThrow(() -> new BusinessException(ResultCode.PARAM_VALID_ERROR, "场景或活动类型未配置"));
    }

    /**
     * 获取通道报备费率
     * @return String
     */
    public static String getFeeTypeByScene(PayChannelEnum channel, PaySceneEnum payScene) {
        return getConfig().stream().filter(e-> channel.equals(e.getChannel()) && payScene.equals(e.getPayScene()))
                .map(SceneAndActivityTypeBO::getChannelFeeType)
                .findFirst().orElseThrow(() -> new BusinessException(ResultCode.PARAM_VALID_ERROR, "场景未配置"));
    }

    /**
     * 获取通道报备费率、通道活动类型
     * @param channel 渠道
     * @param payScene 场景
     * @param activityType 活动类型
     * @return String 通道活动类型
     */
    public static SceneAndActivityTypeBO getSceneAndPromotionTypeBO(PayChannelEnum channel,
                                                                    PaySceneEnum payScene, ActivityTypeEnum activityType) {
         return getConfig().stream().filter(e-> channel == e.getChannel()
                         && payScene == e.getPayScene()
                         && activityType == e.getActivityType()
                ).findFirst().orElseThrow(() -> new BusinessException(ResultCode.PARAM_VALID_ERROR, "场景或活动类型未配置"));
    }

    /**
     * 获取所有的场景和活动类型配置
     * @return List<SceneAndPromotionTypeBO>
     */
    private static List<SceneAndActivityTypeBO> getConfig() {
        List<SceneAndActivityTypeBO> configList = new ArrayList<>();
        Map<String, String> map = ConfigUtil.queryAllSceneAndPromotionType();
        for (Map.Entry<String,String> entry : map.entrySet()) {
            String[] channelAndSceneAndPromotion = entry.getKey().split(Const.SPLIT_CHAR);
            String[] feeTypeAndPromotion = entry.getValue().split(Const.SPLIT_CHAR);
            try {
                PayChannelEnum payChannelEnum = DocumentedEnum.fromValue(PayChannelEnum.class, channelAndSceneAndPromotion[0]);
                PaySceneEnum paySceneEnum = DocumentedEnum.fromValue(PaySceneEnum.class, channelAndSceneAndPromotion[1]);
                ActivityTypeEnum promotionType = channelAndSceneAndPromotion.length == 3 ?
                        DocumentedEnum.fromValue(ActivityTypeEnum.class, channelAndSceneAndPromotion[2])
                        : null;
                String channelFeeType = feeTypeAndPromotion[0];
                String channelPromotionType = feeTypeAndPromotion[1];
                configList.add(SceneAndActivityTypeBO.builder().channel(payChannelEnum).payScene(paySceneEnum)
                        .activityType(promotionType).channelFeeType(channelFeeType)
                        .channelActivityType(channelPromotionType).build()
                );
            } catch (Exception e) {
                log.error("获取渠道枚举失败，key: {}", entry.getKey(), e);
            }
        }
        return configList;
    }

    public static String getUniqueKey(PayChannelEnum payChannel,
                                      PaySceneEnum payScene, ActivityTypeEnum activityType) {
        if (CheckUtils.isEmpty(payChannel) || CheckUtils.isEmpty(payScene)) {
            return null;
        }
        StringBuilder key = new StringBuilder()
                .append(payChannel.getDocument())
                .append(Const.SPLIT_CHAR)
                .append(payScene.getDocument());
        if (!CheckUtils.isEmpty(activityType)) {
            key.append(Const.SPLIT_CHAR).append(activityType);
        }
        return key.toString();
    }

    public static SceneAndActivityTypeBO getSceneByUniqueKey(String key) {
        String[] keys = key.split(Const.SPLIT_CHAR);
        PayChannelEnum payChannelEnum = DocumentedEnum.fromValue(PayChannelEnum.class, keys[0]);
        PaySceneEnum paySceneEnum = DocumentedEnum.fromValue(PaySceneEnum.class, keys[1]);
        ActivityTypeEnum activityTypeEnum = keys.length == 3 ? DocumentedEnum.fromValue(ActivityTypeEnum.class, keys[2]) : ActivityTypeEnum.NORMAL;
        return SceneAndActivityUtils.getSceneAndPromotionTypeBO(payChannelEnum, paySceneEnum, activityTypeEnum);
    }

}
