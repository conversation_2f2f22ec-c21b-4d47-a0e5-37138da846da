package com.yeepay.g3.core.aggregation.config.bo.atmanage.model;

import com.yeepay.aggregation.config.share.kernel.constant.Const;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.core.aggregation.config.enums.OrderStatusEnum;
import com.yeepay.g3.core.aggregation.config.utils.UniqueNoGenerateUtils;
import lombok.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/4 23:01
 */
@Getter
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(force = true)
@AllArgsConstructor
@Builder
public class AnchoredOrder {

    /**
     * 挂靠订单号
     */
    private final String orderNo;

    /**
     * 业务场景
     */
    private final BizSceneEnum bizScene;

    /**
     * 业务申请单号
     */
    private final String bizApplyNo;

    /**
     * 商户编号
     */
    private final String merchantNo;

    /**
     * 父商编
     */
    private final String parentMerchantNo;

    /**
     * 顶级商户编号
     */
    private final String topLevelMerchantNo;

    /**
     * 挂靠订单状态
     */
    private OrderStatusEnum orderStatus;

    /**
     * 是否走单笔同步返回结果链路
     */
    private Boolean singSync;

    /**
     * 挂靠信息列表
     */
    private List<AnchoredApply> anchoredApplyList;

    /**
     * 进件传bizApplyNo
     *
     * @param anchoredCmd
     * @param bizApplyNo
     * @return
     */
    public static AnchoredOrder createAnchoredOrder(final AnchoredCmd anchoredCmd, String bizApplyNo) {

        String orderNo = Const.ANCHORED_ORDER_PREFIX + UniqueNoGenerateUtils.getUniqueNo();
        List<AnchoredApply> anchoredApplyList = anchoredCmd.getAnchoredDetailCmdList()
                .stream().map(anchoredDetailCmd -> AnchoredApply.createAnchoredApply(orderNo, anchoredCmd, anchoredDetailCmd, bizApplyNo))
                .collect(Collectors.toList());

        return AnchoredOrder.builder()
                .orderNo(orderNo)
                .bizScene(anchoredCmd.getBizScene())
                .bizApplyNo(bizApplyNo)
                .merchantNo(anchoredCmd.getMerchantNo())
                .parentMerchantNo(anchoredCmd.getParentMerchantNo())
                .topLevelMerchantNo(anchoredCmd.getTopLevelMerchantNo())
                .anchoredApplyList(anchoredApplyList)
                .orderStatus(OrderStatusEnum.INIT)
                .singSync(true)
                .build();
    }

    public static AnchoredOrder createAnchoredOrder(final AnchoredCmd anchoredCmd) {

        String orderNo = Const.ANCHORED_ORDER_PREFIX + UniqueNoGenerateUtils.getUniqueNo();
        List<AnchoredApply> anchoredApplyList = anchoredCmd.getAnchoredDetailCmdList()
                .stream().map(anchoredDetailCmd -> AnchoredApply.createAnchoredApply(orderNo, anchoredCmd, anchoredDetailCmd))
                .collect(Collectors.toList());

        return AnchoredOrder.builder()
                .orderNo(orderNo)
                .bizScene(anchoredCmd.getBizScene())
                .bizApplyNo(anchoredCmd.getBizApplyNo())
                .merchantNo(anchoredCmd.getMerchantNo())
                .parentMerchantNo(anchoredCmd.getParentMerchantNo())
                .topLevelMerchantNo(anchoredCmd.getTopLevelMerchantNo())
                .anchoredApplyList(anchoredApplyList)
                .orderStatus(OrderStatusEnum.INIT)
                .singSync(true)
                .build();
    }

}
