package com.yeepay.g3.core.aggregation.config.utils;

import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.utils.common.CheckUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * description: 调度工具类
 * <AUTHOR>
 * @since 2025/6/5:16:39
 * Company: 易宝支付(YeePay)
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ScheduleUtils {

    /**
     * 获取需要处理的调度列表
     * @param functionList 查询列表函数
     * @param functionRequestNo 查询单个记录函数
     * @param <T> 泛型类型
     * @return List<T> 需要处理的记录列表
     */
    public static <T> List<T> getScheduleList(ThirdPartyRecord thirdPartyRecord,
                                              Supplier<List<T>> functionList,
                                              Function<String, T> functionRequestNo) {
        List<T> needHandleList = new ArrayList<>();

        if (CheckUtils.isEmpty(thirdPartyRecord)) {
            List<T> entityList = functionList.get();

            if (CheckUtils.isEmpty(entityList)) {
                return Collections.emptyList();
            }
            needHandleList.addAll(entityList);
        } else {
            T entity = functionRequestNo.apply(thirdPartyRecord.getApplyNo());

            if (CheckUtils.isEmpty(entity)) {
                return Collections.emptyList();
            }
            needHandleList.add(entity);
        }
        return needHandleList;
    }
}
