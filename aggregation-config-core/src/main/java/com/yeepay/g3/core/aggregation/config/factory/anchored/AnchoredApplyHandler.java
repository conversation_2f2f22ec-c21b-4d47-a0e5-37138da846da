package com.yeepay.g3.core.aggregation.config.factory.anchored;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.anchored.model.AggAnchoredMerchantInfo;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.anchored.AnchoredCmd;
import com.yeepay.g3.core.aggregation.config.external.bo.RemoteResult;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayAsyncReportRelationRequestDTO;

import java.util.List;

/**
 * 挂靠申请处理
 *
 * @author: Mr.yin
 * @date: 2025/6/6  18:05
 */
public interface AnchoredApplyHandler {

    /**
     * 校验参数
     *
     * @param anchoredApply
     */
    void validateAnchoredParams(AnchoredApply anchoredApply);

    /**
     * 不同挂靠类型+挂靠维度 申请处理
     *
     * @param anchoredApply
     */
    void anchoredApplyHandle(AnchoredApply anchoredApply);

    /**
     * 请求聚合挂靠
     *
     * @param anchoredApply
     */
    RemoteResult<List<AggAnchoredMerchantInfo>> aggregationAnchoredApply(AnchoredApply anchoredApply);

    /**
     * 请求通道挂靠
     *
     * @param anchoredApply
     */
    void channelAnchoredApply(AnchoredApply anchoredApply);

    /**
     * 查找具体实现类
     *
     * @param anchoredType
     * @param anchoredDimension
     * @return
     */
    default boolean isSupport(final AnchoredType anchoredType, final Boolean anchoredDimension) {
        return false;
    }


}
