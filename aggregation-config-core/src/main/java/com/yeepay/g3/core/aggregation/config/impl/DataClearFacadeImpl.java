package com.yeepay.g3.core.aggregation.config.impl;

import com.yeepay.g3.core.aggregation.config.biz.impl.ChannelAppIdBindBizImpl;
import com.yeepay.g3.core.aggregation.config.builder.AppIdMerchantBindBuilder;
import com.yeepay.g3.core.aggregation.config.common.CacheTypeEnum;
import com.yeepay.g3.core.aggregation.config.dao.AppIdMerchantBindDao;
import com.yeepay.g3.core.aggregation.config.dao.AttachResultDao;
import com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.ChannelAppIdBindStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.enums.DataCleanStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.SourceEnum;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import com.yeepay.g3.core.aggregation.config.service.AppIdMerchantBindService;
import com.yeepay.g3.core.aggregation.config.service.AttachResultService;
import com.yeepay.g3.core.aggregation.config.utils.CacheUtil;
import com.yeepay.g3.core.aggregation.config.utils.SpringContextUtils;
import com.yeepay.g3.facade.aggregation.config.facade.DataClearFacade;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import com.yeepay.storage.sdk.StorageClient;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StopWatch;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-20 12:55
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@PropertySource(value = {"classpath:/runtimecfg/storage-config.properties"})
@Service
@Deprecated
public class DataClearFacadeImpl implements DataClearFacade {

    private final static Logger log = LoggerFactory.getLogger(DataClearFacadeImpl.class);

    @Value(value = "${endPoint}")
    private String endPoint;

    @Value(value = "${secret}")
    private String secret;

    @Value(value = "${bucketName}")
    private String bucketName;

    @Autowired
    private AppIdMerchantBindDao appIdMerchantBindDao;

    @Qualifier(value = "dataSourceJdbcTemplate")
    @Autowired
    private JdbcTemplate dataSourceJdbcTemplate;

    private static final  String BIND_APP_ID_RECORD_NUMB_KEY = "agg_config_bind_appId_record_num_key";

    private static final  String ATTACH_RECORD_NUM_KEY = "agg_config_attach_record_num_key";

    @Override
    public void excBind(String name) throws Exception {
        Long recordNumber = 0L;
        log.info("执行文件; {} ",name);
        StorageClient storageClient = new StorageClient(endPoint, secret);
        InputStream inputStream = storageClient.get(bucketName, "tmp/"+name);
        StopWatch stopWatch = new StopWatch();
        boolean isCache = true;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.defaultCharset()));) {
            CSVFormat csvFormat = CSVFormat.Builder.create(CSVFormat.DEFAULT).setHeader().setSkipHeaderRecord(true).build();
            int num = 0;
            SqlSessionFactory sqlSessionFactory = SpringContextUtils.getBean(SqlSessionFactory.class);
            try( SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH,false)) {
                boolean waitCommitData = true;
                AppIdMerchantBindDao mapper = sqlSession.getMapper(AppIdMerchantBindDao.class);
                log.info("APP_ID绑定关系开始处理数据....");
                stopWatch.start();
                for (CSVRecord csvRecord : csvFormat.parse(reader)) {
                    recordNumber = csvRecord.getRecordNumber();
                    if (isCache) {
                        Long cache = CacheUtil.getCache(BIND_APP_ID_RECORD_NUMB_KEY, CacheTypeEnum.REMOTE, Long.class);
                        if (cache != null) {
                            if (!cache.equals(recordNumber)) {
                                continue;
                            }else {
                                CacheUtil.removeFromRemoteCache(BIND_APP_ID_RECORD_NUMB_KEY);
                                isCache = false;
                            }
                        }else {
                            isCache = false;
                        }
                    }
                    if (csvRecord.size() < 6) {
                        continue;
                    }
                    num++;
                    AppIdMerchantBindEntity appIdMerchantBindEntity = new AppIdMerchantBindEntity();
                    appIdMerchantBindEntity.setAppId(csvRecord.get(0));
                    appIdMerchantBindEntity.setMerchantNo(csvRecord.get(1));
                    appIdMerchantBindEntity.setBindStatus(ChannelAppIdBindStatusEnum.valueOf(csvRecord.get(2)));
                    appIdMerchantBindEntity.setChannelType(ChannelTypeEnum.valueOf(csvRecord.get(3)));
                    String substring = csvRecord.get(4).substring(0, csvRecord.get(4).length() - 2);
                    Date date = DateUtils.parseDate(substring, DateUtils.DATE_FORMAT_DATETIME);
                    appIdMerchantBindEntity.setOperateTime(date);
                    if (csvRecord.get(5).isEmpty()){
                        continue;
                    }
                    appIdMerchantBindEntity.setMerchantName(csvRecord.get(5));
                    appIdMerchantBindEntity.setFirstMerchantNo(csvRecord.get(7));
                    appIdMerchantBindEntity.setSecondMerchantNo(csvRecord.size() < 9 ? "" : csvRecord.get(8));
                    appIdMerchantBindEntity.setThreeMerchantNo(csvRecord.size() < 10 ?  "" : csvRecord.get(9));
                    appIdMerchantBindEntity.setReportMerchantNo("");
                    appIdMerchantBindEntity.setCleanStatus(DataCleanStatusEnum.INIT);
                    Date now = new Date();
                    appIdMerchantBindEntity.setCreateTime(now);
                    appIdMerchantBindEntity.setUpdateTime(now);
                    mapper.batchInsert(appIdMerchantBindEntity);
                    if (num == 1000) {
                        sqlSession.commit();
                        num=0;
                        waitCommitData = false;
                    }else {
                        waitCommitData = true;
                    }
                }
                if (waitCommitData) {
                    sqlSession.commit();
                }
            }catch ( Throwable e) {
                log.error("Throwable异常,"+e.getMessage(),e);
                throw e;
            }
        }catch (Throwable e){
            log.error("导入appId的绑定关系异常，当数据编号:"+recordNumber + e.getMessage(),e);
            if (recordNumber > 0L) {
                CacheUtil.put(CacheTypeEnum.REMOTE, BIND_APP_ID_RECORD_NUMB_KEY,recordNumber,3000);
            }
            throw e;
        }finally {
            storageClient.shutdown();
            stopWatch.stop();
            log.info("绑定数据导入处理耗时: {} ",stopWatch.prettyPrint());
        }
    }

    @Override
    public void excAttach()throws Exception {
        Long recordNumber = 0L;
        dataSourceJdbcTemplate.execute("delete from TBL_ATTACH_RESULT where LAST_MODIFIED_TIME >= '2025-01-03 20:00:00'  ");
        log.info("删除成功!");
        StorageClient storageClient = new StorageClient(endPoint, secret);
        InputStream inputStream = storageClient.get(bucketName, "tmp/SCSJ_K1ANUXFQKTAZ11001.csv");
        StopWatch stopWatch = new StopWatch();
        boolean isCache = true;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, Charset.defaultCharset()));) {
            CSVFormat csvFormat = CSVFormat.Builder.create(CSVFormat.DEFAULT).setHeader().setSkipHeaderRecord(true).build();
            int num = 0;
            SqlSessionFactory sqlSessionFactory = SpringContextUtils.getBean(SqlSessionFactory.class);
            try( SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH,false)) {
                AttachResultDao mapper = sqlSession.getMapper(AttachResultDao.class);
                boolean waitCommitData = true;
                log.info("挂靠关系开始处理数据....");
                stopWatch.start();
                for (CSVRecord csvRecord : csvFormat.parse(reader)) {
                    recordNumber = csvRecord.getRecordNumber();
                    if (isCache) {
                        Long cache = CacheUtil.getCache(ATTACH_RECORD_NUM_KEY, CacheTypeEnum.REMOTE, Long.class);
                        if (cache != null) {
                            if (!cache.equals(recordNumber)) {
                                continue;
                            }else {
                                CacheUtil.removeFromRemoteCache(ATTACH_RECORD_NUM_KEY);
                                isCache = false;
                            }
                        }else {
                            isCache = false;
                        }
                    }

                    if (csvRecord.size() < 12) {
                        continue;
                    }
                    num++;
                    AttachResultEntity attachResultEntity = new AttachResultEntity();
                    attachResultEntity.setMainMerchantNo(csvRecord.get(0));
                    attachResultEntity.setSubMerchantNo(csvRecord.get(1));
                    attachResultEntity.setChannel(csvRecord.get(2));
                    attachResultEntity.setConfigStatus(csvRecord.get(3));
                    if (csvRecord.get(4).isEmpty()){
                        continue;
                    }
                    String substring = csvRecord.get(4).substring(0, csvRecord.get(4).length() - 2);
                    Date date = DateUtils.parseDate(substring, DateUtils.DATE_FORMAT_DATETIME);
                    attachResultEntity.setConfigTime(date);
                    attachResultEntity.setMerchantScan(csvRecord.get(5));
                    attachResultEntity.setUserScan(csvRecord.get(6));
                    attachResultEntity.setMiniProgram(csvRecord.get(7));
                    attachResultEntity.setWechatOffiaccount(csvRecord.get(8));
                    attachResultEntity.setAlipayLife(csvRecord.get(9));
                    if (csvRecord.get(10).isEmpty()) {
                        continue;
                    }
                    attachResultEntity.setSubMerchantName(csvRecord.get(10));
                    attachResultEntity.setSubFirstMerchantNo(csvRecord.get(12));
                    attachResultEntity.setSubSecondMerchantNo( csvRecord.size() < 14 ? "" : csvRecord.get(13));
                    attachResultEntity.setSubThreeMerchantNo(csvRecord.size() < 15 ? "": csvRecord.get(14));
                    attachResultEntity.setConfigTime(date);
                    attachResultEntity.setApplyTime(date);
                    attachResultEntity.setCleanStatus(DataCleanStatusEnum.SUCCESS.name());
                    attachResultEntity.setCreatedTime(new Date());
                    attachResultEntity.setLastModifiedTime(new Date());
                    attachResultEntity.setApprovalResult("SUCCESS");
                    attachResultEntity.setFailureReason("");
                    attachResultEntity.setApplyReason("通道数据清洗");
                    attachResultEntity.setSource(SourceEnum.CHANNEL.name());
                    mapper.insertBatch(attachResultEntity);
                    if (num == 800) {
                        sqlSession.commit();
                  //      Thread.sleep(150);
                        num=0;
                        waitCommitData = false;
                    }else {
                        waitCommitData = true;
                    }
                }
                if (waitCommitData) {
                    sqlSession.commit();
                }
            }catch ( Throwable e) {
                log.error("Throwable异常,"+e.getMessage(),e);
                throw e;
            }
        }catch (Throwable e){
            log.error("挂靠关系数据导入，异常数据编号:"+recordNumber + e.getMessage(),e);
            if (recordNumber > 0L) {
                CacheUtil.put(CacheTypeEnum.REMOTE, ATTACH_RECORD_NUM_KEY,recordNumber,3000);
            }
            throw e;
        }finally {
            storageClient.shutdown();
            stopWatch.stop();
            log.info("挂靠关系数据导入处理耗时: {} ",stopWatch.getTotalTimeSeconds());
        }
    }

    @Autowired
    private MerchantCenterExternal merchantCenterExternal;

    @Autowired
    private AppIdMerchantBindService appIdMerchantBindService;

    @Autowired
    private AttachResultService attachResultService;

    @Autowired
    private AppIdMerchantBindBuilder appIdMerchantBindBuilder;

    @Qualifier(value = "asyncExecutor")
    @Autowired
    private AtlasThreadPoolExecutor executor;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public void merge() {
        try {
            int offset = 0;
            int limit = 500;
            ChannelAppIdBindBizImpl channelAppIdBindBiz = new ChannelAppIdBindBizImpl(merchantCenterExternal,
                    appIdMerchantBindService, attachResultService, appIdMerchantBindBuilder, executor, transactionTemplate);
            channelAppIdBindBiz.booleanThreadLocal.set(false);
            Method processMethod = ChannelAppIdBindBizImpl.class.getDeclaredMethod("process", AppIdMerchantBindEntity.class);
            ReflectionUtils.makeAccessible(processMethod);

            while (true) {
                // 查询当前页数据
                List<Map<String, Object>> rows = queryPage(offset, limit);
                if (rows.isEmpty()) {
                    break;
                }
                //发起数据处理方法
                for (Map<String, Object> row : rows) {
                    AppIdMerchantBindEntity entity = new AppIdMerchantBindEntity();
                    entity.setId((Long) row.get("id"));
                    entity.setAppId((String) row.get("appid"));
                    entity.setMerchantNo((String) row.get("merchant_no"));
                    Object o1 = row.get("merchant_name");
                    if (o1 == null) {
                        continue;
                    }
                    entity.setMerchantName((String) row.get("merchant_name"));
                    entity.setFirstMerchantNo(String.valueOf(row.get("first_merchant_no")));
                    Object s = row.get("second_merchant_no");
                    if (s == null) {
                        entity.setSecondMerchantNo("");
                    }else {
                        entity.setSecondMerchantNo(String.valueOf(row.get("second_merchant_no")));
                    }
                    Object t = row.get("three_merchant_no");
                    if (t == null) {
                        entity.setThreeMerchantNo("");
                    }else {
                        entity.setThreeMerchantNo(String.valueOf(row.get("three_merchant_no")));
                    }

                    Object o = row.get("report_merchant_no");
                    if (o == null) {
                        entity.setReportMerchantNo("");
                    }else {
                        entity.setReportMerchantNo((String) row.get("report_merchant_no"));
                    }
                    entity.setChannelType(ChannelTypeEnum.valueOf((String) row.get("channel_type")));
                    entity.setCleanStatus(DataCleanStatusEnum.valueOf((String) row.get("clean_status")));
                    entity.setBindStatus(ChannelAppIdBindStatusEnum.valueOf((String) row.get("bind_status")));
                    entity.setOperateTime((java.sql.Timestamp) row.get("operate_time"));
                    entity.setCreateTime((java.sql.Timestamp) row.get("create_time"));
                    entity.setUpdateTime((java.sql.Timestamp) row.get("update_time"));


                    //反射执行处理绑定关系流程
                    ReflectionUtils.invokeMethod(processMethod, channelAppIdBindBiz, entity);

                }
                offset++;
            }
        }catch (Throwable e) {
            log.error("数据清洗最后一步报错:"+e.getMessage(),e);
        }
    }

    /**
     * 分页查询当前页的数据
     *
     * @param offset 偏移量
     * @param limit 每页查询的记录数
     * @return 当前页的查询结果
     */
    private List<Map<String, Object>> queryPage(int offset, int limit) {
        String sql =
                "WITH RankedData AS ( \n" +
                        "                    SELECT a.id, a.appid, a.merchant_no, a.merchant_name,\n" +
                        "                           a.report_merchant_no, a.clean_status, a.bind_status, a.error_code,\n" +
                        "                           a.first_merchant_no,a.second_merchant_no,a.three_merchant_no,\n" +
                        "                           a.error_msg, a.operate_time, a.create_time, a.update_time, a.channel_type, \n" +
                        "                           ROW_NUMBER() OVER (ORDER BY a.id) AS row_num\n" +
                        "                    FROM  TBL_APPID_MERCHANT_BIND a\n" +
                        "                    WHERE a.clean_status = 'INIT' \n" +
                        "                ) \n" +
                        "                SELECT t.id, t.appid, t.merchant_no, t.merchant_name, \n" +
                        "                t.first_merchant_no,t.second_merchant_no,t.three_merchant_no,\n" +
                        "                       t.report_merchant_no, t.clean_status, t.bind_status, t.error_code,t.channel_type, \n" +
                        "                       t.error_msg, t.operate_time, t.create_time, t.update_time\n" +
                        "                FROM RankedData t  " +
                "WHERE t.row_num > ? AND t.row_num <= (? + ?) " +
                "ORDER BY t.row_num";

        // 使用 queryForList 查询原始结果，并将其映射到实体类
        return dataSourceJdbcTemplate.queryForList(sql, offset, limit, offset);
    }
}
