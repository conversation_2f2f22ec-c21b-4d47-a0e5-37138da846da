package com.yeepay.g3.core.aggregation.config.service.impl.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.TerminalReportBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AliIndirectEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity;
import com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryOrderEntity;
import com.yeepay.g3.core.aggregation.config.service.ChannelEntryResultHandler;
import com.yeepay.g3.core.aggregation.config.service.EntryApplyHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/8 22:53
 */
@Service
public class AliIndirectEntryApplyHandler implements EntryApplyHandler<AliIndirectEntryApply>, ChannelEntryResultHandler<AliIndirectEntryApply> {

    @Qualifier("baseEntryApplyHandler")
    private final EntryApplyHandler<AliIndirectEntryApply> baseEntryApplyHandler;

    private final TerminalReportBiz terminalReportBiz;

    @Autowired
    public AliIndirectEntryApplyHandler(@Qualifier("baseEntryApplyHandler") EntryApplyHandler<AliIndirectEntryApply> baseEntryApplyHandler,
                                        final TerminalReportBiz terminalReportBiz) {
        this.baseEntryApplyHandler = baseEntryApplyHandler;
        this.terminalReportBiz = terminalReportBiz;
    }

    @Override
    public void buildChannelNo(final AliIndirectEntryApply entryApply) {
        baseEntryApplyHandler.buildChannelNo(entryApply);
    }

    @Override
    public void buildBackupInfo(final AliIndirectEntryApply entryApply) {
        baseEntryApplyHandler.buildBackupInfo(entryApply);
    }


    @Override
    public ChannelEntryResult entryApply(final EntryApplyContext<AliIndirectEntryApply> entryApplyContext) {
        return baseEntryApplyHandler.entryApply(entryApplyContext);
    }

    @Override
    public boolean isSupport(final PayChannelEnum payChannel, final PaySceneEnum payScene) {
        boolean flag = PaySceneEnum.DIRECT.equals(payScene) || PaySceneEnum.DIRECT_STANDARD.equals(payScene);
        return PayChannelEnum.ALIPAY == payChannel && !flag;
    }

    @Override
    public AliIndirectEntryApply buildEntryApply(ChannelEntryOrderEntity orderEntity, ChannelEntryApplyDetailEntity detailEntity) {
        return new AliIndirectEntryApply(orderEntity, detailEntity);
    }

    @Override
    public void resultHandle(final EntryApplyContext<AliIndirectEntryApply> context,
                             final ChannelEntryResult channelEntryResult) {
        baseEntryApplyHandler.resultHandle(context, channelEntryResult);
        //如果进件成功且是需要终端报备 进行终端报备申请
        if (context.getEntryApply().canTerminalReport()) {
            terminalReportBiz.applyChannelTerminalReport(context.getEntryApply());
        }
    }
}
