package com.yeepay.g3.core.aggregation.config.entity.atmanage;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredOrder;
import com.yeepay.g3.core.aggregation.config.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/4 20:19
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_anchored_order")
public class AnchoredOrderEntity extends BaseEntity {
    /**
     * 业务场景
     */
    @Column(name = "biz_scene", length = 16)
    private String bizScene;

    /**
     * 业务申请单号
     */
    @Column(name = "biz_apply_no", length = 32)
    private String bizApplyNo;


    @Column(name = "order_no", length = 32)
    private String orderNo;

    @Column(name = "merchant_no")
    private String merchantNo;

    @Column(name = "parent_merchant_no")
    private String parentMerchantNo;

    @Column(name = "top_merchant_no")
    private String topMerchantNo;

    /**
     * 挂靠订单状态
     */
    @Column(name = "order_status")
    private String orderStatus;

    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 构建方法
     * @param anchoredOrder 挂靠订单BO
     * @return 挂靠订单实体
     */
    public static AnchoredOrderEntity build(AnchoredOrder anchoredOrder) {
        AnchoredOrderEntity anchoredOrderEntity = AnchoredOrderEntity.builder().build();
        anchoredOrderEntity.setBizScene(anchoredOrder.getBizScene().getDocument());
        anchoredOrderEntity.setBizApplyNo(anchoredOrder.getBizApplyNo());
        anchoredOrderEntity.setOrderNo(anchoredOrder.getOrderNo());
        anchoredOrderEntity.setMerchantNo(anchoredOrder.getMerchantNo());
        anchoredOrderEntity.setParentMerchantNo(anchoredOrder.getParentMerchantNo());
        anchoredOrderEntity.setTopMerchantNo(anchoredOrder.getTopLevelMerchantNo());
        anchoredOrderEntity.setOrderStatus(anchoredOrder.getOrderStatus().getDocument());
        anchoredOrderEntity.setFinishTime(null);
        anchoredOrderEntity.setFailReason(null);
//        anchoredOrderEntity.setId(anchoredOrder.getId());
//        anchoredOrderEntity.setValid(anchoredOrder.getValid());
//        anchoredOrderEntity.setUpdatedBy(anchoredOrder.getUpdatedBy());
//        anchoredOrderEntity.setUpdateDt(LocalDateTime.now());
//        anchoredOrderEntity.setCreatedBy(anchoredOrder.getCreatedBy());
//        anchoredOrderEntity.setCreateDt(LocalDateTime.now());
        return anchoredOrderEntity;
    }
}
