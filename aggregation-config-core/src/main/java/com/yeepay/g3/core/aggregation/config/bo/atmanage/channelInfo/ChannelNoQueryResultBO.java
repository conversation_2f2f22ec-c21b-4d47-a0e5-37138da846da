package com.yeepay.g3.core.aggregation.config.bo.atmanage.channelInfo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.core.aggregation.config.bo.SceneAndActivityTypeBO;
import com.yeepay.g3.core.aggregation.config.utils.SceneAndActivityUtils;
import com.yeepay.g3.facade.trade.bankcooper.dto.async.OpenPayChannelInfoDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * description: 渠道号查询结果业务对象
 * <AUTHOR>
 * @since 2025/5/27:16:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
@Builder
public class ChannelNoQueryResultBO implements Serializable {


    private static final long serialVersionUID = -5074948975765516836L;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 支付渠道（必填）
     */
    private PayChannelEnum channelType;

    /**
     * 支付场景（必填）
     */
    private PaySceneEnum payScene;

    /**
     * 活动类型（必填）
     */
    private ActivityTypeEnum activityType;

    /**
     * 机构类型
     */
    private String instituteType;

    /**
     * 渠道标识符
     */
    private String channelIdentifier;

    /**
     * 报备类型
     */
    private String reportType;

    /**
     * 交易开关
     */
    private Boolean canTrade;


    public static List<ChannelNoQueryResultBO> build(List<OpenPayChannelInfoDTO> infoList) {
        List<ChannelNoQueryResultBO> resultList = new ArrayList<>();
        for (OpenPayChannelInfoDTO openPayChannelInfoDTO : infoList) {
            SceneAndActivityTypeBO sceneAndPromotionBO = SceneAndActivityUtils.getSceneAndPromotion(
                    openPayChannelInfoDTO.getFeeType(),
                    openPayChannelInfoDTO.getPromotionType()
            );
            ChannelNoQueryResultBO channelNoQueryResultBO = ChannelNoQueryResultBO.builder()
                    .channelNo(openPayChannelInfoDTO.getChannelNo())
                    .channelName(openPayChannelInfoDTO.getChannelName())
                    .channelType(sceneAndPromotionBO.getChannel())
                    .payScene(sceneAndPromotionBO.getPayScene())
                    .activityType(sceneAndPromotionBO.getActivityType())
                    .reportType(openPayChannelInfoDTO.getReportType())
                    .instituteType(openPayChannelInfoDTO.getMchAppId())
                    .channelIdentifier(openPayChannelInfoDTO.getChannelIdentifier())
                    .canTrade(openPayChannelInfoDTO.getCanTrade())
                    .build();
            resultList.add(channelNoQueryResultBO);
        }
        return resultList;
    }
}
