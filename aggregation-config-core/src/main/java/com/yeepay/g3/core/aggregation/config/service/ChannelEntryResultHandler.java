package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.ChannelEntryResult;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;

/**
 * <AUTHOR>
 * @date 2025/6/15 15:15
 */
public interface ChannelEntryResultHandler<T extends EntryApply<? extends ChannelSpecialParams>> {
    void resultHandle(final EntryApplyContext<T> context, final ChannelEntryResult channelEntryResult);
}
