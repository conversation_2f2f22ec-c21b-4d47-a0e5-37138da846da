package com.yeepay.g3.core.aggregation.config.common;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * description: 描述
 *
 * <AUTHOR>
 * @since 2025/6/5:12:01
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum ChannelResultCode {
    SUCCESS("00000", "成功"),
    DIRECT_SUCCESS("0000", "成功"),
    SYSTEM_ERROR("899999", "系统未知异常"),

    // 业务异常
    CHANNEL_APPLY_RECORD_ERROR("840005", "申请记录异常"),
    // 申请渠道号
    CHANNEL_NO_REPEAT("840004", "申请的渠道号已存在"),
    CHANNEL_APPLY_SUBMIT_ERROR("840006", "提交渠道号申请异常"),
    // 挂靠记录查询
    CHANNEL_ATTACH_QUERY_ERROR("800010", "未查询到挂靠记录"),
    // 终端报备记录
    CHANNEL_ENTRY_QUERY_ERROR("830003", "未查询到报备记录"),

    // 进件失败-置为提交成功
    ENTRY_REQUEST_NO_REPEAT("850003", "请求号重复"),

    // 进件失败-失败
    ENTRY_HISTORY_SUCCESS("800007", "报备记录已存在"),
    CHANNEL_NO_UNABLE_ERROR("NOAUTH", "渠道号无子商户录入权限，请检查后重试"),
    SERVICE_PROVIDER_UNABLE_ERROR("CTU_FAIL_ISV_STO", "当前服务商因存在违规，不予进件。"),

    // 进件失败-待重试
    ENTRY_UN_FIND_CHANNEL_NO_ERROR("851059", "未获取到可用的渠道信息"),
    ENTRY_HISTORY_PROCESSING("800002", "该商户号在该渠道上对应的信息正在处理中，请勿重复提交"),
    CONCURRENT_TASKS_FULL("800003", "并发处理任务太多，请稍后再试"),
    ENTRY_EXIST_NO_FAIL("851035", "已存在未报备失败的报备记录"),
    ;

    private final String code;
    private final String message;


    public static List<String> getEntryFailedWaitRetryCode() {
        return Arrays.asList(
                ENTRY_REQUEST_NO_REPEAT.getCode(),
                ENTRY_UN_FIND_CHANNEL_NO_ERROR.getCode(),
                ENTRY_HISTORY_PROCESSING.getCode(),
                CONCURRENT_TASKS_FULL.getCode(),
                ENTRY_EXIST_NO_FAIL.getCode()
        );
    }


    /**
     * 转换渠道返回码
     */
    public static String convertFailCode(String failCode) {
        if (ChannelResultCode.ENTRY_HISTORY_SUCCESS.getCode().equals(failCode)) {
            return ResultCode.CHANNEL_ENTRY_IDEMPOTENT.getCode();
        } else if (ChannelResultCode.CHANNEL_NO_UNABLE_ERROR.getCode().equals(failCode)) {
            return ResultCode.CHANNEL_NO_UNABLE_ERROR.getCode();
        } else if (ChannelResultCode.SERVICE_PROVIDER_UNABLE_ERROR.getCode().equals(failCode)) {
            return ResultCode.SERVICE_PROVIDER_UNABLE_ERROR.getCode();
        } else {
            return ResultCode.CHANNEL_ENTRY_FAILED.getCode();
        }

    }

}
