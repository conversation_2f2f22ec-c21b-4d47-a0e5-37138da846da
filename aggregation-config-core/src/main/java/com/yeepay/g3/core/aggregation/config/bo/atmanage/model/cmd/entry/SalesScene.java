package com.yeepay.g3.core.aggregation.config.bo.atmanage.model.cmd.entry;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.enumerate.SalesScenesType;
import com.yeepay.aggregation.config.share.kernel.util.Assert;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.SalesSceneDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/3 14:36
 */
@Builder
@Getter
@Setter(AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class SalesScene implements Serializable {

    /**
     * 微信直连必填
     */
    private final List<SalesScenesType> salesScenesTypes;

    private final StoreInfo storeInfo;

    private final MpInfo mpInfo;

    private final MiniProgramInfo miniProgramInfo;

    public static SalesScene build(SalesSceneDTO salesScene) {
        Assert.notEmpty(salesScene.getSalesScenesTypes(), ResultCode.PARAM_VALID_ERROR, "经营场景类型不能为空");
        final List<SalesScenesType> scenesTypes = salesScene.getSalesScenesTypes().stream()
                .map(s -> DocumentedEnum.fromValue(SalesScenesType.class, s))
                .collect(Collectors.toList());
        final SalesSceneBuilder builder = SalesScene.builder()
                .salesScenesTypes(scenesTypes);

        if (scenesTypes.contains(SalesScenesType.STORE)) {
            Assert.notNull(salesScene.getStoreInfo(), ResultCode.PARAM_VALID_ERROR, "线下场所信息不能为空");
            builder.storeInfo(StoreInfo.build(salesScene.getStoreInfo()));
        }
        if (scenesTypes.contains(SalesScenesType.MP)) {
            Assert.notNull(salesScene.getMpInfo(), ResultCode.PARAM_VALID_ERROR, "公众号信息不能为空");
            builder.mpInfo(MpInfo.build(salesScene.getMpInfo()));
        }
        if (scenesTypes.contains(SalesScenesType.MINI_PROGRAM)) {
            Assert.notNull(salesScene.getMiniProgramInfo(), ResultCode.PARAM_VALID_ERROR, "小程序信息不能为空");
            builder.miniProgramInfo(MiniProgramInfo.build(salesScene.getMiniProgramInfo()));
        }

        return builder.build();
    }
}
