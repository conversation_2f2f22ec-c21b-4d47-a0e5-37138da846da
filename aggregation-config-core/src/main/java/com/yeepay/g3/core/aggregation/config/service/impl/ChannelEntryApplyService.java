package com.yeepay.g3.core.aggregation.config.service.impl;

import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.ChannelSpecialParams;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.EntryApplyContext;

/**
 * <AUTHOR>
 * @date 2025/6/19 13:02
 */
public interface ChannelEntryApplyService {
    void entryApply(EntryApplyContext<? extends EntryApply<? extends ChannelSpecialParams>> entryApplyContext);
}
