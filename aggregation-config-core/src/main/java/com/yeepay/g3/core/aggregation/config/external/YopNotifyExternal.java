package com.yeepay.g3.core.aggregation.config.external;

import com.yeepay.g3.core.aggregation.config.entity.YopNotifyEntity;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-13
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface YopNotifyExternal {

    /**
     * 通知yop
     * @param yopNotifyEntity 通知实体
     */
    boolean call(YopNotifyEntity yopNotifyEntity);
}
