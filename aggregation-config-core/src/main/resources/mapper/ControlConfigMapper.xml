<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//MyBatis//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.ControlConfigDao">

    <!-- ResultMap 定义，映射查询结果与实体类 -->
    <resultMap id="ControlConfigResultMap" type="com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity">
        <id property="id" column="ID" />
        <result property="merchantNo" column="MERCHANT_NO" />
        <result property="businessType" column="BUSINESS_TYPE" />
        <result property="controlNotifyWay" column="CONTROL_NOTIFY_WAY" />
        <result property="isLower" column="IS_LOWER" />
        <result property="emailAddress" column="EMAIL_ADDRESS"  typeHandler="com.yeepay.g3.core.aggregation.config.common.mybatis.ListToStringTypeHandler"/>
        <result property="ccAddress" column="EMAIL_CC_ADDRESS" typeHandler="com.yeepay.g3.core.aggregation.config.common.mybatis.ListToStringTypeHandler"/>
        <result property="callBackUrl" column="CALL_BACK_URL" />
        <result property="appKey" column="APP_KEY" />
        <result property="operator" column="OPERATOR" />
        <result property="remark" column="REMARK" />
        <result property="status" column="STATUS" />
        <result property="isDel" column="IS_DEL" />
        <result property="createTime" column="CREATE_TIME" />
        <result property="updateTime" column="UPDATE_TIME" />
    </resultMap>

    <!-- 字段选择片段 -->
    <sql id="Base_Column_List">
        ID, MERCHANT_NO, BUSINESS_TYPE, CONTROL_NOTIFY_WAY, IS_LOWER,
        EMAIL_ADDRESS, EMAIL_CC_ADDRESS, CALL_BACK_URL, APP_KEY,
        OPERATOR, REMARK, STATUS, IS_DEL, CREATE_TIME, UPDATE_TIME
    </sql>

    <!-- 根据ID查询记录 -->
    <select id="queryById" resultMap="ControlConfigResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_CONTROL_CONFIG
        WHERE ID = #{id}
        AND IS_DEL = 'N'
    </select>

    <!-- 根据商户号和状态查询 -->
    <select id="queryByMerchantNoAndStatus" resultMap="ControlConfigResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_CONTROL_CONFIG
        WHERE MERCHANT_NO = #{merchantNo}
        AND IS_DEL = 'N'
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        ORDER BY UPDATE_TIME DESC
        FETCH FIRST 1 ROWS ONLY
    </select>

    <!-- 选择性插入 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        INSERT INTO TBL_CONTROL_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantNo != null">MERCHANT_NO,</if>
            <if test="businessType != null">BUSINESS_TYPE,</if>
            <if test="controlNotifyWay != null">CONTROL_NOTIFY_WAY,</if>
            <if test="isLower != null">IS_LOWER,</if>
            <if test="emailAddress != null">EMAIL_ADDRESS,</if>
            <if test="ccAddress != null">EMAIL_CC_ADDRESS,</if>
            <if test="callBackUrl != null">CALL_BACK_URL,</if>
            <if test="appKey != null">APP_KEY,</if>
            <if test="operator != null">OPERATOR,</if>
            <if test="remark != null">REMARK,</if>
            <if test="status != null">STATUS,</if>
            <if test="isDel != null">IS_DEL,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="merchantNo != null">#{merchantNo},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="controlNotifyWay != null">#{controlNotifyWay},</if>
            <if test="isLower != null">#{isLower},</if>
            <if test="emailAddress != null">#{emailAddress,typeHandler=com.yeepay.g3.core.aggregation.config.common.mybatis.ListToStringTypeHandler},</if>
            <if test="ccAddress != null">#{ccAddress,typeHandler=com.yeepay.g3.core.aggregation.config.common.mybatis.ListToStringTypeHandler},</if>
            <if test="callBackUrl != null">#{callBackUrl},</if>
            <if test="appKey != null">#{appKey},</if>
            <if test="operator != null">#{operator},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <!-- 根据ID选择性更新 -->
    <update id="updateSelective">
        UPDATE TBL_CONTROL_CONFIG
        <set>
            <if test="merchantNo != null">MERCHANT_NO = #{merchantNo},</if>
            <if test="businessType != null">BUSINESS_TYPE = #{businessType},</if>
            <if test="controlNotifyWay != null">CONTROL_NOTIFY_WAY = #{controlNotifyWay},</if>
            <if test="isLower != null">IS_LOWER = #{isLower},</if>
            <if test="emailAddress != null">EMAIL_ADDRESS = #{emailAddress,typeHandler=com.yeepay.g3.core.aggregation.config.common.mybatis.ListToStringTypeHandler},</if>
            <if test="ccAddress != null">EMAIL_CC_ADDRESS = #{ccAddress,typeHandler=com.yeepay.g3.core.aggregation.config.common.mybatis.ListToStringTypeHandler},</if>
            <if test="callBackUrl != null">CALL_BACK_URL = #{callBackUrl},</if>
            <if test="appKey != null">APP_KEY = #{appKey},</if>
            <if test="operator != null">OPERATOR = #{operator},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="isDel != null">IS_DEL = #{isDel},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime}</if>
        </set>
        WHERE ID = #{id}
    </update>

</mapper>