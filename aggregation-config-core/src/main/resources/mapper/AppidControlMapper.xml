<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.AppidControlDao">

    <!-- ResultMap 定义，映射查询结果与实体类 -->
    <resultMap id="AddidControlResultMap" type="com.yeepay.g3.core.aggregation.config.entity.AppidControlEntity">
        <id property="id" column="ID" />
        <result property="appid" column="APPID" />
        <result property="controlStartTime" column="CONTROL_START_TIME" />
        <result property="controlEndTime" column="CONTROL_END_TIME" />
        <result property="notifyType" column="NOTIFY_TYPE" />
        <result property="notifyStatus" column="NOTIFY_STATUS" />
        <result property="version" column="VERSION" />
        <result property="errorCode" column="ERROR_CODE" />
        <result property="errorMsg" column="ERROR_MSG" />
        <result property="createTime" column="CREATE_TIME" />
        <result property="updateTime" column="UPDATE_TIME" />
    </resultMap>

    <!-- 字段选择片段 -->
    <sql id="Base_Column_List">
        ID, APPID, CONTROL_START_TIME, CONTROL_END_TIME, NOTIFY_TYPE, NOTIFY_STATUS,VERSION ,
        ERROR_CODE, ERROR_MSG, CREATE_TIME, UPDATE_TIME
    </sql>

    <!-- 根据ID查询记录 -->
    <select id="queryById" resultMap="AddidControlResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_CONTROL
        WHERE ID = #{id}
    </select>

    <!-- 根据APPID查询记录 -->
    <select id="queryByAppid" resultMap="AddidControlResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_CONTROL
        WHERE APPID = #{appid}
    </select>
    <select id="queryByControlList" resultMap="AddidControlResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_CONTROL
        WHERE  1=1
        <if test="appid != null and  appid != '' ">
            and APPID = #{appid}
        </if>
        <if test="notifyStatus != null and notifyStatus != ''  ">
            and NOTIFY_STATUS =#{notifyStatus}
        </if>
    </select>

    <!-- 插入单条记录 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        INSERT INTO TBL_APPID_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">APPID,</if>
            <if test="controlStartTime != null">CONTROL_START_TIME,</if>
            <if test="controlEndTime != null">CONTROL_END_TIME,</if>
            <if test="notifyType != null">NOTIFY_TYPE,</if>
            <if test="notifyStatus != null">NOTIFY_STATUS,</if>
            <if test="version != null">VERSION,</if>
            <if test="errorCode != null">ERROR_CODE,</if>
            <if test="errorMsg != null">ERROR_MSG,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="appid != null">#{appid},</if>
            <if test="controlStartTime != null">#{controlStartTime},</if>
            <if test="controlEndTime != null">#{controlEndTime},</if>
            <if test="notifyType != null">#{notifyType},</if>
            <if test="notifyStatus != null">#{notifyStatus},</if>
            <if test="version != null">#{version},</if>
            <if test="errorCode != null">#{errorCode},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <!-- 更新单条记录 -->
    <update id="updateByIdVersion">
        UPDATE TBL_APPID_CONTROL
        <set>
            <if test="entity.controlStartTime != null">CONTROL_START_TIME = #{entity.controlStartTime},</if>
             CONTROL_END_TIME = #{entity.controlEndTime},
            <if test="entity.notifyType != null">NOTIFY_TYPE = #{entity.notifyType},</if>
            <if test="entity.notifyStatus != null">NOTIFY_STATUS = #{entity.notifyStatus},</if>
            <if test="entity.version != null">VERSION = #{entity.version},</if>
            <if test="entity.errorCode != null">ERROR_CODE = #{entity.errorCode},</if>
            <if test="entity.errorMsg != null">ERROR_MSG = #{entity.errorMsg},</if>
            <if test="entity.createTime != null">CREATE_TIME = #{entity.createTime},</if>
            <if test="entity.updateTime != null">UPDATE_TIME = #{entity.updateTime},</if>
        </set>
        WHERE APPID = #{entity.appid} AND version = #{oldVersion}
    </update>

</mapper>
