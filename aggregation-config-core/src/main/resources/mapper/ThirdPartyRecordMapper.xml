<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.ThirdPartyRecordDao">

    <!-- resultMap -->
    <resultMap id="BaseResultMap" type="com.yeepay.g3.core.aggregation.config.entity.atmanage.ThirdPartyRecordEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="apply_no" property="applyNo" jdbcType="VARCHAR"/>
        <result column="request_no" property="requestNo" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="inner_channel_apply_no" property="innerChannelApplyNo" jdbcType="VARCHAR"/>
        <result column="channel_apply_no" property="channelApplyNo" jdbcType="VARCHAR"/>
        <result column="channel_response_code" property="channelResponseCode" jdbcType="VARCHAR"/>
        <result column="channel_response_message" property="channelResponseMessage" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="create_dt" property="createDt" jdbcType="TIMESTAMP"  />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="update_dt" property="updateDt" jdbcType="TIMESTAMP" />
    </resultMap>
    <sql id="Base_Column_List">
        id, apply_no, request_no, business_type, status, inner_channel_apply_no,
        channel_apply_no, channel_response_code, channel_response_message,
        created_by, create_dt, updated_by, update_dt
    </sql>
    
    <select id="queryThirdPartyRecordByApplyNo" resultMap="BaseResultMap" >
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_THIRD_PARTY_RECORD
        WHERE apply_no = #{applyNo}
          and business_type = #{businessType}
    </select>
    
    
    <select id="queryThirdRecordByRequestNo" resultMap="BaseResultMap" >
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_THIRD_PARTY_RECORD
        WHERE request_no = #{requestNo}
          and business_type = #{businessType}
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsertThirdPartyRecord" parameterType="java.util.List">
        INSERT INTO TBL_THIRD_PARTY_RECORD (
        apply_no, request_no, business_type, status,
            created_by, create_dt, updated_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.applyNo,jdbcType=VARCHAR}, #{item.requestNo,jdbcType=VARCHAR}, #{item.businessType,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
                #{item.createDt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>