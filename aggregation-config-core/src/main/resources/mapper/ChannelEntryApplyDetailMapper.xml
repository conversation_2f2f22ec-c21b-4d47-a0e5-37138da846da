<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.entry.ChannelEntryApplyDetailDao">

    <!-- 定义 resultMap -->
    <resultMap id="BaseResultMap"
               type="com.yeepay.g3.core.aggregation.config.entity.atmanage.ChannelEntryApplyDetailEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="applyNo" column="apply_no" jdbcType="VARCHAR"/>
        <result property="merchantNo" column="merchant_no" jdbcType="VARCHAR"/>
        <result property="merchantName" column="merchant_name" jdbcType="VARCHAR"/>
        <result property="entryType" column="entry_type" jdbcType="VARCHAR"/>
        <result property="channelNo" column="channel_no" jdbcType="VARCHAR"/>
        <result property="channelIdentifier" column="channel_identifier" jdbcType="VARCHAR"/>
        <result property="payScene" column="pay_scene" jdbcType="VARCHAR"/>
        <result property="payChannel" column="pay_channel" jdbcType="VARCHAR"/>
        <result property="channelGatewayCode" column="channel_gateway_code" jdbcType="VARCHAR"/>
        <result property="activityType" column="activity_type" jdbcType="VARCHAR"/>
        <result property="channelParams" column="channel_params" jdbcType="VARCHAR"/>
        <result property="backupCount" column="backup_count" jdbcType="SMALLINT"/>
        <result property="bankCode" column="bank_code" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
        <result property="signUrl" column="sign_url" jdbcType="VARCHAR"/>
        <result property="flag" column="flag" jdbcType="BIGINT"/>
        <result property="flagStatus" column="flag_status" jdbcType="BIGINT"/>
        <result property="channelMchNos" column="channel_mch_nos" jdbcType="VARCHAR"/>
        <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="anchoredOrderNo" column="anchored_order_no" jdbcType="VARCHAR"/>
        <result property="extendInfo" column="extend_info" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="SMALLINT"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updateDt" column="update_dt" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id
        ,
        order_no,
        apply_no,
        merchant_no,
        merchant_name,
        entry_type,
        channel_no,
        channel_identifier,
        pay_scene,
        pay_channel,
        channel_gateway_code,
        activity_type,
        channel_params,
        backup_count,
        status,
        audit_status,
        sign_url,
        flag,
        flag_status,
        channel_mch_nos,
        finish_time,
        fail_reason,
        bank_code,
        anchored_order_no,
        extend_info,
        del_flag,
        created_by,
        create_dt,
        updated_by,
        update_dt
    </sql>
    
    <!-- 批量插入（已提供） -->
    <insert id="batchInsert">
        INSERT INTO t_channel_entry_apply_detail (
        order_no,
        apply_no,
        merchant_no,
        merchant_name,
        entry_type,
        channel_no,
        channel_identifier,
        pay_scene,
        pay_channel,
        bank_code,
        activity_type,
        channel_params,
        backup_count,
        status,
        audit_status,
        flag,
        flag_status,
        extend_info
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderNo},
            #{item.applyNo},
            #{item.merchantNo},
            #{item.merchantName},
            #{item.entryType},
            #{item.channelNo},
            #{item.channelIdentifier},
            #{item.payScene},
            #{item.payChannel},
            #{item.bankCode},
            #{item.activityType},
            #{item.channelParams},
            #{item.backupCount},
            #{item.status},
            #{item.auditStatus},
            #{item.flag},
            #{item.flagStatus},
            #{item.extendInfo}
            )
        </foreach>
    </insert>
    
    <select id="selectChannelEntryApplyDetailEntityForUpdate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_channel_entry_apply_detail
        WHERE apply_no = #{applyNo}
        FOR UPDATE with rr
    </select>
    
    
    <select id="queryNotFinishChannelEntryOrder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_channel_entry_apply_detail
        WHERE
        ( status in ('INIT', 'PROCESSING') or
        (status ='SUCCESS' --成功的但是需要补终端报备/分账方创建的
        and (
        (BITAND(flag, 1) != 0 and BITAND(flag_status, 1) = 0)
        or (BITAND(flag, 2) != 0 and BITAND(flag_status, 2) =0)
        )
        )
        )
    
        <if test="applyNoList != null and applyNoList.size() > 0">
            and apply_no IN
            <foreach item="item" collection="applyNoList" index="index" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_dt >= #{startTime}
            and create_dt <![CDATA[ <= ]]> #{endTime}
        </if>
        order by create_dt desc
        FETCH FIRST 1000 ROWS ONLY
    </select>

</mapper>