<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.AnchoredOrderDao">

    <!-- 添加 resultMap 映射 -->
    <resultMap id="BaseResultMap" type="com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="biz_scene" property="bizScene" jdbcType="VARCHAR"/>
        <result column="biz_apply_no" property="bizApplyNo" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="parent_merchant_no" property="parentMerchantNo" jdbcType="VARCHAR"/>
        <result column="top_merchant_no" property="topMerchantNo" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR"/>
        <result column="finish_time" property="finishTime" jdbcType="TIMESTAMP" />
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="update_dt" property="updateDt" jdbcType="TIMESTAMP" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="create_dt" property="createDt" jdbcType="TIMESTAMP" />
    </resultMap>
    
    <sql id="Base_Column_List">
            id,
            biz_scene,
            biz_apply_no,
            order_no,
            merchant_no,
            parent_merchant_no,
            top_merchant_no,
            order_status,
            finish_time,
            fail_reason,
            del_flag,
            updated_by,
            update_dt,
            created_by,
            create_dt
    </sql>

    <!-- 插入记录 -->
    <insert id="createAnchoredOrder" parameterType="com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredOrderEntity">
        INSERT INTO t_anchored_order (
            biz_scene,
            biz_apply_no,
            order_no,
            merchant_no,
            parent_merchant_no,
            top_merchant_no,
            order_status,
            finish_time,
            fail_reason,
            created_by
        ) VALUES (
                     #{bizScene},
                     #{bizApplyNo},
                     #{orderNo},
                     #{merchantNo},
                     #{parentMerchantNo},
                     #{topMerchantNo},
                     #{orderStatus},
                     #{finishTime},
                     #{failReason},
                     #{createdBy}
        )
    </insert>

    <!-- 更新记录 -->
    <update id="updateAnchoredOrderFinish" >
        UPDATE t_anchored_order
        SET
            order_status = 'FINISH',
            finish_time = current_timestamp
        WHERE order_no = #{orderNo}
            and  order_status != 'FINISH'
    </update>

   

    <!-- 根据业务场景和业务申请单号查询记录 -->
    <select id="selectByBizSceneAndBizApplyNo" parameterType="map" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_anchored_order
        WHERE biz_scene = #{bizScene}
          AND biz_apply_no = #{bizApplyNo}
    </select>
    
    <select id="queryAnchoredOrderByOrderNo" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_anchored_order
        WHERE order_no = #{orderNo}
    </select>
    
    <select id="selectUnFinishAnchoredOrderList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_anchored_order
        WHERE
            order_status != 'FINISH'
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no IN
                <foreach item="item" collection="orderNoList" index="index" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and create_dt >= #{startTime}
                and create_dt <![CDATA[ <= ]]> #{endTime}
            </if>
        order by create_dt desc
        FETCH FIRST 1000 ROWS ONLY
    </select>
    
    

</mapper>



