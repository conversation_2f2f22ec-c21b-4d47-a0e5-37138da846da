<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.AnchoredApplyDao">
	
	<!-- 添加 resultMap 映射 -->
	<resultMap id="BaseResultMap" type="com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="biz_scene" property="bizScene" jdbcType="VARCHAR"/>
		<result column="biz_apply_no" property="bizApplyNo" jdbcType="VARCHAR"/>
		<result column="apply_no" property="applyNo" jdbcType="VARCHAR"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
		<result column="parent_merchant_no" property="parentMerchantNo" jdbcType="VARCHAR"/>
		<result column="top_merchant_no" property="topMerchantNo" jdbcType="VARCHAR"/>
		<result column="anchored_type" property="anchoredType" jdbcType="VARCHAR"/>
		<result column="anchored_dimension" property="anchoredDimension" jdbcType="VARCHAR"/>
		<result column="relation_merchant_no" property="relationMerchantNo" jdbcType="VARCHAR"/>
		<result column="pay_scene" property="payScene" jdbcType="VARCHAR"/>
		<result column="pay_channel" property="payChannel" jdbcType="VARCHAR"/>
		<result column="activity_type" property="activityType" jdbcType="VARCHAR"/>
		<result column="support_pay_type" property="supportPayType" jdbcType="BIGINT"/>
		<result column="auth_file_name" property="authFileName" jdbcType="VARCHAR"/>
		<result column="auth_file_url" property="authFileUrl" jdbcType="VARCHAR"/>
		<result column="group_name" property="groupName" jdbcType="VARCHAR"/>
		<result column="anchored_status" property="anchoredStatus" jdbcType="VARCHAR"/>
		<result column="finish_time" property="finishTime" jdbcType="TIMESTAMP"/>
		<result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
		<result column="agg_anchored_merchant_text" property="aggAnchoredMerchantText" jdbcType="VARCHAR"/>
		<result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
		<result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
		<result column="update_dt" property="updateDt" jdbcType="TIMESTAMP"/>
		<result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
		<result column="create_dt" property="createDt" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<sql id="Base_Column_List">
		id
		,
            biz_scene,
            biz_apply_no,
            apply_no,
            order_no,
            merchant_no,
            parent_merchant_no,
            top_merchant_no,
            anchored_type,
            anchored_dimension,
            pay_scene,
            pay_channel,
            activity_type,
            support_pay_type,
            auth_file_name,
            auth_file_url,
            group_name,
            anchored_status,
            finish_time,
            fail_reason,
            relation_merchant_no,
            agg_anchored_merchant_text,
            del_flag,
            updated_by,
            update_dt,
            created_by,
            create_dt
	</sql>
	<!-- 插入记录 -->
	<insert id="saveAnchoredApply"
	        parameterType="com.yeepay.g3.core.aggregation.config.entity.atmanage.AnchoredApplyEntity">
		INSERT INTO t_anchored_apply (biz_scene,
		                              biz_apply_no,
		                              apply_no,
		                              order_no,
		                              merchant_no,
		                              parent_merchant_no,
		                              top_merchant_no,
		                              anchored_type,
		                              anchored_dimension,
		                              relation_merchant_no,
		                              pay_scene,
		                              pay_channel,
		                              activity_type,
		                              support_pay_type,
		                              auth_file_name,
		                              auth_file_url,
		                              group_name,
		                              anchored_status,
		                              fail_reason,
		                              created_by,
		                              create_dt)
		VALUES (#{bizScene},
		        #{bizApplyNo},
		        #{applyNo},
		        #{orderNo},
		        #{merchantNo},
		        #{parentMerchantNo},
		        #{topMerchantNo},
		        #{anchoredType},
		        #{anchoredDimension},
		        #{relationMerchantNo},
		        #{payScene},
		        #{payChannel},
		        #{activityType},
		        #{supportPayType},
		        #{authFileName},
		        #{authFileUrl},
		        #{groupName},
		        #{anchoredStatus},
		        #{failReason},
		        #{createdBy},
		        #{createDt})
	</insert>
	
	<!-- 批量插入记录 -->
	<insert id="batchSaveAnchoredApply" parameterType="java.util.List">
		INSERT INTO t_anchored_apply (
		biz_scene,
		biz_apply_no,
		apply_no,
		order_no,
		merchant_no,
		parent_merchant_no,
		top_merchant_no,
		anchored_type,
		anchored_dimension,
		relation_merchant_no,
		pay_scene,
		pay_channel,
		activity_type,
		support_pay_type,
		auth_file_name,
		auth_file_url,
		group_name,
		anchored_status,
		fail_reason,
		created_by,
		create_dt
		) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.bizScene},
			#{item.bizApplyNo},
			#{item.applyNo},
			#{item.orderNo},
			#{item.merchantNo},
			#{item.parentMerchantNo},
			#{item.topMerchantNo},
			#{item.anchoredType},
			#{item.anchoredDimension},
			#{item.relationMerchantNo},
			#{item.payScene},
			#{item.payChannel},
			#{item.activityType},
			#{item.supportPayType},
			#{item.authFileName},
			#{item.authFileUrl},
			#{item.groupName},
			#{item.anchoredStatus},
			#{item.failReason},
			#{item.createdBy},
			#{item.createDt,jdbcType=TIMESTAMP}
			)
		</foreach>
	</insert>
	
	<update id="updateAnchoredProcess">
		UPDATE t_anchored_apply
		SET anchored_status = 'PROCESSING'
		WHERE apply_no = #{applyNo}
		  and anchored_status = 'INIT'
	</update>
	
	<update id="updateAggAnchoredResultSuccess">
		UPDATE t_anchored_apply
		SET agg_anchored_merchant_text = #{aggAnchoredMerchantInfoJson},
		    anchored_status            = 'AGG_SUCCESS'
		WHERE apply_no = #{applyNo}
		  and anchored_status = 'PROCESSING'
	</update>
	
	<update id="updateAggAnchoredResultFail">
		UPDATE t_anchored_apply
		SET anchored_status = 'FAIL',
		    fail_reason     = #{failReason},
		    finish_time     = current_timestamp
		WHERE apply_no = #{applyNo}
		  and anchored_status in ('PROCESSING', 'INIT')
	</update>
	
	
	<update id="updateChannelAnchoredInfo">
		UPDATE t_anchored_apply
		SET agg_anchored_merchant_text = #{aggAnchoredMerchantInfoJson}
		WHERE apply_no = #{applyNo}
	</update>
	
	<update id="updateChannelAnchoredResult">
		UPDATE t_anchored_apply
		SET anchored_status = #{anchoredStatus},
		    fail_reason     = #{failReason},
		    finish_time     = current_timestamp
		WHERE apply_no = #{applyNo}
		  and anchored_status in ('AGG_SUCCESS')
	</update>
	
	
	<select id="queryAllAnchoredApplyByOrderNo" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List"/>
		FROM t_anchored_apply
		WHERE order_no = #{orderNo}
	</select>
	
	<!-- 查询单条记录 -->
	<select id="queryAnchoredApplyByApplyNo" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List"/>
		FROM t_anchored_apply
		WHERE apply_no = #{applyNo}
	</select>
	
	
	<select id="queryAnchoredApplyByApplyNoForUpdate" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List"/>
		FROM t_anchored_apply
		WHERE apply_no = #{applyNo}
		for update with rr
	</select>


</mapper>









