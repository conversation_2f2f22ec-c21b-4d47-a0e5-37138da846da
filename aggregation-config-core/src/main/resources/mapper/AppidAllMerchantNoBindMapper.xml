<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.AppidAllMerchantNoBindDao">

    <!-- ResultMap 定义，映射查询结果与实体类 -->
    <resultMap id="AppidAllMerchantNoBindResultMap" type="com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity">
        <id property="id" column="ID" />
        <result property="appid" column="APPID" />
        <result property="merchantNo" column="MERCHANT_NO" />
        <result property="merchantName" column="MERCHANT_NAME" />
        <result property="attachMerchantNo" column="ATTACH_MERCHANT_NO" />
        <result property="firstMerchantNo" column="FIRST_MERCHANT_NO"/>
        <result property="secondMerchantNo" column="SECOND_MERCHANT_NO"/>
        <result property="threeMerchantNo" column="THREE_MERCHANT_NO"/>
        <result property="merchantTag" column="MERCHANT_TAG" />
        <result property="notifyType" column="NOTIFY_TYPE" />
        <result property="bindStatus" column="BIND_STATUS" />
        <result property="emailLevel" column="EMAIL_LEVEL" />
        <result property="urlLevel" column="URL_LEVEL" />
        <result property="waringStatus" column="WARING_STATUS" />
        <result property="emailNotifyStatus" column="EMAIL_NOTIFY_STATUS" />
        <result property="urlNotifyStatus" column="URL_NOTIFY_STATUS" />
        <result property="errorCode" column="ERROR_CODE" />
        <result property="errorMsg" column="ERROR_MSG" />
        <result property="createTime" column="CREATE_TIME" />
        <result property="updateTime" column="UPDATE_TIME" />
    </resultMap>

    <!-- 字段选择片段 -->
    <sql id="Base_Column_List">
        ID, APPID, MERCHANT_NO, MERCHANT_NAME, ATTACH_MERCHANT_NO, FIRST_MERCHANT_NO, SECOND_MERCHANT_NO, THREE_MERCHANT_NO, MERCHANT_TAG, NOTIFY_TYPE, BIND_STATUS, EMAIL_LEVEL, URL_LEVEL, WARING_STATUS,
       EMAIL_NOTIFY_STATUS, URL_NOTIFY_STATUS, ERROR_CODE, ERROR_MSG, CREATE_TIME, UPDATE_TIME
    </sql>

    <!-- 根据ID查询记录 -->
    <select id="queryById" resultMap="AppidAllMerchantNoBindResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM  TBL_APPID_ALL_MERCHANT_NO_BIND
        WHERE ID = #{id}
    </select>

    <!-- 根据APPID和商户号查询 -->
     <select id="queryByAppidAndMerchantNo" resultMap="AppidAllMerchantNoBindResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_ALL_MERCHANT_NO_BIND
        WHERE APPID = #{appid}
         <if test="firstMerchantNo != null" >
             AND FIRST_MERCHANT_NO = #{firstMerchantNo}
         </if>
    </select>
    <select id="queryByAppidGroupByFirstMerchantNo" resultType="java.lang.String">
        select FIRST_MERCHANT_NO
        from TBL_APPID_ALL_MERCHANT_NO_BIND
        where appid=#{appid}
        and BIND_STATUS = 'BIND'
        <if test="warningStatus != null">
            AND WARING_STATUS != #{warningStatus}
        </if>
        group by FIRST_MERCHANT_NO
    </select>
    <select id="queryAllByAppIdList" resultMap="AppidAllMerchantNoBindResultMap">
        SELECT
        <include refid="Base_Column_List" />
        from TBL_APPID_ALL_MERCHANT_NO_BIND
        WHERE APPID IN
        <foreach collection="appidList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND MERCHANT_NO =#{merchantNo}
        AND ATTACH_MERCHANT_NO =#{attachMerchantNo}
    </select>
    <select id="getTopMerchantsByAppidAndConfigAndEmail" resultType="java.lang.String">

        SELECT c_config.MERCHANT_NO FROM
        TBL_APPID_ALL_MERCHANT_NO_BIND AS a_all LEFT JOIN
        <if test="merchantGrade == 3">
        TBL_CONTROL_CONFIG AS c_config ON a_all.THREE_MERCHANT_NO = c_config.MERCHANT_NO
        </if>
        <if test="merchantGrade == 2">
            TBL_CONTROL_CONFIG AS c_config ON a_all.SECOND_MERCHANT_NO = c_config.MERCHANT_NO
        </if>
        <if test="merchantGrade == 1">
            TBL_CONTROL_CONFIG AS c_config ON a_all.FIRST_MERCHANT_NO = c_config.MERCHANT_NO
        </if>
        WHERE a_all.APPID =#{appid}
         <if test="notifyStatus == 'NOTIFIED'">
         AND  a_all.EMAIL_NOTIFY_STATUS != 'NOTIFIED'

         </if>
        and a_all.EMAIL_LEVEL <![CDATA[ <= ]]>  #{merchantGrade}
        AND c_config.IS_DEL='N' AND STATUS = 'ACTIVE'
        and c_config.CONTROL_NOTIFY_WAY in ('EMAIL_AND_CAL_BACK','EMAIL')

        GROUP BY c_config.MERCHANT_NO
    </select>


    <select id="getTopMerchantsByAppidAndConfigAndUrl" resultType="java.lang.String">

        SELECT c_config.MERCHANT_NO FROM
        TBL_APPID_ALL_MERCHANT_NO_BIND AS a_all LEFT JOIN
        <if test="merchantGrade == 3">
            TBL_CONTROL_CONFIG AS c_config ON a_all.THREE_MERCHANT_NO = c_config.MERCHANT_NO
        </if>
        <if test="merchantGrade == 2">
            TBL_CONTROL_CONFIG AS c_config ON a_all.SECOND_MERCHANT_NO = c_config.MERCHANT_NO
        </if>
        <if test="merchantGrade == 1">
            TBL_CONTROL_CONFIG AS c_config ON a_all.FIRST_MERCHANT_NO = c_config.MERCHANT_NO
        </if>
        WHERE a_all.APPID =#{appid}
        <if test="notifyStatus == 'NOTIFIED'">
            AND  a_all.URL_NOTIFY_STATUS != 'NOTIFIED'

        </if>
        and a_all.URL_LEVEL <![CDATA[ <= ]]>  #{merchantGrade}
        AND c_config.IS_DEL='N' AND STATUS = 'ACTIVE'
        and c_config.CONTROL_NOTIFY_WAY in ('EMAIL_AND_CAL_BACK','CALL_BACK')

        GROUP BY c_config.MERCHANT_NO
    </select>



    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        INSERT INTO TBL_APPID_ALL_MERCHANT_NO_BIND
        (
        APPID,
        MERCHANT_NO,
        MERCHANT_NAME,
        MERCHANT_TAG,
        NOTIFY_TYPE,
        BIND_STATUS,
        WARING_STATUS,
        EMAIL_NOTIFY_STATUS,
        URL_NOTIFY_STATUS,
        ERROR_CODE,
        ERROR_MSG,
        CREATE_TIME,
        UPDATE_TIME,
        ATTACH_MERCHANT_NO,
        FIRST_MERCHANT_NO,
        SECOND_MERCHANT_NO,
        THREE_MERCHANT_NO
        )
        VALUES
        (
        #{appid},
        #{merchantNo},
        #{merchantName},
        #{merchantTag},
        #{notifyType},
        #{bindStatus},
        <if test="waringStatus != null">#{waringStatus}</if>
        <if test="waringStatus == null">'INIT'</if>,
        <if test="emailNotifyStatus != null">#{emailNotifyStatus}</if>
        <if test="emailNotifyStatus == null">'INIT'</if>,
        <if test="urlNotifyStatus != null">#{urlNotifyStatus}</if>
        <if test="urlNotifyStatus == null">'INIT'</if>,
        #{errorCode},
        #{errorMsg},
        #{createTime},
        #{updateTime},
        #{attachMerchantNo},
        #{firstMerchantNo},
        #{secondMerchantNo},
        #{threeMerchantNo}
        )
    </insert>

    <update id="updateSelective">
        UPDATE TBL_APPID_ALL_MERCHANT_NO_BIND
        <set>
            <if test="appid != null">APPID = #{appid},</if>
            <if test="merchantNo != null">MERCHANT_NO = #{merchantNo},</if>
            <if test="merchantName != null">MERCHANT_NAME = #{merchantName},</if>
            <if test="merchantTag != null">MERCHANT_TAG = #{merchantTag},</if>
            <if test="notifyType != null">NOTIFY_TYPE = #{notifyType},</if>
            <if test="emailNotifyStatus != null">EMAIL_NOTIFY_STATUS = #{emailNotifyStatus},</if>
            <if test="urlNotifyStatus != null">URL_NOTIFY_STATUS = #{urlNotifyStatus},</if>
            <if test="errorCode != null">ERROR_CODE = #{errorCode},</if>
            <if test="errorMsg != null">ERROR_MSG = #{errorMsg},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="waringStatus != null">WARING_STATUS = #{waringStatus},</if>
            <if test="bindStatus != null">BIND_STATUS = #{bindStatus},</if>
            <if test="attachMerchantNo != null">ATTACH_MERCHANT_NO = #{attachMerchantNo},</if>
        </set>
        WHERE ID = #{id}
    </update>
    <update id="updateWaringStatus">
        UPDATE TBL_APPID_ALL_MERCHANT_NO_BIND
        SET WARING_STATUS='WARING',
        UPDATE_TIME= CURRENT_TIMESTAMP
        WHERE FIRST_MERCHANT_NO IN
        <foreach collection="firstMerchants" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND appid=#{appid}
        AND  WARING_STATUS !='WARING'
        and BIND_STATUS = 'BIND'

    </update>

    <update id="updateNotifyStatusEmail">
        UPDATE TBL_APPID_ALL_MERCHANT_NO_BIND
        set
            EMAIL_NOTIFY_STATUS=#{emailNotifyStatus},
            EMAIL_LEVEL =#{emailLevel},
            UPDATE_TIME= CURRENT_TIMESTAMP
        WHERE
         appid=#{appid}
        and MERCHANT_NO IN
        <foreach collection="subMerchantNos" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="merchantGrade == 1" >
            AND FIRST_MERCHANT_NO = #{merchantGradeNo}
        </if>
        <if test="merchantGrade == 2" >
            AND SECOND_MERCHANT_NO = #{merchantGradeNo}
        </if>
        <if test="merchantGrade == 3" >
            AND THREE_MERCHANT_NO = #{merchantGradeNo}
        </if>

   </update>
    <update id="updateNotifyStatusUrl">
        UPDATE TBL_APPID_ALL_MERCHANT_NO_BIND
        set
            URL_NOTIFY_STATUS=#{urlNotifyStatus},
            URL_LEVEL =#{urlLevel},
            UPDATE_TIME= CURRENT_TIMESTAMP
        WHERE
         appid=#{appid}
        and MERCHANT_NO IN
        <foreach collection="subMerchantNos" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="merchantGrade == 1" >
            AND FIRST_MERCHANT_NO = #{merchantGradeNo}
        </if>
        <if test="merchantGrade == 2" >
            AND SECOND_MERCHANT_NO = #{merchantGradeNo}
        </if>
        <if test="merchantGrade == 3" >
            AND THREE_MERCHANT_NO = #{merchantGradeNo}
        </if>

   </update>
    <update id="updateLevelByAppid">
        UPDATE TBL_APPID_ALL_MERCHANT_NO_BIND
        set
            URL_LEVEL =0,
            EMAIL_LEVEL =0
        where appid = #{appid}

    </update>


    <select id="queryAllMerchantNoBindByMerchantGrade" resultMap="AppidAllMerchantNoBindResultMap" >
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_ALL_MERCHANT_NO_BIND
        WHERE APPID = #{appid}
        <if test="merchantGrade == 1" >
            AND FIRST_MERCHANT_NO = #{merchantGradeNo}
        </if>
          <if test="merchantGrade == 2" >
            AND SECOND_MERCHANT_NO = #{merchantGradeNo}
         </if>
          <if test="merchantGrade == 3" >
            AND THREE_MERCHANT_NO = #{merchantGradeNo}
        </if>
    </select>
    <select id="queryByAppidAndMerchantNoAndAttachMerchantNo" resultMap="AppidAllMerchantNoBindResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_ALL_MERCHANT_NO_BIND
        WHERE APPID = #{appid}
        <if test="attachMerchantNo != null" >
            AND ATTACH_MERCHANT_NO = #{attachMerchantNo}
        </if>
    </select>

</mapper>
