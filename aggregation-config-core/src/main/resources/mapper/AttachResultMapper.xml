<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.AttachResultDao">

    <!-- ResultMap 定义，映射查询结果与实体类 -->
    <resultMap id="AttachResultResultMap" type="com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity">
        <id property="id" column="ID" />
        <result property="mainMerchantNo" column="MAIN_MERCHANT_NO" />
        <result property="subMerchantNo" column="SUB_MERCHANT_NO" />
        <result property="channel" column="CHANNEL" />
        <result property="merchantScan" column="MERCHANT_SCAN" />
        <result property="userScan" column="USER_SCAN" />
        <result property="miniProgram" column="MINI_PROGRAM" />
        <result property="wechatOffiaccount" column="WECHAT_OFFIACCOUNT" />
        <result property="approvalResult" column="APPROVAL_RESULT" />
        <result property="failureReason" column="FAILURE_REASON" />
        <result property="applyReason" column="APPLY_REASON" />
        <result property="applyTime" column="APPLY_TIME" />
        <result property="configStatus" column="CONFIG_STATUS" />
        <result property="configTime" column="CONFIG_TIME" />
        <result property="createdTime" column="CREATED_TIME" />
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" />
        <result property="alipayLife" column="ALIPAY_LIFE" />
        <result property="source" column="SOURCE" />
        <result property="cleanStatus" column="CLEAN_STATUS" />
        <result property="subFirstMerchantNo" column="SUB_FIRST_MERCHANT_NO"/>
        <result property="subSecondMerchantNo" column="SUB_SECOND_MERCHANT_NO"/>
        <result property="subThreeMerchantNo" column="SUB_THREE_MERCHANT_NO"/>
        <result property="subMerchantName" column="SUB_MERCHANT_NAME" />
    </resultMap>

    <!-- 字段选择片段 -->
    <sql id="Base_Column_List">
        ID, MAIN_MERCHANT_NO, SUB_MERCHANT_NO, CHANNEL, MERCHANT_SCAN,
        USER_SCAN, MINI_PROGRAM, WECHAT_OFFIACCOUNT,
        APPROVAL_RESULT, FAILURE_REASON, APPLY_REASON, APPLY_TIME, CONFIG_STATUS, CONFIG_TIME,
        CREATED_TIME, LAST_MODIFIED_TIME, ALIPAY_LIFE , SOURCE ,CLEAN_STATUS,
        SUB_FIRST_MERCHANT_NO,SUB_SECOND_MERCHANT_NO,SUB_THREE_MERCHANT_NO,
        SUB_MERCHANT_NAME
    </sql>

    <!-- 根据ID查询记录 -->
    <select id="queryById" resultMap="AttachResultResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_ATTACH_RESULT
        WHERE ID = #{id}
    </select>

    <!-- 根据mainMerchantNo查询记录 -->
    <select id="queryByMainMerchantNo" resultMap="AttachResultResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_ATTACH_RESULT
        WHERE MAIN_MERCHANT_NO = #{mainMerchantNo}
    </select>
    <select id="queryByUniqueKey" resultMap="AttachResultResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_ATTACH_RESULT
        WHERE MAIN_MERCHANT_NO = #{mainMerchantNo}
        AND SUB_MERCHANT_NO = #{subMerchantNo}
        AND CHANNEL = #{channel}

    </select>
    <select id="queryByMerchantNoAndStatus" resultMap="AttachResultResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM TBL_ATTACH_RESULT
        WHERE MAIN_MERCHANT_NO = #{merchantNo}
        AND CONFIG_STATUS=#{configStatus.name}
        AND CHANNEL = #{channelType.name}
        AND SOURCE =#{source.name}
        AND CLEAN_STATUS=#{dataClearStatus.name}
    </select>

    <insert id="insert" keyColumn="id" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO TBL_ATTACH_RESULT (
            MAIN_MERCHANT_NO,
            SUB_MERCHANT_NO,
            CHANNEL,
            MERCHANT_SCAN,
            USER_SCAN,
            MINI_PROGRAM,
            WECHAT_OFFIACCOUNT,
            APPROVAL_RESULT,
            FAILURE_REASON,
            APPLY_REASON,
            APPLY_TIME,
            CONFIG_STATUS,
            CONFIG_TIME,
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            SOURCE,
            CLEAN_STATUS,
            SUB_FIRST_MERCHANT_NO,
            SUB_SECOND_MERCHANT_NO,
            SUB_THREE_MERCHANT_NO,
            SUB_MERCHANT_NAME
        ) VALUES (
                     #{mainMerchantNo},
                     #{subMerchantNo},
                     #{channel},
                     #{merchantScan},
                     #{userScan},
                     #{miniProgram},
                     #{wechatOffiaccount},
                     #{approvalResult},
                     #{failureReason},
                     #{applyReason},
                     #{applyTime},
                     #{configStatus},
                     #{configTime},
                     #{createdTime},
                     #{lastModifiedTime},
                     #{source},
                     #{cleanStatus},
                    #{subFirstMerchantNo},
                    #{subSecondMerchantNo},
                    #{subThreeMerchantNo},
                    #{subMerchantName}
                 )
    </insert>
    <insert id="insertBatch">
        INSERT INTO TBL_ATTACH_RESULT (
        MAIN_MERCHANT_NO,
        SUB_MERCHANT_NO,
        CHANNEL,
        MERCHANT_SCAN,
        USER_SCAN,
        MINI_PROGRAM,
        WECHAT_OFFIACCOUNT,
        APPROVAL_RESULT,
        FAILURE_REASON,
        APPLY_REASON,
        APPLY_TIME,
        CONFIG_STATUS,
        CONFIG_TIME,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        SOURCE,
        CLEAN_STATUS,
        SUB_FIRST_MERCHANT_NO,
        SUB_SECOND_MERCHANT_NO,
        SUB_THREE_MERCHANT_NO,
        SUB_MERCHANT_NAME,
        ALIPAY_LIFE
        ) VALUES (
        #{mainMerchantNo},
        #{subMerchantNo},
        #{channel},
        #{merchantScan},
        #{userScan},
        #{miniProgram},
        #{wechatOffiaccount},
        #{approvalResult},
        #{failureReason},
        #{applyReason},
        #{applyTime},
        #{configStatus},
        #{configTime},
        #{createdTime},
        #{lastModifiedTime},
        #{source},
        #{cleanStatus},
        #{subFirstMerchantNo},
        #{subSecondMerchantNo},
        #{subThreeMerchantNo},
        #{subMerchantName},
        #{alipayLife}
        )
    </insert>

    <!-- 根据ID选择性更新 -->
    <update id="updateSelective">
        UPDATE TBL_ATTACH_RESULT
        <set>
            <if test="mainMerchantNo != null">MAIN_MERCHANT_NO = #{mainMerchantNo},</if>
            <if test="subMerchantNo != null">SUB_MERCHANT_NO = #{subMerchantNo},</if>
            <if test="channel != null">CHANNEL = #{channel},</if>
            <if test="merchantScan != null">MERCHANT_SCAN = #{merchantScan},</if>
            <if test="userScan != null">USER_SCAN = #{userScan},</if>
            <if test="miniProgram != null">MINI_PROGRAM = #{miniProgram},</if>
            <if test="wechatOffiaccount != null">WECHAT_OFFIACCOUNT = #{wechatOffiaccount},</if>
            <if test="approvalResult != null">APPROVAL_RESULT = #{approvalResult},</if>
            <if test="failureReason != null">FAILURE_REASON = #{failureReason},</if>
            <if test="applyReason != null">APPLY_REASON = #{applyReason},</if>
            <if test="applyTime != null">APPLY_TIME = #{applyTime},</if>
            <if test="configStatus != null">CONFIG_STATUS = #{configStatus},</if>
            <if test="configTime != null">CONFIG_TIME = #{configTime},</if>
            <if test="createdTime != null">CREATED_TIME = #{createdTime},</if>
            <if test="lastModifiedTime != null">LAST_MODIFIED_TIME = #{lastModifiedTime},</if>
            <if test="alipayLife != null">ALIPAY_LIFE = #{alipayLife},</if>
            <if test="cleanStatus != null">CLEAN_STATUS=#{cleanStatus},</if>
            <if test="subFirstMerchantNo != null">SUB_FIRST_MERCHANT_NO=#{subFirstMerchantNo},</if>
            <if test="subSecondMerchantNo != null">SUB_SECOND_MERCHANT_NO=#{subSecondMerchantNo},</if>
            <if test="subThreeMerchantNo != null">SUB_THREE_MERCHANT_NO=#{subThreeMerchantNo},</if>
            <if test="subMerchantName != null">SUB_MERCHANT_NAME=#{subMerchantName},</if>
        </set>
        WHERE MAIN_MERCHANT_NO = #{mainMerchantNo} AND SUB_MERCHANT_NO = #{subMerchantNo} and CLEAN_STATUS=#{oldClearStatus}
    </update>

    <update id="updateClearStatus">
        UPDATE TBL_ATTACH_RESULT
        set CLEAN_STATUS = #{dataCleanStatus}
        WHERE MAIN_MERCHANT_NO = #{merchantNo} AND CLEAN_STATUS = #{oldClearStatus}
    </update>
</mapper>
