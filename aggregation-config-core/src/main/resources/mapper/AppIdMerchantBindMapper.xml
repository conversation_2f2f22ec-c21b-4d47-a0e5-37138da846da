<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//MyBatis//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.g3.core.aggregation.config.dao.AppIdMerchantBindDao">

    <!-- ResultMap 定义，映射查询结果与实体类 -->
    <resultMap id="AppIdMerchantBindResultMap" type="com.yeepay.g3.core.aggregation.config.entity.AppIdMerchantBindEntity">
        <id property="id" column="ID"/>
        <result property="appId" column="APPID" />
        <result property="merchantNo" column="MERCHANT_NO"/>
        <result property="merchantName" column="MERCHANT_NAME" />
        <result property="firstMerchantNo" column="FIRST_MERCHANT_NO"/>
        <result property="secondMerchantNo" column="SECOND_MERCHANT_NO"/>
        <result property="threeMerchantNo" column="THREE_MERCHANT_NO"/>
        <result property="reportMerchantNo" column="REPORT_MERCHANT_NO"/>
        <result property="channelType" column="CHANNEL_TYPE"/>
        <result property="errorCode" column="ERROR_CODE"/>
        <result property="cleanStatus" column="CLEAN_STATUS"/>
        <result property="errorMsg" column="ERROR_MSG"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="bindStatus" column="BIND_STATUS" />
        <result property="operateTime" column="OPERATE_TIME" />
    </resultMap>

    <!-- 字段选择片段 -->
    <sql id="Base_Column_List">
        ID, APPID,MERCHANT_NO, REPORT_MERCHANT_NO, CHANNEL_TYPE,
        ERROR_CODE, CLEAN_STATUS, ERROR_MSG, CREATE_TIME, UPDATE_TIME,BIND_STATUS,OPERATE_TIME,MERCHANT_NAME,
        FIRST_MERCHANT_NO,SECOND_MERCHANT_NO,THREE_MERCHANT_NO
    </sql>

    <!-- 插入选择性数据 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        INSERT INTO TBL_APPID_MERCHANT_BIND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null" >APPID,</if>
            <if test="merchantNo != null">MERCHANT_NO,</if>
            <if test="merchantName != null">MERCHANT_NAME,</if>
            <if test="reportMerchantNo != null">REPORT_MERCHANT_NO,</if>
            <if test="channelType != null">CHANNEL_TYPE,</if>
            <if test="errorCode != null">ERROR_CODE,</if>
            <if test="cleanStatus != null">CLEAN_STATUS,</if>
            <if test="errorMsg != null">ERROR_MSG,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="bindStatus != null">BIND_STATUS,</if>
            <if test="operateTime != null">OPERATE_TIME,</if>
            <if test="firstMerchantNo != null">FIRST_MERCHANT_NO,</if>
            <if test="secondMerchantNo != null">SECOND_MERCHANT_NO,</if>
            <if test="threeMerchantNo != null">THREE_MERCHANT_NO,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="merchantNo != null">#{merchantNo},</if>
            <if test="merchantName != null">#{merchantName},</if>
            <if test="reportMerchantNo != null">#{reportMerchantNo},</if>
            <if test="channelType != null">#{channelType},</if>
            <if test="errorCode != null">#{errorCode},</if>
            <if test="cleanStatus != null">#{cleanStatus},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="bindStatus != null" >#{bindStatus},</if>
            <if test="operateTime != null">#{operateTime},</if>
            <if test="firstMerchantNo != null">#{firstMerchantNo},</if>
            <if test="secondMerchantNo != null">#{secondMerchantNo},</if>
            <if test="threeMerchantNo != null">#{threeMerchantNo},</if>
        </trim>
    </insert>

    <insert id="batchInsert">
        INSERT INTO TBL_APPID_MERCHANT_BIND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null" >APPID,</if>
            <if test="merchantNo != null">MERCHANT_NO,</if>
            <if test="merchantName != null">MERCHANT_NAME,</if>
            <if test="reportMerchantNo != null">REPORT_MERCHANT_NO,</if>
            <if test="channelType != null">CHANNEL_TYPE,</if>
            <if test="errorCode != null">ERROR_CODE,</if>
            <if test="cleanStatus != null">CLEAN_STATUS,</if>
            <if test="errorMsg != null">ERROR_MSG,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="bindStatus != null">BIND_STATUS,</if>
            <if test="operateTime != null">OPERATE_TIME,</if>
            <if test="firstMerchantNo != null">FIRST_MERCHANT_NO,</if>
            <if test="secondMerchantNo != null">SECOND_MERCHANT_NO,</if>
            <if test="threeMerchantNo != null">THREE_MERCHANT_NO,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="merchantNo != null">#{merchantNo},</if>
            <if test="merchantName != null">#{merchantName},</if>
            <if test="reportMerchantNo != null">#{reportMerchantNo},</if>
            <if test="channelType != null">#{channelType},</if>
            <if test="errorCode != null">#{errorCode},</if>
            <if test="cleanStatus != null">#{cleanStatus},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="bindStatus != null" >#{bindStatus},</if>
            <if test="operateTime != null">#{operateTime},</if>
            <if test="firstMerchantNo != null">#{firstMerchantNo},</if>
            <if test="secondMerchantNo != null">#{secondMerchantNo},</if>
            <if test="threeMerchantNo != null">#{threeMerchantNo},</if>
        </trim>
    </insert>


    <!-- 根据ID选择性更新 -->
    <update id="updateSelective">
        UPDATE TBL_APPID_MERCHANT_BIND
        <set>
            <if test="merchantName != null">MERCHANT_NAME=#{merchantName},</if>
            <if test="reportMerchantNo != null">REPORT_MERCHANT_NO = #{reportMerchantNo},</if>
            <if test="channelType != null">CHANNEL_TYPE = #{channelType},</if>
            <if test="errorCode != null">ERROR_CODE = #{errorCode},</if>
            <if test="cleanStatus != null">CLEAN_STATUS = #{cleanStatus},</if>
            <if test="errorMsg != null">ERROR_MSG = #{errorMsg},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="bindStatus != null">BIND_STATUS=#{bindStatus},</if>
            <if test="operateTime != null">OPERATE_TIME=#{operateTime},</if>
            <if test="firstMerchantNo != null">FIRST_MERCHANT_NO=#{firstMerchantNo},</if>
            <if test="secondMerchantNo != null">SECOND_MERCHANT_NO=#{secondMerchantNo},</if>
            <if test="threeMerchantNo != null">THREE_MERCHANT_NO=#{threeMerchantNo},</if>
        </set>
        WHERE  APPID = #{appId}
        and MERCHANT_NO = #{merchantNo}
    </update>

    <!-- 根据ID查询记录 -->
    <select id="queryById" resultMap="AppIdMerchantBindResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_MERCHANT_BIND
        WHERE ID = #{id}
    </select>

    <select id="queryByAppIdAndMerchantNo" resultMap="AppIdMerchantBindResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_APPID_MERCHANT_BIND
        WHERE APPID = #{appId}
        and MERCHANT_NO = #{merchantNo}
    </select>
    <select id="queryAppidByMerchantNo" resultMap="AppIdMerchantBindResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM TBL_APPID_MERCHANT_BIND
        WHERE MERCHANT_NO=#{merchantNo}
        AND CLEAN_STATUS=#{dataCleanStatus.name}
        AND CHANNEL_TYPE = #{channelType.name}
        AND BIND_STATUS = #{bindStatus}
    </select>

    <select id="queryBindByAppid" resultMap="AppIdMerchantBindResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM TBL_APPID_MERCHANT_BIND
        WHERE APPID=#{appid}
        AND MERCHANT_NO=#{merchantNo}
        AND CLEAN_STATUS =#{dataCleanStatus.name}
    </select>
</mapper>
