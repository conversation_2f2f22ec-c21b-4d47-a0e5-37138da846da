<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yeepay.g3.aggregation.config</groupId>
        <artifactId>aggregation-config-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>aggregation-config-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <springboot.repackage.skip>true</springboot.repackage.skip>
    </properties>

    <dependencies>
    
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yp-gm-crypt</artifactId>
            <version>*******</version>
        </dependency>
        <!--spring-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>

        <!--spring-->

        <dependency>
            <groupId>com.yeepay.g3.aggregation.config</groupId>
            <artifactId>aggregation-config-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.facade.aggregation</groupId>
            <artifactId>aggregation-pay-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.aggregation.config</groupId>
            <artifactId>aggregation-config-share-kernel</artifactId>
        </dependency>

        <!--配置中心-->
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-config</artifactId>
            <version>2.0</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.ng.yeeworks</groupId>
            <artifactId>yeeworks-config-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!--配置中心-->

        <!--纯工具-->
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-utils-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-thread-context</artifactId>
            <version>1.8</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>yeepay-infra-threadpool</artifactId>
            <version>2.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.17.0</version>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--纯工具-->

        <!--soa-->
        <dependency>
            <groupId>com.yeepay.g3.starter</groupId>
            <artifactId>yeepay-soa-starter</artifactId>
            <version>4.0.3.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>yeepay-soa</artifactId>
                    <groupId>com.yeepay.g3.utils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>database-plugin</artifactId>
                    <groupId>com.yeepay.infra</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.infra</groupId>
                    <artifactId>database-plugin </artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>tomcat-plugin</artifactId>
                    <groupId>com.yeepay.infra</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>yeepay-rmi</artifactId>
                    <groupId>com.yeepay.g3.utils</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>yeepay-soa</artifactId>
            <groupId>com.yeepay.g3.utils</groupId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.caucho</groupId>
                    <artifactId>hessian</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.g3.utils</groupId>
                    <artifactId>yeepay-utils-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-rmi</artifactId>
        </dependency>
        <!--soa-->

        <!--持久层-->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.15</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>2.1.2</version>
        </dependency>

        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <version>2.1.5</version>
        </dependency>
        <!--持久层-->

        <!--对象映射-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.6.3</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.6.3</version>
        </dependency>
        <!--对象映射-->

        <!--序列化-->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.13.5</version>
        </dependency>
        <!--序列化-->

        <dependency>
            <groupId>com.yeepay.g3.business.manage</groupId>
            <artifactId>business-manage-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.merchant</groupId>
            <artifactId>merchant-platform-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>5.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.trade</groupId>
            <artifactId>bankcooper-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>amqp-client</artifactId>
                    <groupId>com.rabbitmq</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.caucho</groupId>
                    <artifactId>hessian</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.channel.direct.conn</groupId>
            <artifactId>channel-direct-conn-facade</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.facade.aggregation</groupId>
            <artifactId>aggregation-pay-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--邮件通知-->
        <dependency>
            <groupId>com.yeepay.g3.facade.notifier</groupId>
            <artifactId>notifier-facade</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-notifier-facade</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-ca-facade</artifactId>
            <version>1.1</version>
        </dependency>
        <!--excel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.10</version>
        </dependency>
        <dependency>
            <groupId>object-storage</groupId>
            <artifactId>object-storage-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay</groupId>
            <artifactId>yeepay-scheduler-client-starter</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-lock</artifactId>
            <version>1.4</version>
        </dependency>
    </dependencies>
</project>