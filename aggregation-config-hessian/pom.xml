<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>com.yeepay.g3.aggregation.config</groupId>
        <artifactId>aggregation-config-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>aggregation-config-hessian</artifactId>
    <version>1.0-SNAPSHOT</version>

    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <springboot.mainClass>com.yeepay.g3.hessian.aggregation.config.SpringBootInitializer</springboot.mainClass>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.24</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>accesslog-valve</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>fluent-log4j2-appender</artifactId>
            <version>2.1.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>yeepay-log4j2-pattern</artifactId>
                    <groupId>com.yeepay.g3.utils</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>infra-log4j2</artifactId>
            <version>2.1.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.yeepay.g3.aggregation.config</groupId>
            <artifactId>aggregation-config-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>db2jcc4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>db2jcc_license_cu</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>aggregation-config-hessian</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <jvmArguments>
                        -server -Xmx2048m -Xms2048m -XX:MaxPermSize=512m
                        -Xdebug
                        -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8080
                    </jvmArguments>
                    <mainClass>com.yeepay.g3.hessian.aggregation.config.SpringBootInitializer</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
</project>