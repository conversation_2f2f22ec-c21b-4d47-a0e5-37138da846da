rocketmq.nameSrvAddr=rocketmqns.iaas.yp:30770;rocketmqns.iaas.yp:30771;rocketmqns.iaas.yp:30772
## appId_bind_start
rocketmq.appid.config.topic=channel_merchant_appid_config_topic
rocketmq.appid.config.group=channel_merchant_appid_config_biz_notify_group
rocketmq.appid.config.tag=biz_notify_tag
rocketmq.appid.config.thread-min=10
rocketmq.appid.config.thread-max=20
rocketmq.appid.config.pull-batch=1
rocketmq.appid.config.max-reconsume-times=16
## appId_bind_end

## attach_sync_start
rocketmq.attach.sync.topic=offline_pos_relyon_relation_send_topic
rocketmq.attach.sync.group=offline_pos_relyon_relation_send_biz_notify_group
rocketmq.attach.sync.tag=biz_notify_tag
rocketmq.attach.sync.thread-min=10
rocketmq.attach.sync.thread-max=20
rocketmq.attach.sync.pull-batch=1
rocketmq.attach.sync.max-reconsume-times=16
## attach_sync_end

## attach_sync_start
rocketmq.appid.control.topic=channel_merchant_appid_control_topic
rocketmq.appid.control.group=channel_merchantAppIdConfigProducerGroup_biz_notify
rocketmq.appid.control.tag=biz_notify_tag
rocketmq.appid.control.thread-min=10
rocketmq.appid.control.thread-max=20
rocketmq.appid.control.pull-batch=1
rocketmq.appid.control.max-reconsume-times=16
## attach_sync_end

## channel_no_apply_start
rocketmq.channel.no.apply.result.topic=channelApplyRecordResult
rocketmq.channel.no.apply.result.group=channelApplyRecordResultConsumer
rocketmq.channel.no.apply.result.thread-min=10
rocketmq.channel.no.apply.result.thread-max=20
rocketmq.channel.no.apply.result.pull-batch=1
rocketmq.channel.no.apply.result.max-reconsume-times=4
## channel_no_apply_end
## channel_no_apply_start
rocketmq.channel.entry.result.topic=addOpenPayReportResult
rocketmq.channel.entry.result.group=addOpenPayReportResultConsumer
rocketmq.channel.entry.result.thread-min=10
rocketmq.channel.entry.result.thread-max=20
rocketmq.channel.entry.result.pull-batch=1
rocketmq.channel.entry.result.max-reconsume-times=4
## channel_no_apply_end