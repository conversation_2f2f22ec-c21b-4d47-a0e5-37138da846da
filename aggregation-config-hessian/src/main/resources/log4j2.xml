<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.yeepay.g3.utils"><!--packages参数告诉log4j2还需要额外加载哪个包下的Log4j plugin，其中YeepayMessagePatternConverter即为定制的plugin,负责输出的日志带GUID -->

    <Appenders>
        <FluentAppender name="fluent-app" label="" host="app.logsync.yp" port="24324" mode="remote"> <!--
         如果上容器或者新的有appname构建方式，label项无效 -->
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} - %c -%-4r [%t] %-5p %x - %Y%n%throwable"/>
        </FluentAppender>

        <Console name="STDOUT" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %-5p %c:%L [%t] - %m%n"/>
            <!--<PatternLayout pattern="%d %-5p %c [%t] - %MARKETING%n" />-->
        </Console>
    </Appenders>

    <Loggers>
        <Logger name="com.yeepay.g3.core.aggregation.config" level="DEBUG" />
        <Logger name="org.hibernate.validator" level="ERROR"/>
        <Logger name="org.apache" level="ERROR"/>
        <Logger name="org.springframework" level="INFO"/>
        <Logger name="org.apache.commons.httpclient" level="INFO"/>
        <Logger name="org.apache.dubbo" level="ERROR"/>
        <Logger name="com.yeepay.g3.utils" level="WARN"/>
        <Logger name="com.alibaba.druid" level="WARN"/>

        <Root level="INFO">
            <!-- 缺省日志级别，如果package有定制级别，则按package的定制级别走，即使package级别更低 -->
            <!--<AppenderRef ref="STDOUT" />-->
            <AppenderRef ref="fluent-app"/>
        </Root>

    </Loggers>
</Configuration>
