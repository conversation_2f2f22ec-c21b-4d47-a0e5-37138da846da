package com.yeepay.g3.hessian.aggregation.config;

import com.yeepay.g3.core.aggregation.config.common.mybatis.ShadowTableInterceptor;
import com.yeepay.g3.core.aggregation.config.common.mybatis.SqlPrintInterceptor;
import com.yeepay.g3.core.aggregation.config.utils.ConfigUtil;
import com.yeepay.g3.core.aggregation.config.utils.LocalDateTimeTypeHandler;
import com.yeepay.g3.utils.common.datasource.DataSourceFactoryBean;
import com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSourceFactory;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 数据库配置
 * @author: xuchen.liu
 * @date: 2024-12-13 11:09
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@MapperScan(basePackages = "com.yeepay.g3.core.aggregation.config.dao", sqlSessionFactoryRef = "sqlSessionFactory")
@Configuration
public class DataSourceConfig {

    @Bean(name = "dataSource")
    public DataSource dataSourceDB2() throws Exception {
        DataSourceFactoryBean dataSourceFactoryBean = new DataSourceFactoryBean();
        dataSourceFactoryBean.setName("AGGREGATE_DB2");
        dataSourceFactoryBean.setPooledDataSourceFactory(new DruidPooledDataSourceFactory());
        return (DataSource) dataSourceFactoryBean.getObject();
    }

    @Bean("sqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("dataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        // 设置数据源
        sqlSessionFactoryBean.setDataSource(dataSource);
        //加载mybatis插件
        List<Interceptor> interceptors = new ArrayList<Interceptor>();
        interceptors.add(new SqlPrintInterceptor());
        Map<String, String> shadowTableConfig = ConfigUtil.getShadowTableConfig();
        if (!shadowTableConfig.isEmpty()) {
            //如果统一配置中没有配置需要使用的影子表，则默认不会开始这个插件
            interceptors.add(new ShadowTableInterceptor());
        }
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setPlugins(interceptors.toArray(new Interceptor[]{}));
        // 设置别名包
        sqlSessionFactoryBean.setTypeAliasesPackage("com.yeepay.g3.core.aggregation.config.dao");
        //就是这句代码，只能指定单个mapper.xml文件，加通配符的话找不到文件
        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath:mapper/**/*.xml"));
        // 注册自定义 TypeHandler
        sqlSessionFactoryBean.setTypeHandlers(new LocalDateTimeTypeHandler());
        return sqlSessionFactoryBean.getObject();
    }

    @Bean
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("sqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean
    public PlatformTransactionManager transactionManager(@Qualifier("dataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public TransactionTemplate transactionTemplate(@Qualifier(value = "transactionManager")PlatformTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }

    @Bean(value = "dataSourceJdbcTemplate")
    public JdbcTemplate dataSourceJdbcTemplate(@Qualifier(value = "dataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
