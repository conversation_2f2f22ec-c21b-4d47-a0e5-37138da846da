package com.yeepay.g3.hessian.aggregation.config;

import com.yeepay.g3.utils.soa.remoting.http.servlet.HttpURLClassLoaderServlet;

import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName: ServletConfig
 * @date 2020/9/8
 */
@Configuration
public class ServletConfig {

    @Bean
    public HttpURLClassLoaderServlet getHttpURLClassLoaderServlet() {
        return new HttpURLClassLoaderServlet();
    }

    @Bean
    public ServletRegistrationBean testServletRegistrationBean(HttpURLClassLoaderServlet httpURLClassLoaderServlet) {
        ServletRegistrationBean registration = new ServletRegistrationBean(httpURLClassLoaderServlet);
        registration.setEnabled(true);
        registration.addUrlMappings("/class/*");
        return registration;
    }
}
