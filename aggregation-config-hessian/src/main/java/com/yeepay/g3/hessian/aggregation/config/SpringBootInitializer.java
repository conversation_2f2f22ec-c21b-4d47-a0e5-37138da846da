package com.yeepay.g3.hessian.aggregation.config;

import com.yeepay.g3.utils.config.ConfigurationUtils;
import com.yeepay.g3.utils.gmcrypt.utils.SMUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.smartcache.utils.SmartCacheUtils;
import com.yeepay.ng.yeeworks.config.YeeworksConfigUtils;
import com.yeepay.springframework.boot.annotation.EnableSoa;
import com.yeepay.utils.lock.utils.RedisClientUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @description: 启动类
 * @author: xuchen.liu
 * @date: 2024-12-13 11:08
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@EnableSoa
@ComponentScan(basePackages = {
        "com.yeepay.g3.facade.aggregation.config.*",
        "com.yeepay.g3.core.aggregation.config.*",
        "com.yeepay.g3.hessian.aggregation.config",
})
@EnableTransactionManagement
@Import({DataSourceConfig.class})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, RedisAutoConfiguration.class})
public class SpringBootInitializer extends SpringBootServletInitializer {

    static {
        ConfigurationUtils.init();
        RemoteServiceFactory.init();
        YeeworksConfigUtils.init();
        SmartCacheUtils.init();
        RedisClientUtils.init();
        SMUtils.init();
    }

    public static void main(String[] args) throws Exception {
        SpringApplication.run(SpringBootInitializer.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(SpringBootInitializer.class);
    }
}
