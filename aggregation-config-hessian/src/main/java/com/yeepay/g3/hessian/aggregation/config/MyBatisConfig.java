package com.yeepay.g3.hessian.aggregation.config;

import com.yeepay.g3.core.aggregation.config.utils.LocalDateTimeTypeHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tk.mybatis.mapper.autoconfigure.ConfigurationCustomizer;

/**
 * @author: Mr.yin
 * @date: 2025/6/12  20:29
 */
@Configuration
public class MyBatisConfig {
    @Bean
    public ConfigurationCustomizer mybatisConfigurationCustomizer() {
        return configuration -> {
            configuration.getTypeHandlerRegistry().register(LocalDateTimeTypeHandler.class);
        };
    }


}
