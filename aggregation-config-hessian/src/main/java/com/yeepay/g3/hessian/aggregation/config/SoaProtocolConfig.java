package com.yeepay.g3.hessian.aggregation.config;

import org.apache.dubbo.config.ProtocolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: SoaProtocolConfig
 * @date 2023/2/16
 */

@Configuration
public class SoaProtocolConfig {

    /**
     * dubbo3升级支持长链接协议triple
     * @return
     */
    @Bean
    public ProtocolConfig trix() {
        ProtocolConfig protocolConfig = new ProtocolConfig("trix");
        protocolConfig.setPort(8089);
        protocolConfig.setDispatcher("message");
        protocolConfig.setThreadpool("eager");
        protocolConfig.setQueues(0);
        protocolConfig.setCorethreads(10);
        protocolConfig.setThreads(200);
        return protocolConfig;
    }

}
