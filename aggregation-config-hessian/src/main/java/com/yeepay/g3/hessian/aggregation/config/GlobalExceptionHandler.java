package com.yeepay.g3.hessian.aggregation.config;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import com.yeepay.aggregation.config.share.kernel.model.Result;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.ConcurrentModificationException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@ResponseBody
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error ->
                errors.put(error.getField(), error.getDefaultMessage())
        );

        log.warn("参数校验错误: {}", errors);
        return Result.fail(ResultCode.PARAM_VALID_ERROR, JsonUtils.toJSONString(errors));
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public Result<?> handleBindException(BindException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error ->
                errors.put(error.getField(), error.getDefaultMessage())
        );
        log.warn("参数绑定错误: {}", errors);
        return Result.fail(ResultCode.PARAM_BIND_ERROR, JsonUtils.toJSONString(errors));
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public Result<?> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.warn("参数校验错误: {}", ex.getMessage());
        return Result.fail(ResultCode.PARAM_VALID_ERROR, ex.getMessage());
    }
    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        log.warn("不支持当前请求方法: {}", ex.getMessage());
        return Result.fail(ResultCode.METHOD_NOT_ALLOWED);
    }

    /**
     * 处理资源未找到异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public Result<?> handleNoHandlerFoundException(NoHandlerFoundException ex) {
        log.warn("访问资源不存在: {}", ex.getMessage());
        return Result.fail(ResultCode.NOT_FOUND);
    }

    /**
     * 处理数据库异常
     */
    @ExceptionHandler(DataAccessException.class)
    public Result<?> handleDataAccessException(DataAccessException ex) {
        log.error("数据库访问异常", ex);
        return Result.fail(ResultCode.SERVER_ERROR, "数据库访问异常");
    }

    /**
     * 处理并发异常
     */
    @ExceptionHandler(ConcurrentModificationException.class)
    public Result<?> handleConcurrentModificationException(ConcurrentModificationException ex) {
        log.error("并发访问异常", ex);
        return Result.fail(ResultCode.SERVER_ERROR, "系统繁忙，请稍后重试");
    }

    /**
     * 处理JSON解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Result<?> handleHttpMessageNotReadableException(
            HttpMessageNotReadableException ex) {
        log.warn("JSON解析错误: {}", ex.getMessage());
        return Result.fail(ResultCode.JSON_PARSE_ERROR, "JSON格式错误");
    }


    /**
     * 处理未知异常
     */
    @ExceptionHandler(Throwable.class)
    public Result<?> handleException(Throwable ex) {
        log.error("系统未知异常", ex);
        return Result.fail(ResultCode.SERVER_ERROR, "系统繁忙，请稍后重试");
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException ex) {
        log.warn("业务异常: {}", ex.getMessage());
        return Result.fail(ex.getResponseEnum().getCode(), ex.getMessage());
    }
}
