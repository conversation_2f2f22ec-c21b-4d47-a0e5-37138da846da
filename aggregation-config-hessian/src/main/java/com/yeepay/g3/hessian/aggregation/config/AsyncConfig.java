package com.yeepay.g3.hessian.aggregation.config;

import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
@EnableAsync
@PropertySource("classpath:runtimecfg/async-config.properties")
public class AsyncConfig implements AsyncConfigurer {

    @Value(value = "${async.aggregation.config.corePoolSize}")
    private Integer corePoolSize;

    @Value(value = "${async.aggregation.config.maximumPoolSize}")
    private Integer maximumPoolSize;

    @Value(value = "${async.aggregation.config.blockingDequeCapacity}")
    private Integer blockingDequeCapacity;

    @Value(value = "${async.aggregation.config.keepAliveTime}")
    private Integer keepAliveTime;

    @Override
    @Bean("asyncExecutor")
    @Primary
    public AtlasThreadPoolExecutor getAsyncExecutor() {
        return new AtlasThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(blockingDequeCapacity),
                new NamedThreadFactory("aggregation-config-async"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }


    @Bean("anchoredHandleExecutor")
    public AtlasThreadPoolExecutor getAnchoredHandleExecutor() {
        return new AtlasThreadPoolExecutor(
                3,
                20,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(5000),
                new NamedThreadFactory("aggregation-config-anchored-async"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean("entryHandleExecutor")
    public AtlasThreadPoolExecutor getEntryHandleExecutor() {
        return new AtlasThreadPoolExecutor(
                10,
                20,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(1000),
                new NamedThreadFactory("aggregation-config-entry-async"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}

class NamedThreadFactory implements ThreadFactory {

    private final ThreadGroup threadGroup;

    private final AtomicInteger threadNumber = new AtomicInteger(1);

    public final String namePrefix;

    NamedThreadFactory(String name) {
        SecurityManager s = System.getSecurityManager();
        threadGroup = (s != null) ? s.getThreadGroup() :
                Thread.currentThread().getThreadGroup();
        if (null == name || name.trim().isEmpty()) {
            name = "pool";
        }
        AtomicInteger poolNumber = new AtomicInteger(1);
        namePrefix = name + "-" +
                poolNumber.getAndIncrement() +
                "-thread-";
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread t = new Thread(threadGroup, r,
                namePrefix + threadNumber.getAndIncrement(),
                0);
        if (t.isDaemon()) {
            t.setDaemon(false);
        }
        if (t.getPriority() != Thread.NORM_PRIORITY) {
            t.setPriority(Thread.NORM_PRIORITY);
        }
        return t;
    }
}
