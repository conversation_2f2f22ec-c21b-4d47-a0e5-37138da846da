package com.yeepay.g3.hessian.aggregation.config;

import com.yeepay.aggregation.config.share.kernel.constant.RocketMqConst;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.core.aggregation.config.mq.ChannelAppIdBindListener;
import com.yeepay.g3.core.aggregation.config.mq.ChannelAppidControlListener;
import com.yeepay.g3.core.aggregation.config.mq.ChannelAttachListener;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppIdBindEvent;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppidControlEvent;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAttachEvent;
import com.yeepay.g3.core.aggregation.config.mq.listener.ChannelEntryResultListener;
import com.yeepay.g3.core.aggregation.config.mq.listener.ChannelNoApplyResultListener;
import com.yeepay.g3.core.aggregation.config.mq.listener.DivideReceiverCallBackListener;
import com.yeepay.g3.core.aggregation.config.mq.listener.EntryAnchoredCallBackListener;
import com.yeepay.g3.core.aggregation.config.mq.listener.TerminalReportCallBackListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * @description: 消息队列配置
 * @author: xuchen.liu
 * @date: 2025-01-02 13:19
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@Slf4j
@Component
@PropertySource(value = {"classpath:/runtimecfg/rocketmq-conf.properties"})
public class RocketMqConfig {

    @Value("${rocketmq.nameSrvAddr}")
    private String nameServerAddress;

    @Value("${rocketmq.appid.config.topic}")
    private String appIdConfigTopic;

    @Value("${rocketmq.appid.config.group}")
    private String appIdConfigGroup;

    @Value("${rocketmq.appid.config.tag}")
    private String appIdConfigTag;

    @Value("${rocketmq.appid.config.thread-min}")
    private Integer appIdConfigThreadMin;


    @Value("${rocketmq.appid.config.thread-max}")
    private Integer appIdConfigThreadMax;

    @Value("${rocketmq.appid.config.pull-batch}")
    private Integer appIdConfigPullBatch;

    @Value("${rocketmq.appid.config.max-reconsume-times}")
    private Integer appIdConfigMaxReconsumeTimes;

    @Bean(value = "aggConfigAnchoredProducer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQProducer aggConfigAnchoredProducer() {
        return new DefaultMQProducer("agg_config_anchored_producer") {{
            setNamesrvAddr(nameServerAddress);
        }};
    }

    @Bean(value = "appIdBindStart")
    public void appIdBindStart() throws MQClientException {
        DefaultMQPushConsumer defaultMQPushConsumer = new DefaultMQPushConsumer();
        defaultMQPushConsumer.setNamesrvAddr(nameServerAddress);
        defaultMQPushConsumer.subscribe(appIdConfigTopic, appIdConfigTag);
        defaultMQPushConsumer.setConsumerGroup(appIdConfigGroup);
        defaultMQPushConsumer.setConsumeThreadMin(appIdConfigThreadMin);
        defaultMQPushConsumer.setConsumeThreadMax(appIdConfigThreadMax);
        defaultMQPushConsumer.setPullBatchSize(appIdConfigPullBatch);
        defaultMQPushConsumer.setMaxReconsumeTimes(appIdConfigMaxReconsumeTimes);
        ChannelAppIdBindListener channelAppIdBindListener = new ChannelAppIdBindListener(ChannelAppIdBindEvent.class);
        channelAppIdBindListener.setMaxRetryTimes(appIdConfigMaxReconsumeTimes);
        defaultMQPushConsumer.registerMessageListener(channelAppIdBindListener);
        defaultMQPushConsumer.start();
        log.info("启动 topic: {} group : {}  消费者监听 ",appIdConfigTopic,defaultMQPushConsumer.getConsumerGroup());
    }

    @Value("${rocketmq.attach.sync.topic}")
    private String attachSyncTopic;

    @Value("${rocketmq.attach.sync.group}")
    private String attachSyncGroup;

    @Value("${rocketmq.attach.sync.tag}")
    private String attachSyncTag;

    @Value("${rocketmq.attach.sync.thread-min}")
    private Integer attachSyncThreadMin;


    @Value("${rocketmq.attach.sync.thread-max}")
    private Integer attachSyncThreadMax;


    @Value("${rocketmq.attach.sync.pull-batch}")
    private Integer attachSyncPullBatch;

    @Value("${rocketmq.attach.sync.max-reconsume-times}")
    private Integer attachSyncMaxReconsumeTimes;

    @Bean(value = "attachStart")
    public void attachStart() throws MQClientException {
        DefaultMQPushConsumer defaultMQPushConsumer = new DefaultMQPushConsumer();
        defaultMQPushConsumer.setNamesrvAddr(nameServerAddress);
        defaultMQPushConsumer.setMaxReconsumeTimes(10);
        defaultMQPushConsumer.subscribe(attachSyncTopic, attachSyncTag);
        defaultMQPushConsumer.setConsumerGroup(attachSyncGroup);
        defaultMQPushConsumer.setConsumeThreadMin(attachSyncThreadMin);
        defaultMQPushConsumer.setConsumeThreadMax(attachSyncThreadMax);
        defaultMQPushConsumer.setPullBatchSize(attachSyncPullBatch);
        defaultMQPushConsumer.setMaxReconsumeTimes(attachSyncMaxReconsumeTimes);
        ChannelAttachListener channelAttachListener = new ChannelAttachListener(ChannelAttachEvent.class);
        channelAttachListener.setMaxRetryTimes(attachSyncMaxReconsumeTimes);
        defaultMQPushConsumer.registerMessageListener(channelAttachListener);
        defaultMQPushConsumer.start();
        log.info("启动 topic: {} group : {}  消费者监听 ",attachSyncTopic,defaultMQPushConsumer.getConsumerGroup());
    }

    @Value("${rocketmq.appid.control.topic}")
    private String appIdControlTopic;

    @Value("${rocketmq.appid.control.group}")
    private String appIdControlGroup;

    @Value("${rocketmq.appid.control.tag}")
    private String appIdControlTag;

    @Value("${rocketmq.appid.control.thread-min}")
    private Integer appIdControlThreadMin;

    @Value("${rocketmq.appid.control.thread-max}")
    private Integer appIdControlThreadMax;

    @Value("${rocketmq.appid.control.pull-batch}")
    private Integer appIdControlPullBatch;

    @Value("${rocketmq.appid.control.max-reconsume-times}")
    private Integer appIdControlMaxReconsumeTimes;

    @Bean(value = "appIdControlStart")
    public void appIdControlStart() throws MQClientException {
        DefaultMQPushConsumer defaultMQPushConsumer = new DefaultMQPushConsumer();
        defaultMQPushConsumer.setNamesrvAddr(nameServerAddress);
        defaultMQPushConsumer.subscribe(appIdControlTopic, appIdControlTag);
        defaultMQPushConsumer.setConsumerGroup(appIdControlGroup);
        defaultMQPushConsumer.setConsumeThreadMin(appIdControlThreadMin);
        defaultMQPushConsumer.setConsumeThreadMax(appIdControlThreadMax);
        defaultMQPushConsumer.setPullBatchSize(appIdControlPullBatch);
        defaultMQPushConsumer.setMaxReconsumeTimes(appIdControlMaxReconsumeTimes);
        ChannelAppidControlListener channelAppidControlListener = new ChannelAppidControlListener(ChannelAppidControlEvent.class);
        channelAppidControlListener.setMaxRetryTimes(appIdControlMaxReconsumeTimes);
        defaultMQPushConsumer.registerMessageListener(channelAppidControlListener);
        defaultMQPushConsumer.start();
        log.info("启动 topic: {} group : {}  消费者监听 ",appIdControlTopic,defaultMQPushConsumer.getConsumerGroup());
    }

    /**
     * @Description 分账方-通道结果回调
     */
    @Bean(value = "divideReceiverMessageConsumer")
    public DefaultMQPushConsumer divideReceiverMessageConsumer(@Qualifier("divideReceiverCallBackListener") DivideReceiverCallBackListener divideReceiverCallBackListener) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(RocketMqConst.CHANNEL_DIVIDE_CREATE_RESULT_GROUP);
        consumer.setNamesrvAddr(nameServerAddress);
        consumer.setMaxReconsumeTimes(10);
        consumer.setConsumeThreadMin(3);
        consumer.setConsumeThreadMax(3);
        consumer.subscribe(RocketMqConst.CHANNEL_DIVIDE_CREATE_RESULT_TOPIC, "*");
        consumer.registerMessageListener(divideReceiverCallBackListener);
        consumer.start();
        log.info("分账方 启动 topic: {} group : {}  消费者监听 ", RocketMqConst.CHANNEL_DIVIDE_CREATE_RESULT_TOPIC, consumer.getConsumerGroup());
        return consumer;
    }


    /**
     * @Description 终端报备-通道结果回调
     */
    @Bean(value = "terminalReportConsumer")
    public DefaultMQPushConsumer terminalReportConsumer(@Qualifier("terminalReportCallBackListener") TerminalReportCallBackListener terminalReportCallBackListener) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(RocketMqConst.CHANNEL_TERMINAL_REPORT_GROUP);
        consumer.setNamesrvAddr(nameServerAddress);
        consumer.setMaxReconsumeTimes(10);
        consumer.setConsumeThreadMin(3);
        consumer.setConsumeThreadMax(3);
        consumer.subscribe(RocketMqConst.CHANNEL_TERMINAL_REPORT_TOPIC, "*");
        consumer.registerMessageListener(terminalReportCallBackListener);
        consumer.start();
        log.info("终端报备 启动 topic: {} group : {}  消费者监听 ", RocketMqConst.CHANNEL_TERMINAL_REPORT_TOPIC, consumer.getConsumerGroup());
        return consumer;
    }

    /**
     * @Description 进件结果
     */
    @Bean(value = "entryCallBackConsumer")
    public DefaultMQPushConsumer entryCallBackConsumer(@Qualifier("channelEntryResultListener") ChannelEntryResultListener channelEntryResultListener) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(RocketMqConst.CHANNEL_ENTRY_RESULT_GROUP);
        consumer.setNamesrvAddr(nameServerAddress);
        consumer.setMaxReconsumeTimes(10);
        consumer.setConsumeThreadMin(3);
        consumer.setConsumeThreadMax(3);
        consumer.subscribe(RocketMqConst.CHANNEL_ENTRY_RESULT_TOPIC, "*");
        consumer.registerMessageListener(channelEntryResultListener);
        consumer.start();
        log.info("进件结果 启动 topic: {} group : {}  消费者监听 ", RocketMqConst.CHANNEL_ENTRY_RESULT_TOPIC, consumer.getConsumerGroup());
        return consumer;
    }

    @Bean(value = "channelNoApplyResult")
    public DefaultMQPushConsumer channelNoApplyResult(@Qualifier("channelNoApplyResultListener") ChannelNoApplyResultListener channelNoApplyResultListener) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(RocketMqConst.CHANNEL_NO_APPLY_RESULT_GROUP);
        consumer.setNamesrvAddr(nameServerAddress);
        consumer.setMaxReconsumeTimes(10);
        consumer.setConsumeThreadMin(3);
        consumer.setConsumeThreadMax(3);
        consumer.subscribe(RocketMqConst.CHANNEL_NO_APPLY_RESULT_TOPIC, "*");
        consumer.registerMessageListener(channelNoApplyResultListener);
        consumer.start();
        log.info("渠道号申请 启动 topic: {} group : {}  消费者监听 ", RocketMqConst.CHANNEL_NO_APPLY_RESULT_TOPIC, consumer.getConsumerGroup());
        return consumer;
    }

    /**
     * @Description 进件模块的 监听挂靠消息
     */
    @Bean(value = "entryAnchoredCallBackConsumer")
    public DefaultMQPushConsumer entryAnchoredCallBackConsumer(@Qualifier("entryAnchoredCallBackListener") EntryAnchoredCallBackListener entryAnchoredCallBackListener) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(RocketMqConst.ENTRY_ANCHORED_RESULT_GROUP);
        consumer.setNamesrvAddr(nameServerAddress);
        consumer.setMaxReconsumeTimes(10);
        consumer.setConsumeThreadMin(3);
        consumer.setConsumeThreadMax(3);
        consumer.subscribe(RocketMqConst.ANCHORED_RESULT_TOPIC, BizSceneEnum.AGG_ENTRY.getDocument());
        consumer.registerMessageListener(entryAnchoredCallBackListener);
        consumer.start();
        log.info("终端报备 启动 topic: {} group : {}  消费者监听 ", RocketMqConst.ANCHORED_RESULT_TOPIC, consumer.getConsumerGroup());
        return consumer;
    }

}
