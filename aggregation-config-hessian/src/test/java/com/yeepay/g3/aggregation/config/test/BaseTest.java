package com.yeepay.g3.aggregation.config.test;

import com.yeepay.g3.utils.config.ConfigurationUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.soa.context.ApplicationContextHelper;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootInitializerTest.class)
public class BaseTest {

    static {

        ApplicationContextHelper.initApplicationInfo("aggregation-config-hessian", null);
        System.setProperty("dubbo.application.name", "aggregation-config-hessian");
        System.setProperty("dubbo.registry.address", "zookeeperx://zk.bass.3g:2181?file.cache=false");
        System.setProperty("dongtai.app.name", "aggregation-config-hessian");
        System.setProperty("java.util.logging.manager", "org.apache.juli.ClassLoaderLogManager");
        System.setProperty("appname", "aggregation-config-hessian");
        System.setProperty("datacenter", "QA");
        System.setProperty("deploy_env", "product");
        System.setProperty("dubbo.application.environment", "product");
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        System.setProperty("workdir", "/apps/data/tomcat-work");
        System.setProperty("YP_DEPLOY_ENV", "product");
        System.setProperty("YP_DATA_CENTER", "QA");
        System.setProperty("DUBBO_APPLICATION_ENVIRONMENT", "product");
        System.setProperty("YP_APP_NAME", "aggregation-config-hessian");
        System.setProperty("npo", "");
        ConfigurationUtils.init();
        RemoteServiceFactory.init();
    }
}