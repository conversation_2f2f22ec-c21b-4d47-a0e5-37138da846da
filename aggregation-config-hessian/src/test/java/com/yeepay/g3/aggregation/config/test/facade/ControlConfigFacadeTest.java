package com.yeepay.g3.aggregation.config.test.facade;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.entity.ControlConfigEntity;
import com.yeepay.g3.core.aggregation.config.service.ControlConfigService;
import com.yeepay.g3.facade.aggregation.config.dto.ControlConfigRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.ControlConfigResponseDTO;
import com.yeepay.g3.facade.aggregation.config.enums.*;
import com.yeepay.g3.facade.aggregation.config.facade.ControlConfigFacade;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-13 15:22
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class ControlConfigFacadeTest extends BaseTest {

    @Autowired
    private ControlConfigFacade controlConfigFacade;

    @Autowired
    private ControlConfigService controlConfigService;

    @Transactional(rollbackFor = Throwable.class, transactionManager = "transactionManager")
    @Rollback
    @Test
    public void testConfigManage() {
        ControlConfigRequestDTO controlConfigIRequestDTO = new ControlConfigRequestDTO();
        controlConfigIRequestDTO.setMerchantNo("***********");
        controlConfigIRequestDTO.setBusinessType("APPID_NOTIFY");
        controlConfigIRequestDTO.setControlNotifyWay(ControlNotifyWayEnum.EMAIL_AND_CAL_BACK);
        controlConfigIRequestDTO.setIsLower(AccessEnum.Y);
        controlConfigIRequestDTO.setEmailAddress("<EMAIL>");
        controlConfigIRequestDTO.setCcAddress("<EMAIL>");
        controlConfigIRequestDTO.setCallBackUrl("https://qalog.yeepay.com");
        controlConfigIRequestDTO.setAppKey("KEY_***********");
        controlConfigIRequestDTO.setOperator("xuchen.liu");
        controlConfigIRequestDTO.setRemark("");
        controlConfigIRequestDTO.setStatus(ControlStatusEnum.ACTIVE);
        controlConfigIRequestDTO.setOperateType(OperateTypeEnum.CREATE);
        ControlConfigResponseDTO controlConfigIResponseDTO = controlConfigFacade.configManage(controlConfigIRequestDTO);
        assertNotNull(controlConfigIResponseDTO);
        Assertions.assertEquals(controlConfigIResponseDTO.getCode(), ErrorCodeEnum.SUCCESS.getCode());
        Assertions.assertEquals(controlConfigIResponseDTO.getMessage(), ErrorCodeEnum.SUCCESS.getDesc());
        assertNotNull(controlConfigIResponseDTO.getId());

        // **First query validation (after insert)**: Check that values in the database match the DTO
        ControlConfigEntity savedConfig = controlConfigService.queryById(controlConfigIResponseDTO.getId());
        assertNotNull(savedConfig);
        Assertions.assertEquals(controlConfigIResponseDTO.getId(), savedConfig.getId());
        Assertions.assertEquals(controlConfigIRequestDTO.getMerchantNo(), savedConfig.getMerchantNo());
        Assertions.assertEquals(controlConfigIRequestDTO.getBusinessType(), savedConfig.getBusinessType());
        Assertions.assertEquals(controlConfigIRequestDTO.getControlNotifyWay(), savedConfig.getControlNotifyWay());
        Assertions.assertEquals(controlConfigIRequestDTO.getIsLower(), savedConfig.getIsLower());
        Assertions.assertEquals(controlConfigIRequestDTO.getEmailAddress(), String.join(",",savedConfig.getEmailAddress()));
        Assertions.assertEquals(controlConfigIRequestDTO.getCcAddress(), String.join(",",savedConfig.getCcAddress()));
        Assertions.assertEquals(controlConfigIRequestDTO.getCallBackUrl(), savedConfig.getCallBackUrl());
        Assertions.assertEquals(controlConfigIRequestDTO.getAppKey(), savedConfig.getAppKey());
        Assertions. assertEquals(controlConfigIRequestDTO.getOperator(), savedConfig.getOperator());
        Assertions.assertEquals(controlConfigIRequestDTO.getRemark(), savedConfig.getRemark());
        Assertions.assertEquals(controlConfigIRequestDTO.getStatus(), savedConfig.getStatus());


        //进行修改逻辑
        controlConfigIRequestDTO.setOperateType(OperateTypeEnum.UPDATE);
        controlConfigIRequestDTO.setId(controlConfigIResponseDTO.getId());
        controlConfigIRequestDTO.setStatus(ControlStatusEnum.CLOSED);
        ControlConfigResponseDTO controlConfigIResponseDTO_update = controlConfigFacade.configManage(controlConfigIRequestDTO);
        assertNotNull(controlConfigIResponseDTO_update);
        Assertions.assertEquals(controlConfigIResponseDTO_update.getCode(), ErrorCodeEnum.SUCCESS.getCode());
        Assertions.assertEquals(controlConfigIResponseDTO_update.getMessage(), ErrorCodeEnum.SUCCESS.getDesc());
        //比较id不相等
        Assertions.assertNotEquals(controlConfigIResponseDTO.getId(),controlConfigIResponseDTO_update.getId());

        // **Second query validation (after update)**: Check that the updated values are in the database
        ControlConfigEntity updatedConfig = controlConfigService.queryById(controlConfigIResponseDTO_update.getId());
        assertNotNull(updatedConfig);
        Assertions.assertEquals(ControlStatusEnum.CLOSED, updatedConfig.getStatus());
        Assertions.assertEquals(controlConfigIRequestDTO.getMerchantNo(), updatedConfig.getMerchantNo());
        Assertions.assertEquals("APPID_NOTIFY", updatedConfig.getBusinessType());  // Business type should remain the same unless specifically changed
        Assertions.assertEquals(controlConfigIRequestDTO.getControlNotifyWay(), updatedConfig.getControlNotifyWay());
        Assertions.assertEquals(controlConfigIRequestDTO.getIsLower(), updatedConfig.getIsLower());
        Assertions.assertEquals(controlConfigIRequestDTO.getEmailAddress(), String.join(",",savedConfig.getEmailAddress()));
        Assertions.assertEquals(controlConfigIRequestDTO.getCcAddress(), String.join(",",savedConfig.getCcAddress()));
        Assertions.assertEquals(controlConfigIRequestDTO.getCallBackUrl(), updatedConfig.getCallBackUrl());
        Assertions.assertEquals(controlConfigIRequestDTO.getAppKey(), updatedConfig.getAppKey());
        Assertions.assertEquals(controlConfigIRequestDTO.getOperator(), updatedConfig.getOperator());
        Assertions.assertEquals(controlConfigIRequestDTO.getRemark(), updatedConfig.getRemark());
        Assertions.assertEquals(ControlStatusEnum.CLOSED, updatedConfig.getStatus());  // Verify the updated status
    }
}
