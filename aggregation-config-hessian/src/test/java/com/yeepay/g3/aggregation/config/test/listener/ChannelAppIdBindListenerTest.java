package com.yeepay.g3.aggregation.config.test.listener;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.biz.ChannelAppIdBindBiz;
import com.yeepay.g3.core.aggregation.config.builder.AppIdMerchantBindBuilder;
import com.yeepay.g3.core.aggregation.config.enums.ChannelAppIdBindStatusEnum;
import com.yeepay.g3.core.aggregation.config.mq.ChannelAppIdBindListener;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppIdBindEvent;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-16 20:55
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class ChannelAppIdBindListenerTest extends BaseTest {

    @Autowired
    private ChannelAppIdBindBiz channelAppIdBindBiz;

    @Autowired
    private AppIdMerchantBindBuilder channelAppIdBindBuilder;

    /**
     * 使用MockBean 替换真实 bean，启动 rocketMq 客户端，避免本地消费消息
     * @throws InterruptedException InterruptedException
     * @throws ParseException ParseException
     */
   //@Transactional
  //  @Rollback
    @Test
    public void onMessage() throws Exception {
        ChannelAppIdBindListener channelAppIdBindListener = new ChannelAppIdBindListener(ChannelAppIdBindEvent.class);
        ChannelAppIdBindEvent channelAppIdBindEvent = new ChannelAppIdBindEvent();
        channelAppIdBindEvent.setAppId("wx2e515a08a4474acc");
        channelAppIdBindEvent.setYeepayMerchantNo("10040040286");
        channelAppIdBindEvent.setReportMerchantNo("*********");
        channelAppIdBindEvent.setStatus(ChannelAppIdBindStatusEnum.FAILED.name());
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter_time = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String str = formatter_time.format(localDateTime);
        channelAppIdBindEvent.setConfigTime(str);
        channelAppIdBindListener.onMessage(channelAppIdBindEvent);
        //  channelAppIdBindListener.onMessage(channelAppIdBindEvent);
        Thread.sleep(1000000000000L);
    }
}
