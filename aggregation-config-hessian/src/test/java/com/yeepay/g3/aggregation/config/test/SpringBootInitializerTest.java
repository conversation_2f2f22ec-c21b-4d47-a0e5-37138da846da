package com.yeepay.g3.aggregation.config.test;

import com.yeepay.g3.hessian.aggregation.config.DataSourceConfig;
import com.yeepay.g3.utils.config.ConfigurationUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.smartcache.utils.SmartCacheUtils;
import com.yeepay.ng.yeeworks.config.YeeworksConfigUtils;
import com.yeepay.utils.lock.utils.RedisClientUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @description: 启动类
 * @author: xuchen.liu
 * @date: 2024-12-13 11:08
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
@ComponentScan(basePackages = {
        "com.yeepay.g3.facade.aggregation.config.*",
        "com.yeepay.g3.core.aggregation.config.*",
        "com.yeepay.g3.hessian.aggregation.config"
})
@EnableTransactionManagement
@Import({DataSourceConfig.class})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, RedisAutoConfiguration.class})
public class SpringBootInitializerTest extends SpringBootServletInitializer {

    static {
        ConfigurationUtils.init();
        RemoteServiceFactory.init();
        YeeworksConfigUtils.init();
        SmartCacheUtils.init();
        RedisClientUtils.init();
    }

    public static void main(String[] args) throws Exception {
        SpringApplication.run(SpringBootInitializerTest.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(SpringBootInitializerTest.class);
    }
}
