package com.yeepay.g3.aggregation.config.test.facade;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.facade.aggregation.config.facade.DataClearFacade;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-20 15:54
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class DataClearFacadeTest extends BaseTest {

    @Autowired
    private DataClearFacade clearFacade;

    @Test
    public void exc() throws Exception {
     //   clearFacade.excBind();
    //  clearFacade.excAttach();
       clearFacade.merge();
    }
}
