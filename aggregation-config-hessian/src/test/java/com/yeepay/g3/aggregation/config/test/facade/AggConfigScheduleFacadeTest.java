package com.yeepay.g3.aggregation.config.test.facade;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.facade.aggregation.config.facade.AggConfigScheduleFacade;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-31 17:12
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class AggConfigScheduleFacadeTest extends BaseTest {

    @Resource
    private AggConfigScheduleFacade aggConfigScheduleFacade;

    @Test
    public void compensateNotifyTest() {
        aggConfigScheduleFacade.compensateNotify("wx379b62a3cc54f35e");
    }

    @Test
    public void compensateAppIdBindTest() {
        aggConfigScheduleFacade.compensateAppIdBind("999999","10080041543" );
    }

    @Test
    public void compensateAttachBindTest() {
        aggConfigScheduleFacade.compensateAttachBind("10080041543","10080041543","WECHAT");
    }
}
