package com.yeepay.g3.aggregation.config.test.biz;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.entity.MerchantGroupEntity;
import com.yeepay.g3.core.aggregation.config.external.MerchantCenterExternal;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-27 14:29
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class MerchantCenterExternalTest extends BaseTest {

    @Autowired
    private MerchantCenterExternal merchantCenterExternal;

    @Test
    public void queryMerchantGroupByMerchantNoTest(){
        MerchantGroupEntity merchantGroupEntity = merchantCenterExternal.queryMerchantGroupByMerchantNo("10080085692");
        System.out.println(merchantGroupEntity);
    }
}
