package com.yeepay.g3.aggregation.config.test.facade;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.entity.YopNotifyEntity;
import com.yeepay.g3.core.aggregation.config.external.YopNotifyExternal;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.UUID;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-23 15:51
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class YopNotifyExternalTest extends BaseTest {

    @Autowired
    private YopNotifyExternal yopNotifyExternal;

    @Test
    public void test () {
        YopNotifyEntity yopNotifyEntity = new YopNotifyEntity();
        yopNotifyEntity.setMerchantNo("10080654949");
        yopNotifyEntity.setAppKey("app_10080654949");
        yopNotifyEntity.setUrl("https://qayop.yeepay.com");
        yopNotifyEntity.setNotifyNo(UUID.randomUUID().toString());
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("receiver","10080654949");
        objectObjectHashMap.put("controlAppid","10000000");
        objectObjectHashMap.put("congtrolTime","2024-12-23 15:56:00");
        objectObjectHashMap.put("controlExpireTime","2024-12-23 17:00:00");
        ArrayList<String> objects = new ArrayList<>();
        objects.add("10080654949");
        objectObjectHashMap.put("merchantList",objects);
        yopNotifyEntity.setNotifyInfo(objectObjectHashMap);
        yopNotifyExternal.call(yopNotifyEntity);
    }
}
