package com.yeepay.g3.aggregation.config.test.biz;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.dao.AppidAllMerchantNoBindDao;
import com.yeepay.g3.core.aggregation.config.entity.AppidAllMerchantNoBindEntity;
import com.yeepay.g3.core.aggregation.config.enums.BindAllStatusEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-25 10:51
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class AppidAllMerchantNoBindDaoTest extends BaseTest {

    @Autowired
    private AppidAllMerchantNoBindDao appidAllMerchantNoBindDao;

  //  @Transactional
   // @Rollback
    @Test
    public void insert() {
        ArrayList<AppidAllMerchantNoBindEntity> appidAllMerchantNoBindEntities = new ArrayList<>();
        AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity = new AppidAllMerchantNoBindEntity();
        appidAllMerchantNoBindEntity.setAppid("10000");
        appidAllMerchantNoBindEntity.setMerchantName("test1");
        appidAllMerchantNoBindEntity.setMerchantNo("10000");
        appidAllMerchantNoBindEntity.setFirstMerchantNo("2000");
        appidAllMerchantNoBindEntity.setBindStatus(BindAllStatusEnum.BIND);
        appidAllMerchantNoBindEntity.setCreateTime(new Date());
        appidAllMerchantNoBindEntity.setUpdateTime(new Date());
        appidAllMerchantNoBindEntity.setAttachMerchantNo("2000");

        AppidAllMerchantNoBindEntity appidAllMerchantNoBindEntity1 = new AppidAllMerchantNoBindEntity();
        appidAllMerchantNoBindEntity1.setAppid("20000");
        appidAllMerchantNoBindEntity1.setMerchantName("test1");
        appidAllMerchantNoBindEntity1.setMerchantNo("10000");
        appidAllMerchantNoBindEntity1.setSecondMerchantNo("2000");
        appidAllMerchantNoBindEntity1.setBindStatus(BindAllStatusEnum.BIND);
        appidAllMerchantNoBindEntity1.setCreateTime(new Date());
        appidAllMerchantNoBindEntity1.setUpdateTime(new Date());
        appidAllMerchantNoBindEntity1.setAttachMerchantNo("3000");

        appidAllMerchantNoBindEntities.add(appidAllMerchantNoBindEntity);
        appidAllMerchantNoBindEntities.add(appidAllMerchantNoBindEntity1);
     //   appidAllMerchantNoBindDao.insertBatchSelective(appidAllMerchantNoBindEntities);
    }
}
