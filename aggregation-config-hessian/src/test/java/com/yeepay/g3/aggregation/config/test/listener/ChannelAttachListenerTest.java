package com.yeepay.g3.aggregation.config.test.listener;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.biz.AttachResultBiz;
import com.yeepay.g3.core.aggregation.config.biz.ChannelAppIdBindBiz;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import com.yeepay.g3.core.aggregation.config.mq.ChannelAttachListener;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAttachEvent;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.infra.threadpool.AtlasThreadPoolExecutor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * @Description:
 * @ClassName: ChannelAttachListenerTest
 * @Author: cong.huo
 * @Date: 2024/12/17 18:40   // 时间
 * @Version: 1.0
 */
public class ChannelAttachListenerTest extends BaseTest {

    @Autowired
    private AttachResultBiz attachResultBiz;
    @Autowired
    private ChannelAppIdBindBiz channelAppIdBindBiz;

    @Qualifier(value = "asyncExecutor")
    @Autowired
    private  AtlasThreadPoolExecutor executor;

    @Test
    public void onMessage() throws Exception {
        ChannelAttachEvent  channelAttachEvent = new ChannelAttachEvent();
        channelAttachEvent.setMaskMainMerchantNo("10080078023");
        channelAttachEvent.setYeepayMerchantNo("10000470968000000");
        channelAttachEvent.setChannelType(ChannelTypeEnum.WECHAT.name());
        channelAttachEvent.setStatus("1");
        channelAttachEvent.setSuccessTime("2024-12-27 17:03:47");
        System.out.println("打印输入="+ JsonUtils.convert(channelAttachEvent));
        new ChannelAttachListener(ChannelAttachEvent.class).onMessage(channelAttachEvent);
        Thread.sleep(1000000000000L);
    }
}
