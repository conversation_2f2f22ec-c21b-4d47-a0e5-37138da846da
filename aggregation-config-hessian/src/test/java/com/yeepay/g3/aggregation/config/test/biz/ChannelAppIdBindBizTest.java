package com.yeepay.g3.aggregation.config.test.biz;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.biz.ChannelAppIdBindBiz;
import com.yeepay.g3.core.aggregation.config.entity.AttachResultEntity;
import com.yeepay.g3.core.aggregation.config.enums.AttachConfigStatusEnum;
import com.yeepay.g3.core.aggregation.config.enums.ChannelTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-19 13:32
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class ChannelAppIdBindBizTest extends BaseTest {

    @Autowired
    private ChannelAppIdBindBiz channelAppIdBindBiz;

    @Test
    public void testAttachBind() throws InterruptedException {
        AttachResultEntity attachResultEntity = new AttachResultEntity();
        attachResultEntity.setMainMerchantNo("10080009471");
        attachResultEntity.setSubMerchantNo("10080028707");
        attachResultEntity.setChannel(ChannelTypeEnum.WECHAT.name());
        attachResultEntity.setConfigStatus(AttachConfigStatusEnum.SUCCESS.name());
        channelAppIdBindBiz.attachBind(attachResultEntity);
        Thread.sleep(100000L);
    }
}
