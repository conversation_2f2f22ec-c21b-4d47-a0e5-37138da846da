package com.yeepay.g3.aggregation.config.test.facade;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson2.util.DateUtils;
import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.biz.disposal.DisposalQueryEntity;
import com.yeepay.g3.core.aggregation.config.entity.YopNotifyEntity;
import com.yeepay.g3.core.aggregation.config.utils.JsonUtils;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryResponseDTO;
import com.yeepay.g3.facade.aggregation.config.facade.disposal.DisposalFacade;
import org.assertj.core.util.DateUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.UUID;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/14 17:19
 */
public class DisposalFacadeTest extends BaseTest {


    @Autowired
    private DisposalFacade disposalFacade;

    @Test
    public void pageQueryDisposalList () {
        DisposalNotifyQueryRequestDTO requestDTO = new DisposalNotifyQueryRequestDTO();

        requestDTO.setChannelType(1);
        requestDTO.setReportMerchantNo("");
        requestDTO.setYeepayMerchantNo("");
        requestDTO.setChannelNo("");

        requestDTO.setTopMerchantNo(requestDTO.getTopMerchantNo());
        requestDTO.setAgentMerchantNo("SA202506041152250002");

       requestDTO.setStartTime(DateUtils.parseDate("2022-07-01"));
        requestDTO.setEndTime(DateUtils.parseDate("2025-07-30"));
        requestDTO.setPageNum(1);
        requestDTO.setPageSize(10);

        requestDTO.setPunishPlan(requestDTO.getPunishPlan());
        requestDTO.setPunishType(requestDTO.getPunishType());
        requestDTO.setCompanyName(requestDTO.getCompanyName());

        requestDTO.setQueryType(requestDTO.getQueryType());
        requestDTO.setWithSelf(true);
        DisposalNotifyQueryResponseDTO responseDTO = disposalFacade.pageQueryDisposalList(requestDTO);
        System.out.printf(JsonUtils.convert(responseDTO));
    }
}
