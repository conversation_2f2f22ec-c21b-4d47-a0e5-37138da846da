package com.yeepay.g3.aggregation.config.test.listener;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.biz.ChannelAppidControlBiz;
import com.yeepay.g3.core.aggregation.config.mq.ChannelAppidControlListener;
import com.yeepay.g3.core.aggregation.config.mq.event.ChannelAppidControlEvent;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description:
 * @ClassName: ChannelAppidControlListenerTest
 * @Author: cong.huo
 * @Date: 2024/12/23 11:54   // 时间
 * @Version: 1.0
 */
public class ChannelAppidControlListenerTest extends BaseTest {

    @Autowired
    private ChannelAppidControlBiz channelAppIdControlBiz;

//    @Transactional
//    @Rollback
    @Test
    public void onMessage() throws Exception {
        ChannelAppidControlEvent channelAppidControlEvent = new ChannelAppidControlEvent();
        channelAppidControlEvent.setAppId("appid2");
        channelAppidControlEvent.setControlledTime("2024-12-21 11:54:00");
//        channelAppidControlEvent.setUncontrolledTime("2024-12-27 18:00:00");
        channelAppidControlEvent.setNotifyType("UPDATE");
        new ChannelAppidControlListener(ChannelAppidControlEvent.class).onMessage(channelAppidControlEvent);
    }
}
