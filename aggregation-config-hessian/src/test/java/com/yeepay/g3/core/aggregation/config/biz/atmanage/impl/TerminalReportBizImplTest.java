package com.yeepay.g3.core.aggregation.config.biz.atmanage.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.TerminalReportBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WechatIndirectSpecialParams;
import org.junit.Test;

import javax.annotation.Resource;

public class TerminalReportBizImplTest extends BaseTest {
    @Resource
    private TerminalReportBiz terminalReportBiz;

    @Test
    public void applyChannelTerminalReport() {
        String jsonStr = "{\n" +
                "  \"merchantInfo\" : {\n" +
                "    \"merchantNo\" : \"12345678915\",\n" +
                "    \"parentMerchantNo\" : \"10080041543\",\n" +
                "    \"topLevelMerchantNo\" : \"10080041543\",\n" +
                "    \"merchantName\" : \"商户入网测试三\",\n" +
                "    \"shortName\" : \"入网测试三\",\n" +
                "    \"isDirectMerchant\" : false,\n" +
                "    \"industryLine\" : \"测试\",\n" +
                "    \"role\" : \"ORDINARY_MERCHANT\",\n" +
                "    \"isExitArea\" : false\n" +
                "  },\n" +
                "  \"orderNo\" : \"2506301329028291133904110853\",\n" +
                "  \"bizScene\" : \"CUSTOMER_CENTER\",\n" +
                "  \"bizApplyNo\" : \"entryApply73014280\",\n" +
                "  \"applyNo\" : \"WXI2506301329029071133904110854\",\n" +
                "  \"subjectInfo\" : {\n" +
                "    \"subjectType\" : \"ENTERPRISE\",\n" +
                "    \"certificateLetterCopy\" : \"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/loading.gif\",\n" +
                "    \"financeInstitution\" : false,\n" +
                "    \"certificateInfo\" : {\n" +
                "      \"certMerchantName\" : \"商户入网测试三\",\n" +
                "      \"certLegalPerson\" : \"Zcx测试\",\n" +
                "      \"certCompanyAddress\" : \"Zcx测试注册地址\",\n" +
                "      \"effectTime\" : \"2010-06-22\",\n" +
                "      \"expireTime\" : \"2030-06-22\",\n" +
                "      \"certType\" : \"LICENSE\",\n" +
                "      \"certNumber\" : \"11010119900399\",\n" +
                "      \"certCopy\" : \"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/card.png\"\n" +
                "    },\n" +
                "    \"companyRepresentativeInfo\" : {\n" +
                "      \"legalType\" : \"LEGAL\",\n" +
                "      \"cardType\" : \"ID_CARD\",\n" +
                "      \"name\" : \"Zcx测试\",\n" +
                "      \"cardNo\" : \"110101199003079999\",\n" +
                "      \"effectTime\" : \"2010-06-22\",\n" +
                "      \"expireTime\" : \"2030-06-22\",\n" +
                "      \"cardFrontImg\" : \"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/<EMAIL>\",\n" +
                "      \"cardBackImg\" : \"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/<EMAIL>\",\n" +
                "      \"certAddress\" : \"Zcx测试证件居住地址\",\n" +
                "      \"businessAuthorizationLetter\" : \"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/notify.png\"\n" +
                "    },\n" +
                "    \"salesInfo\" : {\n" +
                "      \"servicePhone\" : \"***********\",\n" +
                "      \"qualificationInfo\" : {\n" +
                "        \"categoryId\" : \"062301\",\n" +
                "        \"operationCopyList\" : [ \"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/ad.jpg\", \"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/ad.jpg\" ]\n" +
                "      },\n" +
                "      \"salesAddress\" : {\n" +
                "        \"province\" : \"110000\",\n" +
                "        \"city\" : \"110100\",\n" +
                "        \"district\" : \"110114\",\n" +
                "        \"address\" : \"万通中心\"\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"contactInfo\" : {\n" +
                "    \"contactName\" : \"Zcx联系人\",\n" +
                "    \"contactMobileNo\" : \"18500355487\",\n" +
                "    \"contactEmail\" : \"<EMAIL>\",\n" +
                "    \"contactCertNo\" : \"110105199001011234\",\n" +
                "    \"contactType\" : \"LEGAL\"\n" +
                "  },\n" +
                "  \"channelNo\" : \"*********\",\n" +
                "  \"entryType\" : \"ENTRY\",\n" +
                "  \"payScene\" : \"OFFLINE\",\n" +
                "  \"payChannel\" : \"WECHAT\",\n" +
                "  \"activityType\" : \"NORMAL\",\n" +
                "  \"settleAccountInfo\" : {\n" +
                "    \"bankAccountType\" : \"CORPORATE\",\n" +
                "    \"cardName\" : \"Zcx测试\",\n" +
                "    \"cardNo\" : \"1234567890987654321\",\n" +
                "    \"bankCode\" : \"ICBC\",\n" +
                "    \"distinctCode\" : \"110000\",\n" +
                "    \"branchCode\" : \"129002\",\n" +
                "    \"bankName\" : \"中国工商银行\",\n" +
                "    \"bankBranchName\" : \"中国工商银行北京支行\"\n" +
                "  },\n" +
                "  \"entryAuditStatus\" : \"FINISHED\",\n" +
                "  \"oldEntryAuditStatus\" : \"FINISHED\",\n" +
                "  \"entryStatus\" : \"SUCCESS\",\n" +
                "  \"oldEntryStatus\" : \"SUCCESS\",\n" +
                "  \"flag\" : 1,\n" +
                "  \"flagStatus\" : 0,\n" +
                "  \"oldFlagStatus\" : 0,\n" +
                "  \"backupCounts\" : 0,\n" +
                "  \"backupSwitch\" : false,\n" +
                "  \"channelSpecialParams\" : {\n" +
                "    \"mcc\" : \"mcc\",\n" +
                "    \"feeRuleId\" : \"1234\",\n" +
                "    \"settleAccountInfo\" : {\n" +
                "      \"bankAccountType\" : \"CORPORATE\",\n" +
                "      \"cardName\" : \"Zcx测试\",\n" +
                "      \"cardNo\" : \"1234567890987654321\",\n" +
                "      \"bankCode\" : \"ICBC\",\n" +
                "      \"distinctCode\" : \"110000\",\n" +
                "      \"branchCode\" : \"129002\",\n" +
                "      \"bankName\" : \"中国工商银行\",\n" +
                "      \"bankBranchName\" : \"中国工商银行北京支行\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"channelMchNos\" : \"*********\",\n" +
                "  \"finishTime\" : \"2025-06-30T13:29:06.994\",\n" +
                "  \"failReason\" : \"\",\n" +
                "  \"anchoredApplyNo\" : \"\",\n" +
                "  \"mcc\" : \"mcc\",\n" +
                "  \"feeRuleId\" : \"1234\",\n" +
                "  \"isNeedTerminalReport\" : true\n" +
                "}";

        BaseEntryApply<WechatIndirectSpecialParams> entryApply = JsonUtils.fromJson(jsonStr, new TypeReference<BaseEntryApply<WechatIndirectSpecialParams>>() {
        });
        terminalReportBiz.applyChannelTerminalReport(entryApply);
    }
}