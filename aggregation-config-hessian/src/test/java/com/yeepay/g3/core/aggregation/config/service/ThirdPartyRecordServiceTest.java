package com.yeepay.g3.core.aggregation.config.service;

import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.bo.ThirdPartyRecord;
import com.yeepay.g3.core.aggregation.config.enums.ThirdPartyBusinessTypeEnum;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class ThirdPartyRecordServiceTest extends BaseTest {

    @Resource
    private ThirdPartyRecordService thirdPartyRecordService;

    @Test
    public void queryThirdPartyRecordByApplyNo() {

        List<ThirdPartyRecord> recordList = thirdPartyRecordService.queryThirdPartyRecordByApplyNo("ALI2506271417519371133908110058", ThirdPartyBusinessTypeEnum.CHANNEL_ENTRY.getDocument());
        System.out.println("");


    }

    @Test
    public void queryNotFinishOrSuccessThirdPartyRecordByApplyNo() {

        List<ThirdPartyRecord> recordList = thirdPartyRecordService.queryNotFinishOrSuccessThirdPartyRecordByApplyNo("WXI2506301329029071133904110854", ThirdPartyBusinessTypeEnum.CHANNEL_TERMINAL_REPORT.getDocument());
        System.out.println("");

    }
}