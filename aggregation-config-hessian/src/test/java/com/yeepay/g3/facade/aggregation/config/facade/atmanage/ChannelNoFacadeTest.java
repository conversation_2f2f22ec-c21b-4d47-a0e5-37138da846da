package com.yeepay.g3.facade.aggregation.config.facade.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelApplyResultRespDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelNoApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.QueryChannelApplyResultReqDTO;
import com.yeepay.g3.facade.aggregation.config.facade.schedule.ChannelNoScheduleFacade;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * description: 描述
 *
 * <AUTHOR>
 * @since 2025/6/12:21:21
 * Company: 易宝支付(YeePay)
 */
public class ChannelNoFacadeTest extends BaseTest {

    @Resource
    private ChannelNoFacade channelNoFacade;

    @Resource
    private ChannelNoScheduleFacade channelNoScheduleFacade;

    @Test
    public void applyChannelNo() {
        String str = "{\"merchantNo\":\"***********\",\"shortName\":\"入网测试二\",\"bizScene\":\"CUSTOMER_CENTER\",\"bizApplyNo\":\"bizApplyNo42707651\",\"payChannel\":\"WECHAT\",\"payScene\":\"OFFLINE\",\"activityType\":\"NORMAL\",\"salesName\":\"测试销售名称\",\"industryName\":\"测试行业名称\",\"digitalCurrencyBankCode\":\"\",\"isUseYpChannelNo\":\"Yes\",\"fundLiquidationType\":\"INDIRECT_LIQUIDATION\",\"servicePhone\":\"***********\",\"superAdminInfo\":{\"contactName\":\"测试\",\"contactMobileNo\":\"***********\",\"contactCertNo\":\"370883198812195128\",\"certType\":\"ID_CARD\",\"cardFrontImg\":\"法人正面照\",\"cardBackImg\":\"法人背面照\",\"businessAuthorizationLetter\":\"测试微信渠道授权书\",\"contactEmail\":\"<EMAIL>\"},\"companyRepresentativeInfo\":{\"cardFrontImg\":\"法人正面照\",\"cardBackImg\":\"法人背面照\"},\"certificateInfo\":{\"certMerchantName\":\"测试登记证书名称\",\"certCompanyAddress\":\"测试登记注册地址\",\"certNumber\":\"DJNZBHTEST\",\"certCopy\":\"测试登记证书照片\"},\"settleAccountInfo\":{\"cardNo\":\"*************\",\"cardName\":\"测试银行账户\",\"bankCode\":\"ICBC\",\"bankName\":\"测试银行\",\"bankBranchName\":\"测试银行支行\",\"provName\":\"北京市\",\"cityName\":\"通州区\"},\"extendInfo\":\"{\\\"Key1\\\":\\\"微信线下间清用易宝渠道号\\\"}\"}";
        ChannelNoApplyReqDTO channelNoApplyReqDTO = JsonUtils.fromJson(str, ChannelNoApplyReqDTO.class);
        channelNoApplyReqDTO.setCompanyRepresentativeInfo(null);
        channelNoApplyReqDTO.setMerchantNo("***********test2");
        channelNoApplyReqDTO.setIndustryName("大零售行业线—XLS");
        BaseResponseDTO baseResponseDTO = channelNoFacade.applyChannelNo(channelNoApplyReqDTO);
        System.out.println(JsonUtils.toJSONString(baseResponseDTO));
    }

    @Test
    public void queryChannelApplyResult() {
        QueryChannelApplyResultReqDTO reqDTO = new QueryChannelApplyResultReqDTO();
        reqDTO.setMerchantNo("***********");
        reqDTO.setPayChannel(PayChannelEnum.ALIPAY.getDocument());
        reqDTO.setPayScene(PaySceneEnum.OFFLINE.getDocument());
        reqDTO.setActivityType(ActivityTypeEnum.NORMAL.getDocument());
        ChannelApplyResultRespDTO channelApplyResultRespDTO = channelNoFacade.queryChannelApplyResult(reqDTO);
        System.out.println(JsonUtils.toJSONString(channelApplyResultRespDTO));
    }
    @Test
    public void handleChannelNoApplyResult() {
        channelNoScheduleFacade.handleChannelNoApplyResult(null, 7, -1);
    }
}
