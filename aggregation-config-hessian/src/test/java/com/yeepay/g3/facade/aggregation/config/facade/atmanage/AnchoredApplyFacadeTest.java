package com.yeepay.g3.facade.aggregation.config.facade.atmanage;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yeepay.aggregation.config.share.kernel.enumerate.*;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.biz.atmanage.AnchoredBiz;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.AnchoredOrder;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request.AnchoredApplyDetailDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request.AnchoredApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.response.AnchoredApplyResponseDTO;
import com.yeepay.g3.facade.aggregation.config.facade.schedule.AnchoredScheduleFacade;
import com.yeepay.g3.facade.aggregation.config.facade.schedule.EntryScheduleFacade;
import com.yeepay.g3.utils.common.encrypt.AES;
import org.junit.Test;

import javax.annotation.Resource;

public class AnchoredApplyFacadeTest
        extends BaseTest {

    @Resource
    private AnchoredApplyFacade anchoredApplyFacade;

    @Resource
    private AnchoredScheduleFacade anchoredScheduleFacade;

    @Resource
    private AnchoredBiz anchoredBiz;

    @Resource
    private EntryScheduleFacade entryScheduleFacade;

    @Test
    public void singAnchoredScheduleApply() {
        String jsonStr = "{\n" +
                "  \"orderNo\" : \"f973d8a7b7ea4591b7551d6fd8757617\",\n" +
                "  \"bizScene\" : \"AGG_ENTRY\",\n" +
                "  \"bizApplyNo\" : \"ALI2506201112572751134103810494\",\n" +
                "  \"merchantNo\" : \"10080746618\",\n" +
                "  \"parentMerchantNo\" : \"10080041543\",\n" +
                "  \"topLevelMerchantNo\" : \"10080041543\",\n" +
                "  \"orderStatus\" : \"INIT\",\n" +
                "  \"singSync\" : true,\n" +
                "  \"anchoredApplyList\" : [ {\n" +
                "    \"topMerchantNo\" : \"10080041543\",\n" +
                "    \"parentMerchantNo\" : \"10080041543\",\n" +
                "    \"orderNo\" : \"f973d8a7b7ea4591b7551d6fd8757617\",\n" +
                "    \"applyNo\" : \"f73b4ca38f4d4f6f9253cdca191a3e00\",\n" +
                "    \"merchantNo\" : \"10080746618\",\n" +
                "    \"payScene\" : \"OFFLINE\",\n" +
                "    \"activityType\" : \"NORMAL\",\n" +
                "    \"channelType\" : \"ALIPAY\",\n" +
                "    \"anchoredType\" : \"DESIGNATE_MERCHANT\",\n" +
                "    \"relationMerchantNo\" : \"10080746401\",\n" +
                "    \"groupName\" : \"10080041543\",\n" +
                "    \"anchoredStatus\" : \"INIT\"\n" +
                "  } ]\n" +
                "}";
        AnchoredOrder anchoredOrder = JsonUtils.fromJson(jsonStr, new TypeReference<AnchoredOrder>() {
        });

        anchoredBiz.anchoredApply(anchoredOrder);
    }

    @Test
    public void anchoredScheduleApply() {
        anchoredScheduleFacade.compensateAnchoredOrder(null, null, null, null, "AN2506301450477931133786510985");
    }


    @Test
    public void entryScheduleApply() {
        entryScheduleFacade.compensateEntryApply(300, 0, "2025-06-12 00:00:00", "2025-06-21 00:00:00", "");
    }

    @Test
    public void anchoredApply() {
        //10080021050
        AnchoredApplyReqDTO anchoredApplyReqDTO = new AnchoredApplyReqDTO();
        anchoredApplyReqDTO.setBizScene(BizSceneEnum.CUSTOMER_CENTER.name());
        anchoredApplyReqDTO.setBizApplyNo(System.currentTimeMillis() + "");
        anchoredApplyReqDTO.setTopLevelMerchantNo("10080021037");
        anchoredApplyReqDTO.setParentMerchantNo("10080021037");
        anchoredApplyReqDTO.setMerchantNo("10080021037");

        AnchoredApplyDetailDTO anchoredApplyDetailDTO = new AnchoredApplyDetailDTO();
        anchoredApplyDetailDTO.setAnchoredType(AnchoredType.SAME_SUBJECT.name());
        anchoredApplyDetailDTO.setAnchoredDimension(true);
        anchoredApplyDetailDTO.setAnchoredMerchantNo("10080021050");
        anchoredApplyDetailDTO.setChannelType(PayChannelEnum.WECHAT.name());
        anchoredApplyDetailDTO.setPayScene(PaySceneEnum.OFFLINE.name());
        anchoredApplyDetailDTO.setActivityType(ActivityTypeEnum.NORMAL.name());


        anchoredApplyReqDTO.setAnchoredApplyDetailList(Lists.newArrayList(anchoredApplyDetailDTO));

        try {
            AnchoredApplyResponseDTO responseDTO = anchoredApplyFacade.anchoredApply(anchoredApplyReqDTO);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void testEncrypted() {

        String encrypt = "3rWDrzQCS90NiDz8yMAhCQ==";
        String s = AES.decryptFromBase64(encrypt, "I am a fool, OK?");
        System.out.println("passWord: " + s);
    }
}