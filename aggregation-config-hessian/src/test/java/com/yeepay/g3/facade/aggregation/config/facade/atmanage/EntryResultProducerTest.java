package com.yeepay.g3.facade.aggregation.config.facade.atmanage;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.aggregation.config.share.kernel.enumerate.EntryNotifyBizTypeEnum;
import com.yeepay.aggregation.config.share.kernel.util.JsonUtils;
import com.yeepay.g3.aggregation.config.test.BaseTest;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.BaseEntryApply;
import com.yeepay.g3.core.aggregation.config.bo.atmanage.model.WechatIndirectSpecialParams;
import com.yeepay.g3.core.aggregation.config.mq.producer.EntryResultProducer;
import org.junit.Test;

import javax.annotation.Resource;

public class EntryResultProducerTest
        extends BaseTest {

    @Resource
    private EntryResultProducer entryResultProducer;


    @Test
    public void sendEntryNotifyMessage() {
        String jsonStr = "{\"flag\":5,\"channelNo\":\"*********\",\"contactInfo\":{\"isLegal\":true,\"contactEmail\":\"<EMAIL>\",\"contactName\":\"Zcx联系人\",\"contactMobileNo\":\"***********\",\"contactCertNo\":\"110105199001011234\",\"contactType\":\"LEGAL\",\"isIdCardCertType\":true},\"oldEntryStatus\":\"INIT\",\"mcc\":\"mcc\",\"activityInfo\":{\"activitiesId\":\"1234\",\"debitActivitiesRate\":\"1\",\"creditActivitiesRate\":\"1\",\"activityType\":\"NORMAL\"},\"anchoredApplyFlag\":true,\"anchoredApplyNo\":\"******************************\",\"entryStatus\":\"INIT\",\"bizApplyNo\":\"entryApply04655146\",\"backupCounts\":0,\"channelSpecialParams\":{\"settleAccountInfo\":{\"branchCode\":\"129002\",\"bankCode\":\"ICBC\",\"bankAccountType\":\"CORPORATE\",\"distinctCode\":\"110000\",\"cardName\":\"Zcx测试\",\"bankBranchName\":\"中国工商银行北京支行\",\"bankName\":\"中国工商银行\",\"cardNo\":\"1234567890987654321\"},\"feeRuleId\":\"1234\",\"mcc\":\"mcc\"},\"settleAccountInfo\":{\"branchCode\":\"129002\",\"bankCode\":\"ICBC\",\"bankAccountType\":\"CORPORATE\",\"distinctCode\":\"110000\",\"cardName\":\"Zcx测试\",\"bankBranchName\":\"中国工商银行北京支行\",\"bankName\":\"中国工商银行\",\"cardNo\":\"1234567890987654321\"},\"wxDirectCreateSplitterFlag\":false,\"terminalReportFlagStatus\":false,\"feeRuleId\":\"1234\",\"entryType\":\"ENTRY_ANCHORED\",\"orderNo\":\"2506271012458101133829717700\",\"terminalReportFlag\":true,\"anchoredOrder\":{\"bizApplyNo\":\"WXI2506271012458101133829717701\",\"orderNo\":\"******************************\",\"anchoredApplyList\":[{\"topMerchantNo\":\"***********\",\"orderNo\":\"******************************\",\"channelType\":\"WECHAT\",\"anchoredDimension\":false,\"groupName\":\"***********\",\"singSync\":true,\"applyNo\":\"b6fa78e4f84341958126d72c729f6de8\",\"aggAnchoredMerchantInfoList\":[{\"institutionMchNo\":\"*********\",\"anchoredChannelNo\":\"*********\",\"channelStatus\":\"true\",\"anchoredMerchantNo\":\"***********\",\"requestNo\":\"2506271012462281133829717704\"}],\"parentMerchantNo\":\"***********\",\"relationMerchantNo\":\"***********\",\"activityType\":\"NORMAL\",\"anchoredStatus\":\"SUCCESS\",\"anchoredType\":\"DESIGNATE_MERCHANT\",\"payScene\":\"OFFLINE\",\"merchantNo\":\"***********\"}],\"singSync\":true,\"orderStatus\":\"INIT\",\"bizScene\":\"AGG_ENTRY\",\"parentMerchantNo\":\"***********\",\"topLevelMerchantNo\":\"***********\",\"merchantNo\":\"***********\"},\"bizScene\":\"CUSTOMER_CENTER\",\"anchoredApplyFlagStatus\":true,\"merchantInfo\":{\"role\":\"ORDINARY_MERCHANT\",\"industryLine\":\"测试\",\"parentMerchantNo\":\"***********\",\"shortName\":\"入网测试三\",\"isDirectMerchant\":false,\"topLevelMerchantNo\":\"***********\",\"isExitArea\":false,\"merchantName\":\"商户入网测试三\",\"merchantNo\":\"***********\"},\"oldFlagStatus\":0,\"flagStatus\":4,\"applyNo\":\"WXI2506271012458101133829717701\",\"wxDirectCreateSplitterFlagStatus\":false,\"backupSwitch\":false,\"entryAuditStatus\":\"INIT\",\"payChannel\":\"WECHAT\",\"subjectInfo\":{\"financeInstitution\":false,\"companyRepresentativeInfo\":{\"expireTime\":\"2030-06-22\",\"legalType\":\"LEGAL\",\"businessAuthorizationLetter\":\"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/notify.png\",\"cardBackImg\":\"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/<EMAIL>\",\"cardType\":\"ID_CARD\",\"name\":\"Zcx测试\",\"cardFrontImg\":\"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/<EMAIL>\",\"effectTime\":\"2010-06-22\",\"certAddress\":\"Zcx测试证件居住地址\",\"isIdCardType\":true,\"cardNo\":\"110101199003079999\"},\"certificateLetterCopy\":\"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/loading.gif\",\"salesInfo\":{\"servicePhone\":\"***********\",\"qualificationInfo\":{\"operationCopyList\":[\"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/ad.jpg\",\"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/ad.jpg\"],\"categoryId\":\"062301\"},\"salesAddress\":{\"address\":\"万通中心\",\"province\":\"110000\",\"city\":\"110100\",\"district\":\"110114\"}},\"certificateInfo\":{\"certNumber\":\"11010119900399\",\"certType\":\"LICENSE\",\"expireTime\":\"2030-06-22\",\"certMerchantName\":\"商户入网测试三\",\"unifiedCreditCertificate\":false,\"effectTime\":\"2010-06-22\",\"certCompanyAddress\":\"Zcx测试注册地址\",\"certCopy\":\"https://qastaticres.yeepay.com/jccpzx-nc-cashier/static/ceshi/static/images/card.png\",\"certLegalPerson\":\"Zcx测试\"},\"subjectType\":\"ENTERPRISE\"},\"activityType\":\"NORMAL\",\"payScene\":\"OFFLINE\"}";


        BaseEntryApply<WechatIndirectSpecialParams> baseEntryApply = JsonUtils.fromJson(jsonStr, new TypeReference<BaseEntryApply<WechatIndirectSpecialParams>>() {
        });

        entryResultProducer.sendEntryNotifyMessage(baseEntryApply, EntryNotifyBizTypeEnum.ENTRY);

    }


}