YP_JVM_RESOURCE_CPUCATALINA_OUT=/apps/log/tomcat-nohup/nohup.out
CATALINA_PID=$CATALINA_HOME/bin/CATALINA_PID
CATALINA_TMPDIR=/apps/data/tomcat-temp/
WORK_DIR=/apps/data/tomcat-work

#parameter for CI/CD
APP_NAME="aggregation-config-hessian"
DATA_CENTER=${YP_DATA_CENTER}
DEPLOY_ENV=${YP_DEPLOY_ENV}
#if [ -z $APP_NAME ] 
#then
#    echo "YP_APP_NAME should be specified"
#    exit 1;
#fi

JAVA_OPTS="$JAVA_OPTS -Dsun.net.inetaddr.ttl=-1 -Dsun.net.http.retryPost=false -Dappname=$APP_NAME"

if [ "-$DATA_CENTER" != "-" ] 
then
    JAVA_OPTS="$JAVA_OPTS -Ddatacenter=$DATA_CENTER"
fi
if [ "-$DEPLOY_ENV" != "-" ] 
then
    JAVA_OPTS="$JAVA_OPTS -Ddeploy_env=$DEPLOY_ENV"
fi

#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -Ddubbo.application.environment=$DUBBO_APPLICATION_ENVIRONMENT -Ddbconfigpath=/apps/tomcat/commoncfg/runtimecfg/ -Djava.net.preferIPv4Stack=true -Djava.io.tmpdir=/apps/data/java-tmpdir -Xshare:off -Dhostname=`hostname` -Djute.maxbuffer=41943040 -Dfile.encoding='UTF-8'"
#end of parameter for CI/CD
pubip="`/sbin/ifconfig  | grep 'inet addr:'| grep -v '127.0.0.1' | cut -d: -f2 | awk '{ print $1}'`"
if [ "-$pubip" == "-" ]
then
{
 pubip="`ifconfig eth0 |grep "inet addr"| cut -f 2 -d ":"|cut -f 1 -d " "`"
}
fi
if [ "-$pubip" != "-" ]
then
{
 JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote -Ddubbo.application.environment=$DUBBO_APPLICATION_ENVIRONMENT -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Ddbconfigpath=/apps/tomcat/commoncfg/runtimecfg/ -Dcom.sun.management.jmxremote.authenticate=false -Djava.net.preferIPv4Stack=true -Djava.io.tmpdir=/apps/data/java-tmpdir -Xms1843m -Xmx1843m -XX:PermSize=256m -XX:MaxPermSize=512m -Xshare:off -Dhostname=`hostname` -Djute.maxbuffer=41943040 -Dfile.encoding='UTF-8'"
}
else
{
 JAVA_OPTS="$JAVA_OPTS -Ddbconfigpath=/apps/tomcat/commoncfg/runtimecfg/ -Ddubbo.application.environment=$DUBBO_APPLICATION_ENVIRONMENT -Dcom.sun.management.jmxremote -Djava.rmi.server.hostname=$pubip  -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -Djava.net.preferIPv4Stack=true -Djava.io.tmpdir=/apps/data/java-tmpdir -Xms2048m -Xmx2048m -XX:PermSize=256m -XX:MaxPermSize=512m -Xshare:off -Dhostname=`hostname` -Djute.maxbuffer=41943040 -Dfile.encoding='UTF-8'"
}
fi

JAVA_VERSION=`java -version 2>&1 |awk 'NR==1{ gsub(/"/,""); print $3 }'`

JAVA_OPTS="$JAVA_OPTS -server"

# Druid填坑，usePingMethod默认为true，检测连接有效性不会到数据库执行sql，只会在本地校验内存对象的标记位，所以需要把usePingMethod手动设置为false
JAVA_OPTS="$JAVA_OPTS -Ddruid.mysql.usePingMethod=false"

# 设置JVM GC参数，只考虑JDK 1.7、1.8、1.9
# 将垃圾回收并发线程数设置为docker分配的CPU核数
if [ "-$YP_JVM_RESOURCE_CPU" != "-" ]; then
    JAVA_OPTS="$JAVA_OPTS -XX:ParallelGCThreads=$YP_JVM_RESOURCE_CPU"
fi

# 根据docker分配的内存设置JVM的垃圾回收策略和内存分布
if [ "-$YP_JVM_RESOURCE_MEMORY" == "-16G" ]; then
	JAVA_OPTS="$JAVA_OPTS -Xms10880m -Xmx10880m"
	JAVA_OPTS="$JAVA_OPTS -XX:MaxDirectMemorySize=1024M"
	JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
	JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
	JAVA_OPTS="$JAVA_OPTS -XX:+UseLargePages -XX:LargePageSizeInBytes=16m"
	YP_JVM_GC_ALG=G1
	YP_JVM_GC_PERM_SIZE=512m
	YP_JVM_GC_MAX_PERM_SIZE=512m
elif [ "-$YP_JVM_RESOURCE_MEMORY" == "-8G" ]; then
	JAVA_OPTS="$JAVA_OPTS -Xms5440m -Xmx5440m"
	JAVA_OPTS="$JAVA_OPTS -XX:MaxDirectMemorySize=1024M"
	JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
	JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
	JAVA_OPTS="$JAVA_OPTS -XX:+UseLargePages -XX:LargePageSizeInBytes=16m"
	YP_JVM_GC_ALG=G1
	YP_JVM_GC_PERM_SIZE=512m
	YP_JVM_GC_MAX_PERM_SIZE=512m
elif [ "-$YP_JVM_RESOURCE_MEMORY" == "-4G" ]; then
	JAVA_OPTS="$JAVA_OPTS -Xms2550m -Xmx2550m -Xss512k -XX:+UseConcMarkSweepGC -XX:+UseParNewGC"
	YP_JVM_GC_PERM_SIZE=128m
	YP_JVM_GC_MAX_PERM_SIZE=512m
elif [ "-$YP_JVM_RESOURCE_MEMORY" == "-2G" ]; then
	JAVA_OPTS="$JAVA_OPTS -Xms1024m -Xmx1024m -Xss256k"
	YP_JVM_GC_PERM_SIZE=128m
	YP_JVM_GC_MAX_PERM_SIZE=256m
else
	# 兼容上一版本获取到pubip的配置
	JAVA_OPTS="$JAVA_OPTS -Xms2048m -Xmx2048m -Xss512k"
	YP_JVM_GC_PERM_SIZE=256m
	YP_JVM_GC_MAX_PERM_SIZE=512m
fi


# 根据不同的jdk版本设置不同的永久带参数1.8以上用Metaspace，以下用Perm
# jdk 1.8、1.9启用感知容器的资源限制，如果是用G1算法则压缩String
if [ "${JAVA_VERSION:0:3}" == "1.8" -o "${JAVA_VERSION:0:3}" == "1.9" ]; then
	JAVA_OPTS="$JAVA_OPTS -XX:MetaspaceSize=$YP_JVM_GC_PERM_SIZE -XX:MaxMetaspaceSize=$YP_JVM_GC_MAX_PERM_SIZE"
	# 暂时报错，先去掉 Unrecognized VM option 'UseCGroupMemoryLimitForHeap'
	JAVA_OPTS="$JAVA_OPTS -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap"
	if [ "${YP_JVM_GC_ALG}" == "G1" ]; then
		JAVA_OPTS="$JAVA_OPTS -XX:+UseStringDeduplication"
	fi
else
	JAVA_OPTS="$JAVA_OPTS -XX:PermSize=$YP_JVM_GC_PERM_SIZE -XX:MaxPermSize=$YP_JVM_GC_MAX_PERM_SIZE"
fi
echo "JAVA_VERSION=$JAVA_VERSION"
echo "YP_JVM_RESOURCE_CPU=$YP_JVM_RESOURCE_CPU"
echo "YP_JVM_RESOURCE_MEMORY=$YP_JVM_RESOURCE_MEMORY"
echo "JAVA_OPTS=$JAVA_OPTS"