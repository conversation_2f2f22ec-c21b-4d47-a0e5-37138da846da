# 聚合支付AT管理系统 - 数据流架构图

## 数据流概述

本文档详细描述了聚合支付AT管理系统中的数据流转过程，包括商户进件流程、挂靠申请流程、消息处理流程等核心业务场景的数据流向。

## 核心业务数据流

### 1. 商户进件申请数据流

```mermaid
graph LR
    A[商户发起进件申请] --> B[Hessian接口接收]
    B --> C{参数验证}
    C -->|验证失败| D[返回错误信息]
    C -->|验证成功| E[EntryApplyFacade]
    
    E --> F[业务逻辑处理]
    F --> G[商户信息验证]
    G --> H[渠道信息获取]
    H --> I[进件数据转换]
    
    I --> J{选择渠道类型}
    J -->|微信直连| K[WechatDirectConvert]
    J -->|微信间连| L[WechatIndirectConvert]
    J -->|支付宝间连| M[AliIndirectConvert]
    J -->|数字货币| N[DigitalCurrencyConvert]
    
    K --> O[调用渠道服务]
    L --> O
    M --> O
    N --> O
    
    O --> P[保存进件记录]
    P --> Q[(数据库存储)]
    
    O --> R[发送异步消息]
    R --> S[RocketMQ]
    S --> T[进件结果监听器]
    
    T --> U[更新进件状态]
    U --> Q
    
    P --> V[返回申请结果]
    V --> W[响应客户端]
    
    subgraph "缓存层"
        X[商户信息缓存]
        Y[渠道配置缓存]
        Z[业务规则缓存]
    end
    
    G --> X
    H --> Y
    F --> Z
```

### 2. 挂靠申请数据流

```mermaid
graph LR
    A[商户发起挂靠申请] --> B[AnchoredApplyFacade]
    B --> C[挂靠资格验证]
    C --> D[关联商户信息查询]
    D --> E[挂靠配置获取]
    
    E --> F{挂靠类型判断}
    F -->|普通挂靠| G[NormalAnchoredHandler]
    F -->|特殊挂靠| H[SpecialAnchoredHandler]
    
    G --> I[构建挂靠申请]
    H --> I
    
    I --> J[调用挂靠服务]
    J --> K[外部挂靠系统]
    K --> L[挂靠结果回调]
    
    L --> M[RocketMQ消息]
    M --> N[挂靠结果监听器]
    N --> O[更新挂靠状态]
    
    O --> P[(数据库更新)]
    I --> Q[保存申请记录]
    Q --> P
    
    subgraph "业务流程引擎"
        R[FlowBuilder]
        S[FlowNode]
        T[FlowDecorator]
    end
    
    I --> R
    R --> S
    S --> T
    
    subgraph "数据转换层"
        U[AnchoredResultConvert]
        V[ConvertFactory]
    end
    
    L --> U
    U --> V
```

### 3. 消息处理数据流

```mermaid
graph TD
    subgraph "消息生产端 (Message Producers)"
        A[业务系统]
        B[渠道系统]
        C[外部合作方]
    end
    
    subgraph "RocketMQ集群"
        D[NameServer集群]
        E[Broker集群]
        
        subgraph "Topics"
            F[CHANNEL_ENTRY_RESULT_TOPIC<br/>渠道进件结果]
            G[ANCHORED_RESULT_TOPIC<br/>挂靠结果]
            H[CHANNEL_DIVIDE_CREATE_RESULT_TOPIC<br/>分账创建结果]
            I[CHANNEL_TERMINAL_REPORT_TOPIC<br/>终端报备结果]
            J[APP_ID_CONFIG_TOPIC<br/>AppId配置]
            K[ATTACH_SYNC_TOPIC<br/>关联同步]
        end
    end
    
    subgraph "消息消费端 (Message Consumers)"
        L[ChannelEntryResultListener<br/>进件结果监听器]
        M[EntryAnchoredCallBackListener<br/>挂靠回调监听器]
        N[DivideReceiverCallBackListener<br/>分账方回调监听器]
        O[TerminalReportCallBackListener<br/>终端报备回调监听器]
        P[ChannelAppIdBindListener<br/>AppId绑定监听器]
        Q[ChannelAttachListener<br/>关联监听器]
    end
    
    subgraph "业务处理层"
        R[EntryApplyBiz<br/>进件业务处理]
        S[AnchoredApplyBiz<br/>挂靠业务处理]
        T[DisposalBiz<br/>处罚业务处理]
        U[AuthBiz<br/>认证业务处理]
    end
    
    subgraph "数据持久化"
        V[(主数据库)]
        W[(备份数据库)]
        X[Redis缓存]
        Y[日志存储]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
    E --> K
    
    F --> L
    G --> M
    H --> N
    I --> O
    J --> P
    K --> Q
    
    L --> R
    M --> S
    N --> T
    O --> T
    P --> U
    Q --> R
    
    R --> V
    S --> V
    T --> V
    U --> V
    
    V --> W
    R --> X
    S --> X
    T --> X
    U --> X
    
    L --> Y
    M --> Y
    N --> Y
    O --> Y
```

## 外部服务集成数据流

### 1. 商户平台服务集成

```mermaid
graph LR
    A[AT管理系统] --> B[商户平台Facade]
    B --> C[Hessian/RPC调用]
    C --> D[商户平台服务]
    
    D --> E{商户信息查询}
    E -->|查询成功| F[返回商户信息]
    E -->|查询失败| G[返回错误信息]
    
    F --> H[商户信息缓存]
    G --> I[异常处理]
    
    H --> J[业务逻辑处理]
    I --> K[错误日志记录]
    
    subgraph "数据格式转换"
        L[MerchantInfoConvert]
        M[ErrorResponseConvert]
    end
    
    F --> L
    G --> M
    L --> J
    M --> K
```

### 2. 渠道直连服务集成

```mermaid
graph LR
    A[进件申请] --> B[渠道选择策略]
    B --> C{渠道类型}
    
    C -->|微信| D[微信直连服务]
    C -->|支付宝| E[支付宝直连服务]
    C -->|银联| F[银联直连服务]
    
    D --> G[微信API调用]
    E --> H[支付宝API调用]
    F --> I[银联API调用]
    
    G --> J[微信响应处理]
    H --> K[支付宝响应处理]
    I --> L[银联响应处理]
    
    J --> M[统一结果转换]
    K --> M
    L --> M
    
    M --> N[结果回调处理]
    N --> O[数据库状态更新]
    
    subgraph "异常处理"
        P[重试机制]
        Q[降级处理]
        R[熔断保护]
    end
    
    G --> P
    H --> P
    I --> P
    
    P --> Q
    Q --> R
```

## 数据库访问数据流

### 1. 读写分离数据流

```mermaid
graph TD
    subgraph "应用层"
        A[业务服务层]
    end
    
    subgraph "数据访问层"
        B[EntryDao]
        C[AnchoredDao]
        D[AuthDao]
        E[ChannelDao]
    end
    
    subgraph "数据源路由"
        F{读写分离路由}
    end
    
    subgraph "数据库集群"
        G[(主数据库<br/>写操作)]
        H[(从数据库1<br/>读操作)]
        I[(从数据库2<br/>读操作)]
    end
    
    subgraph "缓存层"
        J[一级缓存<br/>MyBatis]
        K[二级缓存<br/>Redis]
        L[应用缓存<br/>SmartCache]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    
    B --> F
    C --> F
    D --> F
    E --> F
    
    F -->|写操作| G
    F -->|读操作| H
    F -->|读操作| I
    
    B --> J
    C --> J
    D --> J
    E --> J
    
    J --> K
    K --> L
    
    G --> H
    G --> I
```

### 2. 事务处理数据流

```mermaid
graph LR
    A[业务方法调用] --> B[@Transactional注解]
    B --> C[事务管理器]
    C --> D[开始事务]
    
    D --> E[执行业务逻辑]
    E --> F[数据库操作1]
    F --> G[数据库操作2]
    G --> H[数据库操作N]
    
    H --> I{业务执行结果}
    I -->|成功| J[提交事务]
    I -->|失败| K[回滚事务]
    
    J --> L[释放连接]
    K --> M[异常处理]
    M --> L
    
    L --> N[返回结果]
    
    subgraph "事务传播机制"
        O[REQUIRED]
        P[REQUIRES_NEW]
        Q[SUPPORTS]
    end
    
    B --> O
    B --> P
    B --> Q
```

## 缓存数据流

### 1. 多级缓存数据流

```mermaid
graph TD
    A[客户端请求] --> B[应用服务]
    B --> C{本地缓存检查}
    
    C -->|缓存命中| D[返回缓存数据]
    C -->|缓存未命中| E{Redis缓存检查}
    
    E -->|缓存命中| F[更新本地缓存]
    E -->|缓存未命中| G[数据库查询]
    
    F --> H[返回Redis数据]
    G --> I[查询结果]
    
    I --> J[更新Redis缓存]
    J --> K[更新本地缓存]
    K --> L[返回数据库数据]
    
    subgraph "缓存策略"
        M[LRU淘汰策略]
        N[TTL过期策略]
        O[缓存穿透保护]
    end
    
    subgraph "缓存更新"
        P[主动更新]
        Q[被动失效]
        R[定时刷新]
    end
    
    C --> M
    E --> N
    G --> O
    
    I --> P
    J --> Q
    K --> R
```

### 2. 缓存一致性保证

```mermaid
graph LR
    A[数据更新操作] --> B[数据库更新]
    B --> C{更新成功?}
    
    C -->|成功| D[删除相关缓存]
    C -->|失败| E[回滚操作]
    
    D --> F[发送缓存失效消息]
    F --> G[MQ消息队列]
    G --> H[其他节点缓存失效]
    
    H --> I[缓存重新加载]
    I --> J[数据一致性保证]
    
    subgraph "一致性策略"
        K[Cache-Aside模式]
        L[Write-Through模式]
        M[Write-Behind模式]
    end
    
    D --> K
    F --> L
    I --> M
```

## 日志数据流

### 1. 日志收集数据流

```mermaid
graph TD
    subgraph "应用层日志"
        A[业务日志]
        B[访问日志]
        C[错误日志]
        D[性能日志]
    end
    
    subgraph "日志收集层"
        E[Log4j2配置]
        F[Fluentd Agent]
        G[日志文件]
    end
    
    subgraph "日志传输层"
        H[Fluentd Forwarder]
        I[日志聚合服务]
    end
    
    subgraph "日志存储层"
        J[Elasticsearch集群]
        K[日志索引]
        L[历史数据归档]
    end
    
    subgraph "日志分析层"
        M[Kibana仪表板]
        N[日志查询]
        O[监控告警]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    
    H --> I
    I --> J
    J --> K
    K --> L
    
    J --> M
    M --> N
    M --> O
```

## 监控数据流

### 1. 系统监控数据流

```mermaid
graph LR
    subgraph "数据采集层"
        A[JVM指标]
        B[应用指标]
        C[系统指标]
        D[业务指标]
    end
    
    subgraph "数据处理层"
        E[Prometheus Server]
        F[指标聚合]
        G[数据存储]
    end
    
    subgraph "数据展示层"
        H[Grafana仪表板]
        I[实时监控]
        J[历史趋势]
    end
    
    subgraph "告警处理层"
        K[告警规则引擎]
        L[告警通知]
        M[告警收敛]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    
    H --> I
    H --> J
    
    F --> K
    K --> L
    L --> M
```

## 数据安全流

### 1. 数据加密传输流

```mermaid
graph LR
    A[客户端数据] --> B[数据脱敏处理]
    B --> C[SM4对称加密]
    C --> D[HTTPS传输]
    
    D --> E[服务端接收]
    E --> F[SSL证书验证]
    F --> G[数据解密]
    G --> H[业务处理]
    
    H --> I[响应数据加密]
    I --> J[HTTPS返回]
    J --> K[客户端解密]
    
    subgraph "加密算法"
        L[SM2非对称加密]
        M[SM3哈希算法]
        N[SM4对称加密]
    end
    
    subgraph "密钥管理"
        O[密钥生成]
        P[密钥分发]
        Q[密钥轮换]
    end
    
    C --> N
    F --> L
    B --> M
    
    N --> O
    L --> P
    M --> Q
```

### 2. 数据访问控制流

```mermaid
graph TD
    A[用户请求] --> B[身份认证]
    B --> C{认证结果}
    
    C -->|认证失败| D[拒绝访问]
    C -->|认证成功| E[权限验证]
    
    E --> F{权限检查}
    F -->|权限不足| G[访问拒绝]
    F -->|权限充足| H[数据访问]
    
    H --> I[数据脱敏]
    I --> J[审计日志]
    J --> K[返回结果]
    
    subgraph "权限模型"
        L[RBAC角色权限]
        M[ABAC属性权限]
        N[数据权限]
    end
    
    E --> L
    F --> M
    I --> N
```

## 性能优化数据流

### 1. 数据库性能优化

```mermaid
graph LR
    A[SQL请求] --> B[SQL解析]
    B --> C{查询缓存}
    
    C -->|缓存命中| D[返回缓存结果]
    C -->|缓存未命中| E[查询优化器]
    
    E --> F[执行计划生成]
    F --> G[索引选择]
    G --> H[数据访问]
    
    H --> I[结果集处理]
    I --> J[缓存更新]
    J --> K[返回结果]
    
    subgraph "优化策略"
        L[索引优化]
        M[分页查询]
        N[批量操作]
        O[连接池管理]
    end
    
    G --> L
    A --> M
    H --> N
    B --> O
```

### 2. 接口性能优化

```mermaid
graph LR
    A[接口请求] --> B[参数验证]
    B --> C[业务逻辑处理]
    C --> D{是否需要外部调用}
    
    D -->|是| E[异步处理]
    D -->|否| F[同步处理]
    
    E --> G[消息队列]
    G --> H[异步回调]
    
    F --> I[数据库操作]
    I --> J[结果封装]
    
    H --> K[状态更新]
    J --> L[响应返回]
    K --> L
    
    subgraph "性能策略"
        M[并发控制]
        N[限流降级]
        O[超时控制]
        P[资源隔离]
    end
    
    C --> M
    E --> N
    F --> O
    G --> P
```

---

*文档生成时间: 2025-07-28*
*系统版本: 1.0-SNAPSHOT*