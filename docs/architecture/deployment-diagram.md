# 聚合支付AT管理系统 - 部署架构图

## 部署概述

本文档描述了聚合支付AT管理系统在生产环境中的部署架构，包括网络拓扑、服务器配置、数据库部署、中间件配置等关键部署信息。

## 生产环境部署架构

```mermaid
graph TD
    subgraph "外网访问层 (External Network)"
        ELB[企业级负载均衡器<br/>Enterprise Load Balancer]
        CDN[内容分发网络<br/>Content Delivery Network]
        WAF[Web应用防火墙<br/>Web Application Firewall]
    end
    
    subgraph "DMZ区域 (DMZ Zone)"
        NGINX1[Nginx反向代理1<br/>Port: 80/443]
        NGINX2[Nginx反向代理2<br/>Port: 80/443]
        NGINX3[Nginx反向代理N<br/>Port: 80/443]
    end
    
    subgraph "应用服务器集群 (Application Cluster)"
        subgraph "应用节点1 (App Node 1)"
            APP1[aggregation-config-hessian<br/>Port: 8080<br/>JVM: -Xmx2048m -Xms2048m]
            HESSIAN1[Hessian服务<br/>Port: 8080]
            DUBBO1[Dubbo/Trix协议<br/>Port: 8089]
        end
        
        subgraph "应用节点2 (App Node 2)"
            APP2[aggregation-config-hessian<br/>Port: 8080<br/>JVM: -Xmx2048m -Xms2048m]
            HESSIAN2[Hessian服务<br/>Port: 8080]
            DUBBO2[Dubbo/Trix协议<br/>Port: 8089]
        end
        
        subgraph "应用节点N (App Node N)"
            APPN[aggregation-config-hessian<br/>Port: 8080<br/>JVM: -Xmx2048m -Xms2048m]
            HESSIANN[Hessian服务<br/>Port: 8080]
            DUBBON[Dubbo/Trix协议<br/>Port: 8089]
        end
    end
    
    subgraph "数据库层 (Database Layer)"
        subgraph "DB2集群 (DB2 Cluster)"
            DB2_PRIMARY[(DB2主库<br/>AGGREGATE_DB2<br/>Port: 50000)]
            DB2_STANDBY[(DB2备库<br/>Port: 50000)]
            DB2_READ[(DB2只读库<br/>Port: 50000)]
        end
    end
    
    subgraph "缓存层 (Cache Layer)"
        subgraph "Redis集群 (Redis Cluster)"
            REDIS1[(Redis节点1<br/>Port: 6379)]
            REDIS2[(Redis节点2<br/>Port: 6379)]
            REDIS3[(Redis节点3<br/>Port: 6379)]
            REDIS_SENTINEL[Redis哨兵<br/>Port: 26379]
        end
    end
    
    subgraph "消息队列层 (Message Queue Layer)"
        subgraph "RocketMQ集群 (RocketMQ Cluster)"
            NAMESERVER1[NameServer1<br/>Port: 9876]
            NAMESERVER2[NameServer2<br/>Port: 9876]
            BROKER1[Broker1-Master<br/>Port: 10911]
            BROKER2[Broker1-Slave<br/>Port: 10911]
            BROKER3[Broker2-Master<br/>Port: 10911]
            BROKER4[Broker2-Slave<br/>Port: 10911]
        end
    end
    
    subgraph "配置中心 (Configuration Center)"
        YEEWORKS1[YeeWorks配置中心1<br/>Port: 8080]
        YEEWORKS2[YeeWorks配置中心2<br/>Port: 8080]
        ZOOKEEPER1[ZooKeeper1<br/>Port: 2181]
        ZOOKEEPER2[ZooKeeper2<br/>Port: 2181]
        ZOOKEEPER3[ZooKeeper3<br/>Port: 2181]
    end
    
    subgraph "监控与日志 (Monitoring & Logging)"
        PROMETHEUS[Prometheus监控<br/>Port: 9090]
        GRAFANA[Grafana仪表板<br/>Port: 3000]
        FLUENTD1[Fluentd日志收集1<br/>Port: 24224]
        FLUENTD2[Fluentd日志收集2<br/>Port: 24224]
        ELASTICSEARCH[Elasticsearch<br/>Port: 9200]
        KIBANA[Kibana日志分析<br/>Port: 5601]
    end
    
    CDN --> WAF
    WAF --> ELB
    ELB --> NGINX1
    ELB --> NGINX2
    ELB --> NGINX3
    
    NGINX1 --> APP1
    NGINX2 --> APP2
    NGINX3 --> APPN
    
    APP1 --> HESSIAN1
    APP1 --> DUBBO1
    APP2 --> HESSIAN2
    APP2 --> DUBBO2
    APPN --> HESSIANN
    APPN --> DUBBON
    
    APP1 --> DB2_PRIMARY
    APP2 --> DB2_PRIMARY
    APPN --> DB2_PRIMARY
    
    DB2_PRIMARY --> DB2_STANDBY
    DB2_PRIMARY --> DB2_READ
    
    APP1 --> REDIS1
    APP2 --> REDIS2
    APPN --> REDIS3
    
    REDIS_SENTINEL --> REDIS1
    REDIS_SENTINEL --> REDIS2
    REDIS_SENTINEL --> REDIS3
    
    APP1 --> NAMESERVER1
    APP2 --> NAMESERVER2
    APPN --> NAMESERVER1
    
    NAMESERVER1 --> BROKER1
    NAMESERVER1 --> BROKER3
    NAMESERVER2 --> BROKER2
    NAMESERVER2 --> BROKER4
    
    APP1 --> YEEWORKS1
    APP2 --> YEEWORKS2
    APPN --> YEEWORKS1
    
    YEEWORKS1 --> ZOOKEEPER1
    YEEWORKS2 --> ZOOKEEPER2
    YEEWORKS1 --> ZOOKEEPER3
    
    APP1 --> FLUENTD1
    APP2 --> FLUENTD2
    APPN --> FLUENTD1
    
    FLUENTD1 --> ELASTICSEARCH
    FLUENTD2 --> ELASTICSEARCH
    
    ELASTICSEARCH --> KIBANA
    PROMETHEUS --> GRAFANA
```

## 容器化部署架构

```mermaid
graph TD
    subgraph "Kubernetes集群 (K8s Cluster)"
        subgraph "Master节点 (Master Nodes)"
            K8S_MASTER1[K8s Master 1<br/>API Server, etcd, Scheduler]
            K8S_MASTER2[K8s Master 2<br/>API Server, etcd, Scheduler]
            K8S_MASTER3[K8s Master 3<br/>API Server, etcd, Scheduler]
        end
        
        subgraph "Worker节点 (Worker Nodes)"
            subgraph "节点1 (Node 1)"
                POD1[aggregation-config-pod-1<br/>CPU: 2核, 内存: 4GB]
                SIDECAR1[Fluentd Sidecar<br/>日志收集]
            end
            
            subgraph "节点2 (Node 2)"
                POD2[aggregation-config-pod-2<br/>CPU: 2核, 内存: 4GB]
                SIDECAR2[Fluentd Sidecar<br/>日志收集]
            end
            
            subgraph "节点N (Node N)"
                PODN[aggregation-config-pod-N<br/>CPU: 2核, 内存: 4GB]
                SIDECARN[Fluentd Sidecar<br/>日志收集]
            end
        end
        
        subgraph "K8s服务 (K8s Services)"
            SVC[aggregation-config-service<br/>ClusterIP: ***********<br/>Port: 8080]
            INGRESS[Ingress Controller<br/>域名路由]
            HPA[Horizontal Pod Autoscaler<br/>自动扩缩容]
        end
        
        subgraph "配置管理 (Config Management)"
            CONFIGMAP[ConfigMap<br/>应用配置]
            SECRET[Secret<br/>敏感信息]
            PVC[PersistentVolumeClaim<br/>持久化存储]
        end
    end
    
    subgraph "外部依赖 (External Dependencies)"
        EXT_DB[(外部DB2数据库<br/>通过Service连接)]
        EXT_REDIS[(外部Redis集群<br/>通过Service连接)]
        EXT_MQ[(外部RocketMQ<br/>通过Service连接)]
        EXT_CONFIG[外部配置中心<br/>YeeWorks]
    end
    
    subgraph "镜像仓库 (Image Registry)"
        HARBOR[Harbor私有仓库<br/>镜像存储与分发]
    end
    
    INGRESS --> SVC
    SVC --> POD1
    SVC --> POD2
    SVC --> PODN
    
    POD1 --> SIDECAR1
    POD2 --> SIDECAR2
    PODN --> SIDECARN
    
    HPA --> POD1
    HPA --> POD2
    HPA --> PODN
    
    POD1 --> CONFIGMAP
    POD1 --> SECRET
    POD1 --> PVC
    
    POD1 --> EXT_DB
    POD1 --> EXT_REDIS
    POD1 --> EXT_MQ
    POD1 --> EXT_CONFIG
    
    HARBOR --> POD1
    HARBOR --> POD2
    HARBOR --> PODN
```

## 网络架构

```mermaid
graph TD
    subgraph "网络分层 (Network Layers)"
        subgraph "公网层 (Public Network)"
            INTERNET[互联网<br/>0.0.0.0/0]
            PUBLIC_LB[公网负载均衡<br/>公网IP]
        end
        
        subgraph "DMZ网段 (DMZ Network)"
            DMZ_SUBNET[DMZ子网<br/>************/24]
            DMZ_PROXY[反向代理服务器<br/>*************-20]
        end
        
        subgraph "应用网段 (Application Network)"
            APP_SUBNET[应用子网<br/>************/24]
            APP_SERVERS[应用服务器<br/>*************-50]
        end
        
        subgraph "数据网段 (Database Network)"
            DB_SUBNET[数据库子网<br/>************/24]
            DB_SERVERS[数据库服务器<br/>*************-20]
        end
        
        subgraph "缓存网段 (Cache Network)"
            CACHE_SUBNET[缓存子网<br/>************/24]
            CACHE_SERVERS[缓存服务器<br/>*************-20]
        end
        
        subgraph "消息网段 (Message Network)"
            MQ_SUBNET[消息队列子网<br/>************/24]
            MQ_SERVERS[消息队列服务器<br/>*************-20]
        end
        
        subgraph "管理网段 (Management Network)"
            MGMT_SUBNET[管理子网<br/>192.168.60.0/24]
            MGMT_SERVERS[管理服务器<br/>192.168.60.10-20]
        end
    end
    
    subgraph "安全策略 (Security Policies)"
        FIREWALL[防火墙规则]
        ACL[访问控制列表]
        VPN[VPN接入]
        BASTION[堡垒机]
    end
    
    INTERNET --> PUBLIC_LB
    PUBLIC_LB --> DMZ_SUBNET
    DMZ_SUBNET --> DMZ_PROXY
    
    DMZ_PROXY --> APP_SUBNET
    APP_SUBNET --> APP_SERVERS
    
    APP_SERVERS --> DB_SUBNET
    DB_SUBNET --> DB_SERVERS
    
    APP_SERVERS --> CACHE_SUBNET
    CACHE_SUBNET --> CACHE_SERVERS
    
    APP_SERVERS --> MQ_SUBNET
    MQ_SUBNET --> MQ_SERVERS
    
    MGMT_SUBNET --> MGMT_SERVERS
    MGMT_SERVERS --> APP_SERVERS
    MGMT_SERVERS --> DB_SERVERS
    MGMT_SERVERS --> CACHE_SERVERS
    MGMT_SERVERS --> MQ_SERVERS
    
    FIREWALL --> DMZ_SUBNET
    FIREWALL --> APP_SUBNET
    FIREWALL --> DB_SUBNET
    ACL --> APP_SERVERS
    VPN --> MGMT_SUBNET
    BASTION --> MGMT_SUBNET
```

## 服务器配置规格

### 应用服务器配置

| 组件 | CPU | 内存 | 磁盘 | 网络 | 数量 |
|------|-----|------|------|------|------|
| aggregation-config-hessian | 4核 | 8GB | 100GB SSD | 1Gbps | 3+ |
| Nginx反向代理 | 2核 | 4GB | 50GB SSD | 1Gbps | 2+ |

### 数据库服务器配置

| 组件 | CPU | 内存 | 磁盘 | 网络 | 数量 |
|------|-----|------|------|------|------|
| DB2主库 | 8核 | 32GB | 500GB SSD | 10Gbps | 1 |
| DB2备库 | 8核 | 32GB | 500GB SSD | 10Gbps | 1 |
| DB2只读库 | 4核 | 16GB | 500GB SSD | 1Gbps | 2+ |

### 中间件服务器配置

| 组件 | CPU | 内存 | 磁盘 | 网络 | 数量 |
|------|-----|------|------|------|------|
| Redis节点 | 4核 | 16GB | 200GB SSD | 1Gbps | 3+ |
| RocketMQ Broker | 4核 | 8GB | 200GB SSD | 1Gbps | 4+ |
| RocketMQ NameServer | 2核 | 4GB | 50GB SSD | 1Gbps | 2+ |

## JVM配置参数

### 生产环境JVM参数

```bash
# 堆内存配置
-Xms2048m
-Xmx2048m
-XX:MaxPermSize=512m

# 垃圾回收配置
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m

# 内存溢出处理
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/opt/logs/dump/

# GC日志配置
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/opt/logs/gc/gc.log

# 远程调试配置（开发环境）
-Xdebug
-Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8080

# 应用配置
-Dappname=aggregation-config-hessian
-DYP_DATA_CENTER=production
-DYP_APP_NAME=aggregation-config-hessian
```

## 部署脚本示例

### Docker部署脚本

```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine

LABEL maintainer="<EMAIL>"
LABEL version="1.0-SNAPSHOT"

# 创建应用目录
RUN mkdir -p /opt/app /opt/logs /opt/config

# 复制应用文件
COPY target/aggregation-config-hessian.jar /opt/app/app.jar
COPY src/main/resources/runtimecfg/ /opt/config/

# 设置环境变量
ENV JAVA_OPTS="-Xms2048m -Xmx2048m -XX:+UseG1GC"
ENV APP_NAME="aggregation-config-hessian"
ENV DATA_CENTER="production"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 暴露端口
EXPOSE 8080 8089

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -Dappname=$APP_NAME -DYP_DATA_CENTER=$DATA_CENTER -jar /opt/app/app.jar"]
```

### Kubernetes部署配置

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aggregation-config-hessian
  namespace: yeepay-production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: aggregation-config-hessian
  template:
    metadata:
      labels:
        app: aggregation-config-hessian
    spec:
      containers:
      - name: aggregation-config-hessian
        image: harbor.yeepay.com/yeepay/aggregation-config-hessian:1.0-SNAPSHOT
        ports:
        - containerPort: 8080
        - containerPort: 8089
        env:
        - name: JAVA_OPTS
          value: "-Xms2048m -Xmx2048m -XX:+UseG1GC"
        - name: APP_NAME
          value: "aggregation-config-hessian"
        - name: DATA_CENTER
          value: "production"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: config-volume
          mountPath: /opt/config
        - name: logs-volume
          mountPath: /opt/logs
      volumes:
      - name: config-volume
        configMap:
          name: aggregation-config-configmap
      - name: logs-volume
        persistentVolumeClaim:
          claimName: aggregation-config-logs-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: aggregation-config-service
  namespace: yeepay-production
spec:
  selector:
    app: aggregation-config-hessian
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: dubbo
    port: 8089
    targetPort: 8089
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: aggregation-config-hpa
  namespace: yeepay-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: aggregation-config-hessian
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 监控与告警

### 监控指标

1. **应用指标**
   - JVM堆内存使用率
   - GC次数和时间
   - 线程数量
   - HTTP请求响应时间
   - 业务处理成功率

2. **系统指标**
   - CPU使用率
   - 内存使用率
   - 磁盘IO
   - 网络流量

3. **业务指标**
   - 进件申请数量
   - 挂靠申请数量
   - 消息队列堆积数量
   - 数据库连接池使用情况

### 告警策略

1. **严重告警**
   - 应用服务不可用
   - 数据库连接失败
   - 内存使用率 > 90%
   - 磁盘使用率 > 95%

2. **警告告警**
   - 响应时间 > 5秒
   - CPU使用率 > 80%
   - 内存使用率 > 80%
   - 消息堆积 > 1000条

## 安全考虑

1. **网络安全**
   - 防火墙规则配置
   - VPN安全接入
   - SSL/TLS加密传输

2. **应用安全**
   - 国密算法支持
   - 数据脱敏处理
   - 访问权限控制

3. **运维安全**
   - 堡垒机统一管理
   - 操作审计日志
   - 敏感信息加密存储

---

*文档生成时间: 2025-07-28*
*系统版本: 1.0-SNAPSHOT*