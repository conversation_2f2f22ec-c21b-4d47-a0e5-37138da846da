# 聚合支付AT管理系统架构文档

## 系统概述

聚合支付AT管理系统是易宝支付的核心配置管理服务，主要负责聚合支付场景下的商户进件、挂靠申请、渠道配置等业务功能。系统采用分层架构设计，基于Spring Boot + Hessian + MyBatis技术栈构建。

## 技术栈

- **框架**: Spring Boot 2.7.18
- **Java版本**: JDK 1.8
- **RPC协议**: Hessian 4.3.5
- **SOA协议**: Dubbo3 (trix协议)
- **数据库**: DB2
- **连接池**: Druid 1.2.24
- **ORM**: MyBatis 3.5.15 + tk.mybatis
- **消息队列**: RocketMQ 5.0.0
- **缓存**: Redis + SmartCache
- **对象映射**: MapStruct 1.6.3
- **构建工具**: Maven

## 系统架构图

```mermaid
graph TD
    A[客户端请求] --> B[负载均衡器]
    B --> C[Hessian服务层]
    C --> D[Facade接口层]
    D --> E[Core业务层]
    E --> F[数据访问层]
    F --> G[(DB2数据库)]
    
    C --> H[SOA/Dubbo服务]
    E --> I[外部服务集成]
    E --> J[消息队列 RocketMQ]
    E --> K[缓存层 Redis/SmartCache]
    
    I --> L[商户平台服务]
    I --> M[业务管理服务]
    I --> N[银行合作服务]
    I --> O[渠道直连服务]
    
    J --> P[进件结果回调]
    J --> Q[挂靠结果回调]
    J --> R[分账方回调]
    J --> S[终端报备回调]
    
    subgraph "Hessian服务模块"
        C
        T[SpringBootInitializer]
        U[DataSourceConfig]
        V[RocketMqConfig]
        W[SoaProtocolConfig]
    end
    
    subgraph "配置管理"
        X[配置中心 YeeWorks]
        Y[SmartCache配置]
        Z[Redis配置]
        AA[加密配置]
    end
```

## 组件架构图

```mermaid
graph TD
    subgraph "Facade层 - RPC接口"
        A1[EntryApplyFacade 进件申请]
        A2[AnchoredApplyFacade 挂靠申请]
        A3[AuthFacade 认证管理]
        A4[DisposalFacade 处罚管理]
        A5[ChannelNoFacade 渠道号管理]
        A6[ControlConfigFacade 控制配置]
        A7[ScheduleFacade 调度服务]
    end
    
    subgraph "Core业务层"
        B1[EntryApplyFacadeImpl]
        B2[AnchoredApplyFacadeImpl]
        B3[AuthFacadeImpl]
        B4[DisposalFacadeImpl]
        B5[ChannelNoFacadeImpl]
        B6[AtManageBiz AT管理业务]
        B7[DisposalBiz 处罚业务]
        B8[FlowEngine 流程引擎]
        B9[ConvertFactory 转换工厂]
        B10[ExternalService 外部服务]
    end
    
    subgraph "数据层"
        C1[EntryDao 进件数据]
        C2[AnchoredDao 挂靠数据]
        C3[AuthDao 认证数据]
        C4[ChannelDao 渠道数据]
        C5[ConfigDao 配置数据]
    end
    
    subgraph "消息处理"
        D1[ChannelEntryResultListener]
        D2[EntryAnchoredCallBackListener]
        D3[DivideReceiverCallBackListener]
        D4[TerminalReportCallBackListener]
        D5[ChannelAppIdBindListener]
        D6[ChannelAttachListener]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    A5 --> B5
    
    B1 --> B6
    B2 --> B6
    B3 --> B6
    B4 --> B7
    B5 --> B6
    
    B6 --> C1
    B6 --> C2
    B6 --> C3
    B6 --> C4
    B7 --> C5
    
    B1 --> B8
    B2 --> B8
    B6 --> B9
    B6 --> B10
    
    B10 --> D1
    B10 --> D2
    B10 --> D3
    B10 --> D4
    B10 --> D5
    B10 --> D6
```

## 部署架构图

```mermaid
graph TD
    subgraph "生产环境"
        A[负载均衡器 LB]
        
        subgraph "应用集群"
            B1[App Server 1<br/>aggregation-config-hessian]
            B2[App Server 2<br/>aggregation-config-hessian]
            B3[App Server N<br/>aggregation-config-hessian]
        end
        
        subgraph "数据层"
            C[(DB2数据库集群)]
            D[Redis集群]
            E[RocketMQ集群]
        end
        
        subgraph "配置与监控"
            F[YeeWorks配置中心]
            G[SmartCache智能缓存]
            H[日志聚合 Fluentd]
        end
        
        subgraph "外部依赖"
            I[商户平台服务]
            J[业务管理服务]
            K[银行合作服务]
            L[渠道直连服务]
        end
    end
    
    subgraph "网络协议"
        M[Hessian RPC]
        N[Dubbo/Trix协议]
        O[HTTP REST]
    end
    
    A --> B1
    A --> B2
    A --> B3
    
    B1 --> C
    B2 --> C
    B3 --> C
    
    B1 --> D
    B2 --> D
    B3 --> D
    
    B1 --> E
    B2 --> E
    B3 --> E
    
    B1 --> F
    B2 --> F
    B3 --> F
    
    B1 --> G
    B2 --> G
    B3 --> G
    
    B1 --> I
    B1 --> J
    B1 --> K
    B1 --> L
    
    B1 -.-> M
    B1 -.-> N
    B1 -.-> O
```

## 数据流架构图

```mermaid
graph LR
    A[商户进件请求] --> B[Hessian接口]
    B --> C[EntryApplyFacade]
    C --> D[参数验证]
    D --> E[业务逻辑处理]
    E --> F[外部服务调用]
    F --> G[数据持久化]
    
    E --> H[消息发送]
    H --> I[RocketMQ]
    I --> J[异步处理]
    
    F --> K[商户平台服务]
    F --> L[渠道直连服务]
    
    K --> M[商户信息验证]
    L --> N[渠道进件申请]
    
    N --> O[渠道返回结果]
    O --> P[结果回调消息]
    P --> I
    
    J --> Q[更新进件状态]
    Q --> R[(数据库)]
    
    G --> R
    
    subgraph "缓存层"
        S[商户信息缓存]
        T[配置信息缓存]
        U[渠道信息缓存]
    end
    
    E --> S
    E --> T
    E --> U
    
    subgraph "监控与日志"
        V[业务日志]
        W[访问日志]
        X[性能监控]
    end
    
    B --> V
    B --> W
    E --> X
```

## 关键特性

### 1. 分层架构设计
- **Hessian层**: 负责启动类和基础数据源配置
- **Facade层**: RPC双向包，提供给外部调用
- **Core层**: 核心业务逻辑层
- **Share-Kernel层**: 共享内核组件

### 2. 多模块设计
- `aggregation-config-hessian`: 服务启动和配置模块
- `aggregation-config-facade`: 对外接口定义模块
- `aggregation-config-core`: 核心业务实现模块
- `aggregation-config-share-kernel`: 共享组件模块

### 3. 异步消息处理
- 基于RocketMQ实现异步消息处理
- 支持多种业务场景的消息监听
- 完善的重试和异常处理机制

### 4. 外部服务集成
- 集成商户平台、业务管理、银行合作等多个外部服务
- 统一的服务调用和异常处理

### 5. 配置管理
- 集成YeeWorks配置中心
- 支持动态配置更新
- 多环境配置管理

## 安全特性

- 集成SM国密算法支持
- 数据脱敏和加密传输
- 完善的权限控制和审计

## 性能优化

- SmartCache智能缓存机制
- 数据库连接池优化
- 异步处理提升响应性能
- 影子表支持，降低生产环境影响

---

*文档生成时间: 2025-07-28*
*系统版本: 1.0-SNAPSHOT*