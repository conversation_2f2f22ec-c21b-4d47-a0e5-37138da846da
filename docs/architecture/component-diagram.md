# 聚合支付AT管理系统 - 组件架构图

## 组件概述

本文档详细描述了聚合支付AT管理系统的组件架构，展示各个组件之间的依赖关系和交互流程。

## 核心组件架构

```mermaid
graph TD
    subgraph "外部接口层 (Facade)"
        F1[EntryApplyFacade<br/>进件申请接口]
        F2[AnchoredApplyFacade<br/>挂靠申请接口]
        F3[AuthFacade<br/>认证管理接口]
        F4[DisposalFacade<br/>处罚管理接口]
        F5[ChannelNoFacade<br/>渠道号管理接口]
        F6[ControlConfigFacade<br/>控制配置接口]
        F7[AggConfigScheduleFacade<br/>调度配置接口]
        F8[DataClearFacade<br/>数据清理接口]
    end
    
    subgraph "业务实现层 (Core Implementation)"
        I1[EntryApplyFacadeImpl<br/>进件申请实现]
        I2[AnchoredApplyFacadeImpl<br/>挂靠申请实现]
        I3[AuthFacadeImpl<br/>认证管理实现]
        I4[DisposalFacadeImpl<br/>处罚管理实现]
        I5[ChannelNoFacadeImpl<br/>渠道号管理实现]
        I6[ControlConfigFacadeImpl<br/>控制配置实现]
        I7[AggConfigScheduleFacadeImpl<br/>调度配置实现]
        I8[DataClearFacadeImpl<br/>数据清理实现]
    end
    
    subgraph "业务逻辑层 (Business Logic)"
        B1[AtManageBiz<br/>AT管理业务逻辑]
        B2[EntryBiz<br/>进件业务逻辑]
        B3[AnchoredBiz<br/>挂靠业务逻辑]
        B4[DisposalBiz<br/>处罚业务逻辑]
        B5[AuthBiz<br/>认证业务逻辑]
    end
    
    subgraph "流程引擎层 (Flow Engine)"
        FE1[FlowBuilder<br/>流程构建器]
        FE2[FlowNode<br/>流程节点]
        FE3[FlowDecorator<br/>流程装饰器]
        FE4[NodeImpl<br/>节点实现]
    end
    
    subgraph "数据转换层 (Convert)"
        C1[EntryRequestConvert<br/>进件请求转换]
        C2[AnchoredResultConvert<br/>挂靠结果转换]
        C3[ChannelResultConvert<br/>渠道结果转换]
        C4[DisposalFacadeConvert<br/>处罚数据转换]
        C5[ConvertFactory<br/>转换工厂]
    end
    
    subgraph "外部服务层 (External Services)"
        E1[BusinessManageFacade<br/>业务管理服务]
        E2[MerchantPlatformFacade<br/>商户平台服务]
        E3[BankCooperFacade<br/>银行合作服务]
        E4[ChannelDirectConnFacade<br/>渠道直连服务]
    end
    
    subgraph "数据访问层 (Data Access)"
        D1[EntryDao<br/>进件数据访问]
        D2[AnchoredDao<br/>挂靠数据访问]
        D3[AuthDao<br/>认证数据访问]
        D4[ChannelDao<br/>渠道数据访问]
        D5[DisposalDao<br/>处罚数据访问]
        D6[ConfigDao<br/>配置数据访问]
    end
    
    F1 --> I1
    F2 --> I2
    F3 --> I3
    F4 --> I4
    F5 --> I5
    F6 --> I6
    F7 --> I7
    F8 --> I8
    
    I1 --> B1
    I1 --> B2
    I2 --> B1
    I2 --> B3
    I3 --> B5
    I4 --> B4
    I5 --> B1
    
    B1 --> FE1
    B2 --> FE1
    B3 --> FE1
    
    FE1 --> FE2
    FE2 --> FE3
    FE2 --> FE4
    
    I1 --> C1
    I2 --> C2
    I1 --> C3
    I4 --> C4
    C1 --> C5
    C2 --> C5
    C3 --> C5
    C4 --> C5
    
    B1 --> E1
    B1 --> E2
    B1 --> E3
    B1 --> E4
    
    B1 --> D1
    B2 --> D1
    B3 --> D2
    B4 --> D5
    B5 --> D3
    I5 --> D4
    I6 --> D6
```

## 消息处理组件

```mermaid
graph TD
    subgraph "消息生产者 (Message Producers)"
        P1[AggConfigAnchoredProducer<br/>聚合配置挂靠生产者]
    end
    
    subgraph "消息监听器 (Message Listeners)"
        L1[ChannelEntryResultListener<br/>渠道进件结果监听]
        L2[EntryAnchoredCallBackListener<br/>进件挂靠回调监听]
        L3[DivideReceiverCallBackListener<br/>分账方回调监听]
        L4[TerminalReportCallBackListener<br/>终端报备回调监听]
        L5[ChannelAppIdBindListener<br/>渠道AppId绑定监听]
        L6[ChannelAttachListener<br/>渠道关联监听]
        L7[ChannelAppidControlListener<br/>渠道Appid控制监听]
        L8[ChannelNoApplyResultListener<br/>渠道号申请结果监听]
    end
    
    subgraph "消息事件 (Message Events)"
        E1[ChannelEntryResultEvent<br/>渠道进件结果事件]
        E2[EntryAnchoredEvent<br/>进件挂靠事件]
        E3[DivideReceiverEvent<br/>分账方事件]
        E4[TerminalReportEvent<br/>终端报备事件]
        E5[ChannelAppIdBindEvent<br/>渠道AppId绑定事件]
        E6[ChannelAttachEvent<br/>渠道关联事件]
        E7[ChannelAppidControlEvent<br/>渠道Appid控制事件]
        E8[ChannelNoApplyResultEvent<br/>渠道号申请结果事件]
    end
    
    subgraph "RocketMQ Topics"
        T1[CHANNEL_ENTRY_RESULT_TOPIC<br/>渠道进件结果主题]
        T2[ANCHORED_RESULT_TOPIC<br/>挂靠结果主题]
        T3[CHANNEL_DIVIDE_CREATE_RESULT_TOPIC<br/>渠道分账创建结果主题]
        T4[CHANNEL_TERMINAL_REPORT_TOPIC<br/>渠道终端报备主题]
        T5[APP_ID_CONFIG_TOPIC<br/>AppId配置主题]
        T6[ATTACH_SYNC_TOPIC<br/>关联同步主题]
        T7[APP_ID_CONTROL_TOPIC<br/>AppId控制主题]
        T8[CHANNEL_NO_APPLY_RESULT_TOPIC<br/>渠道号申请结果主题]
    end
    
    P1 --> T2
    
    T1 --> L1
    T2 --> L2
    T3 --> L3
    T4 --> L4
    T5 --> L5
    T6 --> L6
    T7 --> L7
    T8 --> L8
    
    L1 --> E1
    L2 --> E2
    L3 --> E3
    L4 --> E4
    L5 --> E5
    L6 --> E6
    L7 --> E7
    L8 --> E8
```

## 工厂模式组件

```mermaid
graph TD
    subgraph "工厂组件 (Factory Components)"
        F1[ChannelEntryRequestConvertFactory<br/>渠道进件请求转换工厂]
        F2[AnchoredFactory<br/>挂靠工厂]
        F3[HandleFactory<br/>处理工厂]
        F4[StrategyFactory<br/>策略工厂]
    end
    
    subgraph "装饰器模式 (Decorator Pattern)"
        D1[AliIndirectChannelEntryRequestConvertImpl<br/>阿里间连渠道进件请求转换]
        D2[WechatDirectChannelEntryRequestConvertImpl<br/>微信直连渠道进件请求转换]
        D3[WechatIndirectChannelEntryRequestConvertImpl<br/>微信间连渠道进件请求转换]
        D4[WechatB2BChannelEntryRequestConvertImpl<br/>微信B2B渠道进件请求转换]
        D5[DigitalCurrencyChannelEntryRequestConvertImpl<br/>数字货币渠道进件请求转换]
    end
    
    subgraph "策略模式 (Strategy Pattern)"
        S1[BaseChannelEntryRequestConvert<br/>基础渠道进件请求转换]
        S2[ChannelEntryRequestConvert<br/>渠道进件请求转换]
    end
    
    subgraph "构建器模式 (Builder Pattern)"
        B1[AppIdMerchantBindBuilder<br/>AppId商户绑定构建器]
        B2[ControlConfigBuilder<br/>控制配置构建器]
    end
    
    F1 --> D1
    F1 --> D2
    F1 --> D3
    F1 --> D4
    F1 --> D5
    
    D1 --> S1
    D2 --> S1
    D3 --> S1
    D4 --> S1
    D5 --> S1
    
    S1 --> S2
    
    F2 --> B1
    F3 --> B1
    F4 --> B2
```

## 配置组件架构

```mermaid
graph TD
    subgraph "启动配置 (Startup Configuration)"
        SC1[SpringBootInitializer<br/>Spring Boot启动器]
        SC2[DataSourceConfig<br/>数据源配置]
        SC3[MyBatisConfig<br/>MyBatis配置]
        SC4[RocketMqConfig<br/>RocketMQ配置]
        SC5[SoaProtocolConfig<br/>SOA协议配置]
        SC6[AsyncConfig<br/>异步配置]
        SC7[ServletConfig<br/>Servlet配置]
        SC8[GlobalExceptionHandler<br/>全局异常处理器]
    end
    
    subgraph "运行时配置 (Runtime Configuration)"
        RC1[ConfigurationUtils<br/>配置工具]
        RC2[YeeworksConfigUtils<br/>Yeeworks配置工具]
        RC3[SmartCacheUtils<br/>智能缓存工具]
        RC4[RedisClientUtils<br/>Redis客户端工具]
        RC5[RemoteServiceFactory<br/>远程服务工厂]
        RC6[SMUtils<br/>国密工具]
    end
    
    subgraph "MyBatis组件 (MyBatis Components)"
        MB1[SqlPrintInterceptor<br/>SQL打印拦截器]
        MB2[ShadowTableInterceptor<br/>影子表拦截器]
        MB3[LocalDateTimeTypeHandler<br/>本地日期时间类型处理器]
    end
    
    SC1 --> RC1
    SC1 --> RC2
    SC1 --> RC3
    SC1 --> RC4
    SC1 --> RC5
    SC1 --> RC6
    
    SC2 --> MB1
    SC2 --> MB2
    SC2 --> MB3
    
    SC1 --> SC2
    SC1 --> SC3
    SC1 --> SC4
    SC1 --> SC5
    SC1 --> SC6
    SC1 --> SC7
    SC1 --> SC8
```

## 工具类组件

```mermaid
graph TD
    subgraph "核心工具类 (Core Utils)"
        U1[ConfigUtil<br/>配置工具类]
        U2[LocalDateTimeTypeHandler<br/>本地日期时间类型处理器]
    end
    
    subgraph "共享内核组件 (Share Kernel)"
        SK1[RocketMqConst<br/>RocketMQ常量]
        SK2[BizSceneEnum<br/>业务场景枚举]
        SK3[Constant<br/>系统常量]
        SK4[Enumerate<br/>枚举定义]
        SK5[Exception<br/>异常定义]
        SK6[Mask<br/>数据脱敏]
        SK7[Model<br/>模型定义]
        SK8[Serialize<br/>序列化工具]
        SK9[Util<br/>通用工具]
    end
    
    subgraph "业务对象 (Business Objects)"
        BO1[SceneAndActivityTypeBO<br/>场景和活动类型BO]
        BO2[ThirdPartyRecord<br/>第三方记录]
        BO3[AtMerchantInfoBO<br/>AT商户信息BO]
        BO4[ContactInfo<br/>联系信息]
        BO5[FinanceInstitutionBO<br/>金融机构BO]
        BO6[RegistrationCertificateBO<br/>注册证书BO]
        BO7[SettleAccountBO<br/>结算账户BO]
        BO8[SubjectInfo<br/>主体信息]
    end
    
    U1 --> SK1
    U1 --> SK2
    U2 --> SK7
    
    BO1 --> SK3
    BO2 --> SK4
    BO3 --> SK5
    BO4 --> SK6
    BO5 --> SK7
    BO6 --> SK8
    BO7 --> SK9
    BO8 --> SK9
```

## 组件依赖关系

### 层级依赖
1. **Facade层** → **Implementation层** → **Business层** → **Data层**
2. **Message层** → **Business层**
3. **External层** → **Business层**
4. **Convert层** → **Business层**

### 横向依赖
1. **Factory组件** 为其他组件提供实例创建
2. **Config组件** 为所有组件提供配置支持
3. **Utils组件** 为所有组件提供工具支持
4. **Share Kernel** 为所有模块提供共享组件

## 关键设计模式

### 1. 工厂模式 (Factory Pattern)
- `ChannelEntryRequestConvertFactory`: 根据渠道类型创建对应的转换器
- `AnchoredFactory`: 创建挂靠相关的处理器
- `RemoteServiceFactory`: 创建远程服务实例

### 2. 装饰器模式 (Decorator Pattern)
- 各种渠道特定的转换实现类装饰基础转换器
- 流程装饰器增强流程节点功能

### 3. 策略模式 (Strategy Pattern)
- 不同渠道的进件请求转换策略
- 不同业务场景的处理策略

### 4. 构建器模式 (Builder Pattern)
- `AppIdMerchantBindBuilder`: 构建AppId商户绑定关系
- `ControlConfigBuilder`: 构建控制配置
- `FlowBuilder`: 构建业务流程

### 5. 观察者模式 (Observer Pattern)
- 消息监听器监听各种业务事件
- 异步事件处理机制

---

*文档生成时间: 2025-07-28*
*系统版本: 1.0-SNAPSHOT*