# 聚合支付AT管理系统 (Aggregation Config <PERSON>)

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/yeepay/aggregation-config-hessian)
[![Java Version](https://img.shields.io/badge/Java-1.8-blue)](https://openjdk.java.net/projects/jdk8/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-green)](https://spring.io/projects/spring-boot)
[![License](https://img.shields.io/badge/License-YeePay-orange)](https://www.yeepay.com)

## 系统概述

聚合支付AT管理系统是易宝支付的核心配置管理服务，专门负责聚合支付场景下的商户进件、挂靠申请、渠道配置、认证管理等关键业务功能。系统采用现代化的分层架构设计，基于Spring Boot + Hessian + MyBatis技术栈构建，提供高性能、高可用的配置管理能力。

### 核心功能

- **商户进件管理**: 支持多渠道商户进件申请和状态管理
- **挂靠申请服务**: 提供商户挂靠申请和审核流程
- **渠道配置管理**: 统一管理各支付渠道的配置信息
- **认证服务**: 处理商户认证和权限管理
- **处罚管理**: 支持商户处罚记录和处置流程
- **调度服务**: 提供定时任务和批处理功能

## 技术架构

### 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Java | 1.8 | 编程语言 |
| Spring Boot | 2.7.18 | 应用框架 |
| Hessian | 4.3.5-yeepay | RPC协议 |
| Dubbo | 3.x | SOA服务框架 |
| MyBatis | 3.5.15 | ORM框架 |
| DB2 | - | 主数据库 |
| Redis | - | 缓存服务 |
| RocketMQ | 5.0.0 | 消息队列 |
| Maven | - | 构建工具 |

### 模块结构

```
aggregation-config-hessian/
├── aggregation-config-hessian/     # 服务启动和配置模块
├── aggregation-config-facade/      # 对外接口定义模块  
├── aggregation-config-core/        # 核心业务实现模块
└── aggregation-config-share-kernel/ # 共享组件模块
```

## 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- DB2 数据库
- Redis 服务
- RocketMQ 服务

### 本地开发环境配置

1. **克隆项目**
```bash
git clone <repository-url>
cd aggregation-config-hessian
```

2. **配置数据库连接**
```bash
# 编辑配置文件
vim aggregation-config-hessian/src/main/resources/dbconf/AGGREGATE_DB2.properties
```

3. **配置消息队列**
```bash
# 编辑RocketMQ配置
vim aggregation-config-hessian/src/main/resources/runtimecfg/rocketmq-conf.properties
```

4. **编译打包**
```bash
mvn clean package -DskipTests
```

5. **启动应用**
```bash
# 设置JVM参数
export JAVA_OPTS="-Xms2048m -Xmx2048m -XX:+UseG1GC"

# 启动服务
java $JAVA_OPTS \
  -Dappname=aggregation-config-hessian \
  -DYP_DATA_CENTER=local \
  -DYP_APP_NAME=aggregation-config-hessian \
  -jar aggregation-config-hessian/target/aggregation-config-hessian.jar
```

### Docker部署

1. **构建镜像**
```bash
docker build -t aggregation-config-hessian:latest .
```

2. **运行容器**
```bash
docker run -d \
  --name aggregation-config-hessian \
  -p 8080:8080 \
  -p 8089:8089 \
  -e JAVA_OPTS="-Xms2048m -Xmx2048m" \
  aggregation-config-hessian:latest
```

## 系统架构文档

### 📖 架构文档导航

| 文档 | 描述 | 链接 |
|------|------|------|
| 系统架构图 | 系统整体架构和技术栈说明 | [system-architecture.md](docs/architecture/system-architecture.md) |
| 组件架构图 | 详细的组件关系和设计模式 | [component-diagram.md](docs/architecture/component-diagram.md) |
| 部署架构图 | 生产环境部署和配置说明 | [deployment-diagram.md](docs/architecture/deployment-diagram.md) |
| 数据流架构图 | 业务数据流转和处理流程 | [data-flow-diagram.md](docs/architecture/data-flow-diagram.md) |

### 架构亮点

- **分层架构**: Hessian层、Facade层、Core层、Share-Kernel层清晰分离
- **异步处理**: 基于RocketMQ的事件驱动架构
- **多模式支持**: 工厂模式、装饰器模式、策略模式等设计模式
- **高可用设计**: 读写分离、缓存机制、熔断降级
- **安全保障**: 国密算法、数据脱敏、权限控制

## 主要接口

### Facade接口列表

| 接口 | 功能 | 路径 |
|------|------|------|
| EntryApplyFacade | 商户进件申请 | `com.yeepay.g3.facade.aggregation.config.facade.atmanage.EntryApplyFacade` |
| AnchoredApplyFacade | 挂靠申请 | `com.yeepay.g3.facade.aggregation.config.facade.atmanage.AnchoredApplyFacade` |
| AuthFacade | 认证管理 | `com.yeepay.g3.facade.aggregation.config.facade.atmanage.AuthFacade` |
| DisposalFacade | 处罚管理 | `com.yeepay.g3.facade.aggregation.config.facade.disposal.DisposalFacade` |
| ChannelNoFacade | 渠道号管理 | `com.yeepay.g3.facade.aggregation.config.facade.atmanage.ChannelNoFacade` |

### 接口调用示例

```java
// 商户进件申请示例
EntryApplyRequest request = new EntryApplyRequest();
request.setMerchantNo("M123456789");
request.setChannelType("WECHAT_DIRECT");
// ... 设置其他参数

EntryApplyDTO result = entryApplyFacade.entryApply(request);
```

## 配置说明

### 核心配置文件

| 配置文件 | 作用 | 位置 |
|----------|------|------|
| application.properties | Spring Boot主配置 | `src/main/resources/` |
| AGGREGATE_DB2.properties | 数据库连接配置 | `src/main/resources/dbconf/` |
| rocketmq-conf.properties | 消息队列配置 | `src/main/resources/runtimecfg/` |
| redis-conf.properties | Redis缓存配置 | `src/main/resources/runtimecfg/` |

### 关键配置项

```properties
# 数据库配置
spring.datasource.url=****************************************
spring.datasource.username=db2admin
spring.datasource.password=******

# RocketMQ配置
rocketmq.nameSrvAddr=localhost:9876
rocketmq.appid.config.topic=APP_ID_CONFIG_TOPIC
rocketmq.appid.config.group=agg_config_app_id_group

# Redis配置
redis.host=localhost
redis.port=6379
redis.password=******
```

## 监控与运维

### 健康检查

```bash
# 应用健康检查
curl http://localhost:8080/health

# JVM状态检查
curl http://localhost:8080/actuator/health
```

### 日志配置

系统使用Log4j2进行日志管理，支持：
- 分级日志输出
- 异步日志写入
- 日志文件滚动
- 集中日志收集(Fluentd)

### 性能监控

- **JVM监控**: 内存使用、GC状况、线程状态
- **业务监控**: 接口响应时间、成功率、错误率
- **基础监控**: CPU、内存、磁盘、网络

## 开发指南

### 代码规范

- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 完善的单元测试

### 分支管理

```
main        # 主分支，用于生产发布
develop     # 开发分支，用于功能集成
feature/*   # 功能分支，用于新功能开发
hotfix/*    # 热修复分支，用于紧急修复
```

### 提交规范

```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建或辅助工具变动
```

## 故障排查

### 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 启动失败 | 数据库连接失败 | 检查数据库配置和网络连接 |
| 接口超时 | 外部服务响应慢 | 检查外部服务状态，调整超时配置 |
| 消息堆积 | 消费者处理慢 | 增加消费者线程，优化处理逻辑 |
| 内存溢出 | JVM配置不当 | 调整堆内存大小，排查内存泄漏 |

### 日志查看

```bash
# 查看应用日志
tail -f /opt/logs/aggregation-config-hessian/app.log

# 查看错误日志
tail -f /opt/logs/aggregation-config-hessian/error.log

# 查看GC日志
tail -f /opt/logs/aggregation-config-hessian/gc.log
```

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0-SNAPSHOT | 2025-01-28 | 初始版本，支持基础进件和挂靠功能 |

## 团队与联系

### 开发团队

- **技术负责人**: xuchen.liu
- **架构师**: RJF
- **开发工程师**: Mr.yin, yp-c0550

### 联系方式

- **邮箱**: <EMAIL>
- **内部IM**: 易宝技术群
- **文档维护**: [架构文档](docs/architecture/)

## 许可证

本项目为易宝支付内部项目，版权所有。

---

**易宝支付(YeePay)** - 专业的数字化支付解决方案提供商

*最后更新时间: 2025-07-28*