package com.yeepay.g3.facade.aggregation.config.enums;

/**
 * @description: 准入枚举
 * @author: xuchen.liu
 * @date: 2024-12-11 13:10
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public enum AccessEnum {

    Y("是"),

    N("否");

    private final String desc;

    AccessEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "AccessEnum{" +
                "desc='" + desc + '\'' +
                '}';
    }
}
