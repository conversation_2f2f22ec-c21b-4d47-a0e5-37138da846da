package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/2 20:39
 */
@Data
public class ActivityInfoDTO implements Serializable {
    private static final long serialVersionUID = -2964951323324985882L;

    /**
     * 优惠费率活动ID
     * 微信：泛行业活动：20191030111cff5b5e
     */
    private String activitiesId;
    /**
     * 活动类型
     */
    @NotBlank(message = "活动类型不能为空")
    private String activityType;
    /**
     * 借记活动费率
     * 微信直连时：
     * 1、若填写了优惠费率活动ID，则该字段必填
     * 2、仅能填入2位以内小数，且在优惠费率活动ID指定费率范围内
     * 微信小程序B2B：该字段必填且需要和信用卡活动费率值保持一致
     */
    private String debitActivitiesRate;
    /**
     * 贷记活动费率
     * 微信直连时：
     * 1、若填写了优惠费率活动ID，则该字段必填
     * 2、仅能填入2位以内小数，且在优惠费率活动ID指定费率范围内
     * 微信小程序B2B：该字段必填且需要和非信用卡活动费率值保持一致
     */
    private String creditActivitiesRate;
}
