package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;

import java.io.Serializable;
import java.util.List;

/**
 * description: 商户认证结果查询响应 DTO
 * <AUTHOR>
 * @since 2025/5/28:11:34
 * Company: 易宝支付(YeePay)
 */
public class QueryMerchantAuthResultRespDTO extends BaseResponseDTO implements Serializable {
    private static final long serialVersionUID = 1245289697556781248L;

    /**
     * 认证结果列表
     */
    List<AuthResultDTO> authResultList;
}
