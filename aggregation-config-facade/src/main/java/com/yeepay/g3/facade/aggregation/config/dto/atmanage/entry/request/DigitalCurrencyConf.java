package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;

import javax.validation.Valid;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/2 20:47
 */
@Data
public class DigitalCurrencyConf implements Serializable {
    private static final long serialVersionUID = 9052199102771122437L;

    @Valid
    @NotNull(message = "结算账户信息不能为空")
    private SettleAccountInfoDTO settleAccountInfo;


    /**
     * 银行编码
     */
    @NotBlank(message = "银行编码不能为空")
    private String bankCode;

    /**
     * 扩展信息
     */
    @Size(max = 1024, message = "扩展信息不能超过1024位")
    private String extendInfo;
}
