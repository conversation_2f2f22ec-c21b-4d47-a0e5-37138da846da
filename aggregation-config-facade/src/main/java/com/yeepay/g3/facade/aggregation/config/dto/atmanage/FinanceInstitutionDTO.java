package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.validator.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * description: 金融机构信息DTO
 *
 * <AUTHOR>
 * @since 2025/5/20:18:58
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class FinanceInstitutionDTO implements Serializable {

    private static final long serialVersionUID = -3379729278737698282L;
    /**
     * 金融机构类型
     */
    @NotBlank(message = "金融机构类型不能为空")
    private String financeType;

    /**
     * 金融机构许可证图片
     */
    private List<String> financeLicensePics;

}
