package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:39
 */

@Data
public class WxDirectB2BConf implements Serializable {
    private static final long serialVersionUID = 5244162488585337818L;

    /**
     * 超级管理员信息
     */
    @Valid
    @NotNull
    private SuperAdminInfoDTO superAdminInfo;

    /**
     * 结算账户信息
     */
    @Valid
    @NotNull(message = "结算账户信息不能为空")
    private SettleAccountInfoDTO settleAccountInfo;


    /**
     * 主营商品类型。
     * 通道侧枚举【01：食品 02：饮料 03：乳制品 04：酒水 05：生鲜果蔬 06：调味品 07：日化个护 08：文具 09：鞋服/配饰 10：家居
     * 11：母婴/玩具   12：数码3C 99：其他】
     */
    //@NotNull(message = "主营商品类型不能为空")
    //@Size(min = 1, message = "主营商品类型不能超过12位")
    private List<String> goodsTypes;
    /**
     * 主要线下销售渠道。
     * 可选项："杂货店", "便利店", "超市", "餐饮店", "母婴店", "烟酒店", "其他"
     * 通道侧枚举：
     * 【A：杂货店
     * B：便利店
     * C：超市
     * D：餐饮店
     * E：母婴店
     * F：烟酒店
     * Z：其他】
     */
    //@NotNull(message = "主要线下销售渠道不能为空")
    //@Size(min = 1, message = "主营商品类型不能超过12位")
    private List<String> goodsSales;

    /**
     * 商户规模 微信小程序B2B必填
     * LARGE: 大型企业 2000 人以上
     * MIDDLE: 中型企业 150 至 2000 人
     * SMALL: 小型企业 15 至 150 人
     * TINY: 微型企业 15 人以下
     */
    //@NotBlank(message = "商户规模不能为空")
    private String merchantScale;

    /**
     * 门店覆盖数。
     * 可选项："0-5千", "5千-1万", "1万-10万", "10万-50万", "50万以上"
     * 通道侧枚举【N1：0-5千 N2：5千-1万 N3：1万-10万  N4：10万-50万 N5：50万以上】
     */
    //@NotBlank(message = "门店覆盖数不能为空")
    private String coverNum;

    /**
     * 所需服务类型。
     * 可选项："门店订货", "门店促销", "门店活动执行", "门店直播", "其他"
     * 通道侧枚举【S1：门店订货 S2：门店促销 S3：门店活动执行 S4：门店直播 S5：其他】
     */
    //@NotNull(message = "所需服务类型不能为空")
    private List<String> services;


    @Valid
    @NotNull(message = "活动信息不能为空")
    private ActivityInfoDTO activityInfo;

    /**
     * 小程序信息
     */
    @Valid
    @NotNull
    private MiniProgramInfoDTO miniProgramInfo;

    //@NotBlank(message = "门店门头照不能为空")
    private String storeEntrancePic;


    /**
     * 扩展信息
     */
    @Size(max = 1024, message = "扩展信息不能超过1024位")
    private String extendInfo;


}
