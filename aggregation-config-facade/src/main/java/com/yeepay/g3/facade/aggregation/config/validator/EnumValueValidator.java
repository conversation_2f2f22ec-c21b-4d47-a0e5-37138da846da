package com.yeepay.g3.facade.aggregation.config.validator;

import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/2/23 16:43
 */
public class EnumValueValidator implements ConstraintValidator<EnumValid, Object> {
    private Set<Object> allowedValues;
    private boolean allowedStrEmpty;


    @Override
    public void initialize(EnumValid constraintAnnotation) {
        Class<? extends Enum<? extends DocumentedEnum<?>>> enumClass = constraintAnnotation.enumClass();
        allowedValues = Stream.of(enumClass.getEnumConstants())
                .map(e -> ((DocumentedEnum<?>) e).getDocument())
                .collect(Collectors.toSet());
        allowedStrEmpty = constraintAnnotation.allowedStrEmpty();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // null values should be handled by @NotNull annotation
        if (value == null) {
            return true;
        }

        // 处理空字符串的情况
        if (value instanceof String) {
            String strValue = (String) value;
            if (StringUtils.isEmpty(strValue)) {
                return allowedStrEmpty;
            }
        }

        return allowedValues.contains(value);
    }
}