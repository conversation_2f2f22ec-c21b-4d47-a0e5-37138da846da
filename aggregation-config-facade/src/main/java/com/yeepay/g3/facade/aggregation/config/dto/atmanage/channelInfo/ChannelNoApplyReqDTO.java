package com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.CompanyRepresentativeDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.RegistrationCertificateDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.SettleAccountDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.SuperAdminInfoDTO;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * description: 渠道信息申请请求 DTO
 *
 * <AUTHOR>
 * @since 2025/5/27:15:37
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
public class ChannelNoApplyReqDTO implements Serializable {

    private static final long serialVersionUID = 7316826274036010063L;

    /**
     * 商户编码（必填）
     */
    @NotBlank(message = "商户编码不能为空")
    private String merchantNo;

    /**
     * 商户简称（必填）
     */
    @NotBlank(message = "商户简称不能为空")
    private String shortName;

    /**
     * 业务场景
     */
    @NotBlank(message = "业务场景不能为空")
    @EnumValid(enumClass = BizSceneEnum.class, message = "业务场景不合法")
    private String bizScene;

    /**
     * 业务申请单号（必填）
     */
    @NotBlank(message = "业务申请单号不能为空")
    private String bizApplyNo;

    /**
     * 业务单号（必填）
     */
    @NotBlank(message = "业务单号不能为空")
    private String bizOrderNo;

    /**
     * 支付渠道（必填）
     */
    @NotBlank(message = "支付渠道不能为空")
    @EnumValid(enumClass = PayChannelEnum.class, message = "支付渠道不合法")
    private String payChannel;

    /**
     * 支付场景（必填）
     */
    @NotBlank(message = "支付场景不能为空")
    @EnumValid(enumClass = PaySceneEnum.class, message = "支付场景不合法")
    private String payScene;

    /**
     * 活动类型（必填）
     */
    @NotBlank(message = "活动类型不能为空")
    @EnumValid(enumClass = ActivityTypeEnum.class, message = "活动类型不合法")
    private String activityType;

    /**
     * 销售名称（必填）
     */
    @NotBlank(message = "销售名称不能为空")
    private String salesName;

    /**
     * 行业名称
     */
    @NotBlank(message = "行业名称不能为空")
    private String industryName;

    /**
     * 数币银行代码（可选）
     */
    private String digitalCurrencyBankCode;

    /**
     * 是否使用易宝渠道号（必填）
     */
    private String isUseYpChannelNo;

    /**
     * 资金清算类型
     */
    private String fundLiquidationType;

    /**
     * 客服电话
     */
    private String servicePhone;

    /**
     * 联系人信息
     */
    @Valid
    @NotEmpty(message = "联系人信息不能为空")
    private SuperAdminInfoDTO superAdminInfo;

    /**
     * 法人信息人信息
     */
    @Valid
    private CompanyRepresentativeDTO companyRepresentativeInfo;

    /**
     * 证书信息
     */
    @Valid
    @NotEmpty(message = "证书信息不能为空")
    private RegistrationCertificateDTO certificateInfo;

    /**
     * 结算账户信息
     */
    @Valid
    @NotEmpty(message = "结算账户信息不能为空")
    private SettleAccountDTO settleAccountInfo;

    /**
     * 扩展信息
     */
    private String extendInfo;
}
