package com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * description: 查询渠道申请结果 DTO
 * <AUTHOR>
 * @since 2025/5/27:15:37
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
public class QueryChannelApplyResultReqDTO implements Serializable {

    private static final long serialVersionUID = 7203661234460793623L;
    /**
     * 商户编码（必填）
     */
    @NotBlank(message = "商户编码不能为空")
    private String merchantNo;

    /**
     * 支付渠道（必填）
     */
    @NotBlank(message = "支付渠道不能为空")
    @EnumValid(enumClass = PayChannelEnum.class, message = "支付渠道不合法")
    private String payChannel;

    /**
     * 支付场景（必填）
     */
    @NotBlank(message = "支付场景不能为空")
    @EnumValid(enumClass = PaySceneEnum.class, message = "支付场景不合法")
    private String payScene;

    /**
     * 活动类型（必填）
     */
    @NotBlank(message = "活动类型不能为空")
    @EnumValid(enumClass = ActivityTypeEnum.class, message = "活动类型不合法")
    private String activityType;

    /**
     * 数币银行代码（可选）
     */
    private String digitalCurrencyBankCode;

    /**
     * 请求号（可选）
     */
    private String requestNo;

}
