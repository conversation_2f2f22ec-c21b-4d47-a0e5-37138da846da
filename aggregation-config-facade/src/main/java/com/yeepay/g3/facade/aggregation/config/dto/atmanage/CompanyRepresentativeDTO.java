package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.IdentificationTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.LegalTypeEnum;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.AuthFacade;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.EntryApplyFacade;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * 描述: 企业代表（法人或经营者）信息 DTO
 * 1、个体户：请上传经营者的身份证件；
 * 2、企业/社会组织：请上传法人的身份证件；
 * 3、政府机关/事业单位：请上传法人/经办人的身份证件。
 * <AUTHOR>
 * @since 2025/5/20
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class CompanyRepresentativeDTO implements Serializable {

    private static final long serialVersionUID = 3687773618227567128L;
    /**
     * 证件持有人类型
     * 微信直连&微信小程序B2B必填
     */
    @EnumValid(enumClass = LegalTypeEnum.class, allowedStrEmpty = true, message = "证件持有人类型不合法")
    @NotBlank(message = "证件持有人类型不能为空", groups = {AuthFacade.class})
    private String legalType;

    /**
     * 证件类型
     *
     */
    @NotBlank(message = "经营者/法人证件类型不能为空", groups = {EntryApplyFacade.class, AuthFacade.class})
    @EnumValid(enumClass = IdentificationTypeEnum.class, allowedStrEmpty = true, message = "经营者/法人证件类型不合法")
    private String cardType;

    /**
     * 证件姓名
     */
    @NotBlank(message = "经营者/法人证件姓名不能为空", groups = {EntryApplyFacade.class, AuthFacade.class})
    private String name;

    /**
     * 证件号码
     */
    @NotBlank(message = "经营者/法人证件号码不能为空", groups = {EntryApplyFacade.class, AuthFacade.class})
    private String cardNo;

    /**
     * 证件生效期
     * 微信直连&微信小程序B2B
     */
    @NotBlank(message = "经营者/法人证件生效期不能为空", groups = {AuthFacade.class})
    private String effectTime;

    /**
     * 证件失效效期
     * 微信直连&微信小程序B2B
     */
    @NotBlank(message = "经营者/法人证件失效效期不能为空", groups = {AuthFacade.class})
    private String expireTime;

    /**
     * 证件正面照片
     */
    private String cardFrontImg;

    /**
     * 证件反面照片
     */
    private String cardBackImg;

    /**
     * 证件居住地址
     */
    private String certAddress;

    /**
     * 法定代表人说明函
     * 1、当证件持有人类型为经办人时，必须上传。其他情况，无需上传；
     * 2、若因特殊情况，无法提供法定代表人证件时，请参照示例图打印法定代表人说明函，全部信息需打印，不支持手写商户信息，并加盖公章；
     */
    private String businessAuthorizationLetter;
}
