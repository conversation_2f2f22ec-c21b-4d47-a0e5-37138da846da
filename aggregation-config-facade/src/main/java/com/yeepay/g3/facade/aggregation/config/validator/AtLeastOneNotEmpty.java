package com.yeepay.g3.facade.aggregation.config.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * description: 至少有一个属性不可为空
 *
 * <AUTHOR>
 * @since 2025/5/7:15:44
 * Company: 易宝支付(YeePay)
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = AtLeastOneNotEmptyValidator.class)
@Repeatable(AtLeastOneNotEmptyContainer.class)
public @interface AtLeastOneNotEmpty {
    String message() default "{至少有一个属性不可为空}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    // 修改: 默认值为空数组，表示未指定字段时使用类的所有属性
    String[] fields() default {};
}
