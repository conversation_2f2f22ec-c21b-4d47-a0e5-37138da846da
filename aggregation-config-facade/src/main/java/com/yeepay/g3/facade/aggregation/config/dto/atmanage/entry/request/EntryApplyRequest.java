package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.ContactInfoDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.SubjectInfoDTO;
import lombok.Data;

import javax.validation.Valid;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 17:18
 */
@Data
public class EntryApplyRequest implements Serializable {
    private static final long serialVersionUID = 2272883755860694962L;

    /**
     * 商户信息
     */
    @Valid
    @NotNull(message = "商户信息不能为空")
    private MerchantInfoDTO merchantInfo;


    @NotBlank(message = "业务场景不能为空")
    @Size(max = 16, message = "业务场景不能超过16位")
    private String bizScene;

    /**
     * 业务申请单号
     */
    @NotBlank(message = "业务申请单号不能为空")
    @Size(max = 32, message = "业务申请单号不能超过32位")
    private String bizApplyNo;


    @Valid
    @NotNull(message = "主体信息不能为空")
    private SubjectInfoDTO subjectInfo;


    @Valid
    @NotNull(message = "联系人信息不能为空")
    private ContactInfoDTO contactInfo;

    /**
     * 微信间连配置
     */
    @Valid
    private List<WxIndirectConf> wxIndirectConfigs;

    /**
     * 微信直连配置
     */
    @Valid
    private WxDirectConf wxDirectConfig;

    /**
     * 微信直连B2B配置
     */
    @Valid
    private WxDirectB2BConf wxDirectB2BConfig;

    /**
     * 支付宝间连配置
     */
    @Valid
    private List<AliIndirectConf> aliIndirectConfigs;

    /**
     * 数字货币配置
     */
    private List<DigitalCurrencyConf> digitalCurrencyConfigs;

    /**
     * 扩展信息
     */
    @Size(max = 1024, message = "扩展信息不能超过1024位")
    private String extendInfo;
}
