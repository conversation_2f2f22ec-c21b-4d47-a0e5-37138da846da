package com.yeepay.g3.facade.aggregation.config.facade.atmanage;

/**
 * description: 实名认证/商户意愿确认facade
 * <AUTHOR>
 * @since 2025/5/20:18:25
 * Company: 易宝支付(YeePay)
 */
public interface AuthFacade {

//    /**
//     * 实名认证申请
//     */
//    Result<AuthApplyRespDTO> authApply(AuthApplyReqDTO authApplyReqDTO);
//
//    /**
//     * 取消实名认证申请
//     */
//    Result<?> cancelAuthApply(CancelAuthApplyReqDTO cancelRequestDTO);
//
//    /**
//     * 实名认证审核结果查询
//     */
//    Result<QueryAuthResultRespDTO> queryAuthAuditResult(QueryAuthResultReqDTO queryAuthResult);
//
//    /**
//     * 实名认证状态查询
//     */
//    Result<QueryMerchantAuthResultRespDTO> queryMerchantAuthResult(QueryMerchantAuthResultReqDTO queryAuthResult);
}
