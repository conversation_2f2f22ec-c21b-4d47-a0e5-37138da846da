package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 认证申请请求 DTO
 * <AUTHOR>
 * @since 2025/5/21:10:55
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@Builder
public class AuthApplyRespDTO extends BaseResponseDTO implements Serializable {

    private static final long serialVersionUID = -3878942944307529368L;

    private String channelApplyNo;
}

