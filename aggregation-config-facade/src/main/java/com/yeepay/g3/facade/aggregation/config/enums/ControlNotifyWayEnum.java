package com.yeepay.g3.facade.aggregation.config.enums;

/**
 * @description: 管控通知类型
 * @author: xuchen.liu
 * @date: 2024-12-11 11:54
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public enum ControlNotifyWayEnum {
    /**
     * 邮件
     */
    EMAIL("邮件"),
    /**
     * 系统回调
     */
    CALL_BACK("系统回调"),

    /**
     * 邮件+回调
     */
    EMAIL_AND_CAL_BACK("邮件与系统回调");

    private final String desc;

    ControlNotifyWayEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static String getNameByDesc(String name) {
        for (ControlNotifyWayEnum status : ControlNotifyWayEnum.values()) {
            if (status.name().equals(name)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("未找到对应的状态枚举值: " + name);
    }

    @Override
    public String toString() {
        return "ControlNotifyWayEnum{" +
                "desc='" + desc + '\'' +
                '}';
    }
}