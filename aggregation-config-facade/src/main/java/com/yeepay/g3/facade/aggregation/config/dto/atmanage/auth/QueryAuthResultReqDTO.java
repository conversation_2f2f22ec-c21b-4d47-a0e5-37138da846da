package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.validator.AtLeastOneNotEmpty;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * description: 查询认证申请
 * <AUTHOR>
 * @since 2025/5/26:18:23
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@AtLeastOneNotEmpty(fields = {"channelNo", "channelIdentifier"}, message = "渠道号和渠道标识,二选一必填")
@AtLeastOneNotEmpty(fields = {"bizApplyNo", "channelApplyNo"}, message = "请求单号和响应单号,二选一必填")
public class QueryAuthResultReqDTO implements Serializable {

    private static final long serialVersionUID = 6696090405078990097L;

    /**
     * 支付场景（必填）
     */
    @NotBlank(message = "支付场景不能为空")
    @EnumValid(enumClass = PaySceneEnum.class, message = "支付场景不合法")
    private String payScene;

    /**
     * 渠道标识（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelIdentifier;

    /**
     * 渠道号（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelNo;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 申请单据结果单号
     */
    private String channelApplyNo;
}
