package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.LegalTypeEnum;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth.AuthApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.EntryApplyRequest;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * description: 联系人信息
 *
 * <AUTHOR>
 * @since 2025/5/20:18:34
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class ContactInfoDTO implements Serializable {

    private static final long serialVersionUID = 2336855408498371527L;
    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;
        
    /**
     * 联系人手机号
     */
    @NotBlank(message = "联系人手机号不能为空")
    private String contactMobileNo;

    /**
     * 联系人身份证号
     */
    @NotBlank(message = "联系人身份证号不能为空", groups = {EntryApplyRequest.class, AuthApplyReqDTO.class})
    private String contactCertNo;

    /**
     * 联系人邮箱
     */
    private String contactEmail;


    @NotBlank(message = "联系人类型不能为空")
    @EnumValid(enumClass = LegalTypeEnum.class, allowedStrEmpty = true, message = "联系人类型不合法")
    private String contactType;
}

