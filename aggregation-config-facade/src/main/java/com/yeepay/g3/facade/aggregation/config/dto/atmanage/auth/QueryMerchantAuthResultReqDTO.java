package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import org.hibernate.validator.constraints.NotBlank;
import java.io.Serializable;

/**
 * description: 商户认证结果查询请求 DTO
 * <AUTHOR>
 * @since 2025/5/28:11:33
 * Company: 易宝支付(YeePay)
 */
public class QueryMerchantAuthResultReqDTO implements Serializable {
    private static final long serialVersionUID = -4128906694320124563L;

    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号不能为空")
    private String merchantNo;

    /**
     * 支付场景
     */
    private String payScene;

    /**
     * 报备商户号
     */
    private String reportMerchantNo;
}
