package com.yeepay.g3.facade.aggregation.config.facade.atmanage;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelApplyResultRespDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.ChannelNoApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo.QueryChannelApplyResultReqDTO;

/**
 * description: 渠道号管理服务接口
 * <AUTHOR>
 * @since 2025/5/27:15:35
 * Company: 易宝支付(YeePay)
 */
public interface ChannelNoFacade {

    /**
     * 申请渠道号
     * @param reqDTO 渠道号申请请求 DTO
     */
    BaseResponseDTO applyChannelNo(ChannelNoApplyReqDTO reqDTO);

    /**
     * 申请渠道号
     * @param reqDTO 渠道号申请请求 DTO
     * @return 渠道号申请响应 DTO
     */
    ChannelApplyResultRespDTO queryChannelApplyResult(QueryChannelApplyResultReqDTO reqDTO);

}
