package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * description: 认证结果 DTO
 * <AUTHOR>
 * @since 2025/5/28:11:39
 * Company: 易宝支付(YeePay)
 */
@Getter
@Builder
public class AuthResultDTO implements Serializable {
    private static final long serialVersionUID = 4270425004948186972L;

   /**
     * 报备商户号
     */
    private String reportMerchantNo;

    /**
     * 授权状态
     */
    private String authorizeState;

    /**
     * 报备费率
     */
    private String feeType;

    /**
     * 错误码
     */
    private String returnCode;

    /**
     * 错误信息
     */
    private String returnMsg;

    /**
     * 易宝商户号
     */
    private String merchantNo;

    /**
     * 易宝商户名称
     */
    private String merchantName;

    /**
     * 报备商户名称
     */
    private String reportMerchantName;

    /**
     * 报备商户简称
     */
    private String reportMerchantAlias;

    /**
     * 报备状态
     */
    private String reportStatus;

    /**
     * 报名状态
     */
    private String reportAttachStatus;

    /**
     * 成功时间
     */
    private String successTime;

    /**
     * 报备类型
     */
    private String reportType;

    /**
     * 活动类型
     */
    private String promotionType;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 客服电话
     */
    private String serviceTel;

    /**
     * 备份标签
     */
    private String backUpFlag;

    /**
     * 开关状态
     */
    private String opena;
}
