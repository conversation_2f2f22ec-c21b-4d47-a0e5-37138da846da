package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 14:36
 */
@Data
public class SalesSceneDTO implements Serializable {
    private static final long serialVersionUID = -3526369858190707510L;
    /**
     * 微信直连必填
     */
    // @EnumValid(enumClass = SalesScenesType.class, message = "经营场景类型不合法")
    @NotNull(message = "经营场景不能为空")
    private List<String> salesScenesTypes;

    @Valid
    private StoreInfoDTO storeInfo;

    @Valid
    private MpInfoDTO mpInfo;

    @Valid
    private MiniProgramInfoDTO miniProgramInfo;
}
