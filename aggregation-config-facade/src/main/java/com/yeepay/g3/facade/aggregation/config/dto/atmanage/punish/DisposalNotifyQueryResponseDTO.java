package com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * 商户处罚返回值
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 16:19
 */
@Setter
@Getter
public class DisposalNotifyQueryResponseDTO extends BaseResponseDTO implements Serializable {

    private static final long serialVersionUID = -2637733653160494857L;

    private Integer totalCount;

    private List<DisposalRecordDto> disposalRecordDtoList;

}
