package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 18:55
 */
@Data
public class AliIndirectConf implements Serializable {
    private static final long serialVersionUID = 4950488500438917001L;

    @Valid
    @NotNull(message = "结算账户信息不能为空")
    private SettleAccountInfoDTO settleAccountInfo;

    /**
     * 支付场景
     */
    @NotBlank(message = "支付场景不能为空")
    @EnumValid(enumClass = PaySceneEnum.class, message = "支付场景不合法")
    private String payScene;

    /**
     * 活动信息
     */
    @Valid
    @NotNull(message = "活动信息不能为空")
    private ActivityInfoDTO activityInfo;

    @Size(max = 7, message = "商户类目不能超过7位")
    private String mcc;

    /**
     * 支付宝渠道编号
     */
    private String channelNo;

    /**
     * 渠道标识 易宝侧商编 会根据此商编的渠道号下进行进件
     */
    private String channelIdentifier;

    /**
     * 进件类型
     */
    private String entryType;

    /**
     * 备份数量
     */
    @Digits(integer = 2, fraction = 0)
    @Max(value = 20)
    private Integer backupCount;

    /**
     * 商户等级
     * INDIRECT_LEVEL_M1
     * INDIRECT_LEVEL_M2
     * INDIRECT_LEVEL_M3
     * INDIRECT_LEVEL_M4
     * 支付宝必填；拟申请的间连商户等级，银联根据 申请等级决定转发报文要素，具体 申请结果以支付宝结果为准
     */
    @NotBlank(message = "商户等级不能为空")
    private String merchantLevel;

    /**
     * 站点信息
     * 支付宝线上报备费率时必填
     */
    private List<SiteInfoDTO> siteInfo;

    /**
     * 挂靠信息
     */
    private AnchoredInfoDTO anchoredInfo;

    /**
     * 是否需要终端报备
     */
    @NotNull(message = "是否需要终端报备不能为空")
    private Boolean isNeedTerminalReport;

    /**
     * 扩展信息
     */
    @Size(max = 1024, message = "扩展信息不能超过1024位")
    private String extendInfo;

}
