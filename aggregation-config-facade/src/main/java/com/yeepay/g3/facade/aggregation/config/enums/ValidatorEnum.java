package com.yeepay.g3.facade.aggregation.config.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by liushanping on 2017/1/16.
 */
public enum ValidatorEnum {
    DATETIME("DATETIME"),
    ENUMS("ENUMS"),
    IP("IP"),
    MONE<PERSON>("MONEY"),
    NOT_EMPTY("NOT_EMPTY"),
    INTEGER("INTEGER"),
    Y_N("Y_N");

    private String validatorType;

    private static Map<String, ValidatorEnum> validatorEnumMap = new HashMap<>();


    static {
        for (ValidatorEnum validatorEnum : ValidatorEnum.values()) {
            validatorEnumMap.put(validatorEnum.getValidatorType(),validatorEnum);
        }
    }

    ValidatorEnum(String validatorType){
        this.validatorType = validatorType;
    }

    public String getValidatorType() {
        return validatorType;
    }

    public void setValidatorType(String validatorType) {
        this.validatorType = validatorType;
    }

    public static Map<String, ValidatorEnum> getValidatorEnumMap() {
        return validatorEnumMap;
    }

    public static void setValidatorEnumMap(Map<String, ValidatorEnum> validatorEnumMap) {
        ValidatorEnum.validatorEnumMap = validatorEnumMap;
    }
}
