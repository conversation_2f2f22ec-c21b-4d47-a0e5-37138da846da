package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.SceneType;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/23 14:33
 */
@Data
public class MerchantInfoDTO implements Serializable {
    private static final long serialVersionUID = 5556540174951636316L;
    /**
     * 商户编号
     */
    @NotBlank(message = "易宝商编不能为空")
    @Size(max = 16, message = "商户编号不能超过16位")
    private String merchantNo;

    @Size(max = 16, message = "父商编不能超过16位")
    private String parentMerchantNo;
    /**
     * 顶级商户编号
     */
    @Size(max = 16, message = "顶级商户编号不能超过16位")
    private String topLevelMerchantNo;

    /**
     * 商户名称
     * 必须与企业证照上的名称一致；
     * 个体工商户的营业执照如没有名称，名称为“*”或空，则商户名称应填 “个体户XXX”（XXX为营业执照上经营者姓名），如“个体户张三”，汉字以2个字符计算 ；
     */
    @NotBlank(message = "商户名称不能为空")
    @Size(max = 128, message = "商户名称不能超过128位")
    private String merchantName;
    /**
     * 商户简称
     * 企业商户必填，需与商户注册名称全称相关；用于微信、支付宝入驻时上传；
     * 注意：微信只支持20个字节，10个汉字；
     */
    @NotBlank(message = "商户简称不能为空")
    @Size(max = 128, message = "商户简称不能超过128位")
    private String shortName;

    /**
     * 是否直营商
     */
    @NotNull(message = "是否直营商不能为空")
    private Boolean isDirectMerchant;

    /**
     * 行业线
     */
    @NotBlank(message = "行业线不能为空")
    private String industryLine;

    /**
     * 商户角色
     */
    @NotBlank(message = "商户角色不能为空")
    private String role;

    /**
     * 是否退出区域
     */
    private Boolean isExitArea;

    /**
     * 场景类型
     * 线上场景
     * 线下场景
     * 线上线下场景
     */
    @EnumValid(enumClass = SceneType.class, allowedStrEmpty = true, message = "场景类型错误")
    private String sceneType;
}
