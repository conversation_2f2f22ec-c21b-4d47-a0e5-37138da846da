package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.UboInfoDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 微信直连
 * <AUTHOR>
 * @date 2025/5/22 22:01
 */
@Data
public class WxDirectConf implements Serializable {
    private static final long serialVersionUID = 7914111106029642800L;


    /**
     * 经营者/法人是否为受益人
     * 微信直连
     */
    @NotNull(message = "经营者/法人是否为受益人不能为空")
    private Boolean isBenefitPerson;

    /**
     * 受益人列表
     */
    @Valid
    private List<UboInfoDTO> uboInfos;


    @Valid
    @NotNull(message = "结算账户信息不能为空")
    private SettleAccountInfoDTO settleAccountInfo;

    /**
     * 超级管理员信息
     */
    @Valid
    @NotNull(message = "超级管理员信息不能为空")
    private SuperAdminInfoDTO superAdminInfo;

    /**
     * 经营场景
     */
    @NotNull(message = "经营场景不能为空")
    @Valid
    private SalesSceneDTO salesScene;

    /**
     *  活动信息
     */
    @Valid
    @NotNull(message = "活动信息不能为空")
    private ActivityInfoDTO  activityInfo;

    /**
     * 补充材料
     */
    private AdditionInfo additionInfo;

    /**
     * 费用类型
     * 差价模式：COMMISSION_CHARGE
     * 返佣模式：REBATE
     */
    @NotNull(message = "费用类型不能为空")
    private String chargeType;

    /**
     * 扩展信息
     */
    @Size(max = 1024, message = "扩展信息不能超过1024位")
    private String extendInfo;

    @Data
    public static class AdditionInfo implements Serializable {

        /**
         * 补充说明信息
         * 若主体为“个人卖家”，该字段必传，则需填写描述“ 该商户已持续从事电子商务经营活动满6个月，且期间经营收入累计超过20万元。
         */
        @Size(max = 512, message = "补充说明不能超过512位")
        private String businessAdditionDesc;
        /**
         * 补充材料
         * 根据实际审核情况，额外要求提供。最多可上传15张照片
         */
        @Size(max = 1024, message = "补充材料不能超过1024位")
        private List<String> businessAdditionPics;

        /**
         * 请上传法定代表人或负责人亲笔签署的开户承诺函扫描件（下载模板）。亲笔签名承诺函内容清晰可见，不得有涂污，破损，字迹不清晰现象
         */
        @Size(max = 1024, message = "法人开户承诺函不能超过1024位")
        private String legalPersonCommitment;

        /**
         * 1、建议法人按如下话术录制“法人开户意愿视频”：
         * 我是#公司全称#的法定代表人（或负责人），特此证明本公司申请的商户号为我司真实意愿开立且用于XX业务（或XX服务）。我司现有业务符合法律法规及腾讯的相关规定。
         * 2、支持上传20M内的视频，格式可为avi、wmv、mpeg、mp4、mov、mkv、flv、f4v、m4v、rmvb；
         */
        @Size(max = 1024, message = "法人开户视频不能超过1024位")
        private String legalPersonVideo;
    }
}
