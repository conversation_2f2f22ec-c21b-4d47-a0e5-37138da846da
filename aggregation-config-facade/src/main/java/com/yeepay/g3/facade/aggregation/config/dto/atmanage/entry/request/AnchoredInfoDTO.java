package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.URL;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/2 17:27
 */
@Data
public class AnchoredInfoDTO implements Serializable {

    private static final long serialVersionUID = -512211868328415687L;
    /**
     * 挂靠类型
     */
    @EnumValid(enumClass = AnchoredType.class, message = "挂靠类型不合法")
    @NotBlank(message = "挂靠类型不能为空")
    private String anchoredType;

    /**
     * 挂靠维度 是否需要跨商户体系
     * 指定挂靠商编可不传
     */
    private Boolean anchoredDimension;

    /**
     * 挂靠商户编号
     */
    private String anchoredMerchantNo;

    /**
     * 授权函文件名称
     */
    private String authFileName;

    /**
     * 授权函文件地址
     */
    @URL(message = "授权函文件地址格式错误")
    private String authFileUrl;

    /**
     * 集团名称
     */
    @NotBlank(message = "集团名称不能为空")
    private String groupName;
}
