package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.EntryTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/22 18:55
 */
@Data
public class WxIndirectConf implements Serializable {

    @Valid
    @NotNull(message = "结算账户信息不能为空")
    private SettleAccountInfoDTO settleAccountInfo;

    /**
     * 支付场景
     */
    @NotBlank(message = "支付场景不能为空")
    @EnumValid(enumClass = PaySceneEnum.class, message = "支付场景不合法")
    private String payScene;

    /**
     * 活动信息
     */
    @Valid
    @NotNull(message = "活动信息不能为空")
    private ActivityInfoDTO activityInfo;

    /**
     * 微信商户经营类目
     */
    @Size(max = 7, message = "微信商户经营类目不能超过7位")
    private String mcc;

    /**
     * 手续费规则id 微信费率规则号
     */
    private String feeRuleId;
    /**
     * 微信渠道编号
     */
    private String channelNo;

    /**
     * 渠道标识 易宝侧商编 会根据此商编的渠道号下进行进件
     */
    private String channelIdentifier;

    /**
     * 进件类型
     */
    @EnumValid(enumClass = EntryTypeEnum.class, message = "进件类型不合法")
    private String entryType;

    /**
     * 挂靠信息
     */
    private AnchoredInfoDTO anchoredInfo;

    /**
     * 备份数量
     */
    @Digits(integer = 3, fraction = 0)
    @Max(value = 200)
    private Integer backupCount;

    /**
     * 是否需要终端报备
     */
    @NotNull(message = "是否需要终端报备不能为空")
    private Boolean isNeedTerminalReport;

    /**
     * 扩展信息
     */
    @Size(max = 1024, message = "扩展信息不能超过1024位")
    private String extendInfo;
}
