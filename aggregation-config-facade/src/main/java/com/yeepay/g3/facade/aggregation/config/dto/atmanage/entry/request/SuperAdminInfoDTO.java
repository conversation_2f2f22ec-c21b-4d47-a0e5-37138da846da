package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.IdentificationTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.LegalTypeEnum;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.AuthFacade;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/7 11:31
 */
@Data
public class SuperAdminInfoDTO implements Serializable {

    private static final long serialVersionUID = 8509537379604430867L;
    /**
     * 联系人姓名
     */
    @NotBlank(message = "微信直连联系人姓名不能为空")
    private String contactName;

    /**
     * 联系人手机号
     */
    @NotBlank(message = "微信直连联系人手机号不能为空")
    private String contactMobileNo;

    /**
     * 联系人身份证号
     * 当超级管理员类型是经办人时，请上传超级管理员证件号码；
     */
    @NotBlank(message = "微信直连联系人身份证号不能为空", groups = {AuthFacade.class})
    private String contactCertNo;

    /**
     * 联系人类型
     * 主体为“个体工商户/企业/政府机关/事业单位/社会组织”，可选择：LEGAL：经营者/法人
     */
    @EnumValid(enumClass = LegalTypeEnum.class, allowedStrEmpty = true, message = "联系人类型不合法")
    private String contactType;

    /**
     * 当超级管理员类型是经办人时，请上传超级管理员证件类型。
     */
    @NotBlank(message = "联系人证件类型不能为空", groups = {AuthFacade.class})
    @EnumValid(enumClass = IdentificationTypeEnum.class, allowedStrEmpty = true, message = "联系人证件类型不合法")
    private String certType;
    /**
     * 联系人证件正面照片
     * 当超级管理员类型是经办人时，请上传超级管理员证件的正面照片；
     */
    private String cardFrontImg;

    /**
     * 联系人证件反面照片
     * 当超级管理员类型是经办人时，请上传超级管理员证件的反面照片；
     */
    private String cardBackImg;

    /**
     * 联系人证件有效期开始时间
     * 当超级管理员类型是经办人时，请上传证件有效期开始时间；
     */
    private String effectTime;

    /**
     * 联系人证件有效期结束时间
     * 当超级管理员类型是经办人时，请上传证件有效期结束时间；
     */
    private String expireTime;

    /**
     * 业务办理授权函
     * 1、当超级管理员类型是经办人时，请上传业务办理授权函；
     */
    private String businessAuthorizationLetter;

    /**
     * 联系人邮箱
     */
    private String contactEmail;
}

