package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryAndAnchoredDetailDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model.QueryEntryApplyInfoDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 聚合进件查询结果DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:57
 */
@Getter
@Setter
public class QueryEntryAndAnchoredResponseDTO extends BaseResponseDTO {


    private static final long serialVersionUID = 9184770010656325557L;
    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 详情列表
     */
    private List<QueryEntryAndAnchoredDetailDTO> entryDetailList;

    /**
     * 最新进件申请结果
     */
    private QueryEntryApplyInfoDTO entityApplyInfo;




}