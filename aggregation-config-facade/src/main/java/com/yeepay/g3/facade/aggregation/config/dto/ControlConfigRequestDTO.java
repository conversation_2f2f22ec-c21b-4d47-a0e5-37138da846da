package com.yeepay.g3.facade.aggregation.config.dto;

import com.yeepay.g3.facade.aggregation.config.enums.AccessEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ControlNotifyWayEnum;
import com.yeepay.g3.facade.aggregation.config.enums.ControlStatusEnum;
import com.yeepay.g3.facade.aggregation.config.enums.OperateTypeEnum;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description: 管控配置请求DTO
 * @author: xuchen.liu
 * @date: 2024-12-13 13:44
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class ControlConfigRequestDTO implements Serializable {

    private static final long serialVersionUID = 7878514054836035321L;
    /**
     * 主键ID，自动生成
     */
    private Long id;

    /**
     * 商户编号
     */
    @NotBlank(message ="商户编号不能为空")
    @Length(max = 32,message = "商户编号长度不能超过32位")
    private String merchantNo;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    /**
     * 管控通知方式
     */
    @NotNull(message = "管控通知方式不能为空")
    private ControlNotifyWayEnum controlNotifyWay;

    /**
     * 是否包含下级
     */
    @NotNull(message ="是否包含下级标记不能为空")
    private AccessEnum isLower;

    /**
     * 邮件地址（可选）
     */
    @Length(max = 500,message = "邮箱地址不可超过500位")
    private String emailAddress;

    /**
     *  抄送人地址（可选）
     */
    @Length(max = 500,message = "抄送人邮箱地址不可超过500位")
    private String ccAddress;

    /**
     * 回调URL（可选）
     */
    @Length(max = 200,message = "通知地址不可超过200位")
    private String callBackUrl;

    /**
     * 应用密钥（可选）
     */
    @Length(max = 64,message = "appKey不可超过64位")
    private String appKey;

    /**
     * 操作员
     */
    @NotBlank(message = "操作员不能为空")
    private String operator;

    /**
     * 备注信息（可选）
     */
    @Length(max = 1000,message = "备注不能超过1000位")
    private String remark;

    /**
     * 状态（启用/禁用）
     */
    @NotNull(message = "管控表状态不能为空")
    private ControlStatusEnum status;

    /**
     * 操作类型不能为空
     */
    @NotNull(message = "操作类型不能为空")
    private OperateTypeEnum operateType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public ControlNotifyWayEnum getControlNotifyWay() {
        return controlNotifyWay;
    }

    public void setControlNotifyWay(ControlNotifyWayEnum controlNotifyWay) {
        this.controlNotifyWay = controlNotifyWay;
    }

    public AccessEnum getIsLower() {
        return isLower;
    }

    public void setIsLower(AccessEnum isLower) {
        this.isLower = isLower;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getCcAddress() {
        return ccAddress;
    }

    public void setCcAddress(String ccAddress) {
        this.ccAddress = ccAddress;
    }

    public String getCallBackUrl() {
        return callBackUrl;
    }

    public void setCallBackUrl(String callBackUrl) {
        this.callBackUrl = callBackUrl;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public ControlStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ControlStatusEnum status) {
        this.status = status;
    }

    public OperateTypeEnum getOperateType() {
        return operateType;
    }

    public void setOperateType(OperateTypeEnum operateType) {
        this.operateType = operateType;
    }

    @Override
    public String toString() {
        return "ControlConfigRequestDTO{" +
                "id=" + id +
                ", merchantNo='" + merchantNo + '\'' +
                ", businessType='" + businessType + '\'' +
                ", controlNotifyWay=" + controlNotifyWay +
                ", isLower=" + isLower +
                ", emailAddress='" + emailAddress + '\'' +
                ", ccAddress='" + ccAddress + '\'' +
                ", callBackUrl='" + callBackUrl + '\'' +
                ", appKey='" + appKey + '\'' +
                ", operator='" + operator + '\'' +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", operateType=" + operateType +
                '}';
    }
}
