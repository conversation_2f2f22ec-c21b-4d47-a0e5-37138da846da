package com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.validator.constraints.NotBlank;
import java.io.Serializable;

/**
 * description: 渠道信息申请请求 DTO
 * <AUTHOR>
 * @since 2025/5/27:15:37
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
public class ChannelApplyResultNotifyReqDTO implements Serializable {

    private static final long serialVersionUID = -6425407315430980673L;

    /**
     * 请求号
     */
    @NotBlank(message = "请求号")
    private String requestNo;

    /**
     * 商户编码
     */
    @NotBlank(message = "商户编码")
    private String merchantNo;

    /**
     * 机构类型
     */
    private String institute;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道号申请结果
     */
    @NotBlank(message = "渠道号申请结果不能为空")
    private String applyResult;

    /**
     * 渠道号申请结果描述
     */
    @NotBlank(message = "渠道号申请结果描述不能为空")
    private String applyResultDes;

}
