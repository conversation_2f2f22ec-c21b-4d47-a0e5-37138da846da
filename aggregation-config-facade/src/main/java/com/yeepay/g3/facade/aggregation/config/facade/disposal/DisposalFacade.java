package com.yeepay.g3.facade.aggregation.config.facade.disposal;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish.DisposalNotifyQueryResponseDTO;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * 商户处罚相关接口,包括微信或者支付宝
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 16:15
 */
public interface DisposalFacade {
    /**
     * 查询处置通知记录
     *
     * @param requestDTO
     * @return
     */
    DisposalNotifyQueryResponseDTO pageQueryDisposalList(DisposalNotifyQueryRequestDTO requestDTO);


}
