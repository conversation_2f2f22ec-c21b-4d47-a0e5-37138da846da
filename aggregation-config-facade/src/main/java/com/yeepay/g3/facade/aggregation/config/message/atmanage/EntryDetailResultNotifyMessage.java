package com.yeepay.g3.facade.aggregation.config.message.atmanage;

import com.yeepay.g3.facade.aggregation.config.message.atmanage.model.AnchoredInfoMessage;
import com.yeepay.g3.facade.aggregation.config.message.atmanage.model.EntryInfoMessage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: Mr.yin
 * @date: 2025/6/3  20:13
 */
@Data
public class EntryDetailResultNotifyMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务方编码
     */
    private String bizScene;

    /**
     * 通知业务类型
     * ENTRY：进件结果通知
     * TERMINAL_REPORT：终端报备结果通知
     * SPLIT_CREATE：分账方结果通知
     * {@link com.yeepay.aggregation.config.share.kernel.enumerate.EntryNotifyBizTypeEnum}
     */
    private String notifyBizType;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 商编
     */
    private String merchantNo;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 场景类型
     */
    private String payScene;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 费率类型
     */
    private String feeType;

    /**
     * 通道返回的活动类型
     */
    private String channelActivityType;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 进件类型
     */
    private String entryType;

    /**
     * 挂靠信息 同主体挂靠可能会有多个
     */
    private List<AnchoredInfoMessage> anchoredInfoList;

    /**
     * 进件信息 分主+多备进件可能会有多个
     */
    private List<EntryInfoMessage> entryInfoList;

    /**
     * 辅助终端报备状态
     */
    private String terminalReportStatus;

    /**
     * 分账方创建状态
     */
    private String splitCreateStatus;



}