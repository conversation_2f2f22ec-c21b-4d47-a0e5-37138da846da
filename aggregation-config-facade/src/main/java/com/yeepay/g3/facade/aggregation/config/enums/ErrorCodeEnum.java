package com.yeepay.g3.facade.aggregation.config.enums;

/**
 * @Description :
 * 1、用于业务返回码，包括异常和成功
 * 2、所有业务产生的异常都应该在此处定义
 * 3、包括运行异常
 * "校验类错误码段
 * 00001-00099"

 * "状态类错误码
 * 00101-00199"
 *
 * "通用业务处理错误码
 * 00201-00299"
 * @Company : 易宝支付(Yeepay)
 * <AUTHOR> ziyu.qiu
 * @Since: 2020/9/1
 * @Version : 1.0.0
 */
public enum ErrorCodeEnum {
    /**
     * 成功
     */
    SUCCESS("00000", "成功"),
    /**
     *参数错误
     */
    PARAM_ERROR("00001", "参数错误"),

    /**
     *商户异常
     */
    MERCHANT_STATUS_ERROR("00011", "商户异常"),

    /**
     * 查询商户分组信息异常
     */
    QUERY_MERCHANT_GROUP_ERROR("00012", "查询商户分组信息异常"),


    QUERY_MERCHANT_NOT_ERROR("00013", "商户不存在"),

    /**
     * 数据状态异常
     */
    DATA_STATUS_ERROR("00014", "状态异常"),



    /**
     *通道异常
     */
    BANKCHANNEL_CONFIG_ERROR("02203", "通道异常"),
    /**
     *通道异常
     */
    BANKCHENNEL_QUERY_ERROR("02204", "发送邮件异常"),

    /**
     * MQ消费异常
     */
    MQ_CONSUMER_ERROR("03001", "MQ消费异常"),

    /**
     *系统异常
     */
    SYSTEM_ERROR("99999", "系统异常"),
    /**
     *数据库操作异常
     */
    DATABASE_OPERATE_ERROR("90005", "数据库操作异常");

    private final String desc;
    private final String code;

    ErrorCodeEnum(String code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return this.desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "desc='" + desc + '\'' +
                ", code='" + code + '\'' +
                '}';
    }

    public String getCode() {
        return this.code;
    }


}
