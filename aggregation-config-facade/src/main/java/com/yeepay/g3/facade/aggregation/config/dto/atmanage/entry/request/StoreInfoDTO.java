package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;

import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 21:00
 */
@Data
public class StoreInfoDTO implements Serializable {
    private static final long serialVersionUID = -2575519514288927856L;
    /**
     * 微信直连 ：
     * 1、 长度为1-50个字符；
     * 2、前后不能有空格、制表符、换行符；
     * 3、不能仅含数字、特殊字符；
     * 4、仅能填写数字、英文字母、汉字及特殊字符；
     * 5、仅支持utf-8格式。
     */
    @NotBlank(message = "门店名称不能为空")
    @Size(max = 50, message = "门店名称不能超过50位")
    private String storeName;

    /**
     * 微信直连
     * 【线下场所地址】 请填写详细的经营场所信息，如有多个场所，选择一个主要场所填写即可。
     * 1、长度为4-512个字符；
     * 2、前后不能有空格、制表符、换行符；
     * 3、不能仅含数字、特殊字符；
     * 4、仅能填写数字、英文字母、汉字及特殊字符；
     * 5、仅支持utf-8格式。
     */
    @NotBlank(message = "线下场所地址不能为空")
    @Size(max = 512, message = "线下场所地址不能超过512位")
    private String storeAddress;

    /**
     * 微信直连
     * 【线下场所省市编码】 必填
     */
    @NotBlank(message = "线下场所省市编码不能为空")
    private String storeAddressCode;


    /**
     * 【线下场所门头照片】 必填
     * store_entrance_pic
     */
    @NotNull(message = "线下场所门头照片不能为空")
    //@Size(min = 1, message = "线下场所门头照片不能小于1")
    private List<String> storeEntrancePics;

    /**
     * 【线下场所内部照片】必填
     */
    @NotNull(message = "线下场所内部照片不能为空")
    //@Size(min = 1, message = "线下场所内部照片不能小于1")
    private List<String> storeInnerPics;

}
