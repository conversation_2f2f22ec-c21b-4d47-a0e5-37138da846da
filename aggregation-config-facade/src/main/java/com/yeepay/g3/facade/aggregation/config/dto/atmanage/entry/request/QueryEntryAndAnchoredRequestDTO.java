package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 查询聚合进件申请结果请求参数
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:50
 */
@Data
public class QueryEntryAndAnchoredRequestDTO implements Serializable {

    private static final long serialVersionUID = -8258128855804064755L;
    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号不能为空")
    @Size(max = 16, message = "商户编号不能超过16位")
    private String merchantNo;

    /**
     * 支付渠道
     */
    @NotBlank(message = "支付渠道不能为空")
    @Size(max = 16, message = "支付渠道不能超过16位")
    @EnumValid(enumClass = PayChannelEnum.class, allowedStrEmpty = true, message = "支付渠道不合法")
    private String payChannel;

    /**
     * 场景
     */
    @NotBlank(message = "支付场景不能为空")
    @Size(max = 32, message = "支付场景不能超过32位")
    @EnumValid(enumClass = PaySceneEnum.class, allowedStrEmpty = true, message = "场景类型不合法")
    private String payScene;

    /**
     * 活动类型
     */
    @NotBlank(message = "活动类型不能为空")
    @Size(max = 32, message = "活动类型不能超过32位")
    @EnumValid(enumClass = ActivityTypeEnum.class, allowedStrEmpty = true, message = "活动类型不合法")
    private String activityType;


    /**
     * 数币银行编码
     */
    private String digitalCurrencyBankCode;

}

