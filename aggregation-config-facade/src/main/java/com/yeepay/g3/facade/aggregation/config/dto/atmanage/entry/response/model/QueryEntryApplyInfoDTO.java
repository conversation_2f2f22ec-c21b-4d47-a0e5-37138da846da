package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 查询进件申请信息
 * @date: 2025/06/15 12:09
 */
@Getter
@Builder
public class QueryEntryApplyInfoDTO implements Serializable {

    private static final long serialVersionUID = -4197205719928649862L;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     * 业务场景
     */
    private String bizScene;

    /**
     * 申请明细
     */
    private List<QueryEntryApplyInfoDetailDTO> detailList;

}