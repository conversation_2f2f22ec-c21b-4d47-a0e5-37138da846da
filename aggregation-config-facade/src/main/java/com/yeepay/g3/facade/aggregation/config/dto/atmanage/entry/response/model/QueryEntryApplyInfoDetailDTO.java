package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description: 查询进件申请信息
 * @date: 2025/06/15 12:09
 */
@Getter
@Builder
public class QueryEntryApplyInfoDetailDTO implements Serializable {

    private static final long serialVersionUID = -4197205719928649862L;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 支付渠道（必填）
     */
    private String payChannel;

    /**
     * 支付场景（必填）
     */
    private String payScene;

    /**
     * 活动类型（必填）
     */
    private String activityType;

    /**
     * 通道费率（必填）
     */
    private String channelFeeType;

    /**
     * 通道活动类型（必填）
     */
    private String channelActivityType;

    /**
     * 渠道号（必填）
     */
    private String channelNo;

    /**
     * 渠道标识
     */
    private String channelIdentifier;

    /**
     * 进件类型
     */
    private String entryType;

    /**
     * 状态
     */
    private String status;

    /**
     * 进件状态
     */
    private String entryStatus;

    /**
     * 进件失败Code
     */
    private String entryFailCode;

    /**
     * 进件失败原因
     */
    private String entryFailReason;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 是否需要终端报备
     */
    private Boolean anchoredFlag;

    /**
     * 终端报备状态
     */
    private Boolean anchoredFlagStatus;

    /**
     * 是否需要终端报备
     */
    private Boolean terminalReportFlag;

    /**
     * 终端报备状态
     */
    private Boolean terminalReportFlagStatus;

    /**
     * 是否需要创建分账方
     */
    private Boolean createSplitterFlag;

    /**
     * 创建分账方状态
     */
    private Boolean createSplitterFlagStatus;

}