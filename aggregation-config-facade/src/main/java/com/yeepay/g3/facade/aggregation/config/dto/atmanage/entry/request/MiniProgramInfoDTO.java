package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 21:13
 */
@Data
public class MiniProgramInfoDTO implements Serializable {
    private static final long serialVersionUID = 2184461575682879942L;
    /**
     * 【服务商小程序AppID】 选填 string(256)
     * 1、服务商小程序AppID与商家小程序AppID，二选一必填；
     * 2、可填写当前服务商商户号已绑定的小程序AppID。
     */
    @Size(max = 256, message = "服务商小程序AppID不能超过256位")
    private String miniProgramAppId;

    /**
     * 【商家小程序AppID】 选填 string(256)
     * 微信B2B必填
     * 1、服务商小程序AppID与商家小程序AppID，二选一必填；
     * 2、请填写已认证的小程序AppID；
     * 3、完成进件后，系统发起特约商户号与该AppID的绑定（即配置为sub_appid可在发起支付时传入）
     * （1）若AppID主体与商家主体/服务商主体一致，则直接完成绑定；
     * （2）若AppID主体与商家主体/服务商主体不一致，则商户签约时显示《联合营运承诺函》，
     * 并且AppID的管理员需登录公众平台确认绑定意愿。
     */
    @Size(max = 256, message = "商家小程序AppID不能超过256位")
    private String miniProgramSubAppId;

    /**
     * 商家小程序名称
     */
    private String miniProgramAppName;


    /**
     * 【小程序截图】 选填 array[string(1024)]
     * 微信直连，请提供展示商品/服务的页面截图/设计稿（最多5张），若小程序未建设完善或未上线，则必填
     * 微信小程序B2B必填，一张即可
     */
    private List<String> miniProgramPics;

    /**
     * 确认订单付款界面截图
     * 微信小程序B2B必填
     */
    private String orderPayPic;

    /**
     * 小程序方案概述。长度限制：21-100字
     * 微信小程序B2B必填
     */
    @Size(min = 21, max = 100, message = "小程序方案概述不能小于21且不超过100字")
    private String description;

    /**
     * 【小程序密钥】 选填 string(1024)
     * 微信小程序B2B，必填，请填写小程序的密钥
     */
    private String miniProgramAppSecret;

}
