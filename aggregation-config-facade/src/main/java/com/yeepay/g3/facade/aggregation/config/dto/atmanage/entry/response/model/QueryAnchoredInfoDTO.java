package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * 挂靠信息子DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:57
 */
@Getter
@Builder
public class QueryAnchoredInfoDTO implements Serializable {
    private static final long serialVersionUID = -8468219594569028649L;

    /**
     * 挂靠状态
     */
    private String anchoredStatus;

    /**
     * 关联申请单号
     */
    private String bizApplyNo;

    /**
     * 关联失败原因
     */
    private String failMessage;

    /**
     * 挂靠商编
     */
    private String anchoredMerchantNo;

    /**
     * 挂靠渠道号
     */
    private String anchoredChannelNo;

}