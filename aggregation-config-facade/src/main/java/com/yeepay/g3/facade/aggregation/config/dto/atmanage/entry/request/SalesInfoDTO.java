package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.QualificationDTO;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/27 20:38
 */
@Data
public class SalesInfoDTO implements Serializable {

    private static final long serialVersionUID = -6120365543744921666L;

    @NotBlank(message = "客服电话不能为空")
    @Size(max = 32, message = "客服电话不能超过32位")
    private String servicePhone;

    /**
     * 经营许可证信息
     */
    @Valid
    @NotNull(message = "经营许可证信息不能为空")
    private QualificationDTO qualificationInfo;

    /**
     * 经营地址信息
     */
    @Valid
    @NotNull(message = "经营地址信息不能为空")
    private SalesAddressDTO salesAddressInfo;
}
