package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/17 21:31
 */
@Data
public class SalesAddressDTO implements Serializable {
    private static final long serialVersionUID = 707523258195434114L;

    /**
     * 省编码
     */
    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    /**
     * 市编码
     */
    @NotBlank(message = "市编码不能为空")
    private String cityCode;
    /**
     * 区编码
     */
    @NotBlank(message = "区编码不能为空")
    private String districtCode;
    /**
     * 地址
     */
    @NotBlank(message = "地址不能为空")
    private String address;

}
