package com.yeepay.g3.facade.aggregation.config.validator;


import com.yeepay.aggregation.config.share.kernel.enumerate.DocumentedEnum;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/2/23 21:44
 */
@Documented
@Constraint(validatedBy = EnumValueValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface EnumValid {
    String message() default "Invalid enum value";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    Class<? extends Enum<? extends DocumentedEnum<?>>> enumClass();

    boolean allowedStrEmpty() default false;
}