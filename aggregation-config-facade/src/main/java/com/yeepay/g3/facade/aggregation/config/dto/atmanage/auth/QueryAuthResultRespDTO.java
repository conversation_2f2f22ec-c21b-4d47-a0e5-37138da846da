package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * description: 认证结果
 * <AUTHOR>
 * @since 2025/5/26:18:23
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@Builder
public class QueryAuthResultRespDTO extends BaseResponseDTO implements Serializable {


    private static final long serialVersionUID = -2364757475461745628L;

    /**
     * 认证状态
     */
    private String applymentState;

    /**
     * 二维码数据
     */
    private String qrcodeData;

    /**
     * 认证驳回原因列表
     */
    private List<AuthRejectReasonDTO> rejectReasonList;
}
