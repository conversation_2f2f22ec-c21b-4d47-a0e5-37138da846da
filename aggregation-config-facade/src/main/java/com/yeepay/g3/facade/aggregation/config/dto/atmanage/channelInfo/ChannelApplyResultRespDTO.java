package com.yeepay.g3.facade.aggregation.config.dto.atmanage.channelInfo;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * description: 查询渠道申请结果响应 DTO
 * <AUTHOR>
 * @since 2025/5/27:16:49
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class ChannelApplyResultRespDTO extends BaseResponseDTO implements Serializable {

    private static final long serialVersionUID = -6681079153204295314L;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道号申请结果
     */
    private String applyResult;

    /**
     * 渠道号申请结果描述
     */
    private String applyResultDesc;
}
