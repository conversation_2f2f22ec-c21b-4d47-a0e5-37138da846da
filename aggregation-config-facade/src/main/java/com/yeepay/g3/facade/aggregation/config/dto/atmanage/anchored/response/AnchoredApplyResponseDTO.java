package com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.response;

import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 挂靠申请响应DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/5  14:04
 */
@Data
public class AnchoredApplyResponseDTO extends BaseResponseDTO {
    private static final long serialVersionUID = 1L;


    @NotBlank(message = "业务场景不能为空")
    @EnumValid(enumClass = BizSceneEnum.class, message = "业务场景不合法")
    private String bizScene;

    /**
     * 业务申请单号
     */
    private String bizApplyNo;

    /**
     *
     */
    private String orderNo;

}
