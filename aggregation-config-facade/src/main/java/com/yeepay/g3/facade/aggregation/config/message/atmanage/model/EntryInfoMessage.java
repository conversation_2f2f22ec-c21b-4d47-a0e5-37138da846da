package com.yeepay.g3.facade.aggregation.config.message.atmanage.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 进件信息
 * @author: Mr.yin
 * @date: 2025/6/3  20:13
 */
@Data
public class EntryInfoMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 进件状态
     */
    private String entryStatus;

    /**
     * 进件审核状态
     */
    private String entryAuditStatus;

    /**
     * 返回异常码
     */
    private String failCode;

    /**
     * 失败原因
     */
    private String failMessage;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 渠道标识
     */
    private String channelIdentifier;

    /**
     * 机构商户号
     */
    private String institutionMerchantNo;

    /**
     * 签约地址
     * 目前只有微信直连场景下进件，部分状态下该字段才有值
     */
    private String signUrl;

    /**
     * 处理单号
     */
    private String entryApplyNo;


}