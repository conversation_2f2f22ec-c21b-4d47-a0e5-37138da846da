package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * 描述: UBO（Ultimate Beneficial Owner）信息 DTO
 * <AUTHOR> @since 2025/5/20
 * Company: 易宝支付(YeePay)
 */
@Data
public class UboInfoDTO implements Serializable {

    private static final long serialVersionUID = 1438643408882872030L;

    /**
     * 证件类型
     */
    @NotBlank(message = "受益人证件类型不能为空")
    private String cardType;

    /**
     * 证件正面照片
     */
    private String cardFrontImg;

    /**
     * 证件反面照片
     */
    private String cardBackImg;

    /**
     * 证件姓名
     */
    private String name;

    /**
     * 证件号码
     */
    private String cardNo;

    /**
     * 证件居住地址
     */
    private String certAddress;

    /**
     * 证件有效期开始时间
     * YYYY-MM-DD
     */
    private String effectTime;

    /**
     * 证件有效期结束时间
     * YYYY-MM-DD 长期时填长期
     */
    private String expireTime;
}
