package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response;

import com.yeepay.g3.facade.aggregation.config.dto.BaseResponseDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.MerchantInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/5/27 20:11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class EntryApplyDTO extends BaseResponseDTO {
    private MerchantInfoDTO merchantInfo;
    private String bizApplyNo;
    private String applyNo;

}
