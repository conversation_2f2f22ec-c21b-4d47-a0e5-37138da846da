package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * 进件信息子DTO
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:57
 */
@Getter
@Builder
public class QueryEntryInfoDTO implements Serializable {

    private static final long serialVersionUID = -4197205719928649862L;
    /**
     * 进件状态
     */
    private String entryStatus;

    /**
     * 关联申请单号
     */
    private String bizApplyNo;

    /**
     * 关联失败原因
     */
    private String failMessage;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 错误码
     */
    private String errorCode;

}