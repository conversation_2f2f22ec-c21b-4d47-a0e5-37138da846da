package com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * 商户处罚请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 16:19
 */
@Setter
@Getter
@NotNull(message = "查询商户处罚参数不能为空")
public class DisposalNotifyQueryRequestDTO implements Serializable {

    private static final long serialVersionUID = -3809774626643023908L;

    /**
     * 渠道,1是微信，2是支付宝
     */
    private Integer channelType;
    /**
     * 易宝商编
     */
    private String yeepayMerchantNo;

    /**
     * 报备商户号
     */
    private String reportMerchantNo;

    /**
     * 每页条数
     */
    @NotNull(message = "处罚查询分页参数不能为空")
    private Integer pageSize;

    /**
     * 页数
     */
    @NotNull(message = "处罚查询分页参数不能为空")
    private Integer pageNum;

    /**
     * 代理商编
     */
    private String agentMerchantNo;
    /**
     * 顶级商编
     */
    private String topMerchantNo;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 处罚类型
     */
    private String punishType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 处罚方案/原因
     */
    private String punishPlan;
    /**
     * 开始时间
     */
    @NotNull(message = "处罚查询开始时间不能为空")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "处罚查询结束时间不能为空")
    private Date endTime;

    /**
     * 查询范围
     */
    private String queryType;
    /**
     * 是否查询自己
     * true:查询自己+下级
     * false:只查下级
     */
    private Boolean withSelf;

    /**
     * 查询下级类型
     */
    private Set<String> roleType;
    /**
     * 子商户角色
     */
    private Set<String> roles;
}
