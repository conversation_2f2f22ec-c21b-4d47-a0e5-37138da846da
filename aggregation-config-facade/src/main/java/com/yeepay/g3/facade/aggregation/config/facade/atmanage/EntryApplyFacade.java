package com.yeepay.g3.facade.aggregation.config.facade.atmanage;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.EntryApplyRequest;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.QueryEntryAndAnchoredRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.EntryApplyDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.QueryEntryAndAnchoredResponseDTO;

/**
 * <AUTHOR>
 * @date 2025/5/27 20:10
 */
public interface EntryApplyFacade {

    EntryApplyDTO entryApply(EntryApplyRequest entryApplyRequest);


    /**
     * 查询商户聚合进件 + 挂靠结果
     * 综合所有请求的当前结果
     * @param reqDTO
     * @return
     */
    QueryEntryAndAnchoredResponseDTO queryMerchantEntryAndAnchoredInfo(QueryEntryAndAnchoredRequestDTO reqDTO);
}
