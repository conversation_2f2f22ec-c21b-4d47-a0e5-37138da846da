package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.ContactInfoDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.SubjectInfoDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.UboInfoDTO;
import com.yeepay.g3.facade.aggregation.config.validator.AtLeastOneNotEmpty;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * description: 认证申请请求 DTO
 * <AUTHOR>
 * @since 2025/5/21:10:55
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
@AtLeastOneNotEmpty(fields = {"channelNo", "channelIdentifier"}, message = "渠道号和渠道标识,二选一必填")
public class AuthApplyReqDTO implements Serializable {

    private static final long serialVersionUID = 5823893800853389621L;
    /**
     * 商户编码（必填）
     */
    @NotBlank(message = "商户编码不能为空")
    private String merchantNo;

    /**
     * （必填）
     */
    @NotBlank(message = "渠道类型不能为空")
    @EnumValid(enumClass = PayChannelEnum.class, message = "渠道类型不合法")
    private String channelType;

    /**
     * 支付场景（必填）
     */
    @NotBlank(message = "支付场景不能为空")
    @EnumValid(enumClass = PaySceneEnum.class, message = "支付场景不合法")
    private String payScene;

    /**
     * 渠道标识（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelIdentifier;

    /**
     * 渠道号（"渠道号" 和 "渠道标识" 二选一必填）
     */
    private String channelNo;

    /**
     * 业务申请单号（必填）
     */
    @NotBlank(message = "业务申请单号不能为空")
    private String bizApplyNo;

    /**
     * 联系人信息（对象字段）
     */
    @Valid
    @NotEmpty(message = "联系人信息不能为空")
    private ContactInfoDTO contactInfo;

    /**
     * 主体信息（对象字段）
     */
    @Valid
    @NotEmpty(message = "主体信息不能为空")
    private SubjectInfoDTO subjectInfo;

    /**
     * 辅助证明信息（选填）
     */
    @Valid
    private AssistProveDTO assistProveInfo;

    /**
     * 最终受益人信息列表（选填）
     */
    @Valid
    @Size(max = 4, message = "最终受益人信息列表不可大于4个")
    private List<UboInfoDTO> uboInfoList;

}

