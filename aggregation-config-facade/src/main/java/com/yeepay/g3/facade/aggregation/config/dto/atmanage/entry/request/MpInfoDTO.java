package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 21:10
 */
@Data
public class MpInfoDTO implements Serializable {
    private static final long serialVersionUID = -6588348129927593405L;
    /**
     * 【服务商服务号或公众号AppID】 选填 string(256)
     * 1、服务商服务号或公众号AppID、商家服务号或公众号AppID，二选一必填；
     * 2、可填写当前服务商商户号已绑定的服务号或公众号AppID。
     */
    @Size(max = 256, message = "服务商服务号或公众号AppID不能超过256位")
    private String mpAppId;

    /**
     * 【商家服务号或公众号AppID】 选填 string(256)
     * 1、服务商服务号或公众号AppID、商家服务号或公众号AppID，二选一必填；
     * 2、可填写与商家主体一致且已认证的服务号或公众号AppID，需是已认证的服务号、政府或媒体类型的公众号；
     * 3、审核通过后，系统将发起特约商家商户号与该AppID的绑定（即配置为sub_appid），
     * 服务商随后可在发起支付时选择传入该appid，以完成支付，并获取sub_openid用于数据统计，营销等业务场景。
     */
    @Size(max = 256, message = "商家服务号或公众号AppID不能超过256位")
    private String mpSubAppId;

    /**
     * 商家服务号或公众号名称
     */
    private String mpAppName;

    /**
     * 【服务号或公众号页面截图】 必填 array[string(1024)]
     * 1、请提供展示商品/服务的页面截图/设计稿（最多5张），若服务号或公众号未建设完善或未上线请务必提供；
     * 2、请填写通过图片上传API预先上传图片生成好的MediaID。
     */
    @NotNull(message = "服务号或公众号页面截图不能为空")
    //@Size(min = 1, max = 5, message = "服务号或公众号页面截图不能小于1且不能大于5")
    private List<String> mpPics;

}
