package com.yeepay.g3.facade.aggregation.config.dto;

import com.yeepay.g3.facade.aggregation.config.enums.ErrorCodeEnum;

import java.io.Serializable;

public class BaseResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 返回码
	 */
	protected String code = ErrorCodeEnum.SUCCESS.getCode();

	/**
	 * 返回信息描述
	 */
	protected String message = ErrorCodeEnum.SUCCESS.getDesc();

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	@Override
	public String toString() {
		return "BaseResponseDTO{" +
				"code='" + code + '\'' +
				", message='" + message + '\'' +
				'}';
	}
}