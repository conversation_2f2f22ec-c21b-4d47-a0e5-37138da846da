package com.yeepay.g3.facade.aggregation.config.validator;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * description:
 *
 * <AUTHOR>
 * @since 2025/5/26:18:38
 * Company: 易宝支付(YeePay)
 */
@Target(ElementType.TYPE) // 应用在类级别
@Retention(RetentionPolicy.RUNTIME)
public @interface AtLeastOneNotEmptyContainer {
    AtLeastOneNotEmpty[] value();
}
