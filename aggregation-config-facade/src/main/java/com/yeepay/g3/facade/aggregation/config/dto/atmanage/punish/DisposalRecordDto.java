package com.yeepay.g3.facade.aggregation.config.dto.atmanage.punish;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * 处罚记录
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/10 16:56
 */
@Setter
@Getter
public class DisposalRecordDto implements Serializable {

    private static final long serialVersionUID = -4914936715553544185L;
    /**
     * 易宝商编
     */
    private String yeepayMerchantNo;

    /**
     * 易宝商户名
     */
    private String yeepayMerchantName;

    /**
     * 报备商户号
     */
    private String reportMerchantNo;

    /**
     * 渠道号
     */
    private String channelNo;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 处罚类型
     */
    private String punishType;

    /**
     * 处罚方案/原因
     */
    private String punishPlan;

    /**
     * 处罚时间
     */
    private Date punishTime;

    /**
     * 限制恢复功能
     */
    private String limitRecoverFunction;


}
