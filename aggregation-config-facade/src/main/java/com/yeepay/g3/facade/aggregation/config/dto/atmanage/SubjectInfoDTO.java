package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.SubjectTypeEnum;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request.SalesInfoDTO;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * description: 主体信息 DTO
 * <AUTHOR>
 * @since 2025/5/21:10:52
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class SubjectInfoDTO implements Serializable {

    private static final long serialVersionUID = 7736086411795363499L;

    /**
     * 主体ID
     */
    private String subjectId;

    /**
     * 主体类型
     */
    @NotBlank(message = "主体类型不能为空")
    @EnumValid(enumClass = SubjectTypeEnum.class, message = "主体类型不合法")
    private String subjectType;

    /**
     * 单位证明函照片（条件必填）
     * 1、主体类型为政府机关、事业单位选传：
     * （1）若上传，则审核通过后即可签约，无需汇款验证；
     * （2）若未上传，则审核通过后，需汇款验证。
     * 2、主体为个体户、企业、其他组织等，不需要上传本字段；
     * 3、请参照示例图打印单位证明函，全部信息需打印，不支持手写商户信息，并加盖公章；
     *
     */
    private String certificateLetterCopy;

    /**
     * 是否金融机构
     * 未传入将默认填写：false
     */
    private Boolean financeInstitution = false;

    /**
     * 登记证书信息
     * 主体为政府机关/事业单位/其他组织/个体户/企业时
     */
    @Valid
    private RegistrationCertificateDTO certificateInfo;

    /**
     * 经营者/法人身份信息（必填）小微
     * 1、个体户：请上传经营者的身份证件；
     * 2、企业/社会组织：请上传法人的身份证件；
     * 3、政府机关/事业单位：请上传法人/经办人的身份证件。
     */
    @Valid
    @NotNull(message = "经营者/法人身份信息不能为空")
    private CompanyRepresentativeDTO companyRepresentativeInfo;


    /**
     * 经营信息
     */
    @Valid
    @NotNull(message = "经营信息不能为空")
    private SalesInfoDTO salesInfo;

    /**
     * 金融机构许可证 金融机构时必填
     */
    @Valid
    private FinanceInstitutionDTO financeInstitutionInfo;

}
