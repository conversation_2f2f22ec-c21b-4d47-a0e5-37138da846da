package com.yeepay.g3.facade.aggregation.config.dto;

import java.io.Serializable;

/**
 * @description:
 * @author: xuchen.liu
 * @date: 2024-12-13 13:44
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public class ControlConfigResponseDTO extends BaseResponseDTO implements Serializable {

    private static final long serialVersionUID = -6506004520761890096L;
    /**
     * 配置id
     */
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "ControlConfigResponseDTO{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
