package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import com.yeepay.aggregation.config.share.kernel.enumerate.CertificateTypeEnum;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.AuthFacade;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.ChannelNoFacade;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.EntryApplyFacade;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * description: 登记证书信息DTO
 *
 * <AUTHOR>
 * @since 2025/5/20:18:44
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class RegistrationCertificateDTO implements Serializable {

    private static final long serialVersionUID = -1360791916115858311L;
    /**
     * 商户名称
     * 请填写登记证书上的商户名称
     */
    @NotBlank(message = "证书商户名称不能为空")
    private String certMerchantName;

    /**
     * 法人姓名/经营者姓名
     */
    @NotBlank(message = "证书法人姓名不能为空", groups = {EntryApplyFacade.class, AuthFacade.class})
    private String certLegalPerson;

    /**
     * 注册地址
     * 进件：微信直连&微信小程序B2B，个体户/企业可填
     */
    @NotBlank(message = "证书注册地址不能为空", groups = {ChannelNoFacade.class})
    private String certCompanyAddress;

    /**
     * 有效期限开始日期
     * YYYY-MM-DD
     */
    @NotBlank(message = "证书有效期限开始日期不能为空", groups = {AuthFacade.class})
    private String effectTime;

    /**
     * 有效期限结束日期
     * YYYY-MM-DD
     * 长期
     */
    @NotBlank(message = "证书有效期限结束日期不能为空", groups = {AuthFacade.class})
    private String expireTime;

    /**
     * 证书类型
     */
    @NotBlank(message = "证书类型不能为空", groups = {EntryApplyFacade.class, AuthFacade.class})
    @EnumValid(enumClass = CertificateTypeEnum.class, allowedStrEmpty = true, message = "证书类型不合法")
    private String certType;

    /**
     * 证书编号
     */
    @NotBlank(message = "证书编号不能为空")
    private String certNumber;

    /**
     * 证书照片
     * 进件：微信直连&微信小程序B2B，个体户/企业必填
     */
    @NotBlank(message = "证书照片不能为空", groups = {ChannelNoFacade.class})
    private String certCopy;

}

