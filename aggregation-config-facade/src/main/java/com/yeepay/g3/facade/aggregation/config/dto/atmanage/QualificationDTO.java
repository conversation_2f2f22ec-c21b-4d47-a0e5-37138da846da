package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.validator.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * description: 资质信息DTO
 *
 * <AUTHOR>
 * @since 2025/5/20:18:56
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class QualificationDTO implements Serializable {

    private static final long serialVersionUID = 305245830986347021L;

    /**
     * 行业类目id
     * 商户二级分类
     */
    @NotBlank(message = "行业类目id不能为空")
    private String categoryId;

    /**
     * 行业经营许可证资质照片列表
     */
    private List<String> operationCopyList;
}
