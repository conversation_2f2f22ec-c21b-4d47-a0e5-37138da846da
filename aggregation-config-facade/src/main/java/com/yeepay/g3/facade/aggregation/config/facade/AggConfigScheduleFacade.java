package com.yeepay.g3.facade.aggregation.config.facade;

/**
 * @description: 聚合配置的定时 facade
 * @author: xuchen.liu
 * @date: 2024-12-30 17:58
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface AggConfigScheduleFacade {

    /**
     * 根据APP_ID 补偿
     *
     * @param appId appId
     */
    void compensateNotify(String appId);

    /**
     * 补偿 appid
     *
     * @param appId      appid
     * @param merchantNo
     */
    void compensateAppIdBind(String appId,String merchantNo);

    /**
     * 挂靠数据绑定补偿
     * @param mainMerchantNo 主商编号
     * @param subMerchantNo 子商编号
     * @param channel 渠道
     */
    void compensateAttachBind(String mainMerchantNo,String subMerchantNo,String channel);
}
