package com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.ActivityTypeEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.AnchoredType;
import com.yeepay.aggregation.config.share.kernel.enumerate.PayChannelEnum;
import com.yeepay.aggregation.config.share.kernel.enumerate.PaySceneEnum;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 挂靠申请信息
 *
 * @author: Mr.yin
 * @date: 2025/6/5  11:34
 */
@Data
public class AnchoredApplyDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 挂靠类型
     */
    @EnumValid(enumClass = AnchoredType.class, message = "挂靠类型不合法")
    @NotBlank(message = "挂靠类型不能为空")
    @Size(max = 32, message = "挂靠类型不能超过32位")
    private String anchoredType;

    /**
     * 挂靠维度 是否需要跨sass体系
     */
    private Boolean anchoredDimension;

    /**
     * 挂靠商户编号
     * 指定挂靠商编时必传
     */
    @Size(max = 32, message = "挂靠商户编号不能超过32位")
    private String anchoredMerchantNo;

    /**
     * 场景
     */
    @EnumValid(enumClass = PaySceneEnum.class, message = "场景类型不合法")
    @NotBlank(message = "场景不能为空")
    @Size(max = 32, message = "场景类型不能超过32位")
    private String payScene;

    /**
     * 活动类型
     */
    @EnumValid(enumClass = ActivityTypeEnum.class, allowedStrEmpty = true, message = "活动类型不合法")
    @Size(max = 32, message = "活动类型不能超过32位")
    private String activityType;

    /**
     * 渠道类型
     */
    @EnumValid(enumClass = PayChannelEnum.class, message = "渠道类型不合法")
    @NotBlank(message = "渠道不能为空")
    @Size(max = 32, message = "渠道类型不能超过32位")
    private String channelType;


}
