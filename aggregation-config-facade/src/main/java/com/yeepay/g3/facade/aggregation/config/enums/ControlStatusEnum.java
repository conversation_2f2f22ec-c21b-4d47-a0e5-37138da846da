package com.yeepay.g3.facade.aggregation.config.enums;

/**
 * @description: 管控状态
 * @author: xuchen.liu
 * @date: 2024-12-10 14:06
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public enum ControlStatusEnum {

    /**
     * 已启用
     */
    ACTIVE("开启"),
    /**
     * 已禁用
     */
    CLOSED("关闭");

    private final String  desc;

    ControlStatusEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static String getNameByDesc(String name) {
        for (ControlStatusEnum status : ControlStatusEnum.values()) {
            if (status.name().equals(name)) {
                return status.getDesc();
            }
        }
        throw new IllegalArgumentException("未找到对应的状态枚举值: " + name);
    }

    @Override
    public String toString() {
        return "ControlStatusEnum{" +
                "desc='" + desc + '\'' +
                '}';
    }
}