package com.yeepay.g3.facade.aggregation.config.dto.atmanage;

import com.yeepay.g3.facade.aggregation.config.facade.atmanage.AuthFacade;
import com.yeepay.g3.facade.aggregation.config.facade.atmanage.EntryApplyFacade;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * description: 账户信息数据传输对象
 * <AUTHOR>
 * @since 2025/5/27:16:26
 * Company: 易宝支付(YeePay)
 */
@Getter
@Setter
public class SettleAccountDTO implements Serializable {

    private static final long serialVersionUID = 4617472030463793024L;

    /**
     * 银行账户
     */
    @NotBlank(message = "银行账户不能为空")
    private String cardNo;

    /**
     * 银行账户名称
     */
    @NotBlank(message = "银行账户名称不能为空")
    private String cardName;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 总行名称
     */
    @NotBlank(message = "总行名称不能为空", groups = {EntryApplyFacade.class, AuthFacade.class})
    private String bankName;

    /**
     * 支行名称
     */
    @NotBlank(message = "支行名称不能为空")
    private String bankBranchName;

    /**
     * 省份名称
     */
    @NotBlank(message = "省不能为空")
    private String provName;

    /**
     * 市名称
     */
    @NotBlank(message = "市不能为空")
    private String cityName;

}
