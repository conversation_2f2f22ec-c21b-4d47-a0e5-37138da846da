package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.response.model;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * 进件详情信息
 *
 * @author: Mr.yin
 * @date: 2025/6/3  10:57
 */
@Builder
@Getter
public class QueryEntryAndAnchoredDetailDTO implements Serializable {

    private static final long serialVersionUID = 6965871346908050833L;
    /**
     * 场景类型
     */
    private String payScene;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 费率类型
     */
    private String feeType;

    /**
     * 通道返回的活动类型
     */
    private String channelActivityType;

    /**
     * 渠道类型
     */
    private String payChannel;

    /**
     * 银行编码
     */
    private String digitalCurrencyBankCode;

    /**
     * 挂靠信息
     */
    private List<QueryAnchoredInfoDTO> anchoredInfoList;

    /**
     * 进件信息
     */
    private QueryEntryInfoDTO entryInfo;

    /**
     * 辅助终端报备状态
     */
    private String terminalReportStatus;

    /**
     * 辅助终端报备失败原因
     */
    private String terminalReportFailMessage;

    /**
     * 创建分账方状态
     */
    private String splitCreateStatus;


}