package com.yeepay.g3.facade.aggregation.config.facade.atmanage;

import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request.AnchoredApplyReqDTO;
import com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.response.AnchoredApplyResponseDTO;

/**
 * 挂靠服务相关接口
 *
 * @author: Mr.y<PERSON>
 * @date: 2025/6/5  10:44
 */
public interface AnchoredApplyFacade {

    /**
     * 挂靠申请
     * @param reqDTO
     * @return
     */
    AnchoredApplyResponseDTO anchoredApply(AnchoredApplyReqDTO reqDTO);

}
