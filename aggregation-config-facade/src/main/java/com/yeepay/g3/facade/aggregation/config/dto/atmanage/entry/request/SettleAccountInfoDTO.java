package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.BankAccountTypeEnum;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/27 20:15
 */
@Data
public class SettleAccountInfoDTO implements Serializable {
    private static final long serialVersionUID = 3383065683192190354L;
    /**
     * 银行账户类型
     * 微信直连&微信小程序B2B必填
     */
    @EnumValid(enumClass = BankAccountTypeEnum.class, allowedStrEmpty = true, message = "银行账户类型不合法")
    private String bankAccountType;

    /**
     * 开户名称
     * 微信直连&微信小程序B2B必填
     * 商户结算卡开户行名称和商户结算卡银行编码二选一必填
     * 工行数币必填
     */
    @NotBlank(message = "开户名称不能为空")
    @Size(max = 128, message = "开户名称不能超过128位")
    private String cardName;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    @Size(max = 32, message = "银行卡号不能超过32位")
    private String cardNo;

    /**
     * 银行编码
     * 工行数币必填
     * 微信直连可填
     */
    private String bankCode;

    /**
     * 开户行所在区
     * 微信直连&微信小程序B2B必填
     */
    private String distinctCode;

    /**
     * 联行号
     * 微信直连&微信小程序B2B &工行数币必填
     */
    @Size(max = 12, message = "联行号不能超过12位")
    private String branchCode;
    /**
     * 开户银行全称
     * 微信直连&微信小程序B2B必填
     * 商户结算卡开户行名称和商户结算卡银行编码二选一必填
     * 工行数币必填
     */
    private String bankName;
    /**
     * 支行名称
     * 微信直连&微信小程序B2B必填
     */
    private String bankBranchName;
}
