package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import lombok.Builder;

import java.io.Serializable;

/**
 * description: 认证拒绝原因
 * <AUTHOR>
 * @since 2025/5/26:19:05
 * Company: 易宝支付(YeePay)
 */
@Builder
public class AuthRejectReasonDTO implements Serializable {

    private static final long serialVersionUID = 4963946294576299562L;

    /**
     * 驳回参数
     */
    private String rejectParam;

    /**
     * 驳回原因
     */
    private String rejectReason;
}
