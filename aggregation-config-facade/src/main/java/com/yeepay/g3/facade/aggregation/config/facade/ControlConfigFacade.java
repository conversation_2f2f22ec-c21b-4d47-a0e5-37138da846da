package com.yeepay.g3.facade.aggregation.config.facade;

import com.yeepay.g3.facade.aggregation.config.dto.ControlConfigRequestDTO;
import com.yeepay.g3.facade.aggregation.config.dto.ControlConfigResponseDTO;

/**
 * @description: 管控配置facade
 * @author: xuchen.liu
 * @date: 2024-12-13 11:26
 * @modification: {}
 * @version: 1.0
 * @company: 易宝支付(YeePay)
 */
public interface ControlConfigFacade {

    /**
     *  配置信息管理
     * @param controlConfigIRequestDTO controlConfigIRequestDTO
     * @return ControlConfigIResponseDTO
     */
    ControlConfigResponseDTO configManage(ControlConfigRequestDTO controlConfigIRequestDTO);
}
