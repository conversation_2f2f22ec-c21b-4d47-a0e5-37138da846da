package com.yeepay.g3.facade.aggregation.config.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;

public class AtLeastOneNotEmptyValidator implements ConstraintValidator<AtLeastOneNotEmpty, Object> {

    private String[] fields;

    @Override
    public void initialize(AtLeastOneNotEmpty constraintAnnotation) {
        // 获取注解中指定的字段列表
        this.fields = constraintAnnotation.fields();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return false;
        }

        // 如果 fields 为空，则默认使用类的所有属性进行校验
        if (fields.length == 0) {
            try {
                Field[] declaredFields = value.getClass().getDeclaredFields();
                for (Field field : declaredFields) {
                    if ("serialVersionUID".equals(field.getName())) {
                        continue;
                    }
                    field.setAccessible(true);
                    Object fieldValue = field.get(value);
                    if (fieldValue != null && !fieldValue.toString().isEmpty()) {
                        return true;
                    }
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("无法访问类的字段", e);
            }
            return false;
        }

        // 如果 fields 不为空，则仅校验指定的字段
        for (String fieldName : fields) {
            try {
                Field field = value.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                Object fieldValue = field.get(value);
                if (fieldValue != null && !fieldValue.toString().isEmpty()) {
                    return true;
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException("字段 " + fieldName + " 不存在或无法访问", e);
            }
        }
        return false;
    }
}
