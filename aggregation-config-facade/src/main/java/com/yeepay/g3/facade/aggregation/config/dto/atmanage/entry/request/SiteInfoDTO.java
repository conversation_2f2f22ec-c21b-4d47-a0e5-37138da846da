package com.yeepay.g3.facade.aggregation.config.dto.atmanage.entry.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/2 20:24
 */
@Data
public class SiteInfoDTO implements Serializable {
    private static final long serialVersionUID = -6376897062918478576L;
    /**
     * 站点类型
     * 网站:01
     * APP :02
     * 服务窗:03
     * 公众号:04
     * 其他:05
     * 支付宝小程序:06
     */
    @NotBlank(message = "站点类型不能为空")
    private String siteType;

    /**
     * 站点Url地址 站点类型：网站时必填
     */
    private String siteUrl;

    /**
     * 站点名称 站点类型：APP时必填
     */
    private String siteName;
}
