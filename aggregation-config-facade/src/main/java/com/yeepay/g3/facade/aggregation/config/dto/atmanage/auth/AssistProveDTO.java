package com.yeepay.g3.facade.aggregation.config.dto.atmanage.auth;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.validator.constraints.NotBlank;
import java.io.Serializable;

/**
 * 描述: 辅助证明材料 DTO
 * <AUTHOR>
 * @since 2025/5/20
 * Company: 易宝支付(YeePay)
 */
@Setter
@Getter
public class AssistProveDTO implements Serializable {

    private static final long serialVersionUID = -5414635792828442945L;

    /**
     * 小微经营类型
     */
    @NotBlank(message = "小微经营类型不能为空")
    private String microBizType;

    /**
     * 门店名称
     */
    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    /**
     * 门店省编码
     */
    @NotBlank(message = "门店省编码不能为空")
    private String provinceCode;

    /**
     * 门店省
     */
    @NotBlank(message = "门店省不能为空")
    private String province;

    /**
     * 门店市编码
     */
    @NotBlank(message = "门店市编码不能为空")
    private String cityCode;

    /**
     * 门店市
     */
    @NotBlank(message = "门店市不能为空")
    private String city;

    /**
     * 门店区编码
     */
    @NotBlank(message = "门店区编码不能为空")
    private String districtCode;

    /**
     * 门店区
     */
    @NotBlank(message = "门店区不能为空")
    private String district;

    /**
     * 门店地址
     */
    @NotBlank(message = "门店地址不能为空")
    private String storeAddress;

    /**
     * 门店门头照片
     */
    @NotBlank(message = "门店门头照片不能为空")
    private String storeHeaderCopy;

    /**
     * 店内环境照片
     */
    @NotBlank(message = "店内环境照片不能为空")
    private String storeIndoorCopy;
}
