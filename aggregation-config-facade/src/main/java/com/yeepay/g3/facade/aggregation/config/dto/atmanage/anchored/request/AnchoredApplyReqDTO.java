package com.yeepay.g3.facade.aggregation.config.dto.atmanage.anchored.request;

import com.yeepay.aggregation.config.share.kernel.enumerate.BizSceneEnum;
import com.yeepay.g3.facade.aggregation.config.validator.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 挂靠申请请求参数DTO
 */
@Data
public class AnchoredApplyReqDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @NotBlank(message = "业务场景不能为空")
    @EnumValid(enumClass = BizSceneEnum.class, message = "业务场景不合法")
    private String bizScene;

    /**
     * 业务申请单号
     */
    @NotBlank(message = "业务申请单号不能为空")
    @Size(max = 32, message = "业务申请单号不能超过32位")
    private String bizApplyNo;

    /**
     * 顶级商编
     */
    private String topLevelMerchantNo;

    /**
     * 父级商编
     */
    private String parentMerchantNo;

    /**
     * 商编
     */
    private String merchantNo;

    /**
     * 挂靠信息申请列表
     */
    @Valid
    private List<AnchoredApplyDetailDTO> anchoredApplyDetailList;


}
