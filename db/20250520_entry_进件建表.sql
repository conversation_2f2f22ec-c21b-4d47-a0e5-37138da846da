-- 数据源：dzh
-- Schema: AGGCONF

-- 渠道号申请记录
CREATE TABLE TBL_CHANNEL_NO_APPLY_RECORD (
    ID BIGINT NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    MERCHANT_NO VARCHAR(16) NOT NULL, -- 商户编码
    BIZ_SCENE VARCHAR(16) NOT NULL, -- 业务场景
    BIZ_APPLY_NO VARCHAR(32) NOT NULL, -- 业务申请单号
    APPLY_NO VARCHAR(32) NOT NULL, -- 申请单号
    PAY_CHANNEL VARCHAR(16) NOT NULL, -- 支付渠道(WECHAT、ALIPAY、BANK)
    CHANNEL_NO VARCHAR(32) DEFAULT '', -- 渠道号
    CHANNEL_IDENTIFIER VARCHAR(16) DEFAULT '', -- 渠道标识
    PAY_SCENE VARCHAR(16) NOT NULL, -- 支付场景
    ACTIVITY_TYPE VARCHAR(32) DEFAULT 'NORMAL', -- 活动类型
    STATUS VARCHAR(16) DEFAULT 'WAIT_SUBMIT', -- 状态
    SALES_NAME VARCHAR(64) DEFAULT '', -- 销售名称
    INDUSTRY_NAME VARCHAR(64) DEFAULT '', -- 行业名称
    IS_USE_YP_CHANNEL_NO SMALLINT DEFAULT 0, -- 是否使用易宝渠道号 (0 或 1)
    FUND_LIQUIDATION_TYPE VARCHAR(16) DEFAULT '', -- 资金清算类型
    SERVICE_PHONE VARCHAR(32) DEFAULT '', -- 客服电话
    DIGITAL_CURRENCY_BANK_CODE VARCHAR(16) DEFAULT '', -- 数币银行代码
    CONTACT_INFO CLOB DEFAULT '', -- 联系人信息
    COMPANY_REPRESENTATIVE_INFO CLOB DEFAULT '', -- 公司经办人/法人信息
    CERTIFICATE_INFO CLOB DEFAULT '', -- 证书信息
    SETTLE_ACCOUNT_INFO CLOB DEFAULT '', -- 结算账户信息
    EXTERNAL_INFO CLOB DEFAULT '', -- 扩展信息
    APPLY_RESULT_DESC CLOB DEFAULT '', -- 申请结果描述
    CREATED_BY VARCHAR(64) NOT NULL, -- 创建人
    CREATE_DT TIMESTAMP DEFAULT CURRENT TIMESTAMP, -- 创建时间
    UPDATED_BY VARCHAR(64) NOT NULL, -- 更新人
    UPDATE_DT TIMESTAMP NOT NULL GENERATED ALWAYS FOR EACH ROW ON UPDATE AS ROW CHANGE TIMESTAMP-- 更新时间
);
CREATE UNIQUE INDEX UK_APPLY_NO ON TBL_CHANNEL_NO_APPLY_RECORD(APPLY_NO);
CREATE UNIQUE INDEX UK_BIZ_APPLY_NO_SCENE ON TBL_CHANNEL_NO_APPLY_RECORD(BIZ_APPLY_NO, BIZ_SCENE);

-- 渠道号信息
CREATE TABLE TBL_MERCHANT_CHANNEL_NO (
    ID BIGINT NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    MERCHANT_NO VARCHAR(16) NOT NULL, -- 商户编码
    CHANNEL_NO VARCHAR(32) NOT NULL, -- 渠道号
    APPLY_NO VARCHAR(32) NOT NULL, -- 申请单号
    PAY_CHANNEL VARCHAR(16) NOT NULL, -- 支付渠道(WECHAT、ALIPAY)
    PAY_SCENE VARCHAR(16) NOT NULL, -- 支付场景
    ACTIVITY_TYPE VARCHAR(32) DEFAULT 'NORMAL', -- 活动类型
    STATUS VARCHAR(16) DEFAULT 'ACTIVE', -- 状态
    CHANNEL_INSTITUTION_CODE VARCHAR(16) DEFAULT 'YP', -- 渠道机构编码(YP：易宝、HK：海科)
    CREATED_BY VARCHAR(64) NOT NULL, -- 创建人
    CREATE_DT TIMESTAMP DEFAULT CURRENT TIMESTAMP, -- 创建时间
    UPDATED_BY VARCHAR(64) NOT NULL, -- 更新人
    UPDATE_DT TIMESTAMP NOT NULL GENERATED ALWAYS FOR EACH ROW ON UPDATE AS ROW CHANGE TIMESTAMP-- 更新时间
);
CREATE UNIQUE INDEX UK_CHANNEL_NO ON TBL_MERCHANT_CHANNEL_NO(CHANNEL_NO);
CREATE INDEX IDX_MCH_CHANNEL_SCENE_ACT
ON TBL_MERCHANT_CHANNEL_NO (MERCHANT_NO, PAY_CHANNEL, PAY_SCENE, ACTIVITY_TYPE);


CREATE TABLE T_ANCHORED_ORDER
(
    ID                 BIGINT      NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,                   -- 主键ID
    BIZ_SCENE          VARCHAR(16) NOT NULL,                                                                -- 业务场景
    BIZ_APPLY_NO       VARCHAR(32) NOT NULL,                                                                -- 业务申请单号
    ORDER_NO           VARCHAR(32) NOT NULL,                                                                -- 订单号
    MERCHANT_NO        VARCHAR(16) NOT NULL,                                                                -- 商户编号
    PARENT_MERCHANT_NO VARCHAR(16)          DEFAULT '',                                                     -- 父商户编号
    TOP_MERCHANT_NO    VARCHAR(16)          DEFAULT '',                                                     -- 集团顶级商户编号
    ORDER_STATUS       VARCHAR(16) NOT NULL,                                                                -- 挂靠订单状态
    FAIL_REASON        VARCHAR(255)         DEFAULT '',                                                     -- 失败原因
    FINISH_TIME        TIMESTAMP            DEFAULT NULL,                                                   -- 完成时间
    DEL_FLAG           SMALLINT    NOT NULL DEFAULT 0,                                                      --删除标记 (0-正常, 1-删除)                                                              -- 有效性 (0-无效, 1-有效)
    CREATED_BY         VARCHAR(64)          DEFAULT '',                                                     -- 创建人
    CREATE_DT          TIMESTAMP            DEFAULT CURRENT TIMESTAMP,                                      -- 创建时间
    UPDATED_BY         VARCHAR(64)          DEFAULT '',                                                     -- 更新人
    UPDATE_DT          TIMESTAMP   NOT NULL GENERATED ALWAYS FOR EACH ROW ON UPDATE AS ROW CHANGE TIMESTAMP -- 更新时间
);

-- 唯一索引：订单号（ORDER_NO）
CREATE UNIQUE INDEX UK_T_ANCHORED_ORDER_ORDER_NO ON T_ANCHORED_ORDER (ORDER_NO);

-- 唯一索引：业务方申请号+业务方编码
CREATE UNIQUE INDEX UK_T_ANCHORED_ORDER_BIZ_APPLY_NO ON T_ANCHORED_ORDER (BIZ_APPLY_NO, BIZ_SCENE);

-- 普通索引：商户编号
CREATE INDEX IDX_T_ANCHORED_ORDER_MERCHANT_NO ON T_ANCHORED_ORDER (MERCHANT_NO);

-- 普通索引：创建时间
CREATE INDEX IDX_T_ANCHORED_ORDER_CREATE_DT ON T_ANCHORED_ORDER (CREATE_DT);



CREATE TABLE T_ANCHORED_APPLY
(
    ID                         BIGINT      NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,                    -- 主键ID
    BIZ_SCENE                  VARCHAR(16) NOT NULL,                                                                 -- 业务场景
    BIZ_APPLY_NO               VARCHAR(32) NOT NULL,                                                                 -- 业务申请单号
    ORDER_NO                   VARCHAR(32) NOT NULL,                                                                 -- 订单号
    APPLY_NO                   VARCHAR(32) NOT NULL,                                                                 -- 申请单号
    MERCHANT_NO                VARCHAR(16) NOT NULL,                                                                 -- 商户编号
    PARENT_MERCHANT_NO         VARCHAR(16)   DEFAULT '',                                                             -- 父商户编号
    TOP_MERCHANT_NO            VARCHAR(16)   DEFAULT '',                                                             -- 集团顶级商户编号
    ANCHORED_TYPE              VARCHAR(32) NOT NULL,                                                                 -- 挂靠类型
    ANCHORED_DIMENSION         VARCHAR(16)   DEFAULT '',                                                             -- 挂靠维度
    RELATION_MERCHANT_NO       VARCHAR(16)   DEFAULT '',                                                             -- 关联挂靠商户编号
    PAY_SCENE                  VARCHAR(32)   DEFAULT '',                                                             -- 支付场景
    PAY_CHANNEL                VARCHAR(16)   DEFAULT '',                                                             -- 渠道类型
    ACTIVITY_TYPE              VARCHAR(32)   DEFAULT '',                                                             -- 活动类型
    SUPPORT_PAY_TYPE           BIGINT        DEFAULT 0,                                                              -- 支持的支付类型
    AUTH_FILE_NAME             VARCHAR(128)  DEFAULT '',                                                             -- 授权函文件名
    AUTH_FILE_URL              VARCHAR(128)  DEFAULT '',                                                             -- 授权函文件地址
    GROUP_NAME                 VARCHAR(64)   DEFAULT '',                                                             -- 集团名称
    ANCHORED_STATUS            VARCHAR(16)   DEFAULT '',                                                             -- 挂靠状态
    FAIL_REASON                VARCHAR(255)  DEFAULT '',                                                             -- 失败原因
    AGG_ANCHORED_MERCHANT_TEXT VARCHAR(2048) DEFAULT '',                                                             -- 聚合挂靠商编信息
    DEL_FLAG                   SMALLINT      DEFAULT 0,                                                              -- 有效性 (0-有效, 1-无效)
    CREATED_BY                 VARCHAR(64)   DEFAULT '',                                                             -- 创建人
    CREATE_DT                  TIMESTAMP     DEFAULT CURRENT TIMESTAMP,                                              -- 创建时间
    UPDATED_BY                 VARCHAR(64)   DEFAULT '',                                                             -- 更新人
    UPDATE_DT                  TIMESTAMP   NOT NULL GENERATED ALWAYS FOR EACH ROW ON UPDATE AS ROW CHANGE TIMESTAMP, -- 更新时间
    FINISH_TIME                TIMESTAMP     DEFAULT NULL
);
-- 唯一索引：申请单号
CREATE UNIQUE INDEX UK_T_ANCHORED_APPLY_APPLY_NO ON T_ANCHORED_APPLY (APPLY_NO);
-- 普通索引：业务方申请号+业务方编码
CREATE INDEX IDX_T_ANCHORED_APPLY_BIZ_APPLY_NO ON T_ANCHORED_APPLY (BIZ_APPLY_NO, BIZ_SCENE);
-- 普通索引：订单号
CREATE INDEX IDX_T_ANCHORED_APPLY_BIZ_ORDER_NO ON T_ANCHORED_APPLY (ORDER_NO);
-- 普通索引：商户编号
CREATE INDEX IDX_T_ANCHORED_APPLY_BIZ_MERCHANT_NO ON T_ANCHORED_APPLY (MERCHANT_NO);
-- 普通索引：创建时间
CREATE INDEX IDX_T_ANCHORED_APPLY_BIZ_CREATE_DT ON T_ANCHORED_APPLY (CREATE_DT);


CREATE TABLE t_channel_entry_order
(
    id            BIGINT        NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1), -- 自增主键

    order_no      VARCHAR(32)   NOT NULL,                                                             -- 订单号
    biz_scene     VARCHAR(16)   NOT NULL,                                                             -- 业务场景
    biz_apply_no  VARCHAR(32)   NOT NULL,                                                             -- 业务申请单号
    merchant_no   VARCHAR(16)   NOT NULL,                                                             -- 商户编号
    merchant_name VARCHAR(128)  NOT NULL,                                                             -- 商户名称
    merchant_info VARCHAR(1024) NOT NULL,                                                             -- 商户信息（大文本）338 1024
    short_name    VARCHAR(128)  NOT NULL,                                                             -- 简称
    subject_info  VARCHAR(2048) NOT NULL,                                                             -- 主体信息（大文本）1732 2048
    contact_info  VARCHAR(1024) NOT NULL,                                                             -- 联系信息（大文本） 201 1024
    extend_info   VARCHAR(1024) NOT NULL DEFAULT '',                                                  -- 扩展信息（大文本）36 1024
    remark        VARCHAR(128)  NOT NULL DEFAULT '',                                                  -- 备注
    del_flag      SMALLINT               DEFAULT 0,                                                   --删除标记 (0-正常, 1-删除)
    created_by    VARCHAR(64)   NOT NULL DEFAULT 'SYSTEM',                                            -- 创建人
    create_dt     TIMESTAMP     NOT NULL DEFAULT CURRENT TIMESTAMP,                                   -- 创建时间，默认当前时间
    updated_by    VARCHAR(64)   NOT NULL DEFAULT 'SYSTEM',                                            -- 更新人
    update_dt     TIMESTAMP     NOT NULL
        GENERATED ALWAYS FOR EACH ROW ON UPDATE AS ROW CHANGE TIMESTAMP                               -- 更新时间

);
CREATE UNIQUE INDEX uk_t_channel_entry_order_order_no ON t_channel_entry_order (order_no);
CREATE UNIQUE INDEX uk_t_channel_entry_order_biz_apply_no_biz_scene ON t_channel_entry_order (biz_apply_no, biz_scene);
-- 普通索引：创建时间
CREATE INDEX idx_t_channel_entry_order_create_dt ON t_channel_entry_order (create_dt);
-- 普通索引：update_dt
CREATE INDEX idx_t_channel_entry_order_update_dt ON t_channel_entry_order (update_dt);

CREATE TABLE t_channel_entry_apply_detail
(
    id                   BIGINT        NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1),

    order_no             VARCHAR(32)   NOT NULL,
    apply_no             VARCHAR(32)   NOT NULL,
    merchant_no          VARCHAR(16)   NOT NULL,
    merchant_name        VARCHAR(128)  NOT NULL,
    entry_type           VARCHAR(16)   NOT NULL,
    channel_no           VARCHAR(32)   NOT NULL DEFAULT '',
    channel_identifier   VARCHAR(16)   NOT NULL DEFAULT '',
    pay_scene            VARCHAR(32)   NOT NULL,
    pay_channel          VARCHAR(16)   NOT NULL,
    bank_code            VARCHAR(16)   NOT NULL DEFAULT '',
    channel_gateway_code VARCHAR(16)   NOT NULL DEFAULT '',
    activity_type        VARCHAR(32)   NOT NULL DEFAULT 'NORMAL',
    channel_params       VARCHAR(4096) NOT NULL,
    backup_count         SMALLINT      NOT NULL,
    status               VARCHAR(16)   NOT NULL DEFAULT 'INIT',
    audit_status         VARCHAR(16)   NOT NULL DEFAULT 'INIT',
    sign_url             VARCHAR(1024) NOT NULL DEFAULT '',
    flag                 BIGINT        NOT NULL DEFAULT 0,
    flag_status          BIGINT        NOT NULL DEFAULT 0,
    channel_mch_nos      VARCHAR(512)  NOT NULL DEFAULT '',
    finish_time          TIMESTAMP              DEFAULT NULL,
    fail_reason          VARCHAR(255)  NOT NULL DEFAULT '',
    anchored_order_no    VARCHAR(32)   NOT NULL DEFAULT '',
    del_flag             SMALLINT      NOT NULL DEFAULT 0, --删除标记 (0-正常, 1-删除)
    created_by           VARCHAR(64)   NOT NULL DEFAULT 'SYSTEM',
    create_dt            TIMESTAMP     NOT NULL DEFAULT CURRENT TIMESTAMP,
    updated_by           VARCHAR(64)   NOT NULL DEFAULT 'SYSTEM',
    update_dt            TIMESTAMP     NOT NULL
        GENERATED ALWAYS FOR EACH ROW ON UPDATE AS ROW CHANGE TIMESTAMP
);

-- 唯一索引：apply_no
CREATE UNIQUE INDEX uk_t_channel_entry_apply_detail_apply_no ON t_channel_entry_apply_detail (apply_no);

-- 普通索引：order_no
CREATE INDEX idx_t_channel_entry_apply_detail_apply_order_no ON t_channel_entry_apply_detail (order_no);
-- 普通索引：create_dt
CREATE INDEX idx_t_channel_entry_apply_detail_apply_create_dt ON t_channel_entry_apply_detail (create_dt);
-- 普通索引：update_dt
CREATE INDEX idx_t_channel_entry_apply_detail_apply_update_dt ON t_channel_entry_apply_detail (update_dt);