package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/2 17:44
 */
@Getter
@AllArgsConstructor
public enum EntryAuditStatus implements DocumentedEnum<String> {
    INIT("INIT", "初始化"),
    EDITING("EDITING", "编辑中（提交申请发生错误导致，请尝试重新提交）"),
    AUDITING("AUDITING", "审核中（申请单正在审核中，超级管理员用微信打开“签约链接”，完成绑定微信号后，申请单进度将通过微信公众号通知超级管理员，引导完成后续步骤）"),
    REJECTED("REJECTED", "已驳回（请按照驳回原因修改申请资料，超级管理员用微信打开“签约链接”，完成绑定微信号，后续申请单进度将通过微信公众号通知超级管理员）"),
    TO_BE_CONFIRMED("TO_BE_CONFIRMED", "待账户验证（请超级管理员使用微信打开返回的“签约链接”，根据页面指引完成账户验证）"),
    TO_BE_SIGNED("TO_BE_SIGNED", "待签约（请超级管理员使用微信打开返回的“签约链接”，根据页面指引完成签约）"),
    SIGNING("SIGNING", "开通权限中（系统开通相关权限中，请耐心等待）"),
    FINISHED("FINISHED", "已完成（商户入驻申请已完成）"),
    CANCELED("CANCELED", "已作废（申请单已被撤销）");
    ;
    private final String document;
    private final String desc;
}
