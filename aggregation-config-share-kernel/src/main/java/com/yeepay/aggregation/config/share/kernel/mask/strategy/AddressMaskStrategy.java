package com.yeepay.aggregation.config.share.kernel.mask.strategy;
import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import org.apache.commons.lang3.StringUtils;

@SensitiveMaskStrategy(type = SensitiveTypeEnum.ADDRESS)
public class AddressMaskStrategy implements MaskStrategy {
    private static final int PRESERVE_LENGTH = 6;
    private static final int MASK_LENGTH = 3;
    private static final String MASK = StringUtils.repeat("*", MASK_LENGTH);

    @Override
    public String mask(String content) {
        if (StringUtils.isEmpty(content)) {
            return StringUtils.EMPTY;
        }
        if (!isValid(content)) {
            return content;
        }
        return content.substring(0, PRESERVE_LENGTH) + MASK;
    }

    @Override
    public boolean isValid(String content) {
        return content != null && content.length() > PRESERVE_LENGTH;
    }
} 