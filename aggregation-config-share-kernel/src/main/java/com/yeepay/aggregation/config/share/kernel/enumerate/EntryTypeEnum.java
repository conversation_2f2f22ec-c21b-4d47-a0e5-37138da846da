package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 进件类型
 *
 * <AUTHOR>
 * @date 2025/6/2 17:22
 */
@Getter
@AllArgsConstructor
public enum EntryTypeEnum implements DocumentedEnum<String> {
    ENTRY("ENTRY", "进件"),
    PRIORITY_ANCHORED("PRIORITY_ANCHORED", "优先挂靠"),
    ENTRY_ANCHORED("ENTRY_ANCHORED", "进件+挂靠"),
    ;
    private final String document;
    private final String desc;

    public static boolean needAnchored(EntryTypeEnum entryTypeEnum) {
        return entryTypeEnum.equals(ENTRY_ANCHORED) || entryTypeEnum.equals(PRIORITY_ANCHORED);
    }
}
