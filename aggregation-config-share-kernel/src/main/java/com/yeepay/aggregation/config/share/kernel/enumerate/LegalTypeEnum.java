package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 法人类型枚举
 * <AUTHOR>
 * @since 2025/5/21:17:29
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum LegalTypeEnum implements DocumentedEnum<String>{

    /**
     * 法人
     */
    LEGAL("LEGAL", "经营者/法人"),

    /**
     * 经办人
     */
    SUPER("SUPER", "经办人");

    private final String document;
    private final String desc;

    public String getChannelLegalType() {
        switch (this) {
            case LEGAL:
                return "LEGAL_PERSON";
            case SUPER:
                return "AGENT";
            default:
                throw new IllegalArgumentException("非法的法人类型");
        }
    }
}
