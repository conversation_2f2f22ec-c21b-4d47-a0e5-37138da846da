package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 渠道申请状态枚举
 * <AUTHOR>
 * @since 2025/6/3:10:43
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum ChannelNoApplyStatusEnum implements DocumentedEnum<String> {

    WAIT_SUBMIT("WAIT_SUBMIT", "待提交", null, ThirdPartyRecordStatusEnum.INIT),

    APPLYING("APPLYING", "申请中", "SUBMINTTING", ThirdPartyRecordStatusEnum.PROCESSING),

    APPLY_SUCCESS("APPLY_SUCCESS", "申请成功", "SUCCESS", ThirdPartyRecordStatusEnum.SUCCESS),

    APPLY_FAIL("APPLY_FAIL", "申请失败", "FAIL", ThirdPartyRecordStatusEnum.CLOSED),

    ;


    private String document;
    private String desc;
    private String channelStatus;
    private ThirdPartyRecordStatusEnum thirdRecordStatus;

    /**
     * 根据通道状态获取渠道申请状态枚举
     * @param channelStatus 通道状态
     * @return AuthStatusEnum
     */
    public static ChannelNoApplyStatusEnum getEnumByChannelStatus(String channelStatus) {
        for (ChannelNoApplyStatusEnum value : ChannelNoApplyStatusEnum.values()) {
            if (value.getChannelStatus() != null && value.getChannelStatus().contains(channelStatus)) {
                return value;
            }
        }
        throw new IllegalArgumentException("未找到对应申请状态枚举，通道状态：" + channelStatus);
    }
}
