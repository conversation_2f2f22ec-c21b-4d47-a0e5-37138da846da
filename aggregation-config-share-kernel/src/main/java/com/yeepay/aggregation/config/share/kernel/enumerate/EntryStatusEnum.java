package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/15 14:40
 */
@Getter
@AllArgsConstructor
public enum EntryStatusEnum implements DocumentedEnum<String> {
    INIT("INIT", "初始化"),
    PROCESSING("PROCESSING", "处理中"),
    SUCCESS("SUCCESS", "成功"),
    CLOSED("CLOSED", "关闭"),
    FAIL("FAIL", "失败"),
    ;
    private final String document;
    private final String desc;

    public boolean finalStatus() {
        return this == SUCCESS || this == FAIL || this == CLOSED;
    }
}
