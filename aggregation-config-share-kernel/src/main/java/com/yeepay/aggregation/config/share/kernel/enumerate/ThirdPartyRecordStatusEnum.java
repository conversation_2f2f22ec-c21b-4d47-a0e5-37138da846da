package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 第三方记录状态枚举
 *
 * <AUTHOR>
 * @since 2025/5/28:11:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum ThirdPartyRecordStatusEnum implements DocumentedEnum<String> {
    INIT("INIT", "初始化"),
    PROCESSING("PROCESSING", "处理中"),
    SUCCESS("SUCCESS", "成功"),
    CLOSED("CLOSED", "关闭"),
    FAIL("FAIL", "失败"),
    NOT_EXIST("NOT_EXIST", "不存在"),
    ;

    private final String document;
    private final String desc;

    public boolean isFinish() {
        return SUCCESS == this || FAIL == this || CLOSED == this;
    }
}
