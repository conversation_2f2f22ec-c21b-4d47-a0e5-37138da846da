package com.yeepay.aggregation.config.share.kernel.enumerate;

import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/27 20:44
 */
@Getter
@AllArgsConstructor
public enum SalesScenesType implements DocumentedEnum<String> {
    STORE("STORE", "线下场所"),

    MP("MP", "服务号或公众号"),

    MINI_PROGRAM("MINI_PROGRAM", "小程序"),

    WEB("WEB", "互联网网站"),

    APP("APP", "APP"),

    WEWORK("WEWORK", "企业微信");

    private final String document;
    private final String desc;

    public String getChannelSalesScenesType() {
        switch (this) {
            case STORE:
                return "SALES_SCENES_STORE";
            case MP:
                return "SALES_SCENES_MP";
            case MINI_PROGRAM:
                return "SALES_SCENES_MINI_PROGRAM";
            case WEB:
                return "SALES_SCENES_WEB";
            case APP:
                return "SALES_SCENES_APP";
            case WEWORK:
                return "SALES_SCENES_WEWORK";
            default:
                throw new BusinessException(ResultCode.PARAM_VALID_ERROR, "不支持的经营场景");
        }
    }
}
