package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 通道枚举类
 * <AUTHOR>
 * @since 2025/5/22:15:21
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum implements DocumentedEnum<String> {
    WECHAT("WECHAT", "微信"),
    ALIPAY("ALIPAY", "支付宝"),
    BANK("BANK", "银行"),
    ;

    private final String document;
    private final String desc;
}
