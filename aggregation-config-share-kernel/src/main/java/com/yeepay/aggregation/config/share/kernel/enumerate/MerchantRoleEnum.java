package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 商户角色
 * <AUTHOR>
 * @since 2025/6/6:16:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum MerchantRoleEnum implements DocumentedEnum<String>{
    ORDINARY_MERCHANT("ORDINARY_MERCHANT", "标准商户"),
    MANAGEMENT("MANAGEMENT", "管理单位"),
    SERVICE_PROVIDER("SERVICE_PROVIDER", "服务商"),
    PLATFORM_MERCHANT("PLATFORM_MERCHANT", "平台商"),
    SAAS_SERVICE_PROVIDER("SAAS_SERVICE_PROVIDER", "SaaS服务商"),
    SETTLED_MERCHANT("SETTLED_MERCHANT", "入驻商户"),
    SHARE_MERCHANT("SHARE_MERCHANT", "分账接收方"),
    ORGAN_SERVICE_PROVIDER("ORGAN_SERVICE_PROVIDER", "渠道服务商"),
    HEAD_CHAIN("HEAD_CHAIN", "连锁总店"),
    BRANCH_CHAIN("BRANCH_CHAIN", "连锁分店");

    private final String document;
    private final String desc;
}
