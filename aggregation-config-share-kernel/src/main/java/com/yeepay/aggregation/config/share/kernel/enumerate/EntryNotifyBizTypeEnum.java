package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 进件通知业务 类型
 */
@Getter
@AllArgsConstructor
public enum EntryNotifyBizTypeEnum implements DocumentedEnum<String> {

    ENTRY("ENTRY", "进件结果通知"),
    ENTRY_ANCHORED("ENTRY", "进件+挂靠场景的挂靠结果通知"),
    TERMINAL_REPORT("TERMINAL_REPORT", "终端报备结果通知"),
    DIVIDE_CREATE("DIVIDE_CREATE", "分账方结果通知"),
    ;


    private final String document;
    private final String desc;
}
