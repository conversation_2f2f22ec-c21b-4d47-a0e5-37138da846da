package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/16 17:50
 */
@Getter
@AllArgsConstructor
public enum SceneType implements DocumentedEnum<String> {
    ONLINE("ONLINE", "线上场景"),
    OFFLINE("OFFLINE", "线下场景"),
    ALL("ALL", "线上线下"),
    ;
    private final String document;
    private final String desc;

    public boolean isContainsOffline() {
        return SceneType.OFFLINE.equals(this) || SceneType.ALL.equals(this);
    }
}
