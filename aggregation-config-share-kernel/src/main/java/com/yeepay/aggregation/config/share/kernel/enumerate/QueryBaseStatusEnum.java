package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 基础状态枚举
 *
 * <AUTHOR>
 * @since 2025/6/20:17:52
 * Company: 易宝支付(YeePay)
 */
@AllArgsConstructor
@Getter
public enum QueryBaseStatusEnum implements DocumentedEnum<String> {
    PROCESSING("PROCESSING", "处理中"),
    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    ;

    private final String document;
    private final String desc;
}
