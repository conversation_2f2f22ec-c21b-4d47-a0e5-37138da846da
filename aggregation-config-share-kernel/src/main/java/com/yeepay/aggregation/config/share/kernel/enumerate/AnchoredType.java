package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/2 17:48
 */
@Getter
@AllArgsConstructor
public enum AnchoredType implements DocumentedEnum<String>{

    NOT_LIMIT_SUBJECT("NOT_LIMIT_SUBJECT", "不限制主体"),
    SAME_SUBJECT("SAME_SUBJECT", "同主体挂靠"),
    DESIGNATE_MERCHANT("DESIGNATE_MERCHANT", "指定挂靠商编")
    ;
    private final String document;
    private final String desc;
}
