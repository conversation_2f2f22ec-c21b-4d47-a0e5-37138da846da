
package com.yeepay.aggregation.config.share.kernel.mask.strategy;

import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

@SensitiveMaskStrategy(type = SensitiveTypeEnum.BANK_CARD)
public class BankCardMaskStrategy implements MaskStrategy {
    // 验证模式：允许带分隔符的16-19位银行卡号
    private static final Pattern VALID_PATTERN = Pattern.compile(
            "^\\s*(?:\\d{4}[- ]?){3,4}\\d{4}\\s*$"
    );

    // 清洗模式：去除所有非数字字符
    private static final Pattern CLEAN_PATTERN = Pattern.compile("[^\\d]");

    // 掩码模式
    private static final Pattern MASK_PATTERN = Pattern.compile("(\\d{4})(\\d+)(\\d{4})");
    // 固定8个星号
    private static final String MASK_REPLACEMENT = "$1********$3";

    @Override
    public String mask(String content) {
        if (StringUtils.isBlank(content)) {
            return StringUtils.EMPTY;
        }

        // 先验证后处理
        if (!isValid(content)) {
            // 无效输入返回空
            return StringUtils.EMPTY;
        }

        // 标准化处理：去除所有非数字字符
        String cleanNumber = CLEAN_PATTERN.matcher(content).replaceAll("");
        return MASK_PATTERN.matcher(cleanNumber).replaceAll(MASK_REPLACEMENT);
    }

    @Override
    public boolean isValid(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }

        // 格式验证
        boolean formatValid = VALID_PATTERN.matcher(content.trim()).matches();
        if (!formatValid) {
            return false;
        }

        // 长度验证（清洗后长度）
        String clean = CLEAN_PATTERN.matcher(content).replaceAll("");
        return clean.length() >= 16 && clean.length() <= 19;
    }
}