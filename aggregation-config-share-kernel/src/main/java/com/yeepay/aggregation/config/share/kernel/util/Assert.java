package com.yeepay.aggregation.config.share.kernel.util;

import com.yeepay.aggregation.config.share.kernel.enumerate.IResponseEnum;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@UtilityClass
public class Assert {

    /**
     * 断言对象不为空
     */
    public static void notNull(Object object, IResponseEnum resultCode) {
        if (object == null) {
            throw new BusinessException(resultCode);
        }
    }

    public static void notNull(Object object, IResponseEnum resultCode, String message) {
        if (object == null) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言字符串不为空
     */
    public static void notEmpty(String string, IResponseEnum resultCode) {
        if (StringUtils.isEmpty(string)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言字符串不为空
     */
    public static void notEmpty(String string, IResponseEnum resultCode, String message) {
        if (StringUtils.isEmpty(string)) {
            throw new BusinessException(resultCode, message);
        }
    }

    public static void notBlank(String string, IResponseEnum resultCode) {
        if (StringUtils.isBlank(string)) {
            throw new BusinessException(resultCode);
        }
    }

    public static void notBlank(String string, IResponseEnum resultCode, String message) {
        if (StringUtils.isBlank(string)) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言集合不为空
     */
    public static void notEmpty(Collection<?> collection, IResponseEnum resultCode) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言集合不为空
     */
    public static void notEmpty(Collection<?> collection, IResponseEnum resultCode, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言Map不为空
     */
    public static void notEmpty(Map<?, ?> map, IResponseEnum resultCode) {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言Map不为空
     */
    public static void notEmpty(Map<?, ?> map, IResponseEnum resultCode, String message) {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言数组不为空
     */
    public static void notEmpty(Object[] array, IResponseEnum resultCode) {
        if (ArrayUtils.isEmpty(array)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言数组不为空
     */
    public static void notEmpty(Object[] array, IResponseEnum resultCode, String message) {
        if (ArrayUtils.isEmpty(array)) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言表达式为true
     */
    public static void isTrue(boolean expression, IResponseEnum resultCode) {
        if (!expression) {
            throw new BusinessException(resultCode);
        }
    }

    public static void isTrue(boolean expression, IResponseEnum resultCode, String message) {
        if (!expression) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言表达式为false
     */
    public static void isFalse(boolean expression, IResponseEnum resultCode) {
        if (expression) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言表达式为false
     */
    public static void isFalse(boolean expression, IResponseEnum resultCode, String message) {
        if (expression) {
            throw new BusinessException(resultCode, message);
        }
    }

    /**
     * 断言对象相等
     */
    public static void equals(Object obj1, Object obj2, IResponseEnum resultCode) {
        if (!Objects.equals(obj1, obj2)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言对象不相等
     */
    public static void notEquals(Object obj1, Object obj2, IResponseEnum resultCode) {
        if (Objects.equals(obj1, obj2)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言金额相等
     */
    public static void equalsAmount(BigDecimal amount1, BigDecimal amount2, IResponseEnum resultCode) {
        if (amount1 == null || amount2 == null || amount1.compareTo(amount2) != 0) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言金额大于零
     */
    public static void gtZero(BigDecimal amount, IResponseEnum resultCode) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言金额大于等于零
     */
    public static void geZero(BigDecimal amount, IResponseEnum resultCode) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言状态
     */
    public static void state(Enum<?> state, Enum<?> expectedState, IResponseEnum resultCode) {
        if (state != expectedState) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言在允许的状态列表中
     */
    @SafeVarargs
    public static <T extends Enum<T>> void stateIn(T state, IResponseEnum resultCode, T... allowedStates) {
        if (state == null || !Arrays.asList(allowedStates).contains(state)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言不在禁止的状态列表中
     */
    @SafeVarargs
    public static <T extends Enum<T>> void stateNotIn(T state, IResponseEnum resultCode, T... forbiddenStates) {
        if (state == null || Arrays.asList(forbiddenStates).contains(state)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言时间在指定范围内
     */
    public static void timeIn(LocalDateTime time, LocalDateTime start, LocalDateTime end, IResponseEnum resultCode) {
        if (time == null || time.isBefore(start) || time.isAfter(end)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言正则匹配
     */
    public static void matches(String string, String regex, IResponseEnum resultCode) {
        if (!Pattern.matches(regex, string)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言字符串长度在范围内
     */
    public static void lengthBetween(String string, int minLength, int maxLength, IResponseEnum resultCode) {
        if (string == null || string.length() < minLength || string.length() > maxLength) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言数值在范围内
     */
    public static void numberBetween(Number number, Number min, Number max, IResponseEnum resultCode) {
        if (number == null || number.doubleValue() < min.doubleValue()
                || number.doubleValue() > max.doubleValue()) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言对象为指定类型
     */
    public static void instanceOf(Object obj, Class<?> type, IResponseEnum resultCode) {
        if (!type.isInstance(obj)) {
            throw new BusinessException(resultCode);
        }
    }
}