package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/27 19:00
 */
@Getter
@AllArgsConstructor
public enum BankAccountTypeEnum implements DocumentedEnum<String>{
    CORPORATE("CORPORATE",  "对公银行账户"),
    PERSONAL("PERSONAL",  "对私银行账户");
    ;
    private final String document;
    private final String desc;

    public String getChannelBankAccountType() {
        switch (this) {
            case CORPORATE:
                return "PUBLIC_ACCOUNT";
            case PERSONAL:
                return "PRIVATE_ACCOUNT";
            default:
                throw new IllegalArgumentException("BankAccountTypeEnum not support");
        }
    }
}
