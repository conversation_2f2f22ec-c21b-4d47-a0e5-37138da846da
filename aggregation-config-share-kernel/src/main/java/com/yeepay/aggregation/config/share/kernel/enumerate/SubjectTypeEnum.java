package com.yeepay.aggregation.config.share.kernel.enumerate;

import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 主体类型枚举类
 *
 * <AUTHOR>
 * @since 2025/5/22:16:18
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum SubjectTypeEnum implements DocumentedEnum<String> {
    ENTERPRISE("ENTERPRISE", "企业"),
    INSTITUTION("INSTITUTION", "事业单位"),
    INDIVIDUAL("INDIVIDUAL", "个体工商户"),
    MICRO("PERSON", "小微商户"),
    GOVERNMENT("GOVERNMENT", "政府机关"),
    SOCIAL_ORGANIZATION("COMMUNITY", "社会团体"),
    PRIVATE_NON_ENTERPRISE("PEOPLE_RUN_NON_ENTERPRISE", "民办非企业"),
    OTHERS("OTHERS", "（社会组织） 包括社会团体、民办非企业、基金会、基层群众性自治组织、农村集体经济组织等组织");

    private final String document;
    private final String desc;

    public String getChannelSubjectType() {
        switch (this) {
            case ENTERPRISE:
                return "企业";
            case INSTITUTION:
                return "事业单位";
            case INDIVIDUAL:
                return "个体";
            case MICRO:
                return "小微";
            case SOCIAL_ORGANIZATION:
                return "社会团体";
            case PRIVATE_NON_ENTERPRISE:
                return "民办非企业";
            default:
                throw new BusinessException(ResultCode.NOT_SUPPORT_SUBJECT_TYPE);

        }
    }

    public boolean isMicro() {
        return SubjectTypeEnum.MICRO == this;
    }
}
