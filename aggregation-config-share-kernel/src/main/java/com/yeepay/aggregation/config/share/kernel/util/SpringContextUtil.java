package com.yeepay.aggregation.config.share.kernel.util;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    public static Object getBean(String beanId) {
        return applicationContext.getBean(beanId);
    }

    public static <T> T getBean(Class<T> beanId) {
        return applicationContext.getBean(beanId);
    }

    public static ApplicationContext getContext() {
        return applicationContext;
    }

    @Override
    public void setApplicationContext(ApplicationContext context) {
        applicationContext = context;
    }

    /**
     * 获取指定class所有的实现类实例
     */
    public static <T> Map<String, T> getBeanOfType(Class<T> clazz) {
        return getContext().getBeansOfType(clazz);
    }

}
