package com.yeepay.aggregation.config.share.kernel.mask.annotation;
import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;

import java.lang.annotation.*;

/**
 * 敏感信息掩码注解
 * <AUTHOR>
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Sensitive {
    
    /**
     * 敏感信息类型
     */
    SensitiveTypeEnum type();
    
    /**
     * 自定义掩码规则时，保留前几位
     */
    int prefix() default 0;
    
    /**
     * 自定义掩码规则时，保留后几位
     */
    int suffix() default 0;
    
    /**
     * 掩码字符
     */
    char maskChar() default '*';
} 