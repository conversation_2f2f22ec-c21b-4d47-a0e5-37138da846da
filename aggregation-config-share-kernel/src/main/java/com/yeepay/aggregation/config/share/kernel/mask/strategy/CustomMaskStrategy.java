package com.yeepay.aggregation.config.share.kernel.mask.strategy;
import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

@SensitiveMaskStrategy(type = SensitiveTypeEnum.CUSTOM)
public class CustomMaskStrategy implements MaskStrategy {
    private final int prefix;
    private final int suffix;
    private final char maskChar;

    public CustomMaskStrategy(int prefix, int suffix, char maskChar) {
        Validate.isTrue(prefix >= 0, "prefix 必须大于等于 0");
        Validate.isTrue(suffix >= 0, "suffix 必须大于等于 0");
        this.prefix = prefix;
        this.suffix = suffix;
        this.maskChar = maskChar;
    }

    @Override
    public String mask(String content) {
        if (StringUtils.isBlank(content)) {
            return StringUtils.EMPTY;
        }
        if (!isValid(content)) {
            return content;
        }
        return doMask(content);
    }

    @Override
    public boolean isValid(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        int length = content.length();
        return length > (prefix + suffix);
    }

    /**
     * 支持Unicode字符的掩码处理
     */
    String doMask(String content) {
        int length = content.length();
        int maskLength = length - prefix - suffix;

        // 处理Unicode字符边界
        int actualPrefix = content.offsetByCodePoints(0, prefix);
        int actualSuffixIndex = content.offsetByCodePoints(length, -suffix);

        // 构建掩码字符串
        return content.substring(0, actualPrefix) +
                StringUtils.repeat(maskChar, maskLength) +
                content.substring(actualSuffixIndex, length);
    }
}
