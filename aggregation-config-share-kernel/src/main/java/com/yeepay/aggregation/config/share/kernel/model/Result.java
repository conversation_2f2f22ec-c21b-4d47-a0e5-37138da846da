package com.yeepay.aggregation.config.share.kernel.model;

import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import lombok.Data;

import java.io.Serializable;

@Data
public class Result<T> implements Serializable {
    private T data;
    private String message;
    private String code;

    private static final String SUCCESS_CODE = "0000";

    public Result() {
    }

    public boolean isSuccess() {
        return getCode().equals(SUCCESS_CODE);
    }

    public static <T> Result<T> success() {
        return success(null);
    }

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setData(data);
        result.setCode(SUCCESS_CODE);
        result.setMessage("success");
        return result;
    }

    public static <T> Result<T> fail(String code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    public static <T> Result<T> fail(ResultCode errorCodeEnum) {
        Result<T> result = new Result<>();
        result.setCode(errorCodeEnum.getCode());
        result.setMessage(errorCodeEnum.getMessage());
        return result;
    }

    public static <T> Result<T> fail(ResultCode errorCodeEnum, String message) {
        Result<T> result = new Result<>();
        result.setCode(errorCodeEnum.getCode());
        result.setMessage(message);
        return result;
    }
}
