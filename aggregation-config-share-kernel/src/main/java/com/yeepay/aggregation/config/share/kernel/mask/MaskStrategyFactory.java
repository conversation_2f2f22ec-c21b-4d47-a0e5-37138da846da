package com.yeepay.aggregation.config.share.kernel.mask;

import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.strategy.CustomMaskStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class MaskStrategyFactory implements InitializingBean {
    private static final Map<SensitiveTypeEnum, MaskStrategy> STRATEGY_MAP = new ConcurrentHashMap<>();
    private static final Map<String, CustomMaskStrategy> CUSTOM_STRATEGY_CACHE = new ConcurrentHashMap<>();
    
    private final ApplicationContext applicationContext;
    
    public MaskStrategyFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() {
        // 扫描所有带有@SensitiveMaskStrategy注解的类并注册
        Map<String, Object> strategyBeans = applicationContext.getBeansWithAnnotation(SensitiveMaskStrategy.class);
        strategyBeans.forEach((name, bean) -> {
            if (bean instanceof MaskStrategy) {
                SensitiveMaskStrategy annotation = bean.getClass().getAnnotation(SensitiveMaskStrategy.class);
                SensitiveTypeEnum type = annotation.type();
                registerStrategy(type, (MaskStrategy) bean);
                log.info("Registered mask strategy: {} for type: {}", bean.getClass().getSimpleName(), type);
            }
        });
    }

    /**
     * 注册掩码策略
     */
    public void registerStrategy(SensitiveTypeEnum type, MaskStrategy strategy) {
        STRATEGY_MAP.put(type, strategy);
    }

    /**
     * 获取掩码策略
     */
    public static MaskStrategy getStrategy(SensitiveTypeEnum type) {
        MaskStrategy strategy = STRATEGY_MAP.get(type);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported sensitive type: " + type);
        }
        return strategy;
    }

    /**
     * 获取自定义掩码策略
     */
    public static MaskStrategy getCustomStrategy(int prefix, int suffix, char maskChar) {
        String key = prefix + ":" + suffix + ":" + maskChar;
        return CUSTOM_STRATEGY_CACHE.computeIfAbsent(key, k -> new CustomMaskStrategy(prefix, suffix, maskChar));
    }
} 