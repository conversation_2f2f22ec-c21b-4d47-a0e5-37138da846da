package com.yeepay.aggregation.config.share.kernel.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * description: 聚合配置常量类
 * <AUTHOR>
 * @since 2025/5/26:15:35
 * Company: 易宝支付(YeePay)
 */
public interface Const {
    String DEFAULT_SYSTEM_OPERATOR_NAME = "system";

    String DEFAULT_SYSTEM_NAME = "aggregation-config-hessian";

    Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");

    String PERMANENT = "长期";

    String ANCHORED_ORDER_PREFIX = "AN";

    /**
     * 商户认证申请单-前缀
     */
    String MERCHANT_AUTH_APPLY_ORDER_PREFIX = "RZ";

    /**
     * 商户渠道申请单-前缀
     */
    String MERCHANT_CHANNEL_APPLY_ORDER_PREFIX = "QD";

    /**
     * 请求号
     */
    String REQUEST_NO = "requestNo";

    /**
     * 申请单号
     */
    String APPLY_NO = "applyNo";

    /**
     * 更新时间
     */
    String UPDATE_DT = "updateDt";

    /**
     * 创建时间
     */
    String CREATE_DT = "createDt";

    /**
     * system
     */
    String SYSTEM = "SYSTEM";

    /**
     * 分隔符
     */
    String SPLIT_CHAR = ",";

    /**
     * 渠道活动状态-普通
     */
    String CHANNEL_ACTIVITY_NORMAL = "Normal";

    /**
     * 航旅事业部行业线
     */
    List<String> HL_INDUSTRY_LINE = Collections.unmodifiableList(Arrays.asList("航旅事业部", "旅游行业线"));

}
