package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: 终端报备状态
 * <AUTHOR>
 * @since 2025/6/9:19:59
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum TerminalEntryStatus implements DocumentedEnum<String>{
    INIT("INIT", "初始化"),
    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    ;
    private final String document;
    private final String desc;
}
