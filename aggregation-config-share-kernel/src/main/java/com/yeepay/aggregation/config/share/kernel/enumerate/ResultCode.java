package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/23 21:21
 */
@Getter
public enum ResultCode implements IResponseEnum {
    //系统标识_模块标识_错误码 系统标识：AC 模块标识：00进件模块 01挂靠模块 02实名认证模块 03活动报名 04微信配置 05申请渠道号 07商户处罚模块
    NOT_FOUND("404", "访问资源不存在"),
    METHOD_NOT_ALLOWED("405", "不支持当前请求方法"),
    SERVER_ERROR("500", "服务器异常"),


    PARAM_VALID_ERROR("00001", "参数校验错误"),
    PARAM_BIND_ERROR("00002", "参数绑定错误"),
    PARAM_TYPE_ERROR("00003", "参数类型错误"),
    JSON_PARSE_ERROR("00004", "JSON解析错误"),


    /**
     * 进件模块
     */
    ENTRY_SUBMIT_ERROR("AC00001", "报备提交异常"),
    ENTRY_ROUTE_CHANNEL_NO_ERROR("AC00002", "渠道号路由异常"),
    ENTRY_ROUTE_UN_FIND_CHANNEL_NO_ERROR("AC00003", "无可用渠道标识/渠道号"),
    NOT_SUPPORT_SCENES_TYPE("AC00004", "不支持的经营场景类型"),
    CHANNEL_ENTRY_IDEMPOTENT("AC00005", "通道【内部】进件幂等"),
    ENTRY_QUERY_TERMINAL_ERROR("AC00006", "查询报备终端异常"),
    ENTRY_QUERY_RECEIVER_ERROR("AC00007", "查询分账方异常"),
    NOT_SUPPORT_SUBJECT_TYPE("AC00008", "不支持的主体类型"),
    ENTRY_APPLY_IDEMPOTENT("AC00009", "进件申请单已存在"),
    DIVIDE_CREATE_SUBMIT_ERROR("AC00010", "分账方创建提交异常"),
    ENTRY_APPLY_FINISHED("AC00011", "进件申请单已终态"),
    QUERY_CUSTOMER_CONFIG_ERROR("AC00012", "查需客户中心配置异常"),
    CHANNEL_NO_UNABLE_ERROR("AC00013", "渠道号无子商户录入权限，请检查后重试"),
    SERVICE_PROVIDER_UNABLE_ERROR("AC00014", "当前服务商因存在违规，不予进件。"),
    CHANNEL_ENTRY_FAILED("AC00015", "渠道进件失败"),
    ENTRY_CHANNEL_NO_VALID_ERROR("AC00016", "指定渠道号/渠道标识校验失败"),

    /**
     * 挂靠模块
     */
    NOT_SUPPORT_ANCHORED_MERCHANT("AC02001", "无可用的同主体挂靠商编"),
    NOT_SUPPORT_BIZ("AC02002", "暂不支持的业务"),
    ATTACH_QUERY_ERROR("AC02003", "查询挂靠记录异常"),


    /**
     * 渠道号-申请、查询
     */
    CHANNEL_NO_REPEAT("AC05001", "申请的渠道号已存在"),
    CHANNEL_APPLY_RECORD_REPEAT("AC05002", "已存在申请的渠道记录"),
    CHANNEL_APPLY_SUBMIT_ERROR("AC05003", "提交渠道号申请异常"),
    CHANNEL_APPLY_QUERY_ERROR("AC05004", "查询渠道号申请异常"),
    CHANNEL_NO_QUERY_ERROR("AC05005", "查询渠道号异常"),
    CHANNEL_NO_BUSINESS_LINE_QUERY_ERROR("AC05006", "行业线查询渠道号异常"),
    CHANNEL_NO_APPLY_NOT_EXIST("AC05007", "查询渠道号申请不存在"),

    /**
     * 商户处罚通知模块
     */
    DISPOSAL_NOTIFY_QUERY_FAIL("AC07001", "查询处置通知记录失败"),

    /**
     * 系统异常
     */
    SYSTEM_ERROR("99999", "系统异常"),

    /**
     * 数据库操作异常
     */
    DATABASE_OPERATE_ERROR("90005", "数据库操作异常"),

    /**
     * 数据库并发操作异常
     */
    CONCURRENT_OPERATE_ERROR("90006", "并发操作"),

    ;

    private final String code;
    private final String message;

    ResultCode(final String code, final String message) {
        this.code = code;
        this.message = message;
    }
}
