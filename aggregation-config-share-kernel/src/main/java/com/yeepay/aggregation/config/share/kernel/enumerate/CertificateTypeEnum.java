package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * description: 登记证书类型
 *
 * <AUTHOR>
 * @since 2025/5/22:20:13
 * Company: 易宝支付(YeePay)
 */
@Getter
public enum CertificateTypeEnum implements DocumentedEnum<String> {

    LICENSE("LICENSE", "营业执照", Arrays.asList(SubjectTypeEnum.ENTERPRISE, SubjectTypeEnum.INDIVIDUAL)),
    PUBLIC_INSTITUTION_CERTIFICATE("PUBLIC_INSTITUTION_CERTIFICATE", "事业单位法人证书",
            Collections.singletonList(SubjectTypeEnum.INSTITUTION)),
    UNIFIED_CREDIT_CERTIFICATE("UNIFIED_CREDIT_CERTIFICATE", "统一社会信用代码证书",
            Arrays.asList(SubjectTypeEnum.ENTERPRISE, SubjectTypeEnum.INDIVIDUAL, SubjectTypeEnum.SOCIAL_ORGANIZATION,
                    SubjectTypeEnum.PRIVATE_NON_ENTERPRISE, SubjectTypeEnum.GOVERNMENT)),
    SOCIAL_ORGANIZATION_CERTIFICATE("SOCIAL_ORGANIZATION_CERTIFICATE", "社会团体法人登记证书", Collections.singletonList(SubjectTypeEnum.SOCIAL_ORGANIZATION)),
    PRIVATE_NON_ENTERPRISE_CERTIFICATE("PRIVATE_NON_ENTERPRISE_CERTIFICATE", "民办非企业单位登记证书", Collections.singletonList(SubjectTypeEnum.PRIVATE_NON_ENTERPRISE)),
    FOUNDATION_CERTIFICATE("FOUNDATION_CERTIFICATE", "基金会法人登记证书", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    LICENSE_CERTIFICATE("LICENSE_CERTIFICATE", "执业许可证/执业证", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    GRASSROOTS_ORG_CERTIFICATE("GRASSROOTS_ORG_CERTIFICATE", "基层群众性自治组织特别法人统一社会信用代码证", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    RURAL_COLLECTIVE_CERTIFICATE("RURAL_COLLECTIVE_CERTIFICATE", "农村集体经济组织登记证", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    RELIGIOUS_PLACE_CERTIFICATE("RELIGIOUS_PLACE_CERTIFICATE", "宗教活动场所登记证", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    GOVERNMENT_OTHER_CERTIFICATE("GOVERNMENT_OTHER_CERTIFICATE", "政府部门下发的其他有效证明文件", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    CHARITY_FUNDRAISING_CERTIFICATE("CHARITY_FUNDRAISING_CERTIFICATE", "慈善组织公开募捐资格证书", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    OTHER("OTHER", "其他证书/批文/证明", Collections.singletonList(SubjectTypeEnum.OTHERS)),
    ;

    private final String document;

    private final String desc;

    private final List<SubjectTypeEnum> supportSubjectTypeList;


    CertificateTypeEnum(final String document, String desc, final List<SubjectTypeEnum> subjectTypeList) {
        this.document = document;
        this.supportSubjectTypeList = subjectTypeList;
        this.desc = desc;
    }

    public String getChannelCertificateType() {
        switch (this) {
            case PUBLIC_INSTITUTION_CERTIFICATE:
                return "INST_RGST_CTF";
            case LICENSE:
                return "NATIONAL_LEGAL";
            default:
                return "NATIONAL_LEGAL_MERGE";
        }
    }
}

