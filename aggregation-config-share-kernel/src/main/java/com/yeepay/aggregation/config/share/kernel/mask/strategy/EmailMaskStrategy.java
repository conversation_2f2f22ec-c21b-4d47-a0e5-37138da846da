package com.yeepay.aggregation.config.share.kernel.mask.strategy;

import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

@SensitiveMaskStrategy(type = SensitiveTypeEnum.EMAIL)
public class EmailMaskStrategy implements MaskStrategy {
    public static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");


    @Override
    public String mask(String content) {
        if (StringUtils.isEmpty(content)) {
            return StringUtils.EMPTY;
        }
        if (!isValid(content)) {
            return content;
        }
        
        int atIndex = content.indexOf('@');
        if (atIndex <= 1) {
            return content;
        }
        
        return content.charAt(0) + 
               StringUtils.repeat("*", atIndex - 1) + 
               content.substring(atIndex);
    }

    @Override
    public boolean isValid(String content) {
        return content != null && EMAIL_PATTERN.matcher(content).matches();
    }
} 