package com.yeepay.aggregation.config.share.kernel.exception;


import com.yeepay.aggregation.config.share.kernel.enumerate.IResponseEnum;

public class BusinessException extends BaseException {
    private static final long serialVersionUID = 1L;

    public BusinessException(IResponseEnum responseEnum, Object[] args, String message) {
        super(responseEnum, args, message);
    }

    public BusinessException(IResponseEnum responseEnum, Object[] args, String message, Throwable cause) {
        super(responseEnum, args, message, cause);
    }

    public BusinessException(IResponseEnum responseEnum) {
        super(responseEnum);
    }

    public BusinessException(IResponseEnum responseEnum,String message) {
        super(responseEnum, message);
    }

    public BusinessException(String code, String msg) {
        super(code, msg);
    }
}
