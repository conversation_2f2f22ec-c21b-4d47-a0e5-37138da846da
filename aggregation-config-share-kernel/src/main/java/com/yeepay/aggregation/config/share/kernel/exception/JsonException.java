package com.yeepay.aggregation.config.share.kernel.exception;

import com.yeepay.aggregation.config.share.kernel.enumerate.IResponseEnum;


public class JsonException extends BaseException {


    public JsonException(final IResponseEnum responseEnum) {
        super(responseEnum);
    }

    public JsonException(final IResponseEnum responseEnum, final String message) {
        super(responseEnum, message);
    }

    public JsonException(final String code, final String msg) {
        super(code, msg);
    }

    public JsonException(final IResponseEnum responseEnum, final Object[] args, final String message) {
        super(responseEnum, args, message);
    }

    public JsonException(final IResponseEnum responseEnum, final Object[] args, final String message, final Throwable cause) {
        super(responseEnum, args, message, cause);
    }
}
