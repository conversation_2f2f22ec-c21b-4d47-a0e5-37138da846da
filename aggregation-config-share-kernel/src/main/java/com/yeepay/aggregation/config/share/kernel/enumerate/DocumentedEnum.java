package com.yeepay.aggregation.config.share.kernel.enumerate;

import com.fasterxml.jackson.annotation.JsonValue;
import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface DocumentedEnum<T> {

    @JsonValue
    T getDocument();

    String getDesc();

    /**
     * 根据枚举值查找对应的枚举实例。
     *
     * @param clazz 枚举类
     * @param value 枚举值
     * @param <E>   枚举类型
     * @param <T>   枚举值的类型
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果找不到对应的枚举实例
     */
    static <E extends Enum<E> & DocumentedEnum<T>, T> E fromValue(Class<E> clazz, T value) {
        return Arrays.stream(clazz.getEnumConstants())
                .filter(e -> e.getDocument().equals(value))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ResultCode.PARAM_VALID_ERROR, "Invalid value: " + value));
    }

    /**
     * 根据枚举值查找对应的枚举实例。
     *
     * @param clazz 枚举类
     * @param value 枚举值
     * @param <E>   枚举类型
     * @param <T>   枚举值的类型
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果找不到对应的枚举实例
     */
    static <E extends Enum<E> & DocumentedEnum<T>, T> E fromValueOfNullable(Class<E> clazz, T value) {

        return Arrays.stream(clazz.getEnumConstants())
                .filter(e -> e.getDocument().equals(value))
                .findFirst()
                .orElse(null);
    }

    /**
     * 将枚举类转换为 Map，方便快速查找。
     *
     * @param clazz 枚举类
     * @param <E>   枚举类型
     * @param <T>   枚举值的类型
     * @return 枚举值到枚举实例的映射
     */
    static <E extends Enum<E> & DocumentedEnum<T>, T> Map<T, E> toMap(Class<E> clazz) {
        return Arrays.stream(clazz.getEnumConstants())
                .collect(Collectors.toMap(DocumentedEnum::getDocument, Function.identity()));
    }

    /**
     * 判断左字符串是否与右边的集合元素有一个匹配
     */
    @SafeVarargs
    static <T> boolean stringInRightEnums(T left, DocumentedEnum<T>... right) {
        if (left == null && right == null) {
            return false;
        }
        if (left == null || right == null) {
            return false;
        }
        return Arrays.stream(right).anyMatch(e -> e.getDocument().equals(left));
    }

    /**
     * 判断左枚举是否与右边的集合元素有一个匹配
     */
    @SafeVarargs
    static <T> boolean inRightEnums(DocumentedEnum<T> left, DocumentedEnum<T>... right) {
        if (left == null && right == null) {
            return false;
        }
        if (left == null || right == null) {
            return false;
        }
        return Arrays.asList(right).contains(left);
    }

    /**
     * 根据枚举解析为枚举值。
     *
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果找不到对应的枚举实例
     */
    static String parseName(DocumentedEnum<String> documentedEnum) {
        return null == documentedEnum ? null : documentedEnum.getDocument();
    }

}
