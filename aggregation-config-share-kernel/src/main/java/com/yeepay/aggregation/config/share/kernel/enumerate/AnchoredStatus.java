package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/2 21:10
 */
@Getter
@AllArgsConstructor
public enum AnchoredStatus implements DocumentedEnum<String>{
    INIT("INIT", "初始化"),
    PROCESSING("PROCESSING", "处理中"),
    AGG_SUCCESS("AGG_SUCCESS", "agg挂靠成功"),
    FAIL("FAIL", "失败"),
    CHANNEL_FAIL("CHANNEL_FAIL", "渠道挂靠失败"),
    SUCCESS("SUCCESS", "成功"),

    ;
    private final String document;
    private final String desc;
}
