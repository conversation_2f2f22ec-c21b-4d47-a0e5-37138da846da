package com.yeepay.aggregation.config.share.kernel.enumerate;

import com.yeepay.aggregation.config.share.kernel.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description: 个人证件类型枚举
 *
 * <AUTHOR>
 * @since 2025/5/22:19:50
 * Company: 易宝支付(YeePay)
 */
@Getter
public enum IdentificationTypeEnum implements DocumentedEnum<String> {
    ID_CARD("ID_CARD", "中国大陆居民-身份证", "ALL"),
    SOLDIER("SOLDIER", "士兵证", null),
    OFFICERS("OFFICERS", "军官证", null),
    OVERSEA_PASSPORT("OVERSEA_PASSPORT", "其他国家或地区居民-护照", "ALL"),
    HONG_KONG_PASSPORT("HONG_KONG_PASSPORT", "中国香港居民-来往内地通行证", "ALL"),
    HONG_KONG_MACAO_RESIDENT("HONG_KONG_MACAO_RESIDENT", "港澳居民证", "WECHAT"),
    MACAO_PASSPORT("MACAO_PASSPORT", "中国澳门居民-来往内地通行证", "ALL"),
    TAIWAN_PASSPORT("TAIWAN_PASSPORT", "中国台湾居民-来往大陆通行证", "ALL"),
    FOREIGN_RESIDENT("FOREIGN_RESIDENT", "外国人居留证", "WECHAT"),
    TAIWAN_RESIDENT("TAIWAN_RESIDENT", "台湾居民证", "WECHAT");

    private final String document;
    private final String desc;
    private final String channel;

    IdentificationTypeEnum(final String document, final String desc, final String channel) {
        this.document = document;
        this.desc = desc;
        this.channel = channel;
    }

    public static List<IdentificationTypeEnum> getWechatIdentificationType() {
        return Arrays.stream(IdentificationTypeEnum.values())
                .filter(e -> "WECHAT".equals(e.channel) || "ALL".equals(e.channel)).collect(Collectors.toList());
    }

    public static List<IdentificationTypeEnum> getAliPayIdentificationType() {
        return Arrays.stream(IdentificationTypeEnum.values())
                .filter(e -> "ALL".equals(e.channel)).collect(Collectors.toList());
    }

    public String getChannelIdentificationType() {
        switch (this) {
            case ID_CARD:
                return "IDCARD";
            case OVERSEA_PASSPORT:
                return "PASSPORT";
            case HONG_KONG_PASSPORT:
            case MACAO_PASSPORT:
                return "HM_VISITORPASS";
            case FOREIGN_RESIDENT:
                return "FPRP";
            case HONG_KONG_MACAO_RESIDENT:
                return "HK_ID_CARD";
            //todo TAIWAN_PASSPORT
            case TAIWAN_PASSPORT:
                return "TAIWAN_PASSPORT";
            case TAIWAN_RESIDENT:
                return "TAIWAN";
            case SOLDIER:
                return "SOLDIER";
            case OFFICERS:
                return "OFFICERS";
            default:
                throw new BusinessException(ResultCode.PARAM_VALID_ERROR, "不支持的证件类型");
        }
    }


    public String getApplyChannelNoIdentificationType() {
        switch (this) {
            case ID_CARD:
                return "ID_CARD";
            case OVERSEA_PASSPORT:
                return "PASSPORT";
            case HONG_KONG_PASSPORT:
            case MACAO_PASSPORT:
                return "HM_VISITORPASS";
            case FOREIGN_RESIDENT:
                return "FPRP";
            case HONG_KONG_MACAO_RESIDENT:
                return "HM";
            case TAIWAN_PASSPORT:
                return "T_VISITORPASS";
            case TAIWAN_RESIDENT:
                return "TAIWAN";
            case SOLDIER:
                return "SOLDIER";
            case OFFICERS:
                return "OFFICERS";
            default:
                throw new BusinessException(ResultCode.PARAM_VALID_ERROR, "不支持的证件类型");
        }
    }
}
