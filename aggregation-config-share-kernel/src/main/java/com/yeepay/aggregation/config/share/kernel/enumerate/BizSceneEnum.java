package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/6 14:01
 */
@Getter
@AllArgsConstructor
public enum BizSceneEnum implements DocumentedEnum<String> {

    CUSTOMER_CENTER("CUSTOMER_CENTER", "客户中心进件"),
    AGG_ENTRY("AGG_ENTRY", "聚合进件");
    private final String document;
    private final String desc;
}
