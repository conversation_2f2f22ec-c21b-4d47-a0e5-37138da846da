package com.yeepay.aggregation.config.share.kernel.mask.strategy;
import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import org.apache.commons.lang3.StringUtils;

@SensitiveMaskStrategy(type = SensitiveTypeEnum.NAME)
public class NameMaskStrategy implements MaskStrategy {
    @Override
    public String mask(String content) {
        if (StringUtils.isEmpty(content)) {
            return StringUtils.EMPTY;
        }
        return content.charAt(0) + StringUtils.repeat("*", content.length() - 1);
    }

    @Override
    public boolean isValid(String content) {
        return content != null && content.length() >= 2;
    }
} 