package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * description: 认证状态枚举
 * <AUTHOR>
 * @since 2025/5/28:10:43
 * Company: 易宝支付(YeePay)
 */
@Getter
@AllArgsConstructor
public enum AuthStatusEnum implements DocumentedEnum<String> {

    WAIT_SUBMIT("WAIT_SUBMIT", "待提交", Collections.singletonList("APPLYMENT_STATE_EDITTING"),
            ThirdPartyRecordStatusEnum.INIT),

    PROCESSING("PROCESSING", "审核中", Collections.singletonList("APPLYMENT_STATE_WAITTING_FOR_AUDIT"),
            ThirdPartyRecordStatusEnum.PROCESSING),

    WAIT_CONTACT_CONFIRM("WAIT_CONTACT_CONFIRM", "待联系人确认",
            Arrays.asList("APPLYMENT_STATE_WAITTING_FOR_CONFIRM_CONTACT", "CONTACT_PROCESSING"),
            ThirdPartyRecordStatusEnum.PROCESSING),

    WAIT_LEGAL_CONFIRM("WAIT_LEGAL_CONFIRM", "待法人确认",
            Arrays.asList("APPLYMENT_STATE_WAITTING_FOR_CONFIRM_LEGALPERSON", "LEGAL_CONFIRM"),
            ThirdPartyRecordStatusEnum.PROCESSING),

    WAIT_ACCOUNT_VERIFY("WAIT_ACCOUNT_VERIFY", "待账户验证",
            Collections.singletonList("APPLYMENT_STATE_WAITTING_FOR_CONFIRM_LEGALPERSON"),
            ThirdPartyRecordStatusEnum.PROCESSING),

    AUDIT_PASS("AUDIT_PASS", "审核通过", Collections.singletonList("APPLYMENT_STATE_PASSED"),
            ThirdPartyRecordStatusEnum.SUCCESS),

    AUDIT_REJECT("AUDIT_REJECT", "审核驳回", Collections.singletonList("APPLYMENT_STATE_REJECTED"),
            ThirdPartyRecordStatusEnum.PROCESSING),

    AUDIT_FREEZE("AUDIT_FREEZE", "审核冻结", Collections.singletonList("APPLYMENT_STATE_FREEZED"),
            ThirdPartyRecordStatusEnum.PROCESSING),

    AUDIT_CANCELED("AUDIT_CANCELED", "已作废", Collections.singletonList("APPLYMENT_STATE_CANCELED"),
            ThirdPartyRecordStatusEnum.CLOSED),
    ;


    private String document;
    private String desc;
    private List<String> channelStatusList;
    private ThirdPartyRecordStatusEnum thirdRecordStatus;

    /**
     * 根据通道状态获取认证状态枚举
     * @param channelStatus 通道状态
     * @return AuthStatusEnum
     */
    public static AuthStatusEnum getEnumByChannelStatus(String channelStatus) {
        for (AuthStatusEnum value : AuthStatusEnum.values()) {
            if (value.getChannelStatusList() != null && value.getChannelStatusList().contains(channelStatus)) {
                return value;
            }
        }
        throw new IllegalArgumentException("未找到对应的认证状态枚举，通道状态：" + channelStatus);
    }
}
