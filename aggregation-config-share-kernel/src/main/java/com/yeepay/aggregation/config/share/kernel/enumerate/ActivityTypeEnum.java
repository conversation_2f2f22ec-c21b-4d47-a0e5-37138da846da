package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报备活动类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ActivityTypeEnum implements DocumentedEnum<String> {
    NORMAL("NORMAL", "默认"),
    EDUCATION("EDUCATION", "教培机构"),
    HEALTHCARE("HEALTHCARE", "商业医疗"),
    PARKING("PARKING", "停车缴费"),
    BLUE_SEE("BLUE_SEE", "蓝海"),
    GREEN_ISLAND("GREEN_ISLAND", "绿洲"),
    OLD_GREEN_ISLAND("OLD_GREEN_ISLAND", "老绿洲"),
    INSTITUTIONAL("INSTITUTIONAL", "事业单位"),
    CANTEEN("CANTEEN", "食堂"),

    ;

    private final String document;
    private final String desc;
}
