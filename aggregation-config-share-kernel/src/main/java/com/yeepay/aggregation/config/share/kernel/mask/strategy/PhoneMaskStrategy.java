package com.yeepay.aggregation.config.share.kernel.mask.strategy;
import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 手机号掩码策略
 * <AUTHOR>
 * @date 2025/2/23 17:05
 */
@SensitiveMaskStrategy(type = SensitiveTypeEnum.PHONE)
public class PhoneMaskStrategy implements MaskStrategy {

    // 验证模式：允许带空格/连字符的11位手机号
    private static final Pattern VALID_PATTERN = Pattern.compile("^\\s*(?:\\d{3}[ -]?\\d{4}[ -]?\\d{4}|\\d{11})\\s*$");

    // 清洗模式：去除所有非数字字符
    private static final Pattern CLEAN_PATTERN = Pattern.compile("[^\\d]");

    // 掩码模式（预编译提升性能）
    private static final Pattern MASK_PATTERN = Pattern.compile("(\\d{3})(\\d{4})(\\d{4})");
    private static final String MASK_REPLACEMENT = "$1****$3";

    @Override
    public String mask(String content) {
        // 快速返回空字符串，如果输入为空或空白
        if (StringUtils.isBlank(content)) {
            return StringUtils.EMPTY;
        }

        // 验证输入的手机号是否有效
        if (!isValid(content)) {
            return content;
        }

        // 标准化手机号，去除非数字字符
        String standardizedPhone = standardizePhoneNumber(content);

        // 应用掩码规则
        return applyMask(standardizedPhone);
    }

    @Override
    public boolean isValid(String content) {
        // 快速返回 false，如果输入为空或空白
        if (StringUtils.isBlank(content)) {
            return false;
        }
        // 验证手机号是否符合格式
        return VALID_PATTERN.matcher(content.trim()).matches();
    }

    /**
     * 标准化手机号，去除所有非数字字符
     * @param phoneNumber 原始手机号
     * @return 标准化后的手机号
     */
    private String standardizePhoneNumber(String phoneNumber) {
        return CLEAN_PATTERN.matcher(phoneNumber).replaceAll("");
    }

    /**
     * 应用手机号掩码规则
     * @param phoneNumber 标准化后的手机号
     * @return 掩码后的手机号
     */
    private String applyMask(String phoneNumber) {
        return MASK_PATTERN.matcher(phoneNumber).replaceAll(MASK_REPLACEMENT);
    }
}