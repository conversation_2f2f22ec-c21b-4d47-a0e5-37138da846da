package com.yeepay.aggregation.config.share.kernel.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Title: 类主题
 * Description: 使用说明/描述
 */
@Getter
@AllArgsConstructor
public enum PaySceneEnum implements DocumentedEnum<String> {

    ONLINE("ONLINE","线上"),
    OFFLINE("OFFLINE","线下"),
    INSURANCE("BAOXIAN","保险"),
    CREDIT("CREDIT","借记卡"),
    DEBIT("DEBIT","贷记卡"),
    COMMONWEAL("GONGYI","公益"),
    DC_SEPARATION("DC_SEPARATION","借贷分离"),
    DIGITAL("DIGITAL","数娱"),
    REGISTRATION("REGISTRATION","报名"),
    PRIVATE_EDUCATION("PRIVATE_EDUCATION","民办教育"),
    LARGE("LARGE","大额"),
    DIRECT("DIRECT","直连"),
    DIRECT_STANDARD("DIRECT_STANDARD","直连标准"),
    PUBLIC_CONTRIBUTIONS("GONGJIAO","公缴"),
    STORE_ASST("STORE_ASST", "微信B2B门店助手"),
    DIGITAL_CURRENCY("DIGITAL_CURRENCY", "数币"),
    SESAME_CREDIT("SESAME_CREDIT", "芝麻先享"),
    SCHOOL("SCHOOL", "校园"),
    ;

    private final String document;
    private final String desc;
}
