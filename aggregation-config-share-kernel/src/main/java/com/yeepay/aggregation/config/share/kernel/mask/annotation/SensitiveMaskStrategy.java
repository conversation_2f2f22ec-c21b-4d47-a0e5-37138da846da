package com.yeepay.aggregation.config.share.kernel.mask.annotation;

import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;

import java.lang.annotation.*;



/**
 * <AUTHOR>
 * @date 2025/2/23 16:39
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SensitiveMaskStrategy {
    /**
     * 敏感信息类型
     */
    SensitiveTypeEnum type();
} 