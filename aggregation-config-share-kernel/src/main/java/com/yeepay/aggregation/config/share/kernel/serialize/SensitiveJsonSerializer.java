package com.yeepay.aggregation.config.share.kernel.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategyFactory;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.Sensitive;


import java.io.IOException;
import java.util.Objects;

/**
 * 敏感信息序列化器
 */
public class SensitiveJsonSerializer extends JsonSerializer<String> implements ContextualSerializer {
    
    private final SensitiveTypeEnum type;
    private final int prefix;
    private final int suffix;
    private final char maskChar;

    public SensitiveJsonSerializer() {
        this.type = null;
        this.prefix = 0;
        this.suffix = 0;
        this.maskChar = '*';
    }

    public SensitiveJsonSerializer(SensitiveTypeEnum type, int prefix, int suffix, char maskChar) {
        this.type = Objects.requireNonNull(type, "Sensitive type cannot be null");
        this.prefix = prefix;
        this.suffix = suffix;
        this.maskChar = maskChar;
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeString(mask(value));
    }
    
    private String mask(String value) {
        if (type == null) {
            return value;
        }
        
        MaskStrategy strategy = type == SensitiveTypeEnum.CUSTOM ?
            MaskStrategyFactory.getCustomStrategy(prefix, suffix, maskChar) :
            MaskStrategyFactory.getStrategy(type);
            
        return strategy.mask(value);
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) {
        if (property == null) {
            return this;
        }
        
        Sensitive sensitive = property.getAnnotation(Sensitive.class);
        if (sensitive == null) {
            return this;
        }
        
        return new SensitiveJsonSerializer(
            sensitive.type(),
            sensitive.prefix(),
            sensitive.suffix(),
            sensitive.maskChar()
        );
    }
} 