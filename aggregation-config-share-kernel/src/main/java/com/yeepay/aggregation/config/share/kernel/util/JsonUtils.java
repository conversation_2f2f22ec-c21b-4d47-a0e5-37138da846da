package com.yeepay.aggregation.config.share.kernel.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.yeepay.aggregation.config.share.kernel.enumerate.ResultCode;
import com.yeepay.aggregation.config.share.kernel.exception.JsonException;
import com.yeepay.aggregation.config.share.kernel.serialize.SensitiveJsonSerializer;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

@UtilityClass
@Slf4j
public class JsonUtils {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final ObjectMapper SENSITIVE_OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
        OBJECT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 配置日期时间格式
        OBJECT_MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        // 配置 Java 8 日期格式
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        // 配置 LocalDate 格式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));
        // 配置 LocalTime 格式
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));
        // 配置 LocalDateTime 格式
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
        // 启用漂亮的格式化输出
        //OBJECT_MAPPER.enable(SerializationFeature.INDENT_OUTPUT);

        SimpleModule sensitiveModule = new SimpleModule();
        sensitiveModule.addSerializer(String.class, new SensitiveJsonSerializer());
        SENSITIVE_OBJECT_MAPPER.registerModule(sensitiveModule);
        SENSITIVE_OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        SENSITIVE_OBJECT_MAPPER.registerModule(new JavaTimeModule());
        SENSITIVE_OBJECT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        SENSITIVE_OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 配置日期时间格式
        SENSITIVE_OBJECT_MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        SENSITIVE_OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("UTC"));
        // 启用漂亮的格式化输出
        SENSITIVE_OBJECT_MAPPER.enable(SerializationFeature.INDENT_OUTPUT);
    }

    public static boolean isJson(String str) {
        try {
            OBJECT_MAPPER.readTree(str);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /**
     * 将Java对象转换为JSON字符串
     *
     * @param obj 需要转换的对象
     * @return JSON字符串
     */
    public static String toJSONString(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to JSONString", e);
            throw new JsonException(ResultCode.JSON_PARSE_ERROR, "Failed to convert object to JSONString");
        }
    }

    public static String toSensitiveJson(Object obj) {
        try {
            return SENSITIVE_OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Sensitive-Failed to convert object to JSONString", e);
            throw new JsonException(ResultCode.JSON_PARSE_ERROR, "Failed to convert object to JSONString");
        }
    }

    /**
     * 将JSON字符串转换为Java对象
     *
     * @param json  JSON字符串
     * @param clazz 目标对象的Class类型
     * @param <T>   目标对象的类型
     * @return 转换后的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert String to Object", e);
            throw new JsonException(ResultCode.JSON_PARSE_ERROR, "Failed to convert String to Object");
        }
    }

    public static <T> T fromJson(String json, TypeReference<T> type) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, type);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert String to Object", e);
            throw new JsonException(ResultCode.JSON_PARSE_ERROR, "Failed to convert String to Object");
        }
    }


}
