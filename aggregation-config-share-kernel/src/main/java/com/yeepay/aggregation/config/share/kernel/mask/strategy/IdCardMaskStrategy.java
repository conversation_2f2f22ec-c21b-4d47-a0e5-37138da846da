package com.yeepay.aggregation.config.share.kernel.mask.strategy;
import com.yeepay.aggregation.config.share.kernel.enumerate.SensitiveTypeEnum;
import com.yeepay.aggregation.config.share.kernel.mask.MaskStrategy;
import com.yeepay.aggregation.config.share.kernel.mask.annotation.SensitiveMaskStrategy;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 身份证号掩码策略（支持带分隔符格式）
 * <AUTHOR>
 * @date 2025/2/23 17:21
 */
@SensitiveMaskStrategy(type = SensitiveTypeEnum.ID_CARD)
public class IdCardMaskStrategy implements MaskStrategy {
    // 综合验证清洗模式（合并处理）
    private static final Pattern VALID_CLEAN_PATTERN = Pattern.compile(
            "^\\s*" +
                    "(\\d{6})" +                 // 行政区划码
                    "(19|20)(\\d{2})" +          // 年份前缀+年份后两位
                    "(0[1-9]|1[0-2])" +          // 月份
                    "(0[1-9]|[12]\\d|3[01])" +   // 日期
                    "(\\d{3})" +                 // 顺序码
                    "([0-9Xx])" +                // 校验码
                    "\\s*$"
    );

    // 掩码替换模式（预编译）
    private static final String MASK_REPLACEMENT = "$1$2$3$4$5******$6";

    @Override
    public String mask(String content) {
        if (StringUtils.isBlank(content)) {
            return StringUtils.EMPTY;
        }

        Matcher matcher = VALID_CLEAN_PATTERN.matcher(content);
        if (!matcher.matches()) {
            return StringUtils.EMPTY;
        }

        // 拼接标准格式并掩码
        return matcher.replaceAll(MASK_REPLACEMENT)
                .toUpperCase()
                .replaceAll("(.{4})(.{6})(.{8})", "$1**********$3");
    }

    @Override
    public boolean isValid(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        return VALID_CLEAN_PATTERN.matcher(content).matches();
    }
}
