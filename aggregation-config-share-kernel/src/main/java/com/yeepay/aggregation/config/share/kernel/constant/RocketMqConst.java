package com.yeepay.aggregation.config.share.kernel.constant;


public interface RocketMqConst {

    /**
     * 挂靠结果通知topic
     */
    String ANCHORED_RESULT_TOPIC = "agg_config_at_anchored_result_notify";

    /**
     * 挂靠结果通知  进件模块消费者组 用于闭环成功
     */
    String ENTRY_ANCHORED_RESULT_GROUP = "agg_config_at_anchored_result_notify_entry_group";

    /**
     * 进件结果通知topic
     */
    String ENTRY_RESULT_TOPIC = "agg_config_at_entry_result_notify";

    /**
     * 渠道分帐方创建通知MQ
     */
    String CHANNEL_DIVIDE_CREATE_RESULT_TOPIC = "wechat_direct_divide_receiver_topic";

    /**
     * 渠道分帐方创建通知MQ
     */
    String CHANNEL_DIVIDE_CREATE_RESULT_GROUP = "agg_config_divide_create_result_notify_group";

    /**
     * 渠道终端报备结果回调
     */
    String CHANNEL_TERMINAL_REPORT_TOPIC = "addOpenPayTerminalReportResult";

    String CHANNEL_TERMINAL_REPORT_GROUP = "agg_config_terminal_report_result_notify_group";


    /**
     * 进件结果回调-topic
     */
    String CHANNEL_ENTRY_RESULT_TOPIC = "addOpenPayReportResult";

    /**
     * 进件结果回调-group
     */
    String CHANNEL_ENTRY_RESULT_GROUP = "agg_config_channel_entry_result_notify_group";


    /**
     * 渠道号申请结果回调-topic
     */
    String CHANNEL_NO_APPLY_RESULT_TOPIC = "channelApplyRecordResult";

    /**
     * 渠道号申请结果回调-group
     */
    String CHANNEL_NO_APPLY_RESULT_GROUP = "agg_config_channel_no_apply_result_notify_group";

}
